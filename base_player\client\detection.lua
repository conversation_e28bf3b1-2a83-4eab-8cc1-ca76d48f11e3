local lastHealth = 200
local lastSpeed = 0.0

local DAMAGE_OVER_TIME_TYPES = {
    ENV_BURNS = true,
    ENV_GAS = true,
    ENV_DROWNING = true
}

CreateThread(function()
    while true do
        Wait(0)
        local health = GetEntityHealth(PlayerPedId())
        if health > lastHealth or (lastHealth > 200 and health <= 200) then
            lastHealth = health
        end

        local speed = GetEntitySpeed(PlayerPedId())
        lastSpeed = speed
    end
end)

AddEventHandler("gameEventTriggered", function(name, args)
    local victim, attacker, fatal, weaponHash, isMelee = args[1], args[2], args[6], args[7], args[12]
    if name ~= "CEventNetworkEntityDamage" or victim ~= PlayerPedId() then
        return
    end

    local damageData = {
        weaponHash = weaponHash,
        isMelee = isMelee == 1,
        fatal = fatal == 1,
        isDead = isDead,
        severity = nil
    }


    -- Resolve the damage amount
    local health = GetEntityHealth(victim)
    if health < lastHealth then
        damageData.damage = lastHealth - health
    elseif not damageData.fatal then
        return
    end
    lastHealth = health

    local victimCoords = GetEntityCoords(PlayerPedId())
    damageData.victimCoords = victimCoords

    -- Resolve the weapon type
    local weaponType = WeaponCategories[weaponHash]
    if not weaponType then
        local typeGroup = GetWeapontypeGroup(weaponHash)
        TriggerServerEvent("playerDamage:missingWeapon", weaponHash, typeGroup)
        if not damageData.fatal then
            return
        end
    end
    damageData.weaponType = weaponType

    -- if MINIMUM_DAMAGES[damageData.weaponType] ~= nil then
    ClearEntityLastDamageEntity(PlayerPedId())
    -- end

    -- This will only trigger if the wound was fatal
    if not weaponType then
        damageData.weaponType = "ENV_UNK"
        damageData.weaponLabel = "Unknown"
    elseif weaponType == "ENV_UNK" then
        if GetPlayerUnderwaterTimeRemaining(PlayerId()) <= 0 then
            damageData.weaponType = "ENV_DROWNING"
            damageData.weaponLabel = "Drowning"
        elseif IsEntityOnFire(victim) then
            damageData.weaponType = "ENV_BURNS"
            damageData.weaponLabel = "Burns"
        else
            damageData.weaponType = "ENV_FALL"
            damageData.weaponLabel = "Fall"
        end
    else
        -- Resolve the weapon label
        local weaponLabel = WeaponLabels[weaponHash]
        if weaponLabel ~= nil then
            damageData.weaponLabel = weaponLabel
        end
    end

    -- Resolve the bone the player took the damage on
    local _, bone = GetPedLastDamageBone(victim)
    damageData.bone = bone

    local boneLabel = BoneLabels[bone]
    if boneLabel ~= nil then
        damageData.boneLabel = boneLabel

        local zone = WoundLocationZones[boneLabel]
        if zone ~= nil then
            damageData.zone = zone
        end
    end

    -- Resolve when a damage is over time
    if DAMAGE_OVER_TIME_TYPES[damageData.weaponType] then
        damageData.damageOverTime = true
    end

    -- Resolve the attacker to a player
    if DoesEntityExist(attacker) and IsPedAPlayer(attacker) then
        local attackerPlayer = NetworkGetPlayerIndexFromPed(attacker)

        local foundImpact, impactCoords = GetPedLastWeaponImpactCoord(attacker)
        if foundImpact then
            local boneIndex = GetPedBoneIndex(attacker, bone)
            local boneCoords = GetWorldPositionOfEntityBone(PlayerPedId(), boneIndex)
            damageData.distFromBone = #(impactCoords - boneCoords)
        end

        if attackerPlayer ~= nil then
            damageData.attacker = GetPlayerServerId(attackerPlayer)
            damageData.attackerCoords = GetEntityCoords(attacker)
            damageData.range = #(victimCoords - damageData.attackerCoords)
        end
    end

    -- If the damageType is env_vehicle, get the car that hit the player and the speed
    if damageData.weaponType == "ENV_VEHICLE" then
        local vehicle = GetClosestVehicleToCoords(victimCoords, 5.0)
        if vehicle ~= nil then
            damageData.vehicle = vehicle
            damageData.vehicleNet = NetworkGetNetworkIdFromEntity(vehicle)
            damageData.vehicleSpeed = GetEntitySpeed(vehicle) * 3.6
        end
    end

    if weaponType then
        local vehicle = GetVehiclePedIsIn(victim, false)
        if vehicle ~= nil then
            damageData.victimVehicleSpeed = GetEntitySpeed(vehicle) * 3.6
        else
            damageData.victimVehicleSpeed = 0
        end
    end

    if damageData.weaponType == "ENV_FALL" then
        damageData.speed = lastSpeed * 3.6
    end

    if damageData.weaponHash == `WEAPON_IMPACTGLOCK` or damageData.weaponHash == `WEAPON_RUBBERM4A1` then -- Impact glock and rubber M4A1
        local targetVeh = GetVehiclePedIsIn(GetPlayerPed(-1), false)
        if DoesEntityExist(targetVeh) then
            TaskLeaveVehicle(GetPlayerPed(-1), targetVeh, 4160)
        end
        Citizen.CreateThread(function()
            while IsPedInVehicle(GetPlayerPed(-1), targetVeh, false) do
                Wait(50)
            end
            SetPedToRagdoll(GetPlayerPed(-1), 2000, 2000, 0, 0, 0, 0)
        end)
    end


    if damageData.fatal then
        damageData.severity = getWoundSeverity(damageData)
    end

    TriggerEvent("playerDamage", damageData)
    TriggerServerEvent("playerDamage", damageData)
end)

-- Wounds that are considered knockout will cause the player to be knocked out, with an auto recovery
local KO_WOUNDS = {
    WATERCANNON = true,
    MELEE_BLUNT = true,
    WELT_PAINTBALL = true,
}

-- Wounds that are considered minor will allow the player to respawn after 5 minutes with their items
local MINOR_WOUNDS = {
    BITE = true,
    ENV_VEHICLE_INTERNAL = true,
    MELEE_STAB = true,
}

local SPEEDS = {
    ENV_VEHICLE = {
        knockout = 40, -- 40 or under = knockout
        minor = 100,   -- 80 or under = minor
        severe = 999,  -- 180 or under = severe
        -- anything else, fatal
    },
    ENV_FALL = {
        knockout = 40,
        minor = 80,
        severe = 999,
    }
}

local DAMAGE_OVERRIDES = {
    ["OVRD_KO"] = 1,
    ["OVRD_MINOR"] = 2,
    ["OVRD_SEVERE"] = 3,
    ["OVRD_DEAD"] = 4,
}

-- script
wounds = {}

function getWoundSeverity(wound)
    if DAMAGE_OVERRIDES[wound.weaponType] then
        return DAMAGE_OVERRIDES[wound.weaponType]
    end

    if KO_WOUNDS[wound.weaponType] then
        return 1
    end

    if wound.weaponType == "MELEE_STAB" and wound.damage >= 200 then
        return 3
    end

    if MINOR_WOUNDS[wound.weaponType] then
        return 2
    end

    local speed = wound.speed or wound.vehicleSpeed
    if speed and SPEEDS[wound.weaponType] then
        local speedLimits = SPEEDS[wound.weaponType]

        if speed <= speedLimits.knockout then
            return 1
        elseif speed <= speedLimits.minor then
            return 2
        elseif speed <= speedLimits.severe then
            return 3
        else
            return 4
        end
    end

    -- Assume any other damage is severe
    return 3
end

local DOT_SEVERITY_THRESHOLDS = {
    ENV_BURNS = {
        knockout = 0,
        minor = 3600,
        severe = 9600,
    },
    ENV_DROWNING = {
        knockout = 0,
        minor = 600,   -- 5 mins
        severe = 1200, -- 10 mins
    },
    ENV_GAS = {
        knockout = 0, -- 2 mins
        minor = 1000,
        severe = 5000,
    },
}
local dotCounts = {}
local dotWounds = {}

local function getDOTWoundSeverity(wound)
    local count = dotCounts[wound.weaponType]
    local threshold = DOT_SEVERITY_THRESHOLDS[wound.weaponType]

    if not threshold then
        return 3, "Severe"
    end

    if count <= (threshold.knockout or 0) then
        return 1, "Insignificant"
    elseif count <= (threshold.minor or 0) then
        return 2, "Minor"
    elseif count <= (threshold.severe or 0) then
        return 3, "Severe"
    else
        return 4, "Fatal"
    end
end

local function processDamageOverTime(wound)
    dotCounts[wound.weaponType] = (dotCounts[wound.weaponType] or 0) + 1

    if not dotWounds[wound.weaponType] then
        dotWounds[wound.weaponType] = wound
        table.insert(wounds, wound)
    else
        wound = dotWounds[wound.weaponType]
    end

    local severity, severityLabel = getDOTWoundSeverity(wound)
    wound.severity, wound.severityLabel = severity, severityLabel
end

local function processDamage(wound)
    wound.severity = getWoundSeverity(wound)
    table.insert(wounds, wound)
end

AddEventHandler("playerDamage", function(wound)
    if wound.damageOverTime then
        processDamageOverTime(wound)
    else
        processDamage(wound)
    end

    if wound.fatal and not isDead then
        handlePlayerDeath(wound)
    end
end)

AddEventHandler("playerRevived", function()
    wounds = {}
    dotCounts = {}
    dotWounds = {}
end)

-- Thread to detect if a player is in a car that has driven into the water
CreateThread(function()
    while true do
        Wait(1000)

        if not LocalPlayer.wounded then
            local ped = PlayerPedId()
            local vehicle = GetVehiclePedIsIn(ped, false)
            if vehicle ~= 0 then
                local model = GetEntityModel(vehicle)
                local isBoat = IsThisModelABoat(model) or IsThisModelAnAmphibiousCar(model) or IsThisModelAJetski(model) or
                    IsThisModelAnAmphibiousQuadBike(model)
                if not isBoat and GetEntitySubmergedLevel(vehicle) >= 1.0 then
                    ApplyDamageToPed(ped, 1000, false)

                    Wait(3000)

                    TriggerEvent("playerDamage", {
                        weaponLabel = "Water Impact",
                        weaponType = "OVRD_MINOR",
                        damageOverTime = false,
                        fatal = true,
                        isMelee = false,
                        boneLabel = "Unknown",
                        damage = 100,
                    })
                end
            end
        end
    end
end)

-- debug function
CreateThread(function()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    NetworkResurrectLocalPlayer(coords.x, coords.y, coords.z, 0.0, true, false)
    LocalPlayer.state:set("wounded", false, true)
    LocalPlayer.state:set("examined", false, true)
    -- SetEntityCoords(ped, coords.x, coords.y, coords.z + 20.0)

    Wait(1000)

    -- TriggerEvent("playerDamage", {
    --     -- speed = 40,
    --     fatal = true,
    --     weaponLabel = "Light GSW",
    --     isMelee = false,
    --     boneLabel = "Head",
    --     weaponType = "BITE",
    --     damage = 100,
    --     victimCoords = coords,
    --     bone = 31086,
    --     weaponHash = 453432689,
    --     isDead = false,
    -- })

    -- TriggerEvent("playerDamage", {
    --     -- speed = 40,
    --     fatal = true,
    --     weaponLabel = "Light GSW",
    --     isMelee = false,
    --     boneLabel = "Head",
    --     weaponType = "GSW_LIGHT",
    --     damage = 100,
    --     victimCoords = coords,
    --     bone = 31086,
    --     weaponHash = 453432689,
    --     isDead = false,
    -- })
end)
