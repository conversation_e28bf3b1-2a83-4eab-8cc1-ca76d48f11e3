ESCORT_ZONES = {
    -- <PERSON>
        {
            name = "sandyhospital",
            Orgs = { "lses", "afp", "police" },
            coords = {
                vector2(1762.17, 3634.31),
                vector2(1752.50, 3651.15),
                vector2(1731.89, 3639.22),
                vector2(1741.30, 3622.43),
            },
            details = {
                label = "Sandy Hospital",
                minZ = 33.0,
                maxZ = 37.0,
                debugGrid = false,
                gridDivisions = 25,
            },
            escortLocation = {
                coords = vector3(1731.19, 3640.96, 34.9),
                heading = 0.0,
                options = {}
            },
        },
    -- Paleto Hospital
        {
            name = "paletohospital",
            Orgs = { "lses", "afp", "police" },
            coords = {
                vector2(-271.17, 6320.01),
                vector2(-255.92, 6305.56),
                vector2(-245.10, 6315.63),
                vector2(-259.77, 6331.53),
            },
            details = {
                label = "Paleto Hospital",
                minZ = 31.0,
                maxZ = 35.0,
                debugGrid = false,
                gridDivisions = 25
            },
            escortLocation = {
                coords = vector3(-262.29, 6301.37, 32.21),
                heading = 0.0,
                options = {}
            },
        },
    -- Pillbox Hospital
        {
            name = "pillbox",
            Orgs = { "lses", "afp", "police" },
            coords = {
                vector2(294.32, -602.27),
                vector2(306.06, -561.36),
                vector2(343.56, -569.32),
                vector2(350.76, -553.41),
                vector2(373.11, -559.09),
                vector2(350.38, -603.41),
                vector2(333.71, -601.52)
            },
            details = {
                label = "Pillbox Hospital",
                minZ = 27.0,
                maxZ = 44.0,
                debugGrid = false,
                gridDivisions = 25
            },
            escortLocation = {
                coords = vector3(326.97, -624.17, 29.29),
                heading = 0.0,
                options = {}
            }
        },
    -- Diamond Casino
        {
            name = "casino",
            Orgs = { "casino", "afp", "police" },
            coords = {
                vector2(930.22869873047, 105.08248138428),
                vector2(1113.962890625, 423.68887329102),
                vector2(1310.5697021484, 303.98895263672),
                vector2(1089.1046142578, 12.016175270081)
            },
            details = {
                label = "Casino",
                minZ = -80.0,
                maxZ = -20.0,
                debugGrid = false,
                gridDivisions = 25
            },
            escortLocation = {
                coords = vector3(940.33618164063, 94.155006408691, 79.346870422363),
                heading = 47.37,
                options = {}
            }
        },
    -- Vanilla Unicorn
        {
            name = "vanilla_unicorn_ground",
            Orgs = { "unicorn", "afp", "police" },
            coords = {
                vector2(141.96, -1289.74),
                vector2(114.4, -1306),
                vector2(115.83, -1308.84),
                vector2(101.29, -1317.15),
                vector2(86.05, -1289.96),
                vector2(100.45, -1282.67),
                vector2(133.9, -1276.12),

            },
            details = {
                label = "VU",
                minZ = 28.4,
                maxZ = 33.0,
                debugGrid = false,
                gridDivisions = 25
            },
            escortLocation = {
                coords = vector3(127.12, -1304.72, 29.24),
                heading = 225.2189,
                options = {}
            }
        },
        {
            name = "vanilla_unicorn_upstairs",
            Orgs = { "unicorn", "afp", "police" },
            coords = {
                vector2(93.68, -1298.19),
                vector2(101.43, -1294.21),
                vector2(111.64, -1311.08),
                vector2(103.76, -1315.44),
            },
            details = {
                label = "VU",
                minZ = 33.0,
                maxZ = 38.0,
                debugGrid = false,
                gridDivisions = 25
            },
            escortLocation = {
                coords = vector3(127.12, -1304.72, 29.24),
                heading = 225.2189,
                options = {}
            }
        },
        {
            name = "vanilla_unicorn_underground",
            Orgs = { "unicorn", "afp", "police" },
            coords = {
                vector2(143.44, -1292.49),
                vector2(135.62, -1296.74),
                vector2(135.05, -1302.35),
                vector2(118.55, -1312.22),
                vector2(114.5, -1305.46),
                vector2(96.03, -1315.88),
                vector2(86.11, -1298.61),
                vector2(104.69, -1288.14),
                vector2(101.62, -1280.87),
                vector2(117.45, -1271.6),
                vector2(117.59, -1266.2),
                vector2(128.74, -1260.38),
            },
            details = {
                label = "VU",
                minZ = 17.0,
                maxZ = 28.4,
                debugGrid = false,
                gridDivisions = 25
            },
            escortLocation = {
                coords = vector3(127.12, -1304.72, 29.24),
                heading = 225.2189,
                options = {}
            }
        },
    -- Cyberbar
        {
            name = "cyberbar_top",
            Orgs = { "cyberbar", "afp", "police" },
            coords = {
                vector2(-866.2025, -2136.985),
                vector2(-833.063, -2106.305),
                vector2(-816.8133, -2132.022),
                vector2(-838.9183, -2160.184)
            },
            details = {
                label = "Cyber Bar",
                minZ = 8.0,
                maxZ = 12.0,
                debugGrid = false,
                gridDivisions = 25
            },
            escortLocation = {
                coords = vector3(-881.3492, -2166.903, 8.938734),
                heading = 47.4,
                options = {}
            }
        },
        {
            name = "cyberbar_bottom",
            Orgs = { "cyberbar", "afp", "police" },
            coords = {
                vector2(-825.0635, -2100.247),
                vector2(-811.1601, -2086.661),
                vector2(-785.9966, -2112.401),
                vector2(-799.8656, -2125.306)
            },
            details = {
                label = "Cyber Bar",
                minZ = 3.0,
                maxZ = 6.0,
                debugGrid = false,
                gridDivisions = 25
            },
            escortLocation = {
                coords = vector3(-808.6828, -2113.682, 3.9375),
                heading = 47.4,
                options = {}
            }
        }
}

function AreCoordsInEscortZone(coords)
    for i, zone in ipairs(ESCORT_ZONES) do
        local pz = PolyZone:Create(zone.coords, zone.details)
        if pz and pz:isPointInside(coords) then
            return true, zone
        end
    end

    return false
end

exports("AreCoordsInEscortZone", AreCoordsInEscortZone)
