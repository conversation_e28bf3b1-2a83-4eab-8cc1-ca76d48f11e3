-- This file handles the UI while a player is wounded and EMS alert logic

local emsAlerted = false
local timeUntilAlert = EMS_ALERT_TIMER

local function drawText(text, x, y, size, align)
    SetTextFont(4)
    SetTextProportional(0)
    SetTextScale(0.0, size)
    SetTextColour(255, 255, 255, 255)
    SetTextDropshadow(0, 0, 0, 0, 255)
    SetTextEdge(1, 0, 0, 0, 255)
    SetTextDropShadow()
    SetTextOutline()

    if align == "center" then
        SetTextJustification(0)
    elseif align == "right" then
        SetTextJustification(2)
        SetTextWrap(0.0, x)
    else
        SetTextJustification(1)
    end

    BeginTextCommandDisplayText("STRING")
    AddTextComponentSubstringPlayerName(text)
    EndTextCommandDisplayText(x, y)
end

local function isAlertBlocked()
    return LocalPlayer.state.inArena or LocalPlayer.state.disarmed or LocalPlayer.state.atCayo or LocalPlayer.state.inZancEvent or LocalPlayer.state.inBattle
end

--#region On screen display

local function startUIThread()
    CreateThread(function()
        while LocalPlayer.state.wounded do
            Wait(0)
            local woundState = LocalPlayer.state.woundedSeverity

            -- Draw UI
            if LocalPlayer.state.bodybag then
                drawText("You have been ~r~body bagged~w~ and ~b~need to respawn~b~", 0.5, 0.81, 0.7, "center")
                drawText("You will ~r~lose~w~ your items and memories", 0.5, 0.86, 0.5, "center")
            elseif woundState == "knockedout" then
                local elapsed = math.ceil((GetGameTimer() - LocalPlayer.state.woundedTime) / 1000)
                local timeRemaining = KNOCKOUT_TIME - elapsed
                drawText("You are ~g~knocked out~w~ and ~b~unable to communicate~b~", 0.5, 0.81, 0.7, "center")
                drawText("You will recover in ~g~" .. math.max(timeRemaining, 0) .. "~w~ seconds", 0.5, 0.86, 0.5, "center")
            else
                if hospitaliseTime > 0 then
                    drawText("You are ~g~hospitalised~w~", 0.5, 0.81, 0.7, "center")
                    drawText("Treatment will be completed in ~g~" .. math.ceil(hospitaliseTime) .. "~w~ seconds", 0.5, 0.86, 0.5, "center")
                elseif woundState == "dead" then
                    drawText("You are ~r~dead~w~ and ~b~unable to communicate~b~", 0.5, 0.81, 0.8, "center")
                    drawText("Your injuries are not treatable and paramedics cannot revive you.", 0.5, 0.86, 0.5, "center")
                elseif LocalPlayer.state.stabilised then
                    drawText("You are ~y~stable~w~ and ~b~able to communicate~b~", 0.5, 0.81, 0.8, "center")
                    drawText("You're conscious, but will need paramedics to transport you to a hospital.", 0.5, 0.86, 0.5, "center")
                elseif woundState == "severe" and not LocalPlayer.state.stabilised then
                    drawText("You are ~o~severely wounded~w~ and ~b~unable to communicate~b~", 0.5, 0.81, 0.7, "center")
                    drawText("Paramedics will need to stabilise and transport you to hospital.", 0.5, 0.86, 0.5, "center")
                elseif woundState == "minor" then
                    drawText("You are ~y~wounded~w~ but still ~b~able to communicate~b~", 0.5, 0.81, 0.8, "center")
                    drawText("You're conscious, but will need paramedics to treat you", 0.5, 0.86, 0.5, "center")
                end

                if hospitaliseTime <= 0 then
                    -- Respawn UI
                    local respawnItems = "~r~without your items or memories~w~"
                    if woundState == "minor" then
                        respawnItems = "~g~with your items and memories~w~"
                    end

                    if timeUntilRespawn <= 0 then
                        drawText("Press [~g~E~w~] to respawn " .. respawnItems, 0.93, 0.86, 0.5, "right")
                    else
                        drawText("Respawn in ~g~" .. math.ceil(timeUntilRespawn) .. "~w~ seconds " .. respawnItems, 0.93, 0.86, 0.5, "right")
                    end

                    -- EMS alert UI
                    if LocalPlayer.state.stabilised then
                        drawText("Paramedics have stabilised you, awaiting transport to hospital", 0.07, 0.86, 0.5, "left")
                    elseif isAlertBlocked() then
                        drawText("You are ~r~blocked~w~ from calling paramedics", 0.07, 0.86, 0.5, "left")
                    elseif emsAlerted then
                        drawText("Paramedics have been alerted to your location", 0.07, 0.86, 0.5, "left")
                    elseif timeUntilAlert <= 0 then
                        drawText("Press [~g~G~w~] to notify paramedics", 0.07, 0.86, 0.5, "left")
                    else
                        drawText("You can notify paramedics in ~g~" .. math.ceil(timeUntilAlert) .. "~w~ seconds", 0.07, 0.86, 0.5, "left")
                    end
                end
            end
        end
    end)
end

--#endregion

--#region EMS alert

local function susDeath()
    for _, wound in ipairs(wounds) do
        if wound.weaponType == "GSW_LIGHT" or wound.weaponType == "GSW_MEDIUM" or wound.weaponType == "GSW_HEAVY" then
            return true
        end
    end
    return false
end


local function alertEMS()
    if isAlertBlocked() then
        Base.Notification("You cannot call EMS right now")
        emsAlerted = false
        return
    end
    emsAlerted = true

    if susDeath() then
        HandleSusDeath()
    end
    -- Thread to update distress signal periodically
    CreateThread(function()
        while LocalPlayer.state.wounded and emsAlerted and not LocalPlayer.state.bodybagged do
            if isAlertBlocked() then
                emsAlerted = false
                return
            end

            local title = "Injured Person"
            local details = PlayerData.icname .. " is wounded and needs assistance"
            local colour = 5 -- yellow

            if LocalPlayer.state.hospitalised then
                title = "Hospitalised Patient"
                details = PlayerData.icname .. " is hospitalised and will be released soon"
                colour = 3 -- blue
            elseif LocalPlayer.state.stabilised then
                title = "Stabilised Patient"
                details = PlayerData.icname .. " has been stabilised and needs transport to hospital"
                colour = 2 -- green
            elseif LocalPlayer.state.woundedSeverity == "severe" or LocalPlayer.state.woundedSeverity == "dead" then
                title = "Critical Patient"
                details = PlayerData.icname .. " is in critical condition and needs urgent assistance"
                colour = 1 -- red
            end

            local coords = GetEntityCoords(PlayerPedId())
            TriggerServerEvent("main_dispatch:StartCall", 'unconcious_' .. GetPlayerServerId(PlayerId()), 'lses', title, details,
                {
                    location = exports["core_hud"]:getPlayerLocation()
                },
                {
                    blockUpdateNotif = true,
                    blockReceivedUpdate = true,
                    caller = PlayerId(),
                    theme = "error",
                    gps = { x = coords.x, y = coords.y, z = coords.z },
                    blip = {
                        label = title,
                        pos = { x = coords.x, y = coords.y, z = coords.z },
                        colour = colour
                    },
                }
            )

            Wait(EMS_ALERT_UPDATE_INTERVAL * 1000)
        end
    end)
end

local function startAlertTimerThread()
    CreateThread(function()
        while LocalPlayer.state.wounded and timeUntilAlert > 0 do
            timeUntilAlert = timeUntilAlert - 1
            Wait(1000)
        end
    end)
end

--#endregion

AddEventHandler("playerWounded", function()
    SetPedConfigFlag(PlayerPedId(), 461, true) --Disables stungun ragdoll effect
    startUIThread()

    -- Set up alert timer
    emsAlerted = false
    timeUntilAlert = EMS_ALERT_TIMER
    startAlertTimerThread()

    -- Control thread for alert
    while LocalPlayer.state.wounded do
        Wait(0)

        -- Handle input
        if LocalPlayer.state.woundedSeverity ~= "knockedout" and not emsAlerted then
            if IsControlJustReleased(0, 47) and timeUntilAlert <= 0 then
                alertEMS()
            end
        end
    end
    SetPedConfigFlag(PlayerPedId(), 461, false) --Enables stungun ragdoll effect
end)
