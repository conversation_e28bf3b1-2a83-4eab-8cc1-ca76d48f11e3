Citizen.CreateThread(function()
  while true do
    Wait(0)
    if (IsControlJustPressed(1, 212) and IsControlJustPressed(1, 213)) then
      if group ~= "user" then
        SetNuiFocus(true, true)
        SendNUIMessage({ type = 'open', players = playerList, admin = GetPlayerName(GetPlayerPed(-1)), jobs = {}, weapons = Base.GetWeaponList() })
        GeneralLog("Home Menu Log", PlayerData.logName .. " has opened the home menu", "blue", "admin-menu")
      end
    end
  end
end)

RegisterNUICallback('close', function(data, cb)
  SetNuiFocus(false)
end)

RegisterNetEvent("admin:spawnVehicleOnPed")
AddEventHandler("admin:spawnVehicleOnPed", function(model, job)
  exports["base_vehicle"]:createFromModel(model, GetEntityCoords(GetPlayerPed(-1)), GetEntityHeading(GetPlayerPed(-1)), (job == nil and { ["admin_spawned"] = true } or { ["job"] = job, ["admin_spawned"] = true }),
    function(vehicle)
      SetPedIntoVehicle(GetPlayerPed(-1), vehicle, -1)
      TriggerServerEvent("orgs:trackVehicle", NetworkGetNetworkIdFromEntity(vehicle), job)
    end,
    false)
end)

RegisterNUICallback('singleaction', function(data, cb)
  if data.type == "revive_all" or data.type == "bring_all" or data.type == "slap_all" then
    TriggerServerEvent('fdg_admin:all', data.type)
  else
    TriggerServerEvent('fdg_admin:singleaction', data.id, data.type, data.reason, data.time)
  end
end)

RegisterNUICallback('set', function(data, cb)
  TriggerServerEvent('fdg_admin:set', data.type, data.user, data.param)
end)

RegisterNUICallback('newadmin', function(data, cb)
  TriggerServerEvent('fdg_admin:newadmin', data.user, data.type, data.reason)
end)

RegisterNUICallback('history', function(data, cb)
  TriggerServerEvent('fdg_admin:history', data.id)
end)

-- lol this did not fix the bug where when car not rendered no worky, hehe whoops
local function getVehicleTargetIsIn(targetPed)
  local vehicle = GetVehiclePedIsIn(targetPed, false)
  while not DoesEntityExist(vehicle) and IsPedInAnyVehicle(targetPed, true) do
    vehicle = GetVehiclePedIsIn(targetPed, false)
    Wait(0)
  end
  GetVehiclePedIsIn(targetPed, false)

  if DoesEntityExist(vehicle) then
    return vehicle
  else
    return nil
  end
end

local function putPlayerInAnySeat(targetPed, vehicle)
  for i = 0, GetVehicleModelNumberOfSeats(GetEntityModel(vehicle)) do
    if IsVehicleSeatFree(vehicle, i) then
      SetPedIntoVehicle(targetPed, vehicle, i)
      return true
    end
  end
  return false
end

RegisterNetEvent('fdg_admin:bringPlayer')
AddEventHandler("fdg_admin:bringPlayer", function(target)
  Wait(1000)
  local playerPed = PlayerPedId()
  local player = GetPlayerFromServerId(target)

  if player ~= -1 then
    local targetPed = GetPlayerPed(player)
    local targetCoords = GetEntityCoords(targetPed)

    local vehicle = getVehicleTargetIsIn(targetPed)
    if not vehicle or not putPlayerInAnySeat(playerPed, vehicle) then
      SetEntityCoords(playerPed, targetCoords.x, targetCoords.y, targetCoords.z)
    end
  end
end)

RegisterNetEvent('fdg_admin:gotoPlayer')
AddEventHandler("fdg_admin:gotoPlayer", function(target)
  local playerPed = PlayerPedId()
  local targetPed = GetPlayerPed(GetPlayerFromServerId(target))
  local targetCoords = GetEntityCoords(targetPed)

  SetEntityCoords(playerPed, targetCoords.x, targetCoords.y, targetCoords.z - 10.0)
  Wait(250)

  local vehicle = getVehicleTargetIsIn(targetPed)
  if not vehicle or not putPlayerInAnySeat(playerPed, vehicle) then
    SetEntityCoords(playerPed, targetCoords.x, targetCoords.y, targetCoords.z)
  end
end)

local LastPosition
RegisterNetEvent('core_admin:setBack')
AddEventHandler("core_admin:setBack", function(Position)
  LastPosition = Position
end)

RegisterNetEvent('core_admin:goBack')
AddEventHandler("core_admin:goBack", function()
  if LastPosition then
    TriggerEvent('chat:addMessage', "SYSTEM", { 255, 0, 0 }, "You have been returned to your last position")
    SetEntityCoords(PlayerPedId(), LastPosition, 0.0, 0.0, 0.0, false)
  else
    TriggerEvent('chat:addMessage', "SYSTEM", { 255, 0, 0 }, "No last position found")
  end
end)

RegisterNetEvent('fdg_admin:freeze')
AddEventHandler("fdg_admin:freeze", function(isFrozen)
  FreezeEntityPosition(GetPlayerPed(-1), isFrozen)
end)

RegisterNetEvent('fdg_admin:slap')
AddEventHandler('fdg_admin:slap', function()
  ApplyForceToEntity(GetPlayerPed(-1), 1, 9500.0, 3.0, 7100.0, 1.0, 0.0, 0.0, 1, false, true, false, false)
end)

RegisterNetEvent('fdg_admin:crash')
AddEventHandler('fdg_admin:crash', function()
  while true do
  end
end)

RegisterNetEvent('fdg_admin:info')
AddEventHandler('fdg_admin:info', function(data)
  local sexLabel    = nil
  local sex         = nil
  local dobLabel    = nil
  local heightLabel = nil
  local idLabel     = nil
  local Money       = 0
  local Bank        = 0
  local blackMoney  = 0
  local Inventory   = nil

  -- main jb
  if data.sex ~= nil then
    if (data.sex == 'm') or (data.sex == 'M') then
      sex = 'Male'
    else
      sex = 'Female'
    end
    sexLabel = 'Sex : ' .. sex
  else
    sexLabel = 'Sex : Unknown'
  end

  if data.money ~= nil then
    Money = data.money
  else
    Money = 'No Data'
  end

  if data.bank ~= nil then
    Bank = data.bank
  else
    Bank = 'No Data'
  end

  if data.dirty ~= nil then
    blackMoney = data.dirty
  else
    blackMoney = 'No Data'
  end

  if data.dob ~= nil then
    dobLabel = 'DOB : ' .. data.dob
  else
    dobLabel = 'DOB : Unknown'
  end

  if data.height ~= nil then
    heightLabel = 'Height : ' .. data.height
  else
    heightLabel = 'Height : Unknown'
  end

  if data.name ~= nil then
    idLabel = 'Steam ID : ' .. data.name
  else
    idLabel = 'Steam ID : Unknown'
  end

  if data.identifier then
    identifierLabel = 'Account ID : ' .. data.identifier
  else
    identifierLabel = 'Account ID : Unknown'
  end

  local elements = {
    { label = 'Name: ' .. data.icname,        value = nil },
    { label = 'Money: ' .. data.money,        value = nil },
    { label = 'Bank: ' .. data.bank,          value = nil },
    { label = 'Casino Chips: ' .. blackMoney, value = nil, itemType = 'item_account', amount = blackMoney },
    { label = idLabel,                        value = nil },
    { label = identifierLabel,                value = nil }
  }

  if data.licenses ~= nil then
    table.insert(elements, { label = '--- Licenses ---', value = nil })

    for i = 1, #data.licenses, 1 do
      table.insert(elements, { label = data.licenses[i].label, value = nil })
    end
  end

  Base.UI.Menu.Open(
    'default', GetCurrentResourceName(), 'citizen_interaction',
    {
      title    = 'Player Control',
      align    = 'bottom-right',
      elements = elements,
    },
    function(data, menu) end,
    function(data, menu)
      menu.close()
    end
  )
end)

RegisterNetEvent('fdg_admin:history')
AddEventHandler('fdg_admin:history', function(result, pname, id)
  SetNuiFocus(true, true)
  SendNUIMessage({ type = 'history', history = result, name = pname, id = id })
end)

RegisterNetEvent('fdg_admin:slay')
AddEventHandler('fdg_admin:slay', function()
  TriggerEvent("playerDamage", {
    fatal = true,
    weaponLabel = "Unknown",
    isMelee = false,
    boneLabel = "Unknown",
    weaponType = "OVRD_DEAD",
    damage = 100,
  })
end)

RegisterNetEvent('fdg_admin:explode')
AddEventHandler('fdg_admin:explode', function()
  local coords = GetEntityCoords(GetPlayerPed(-1))
  AddExplosion(coords.x, coords.y, coords.z, "EXPLOSION_BOAT", 8.0, true, false, true)
  SetEntityHealth(GetPlayerPed(-1), 0)
end)

RegisterNetEvent('fdg_admin:spawnVehicle')
AddEventHandler('fdg_admin:spawnVehicle', function(v)
  local carid = GetHashKey(v)
  local playerPed = GetPlayerPed(-1)
  if playerPed and playerPed ~= -1 then
    RequestModel(carid)
    while not HasModelLoaded(carid) do
      Citizen.Wait(0)
    end
    local playerCoords = GetEntityCoords(playerPed)

    veh = CreateVehicle(carid, playerCoords, 0.0, true, false)
    TaskWarpPedIntoVehicle(playerPed, veh, -1)
    SetModelAsNoLongerNeeded(carid)
  end
end)

RegisterNetEvent('fdg_admin:dv')
AddEventHandler('fdg_admin:dv', function()
  local closestVehicle = Base.Vehicles.GetClosestVehicle(GetPlayerPed(-1), 10.0, true)

  if closestVehicle then
    TriggerServerEvent('fdg_admin:dv', NetworkGetNetworkIdFromEntity(closestVehicle))
  else
    TriggerEvent('chat:addMessage', "DV", { 255, 0, 0 }, "No Vehicle Nearby")
  end
end)

RegisterNetEvent('fdg_admin:dvRadius')
AddEventHandler('fdg_admin:dvRadius', function(radius)
  local vehicles = Base.Vehicles.GetVehiclesInRadius(GetPlayerPed(-1), radius - 0.001)

  local plates = {}
  for i = 1, #vehicles do
    Base.DeleteEntity(vehicles[i])
    table.insert(plates, GetVehicleNumberPlateText(vehicles[i]))
  end
  GeneralLog("Delete Vehicle Radius Log", PlayerData.logName .. " has used dvr on " .. json.encode(plates), "blue", "admin-commands")
end)

local entityEnumerator = {
  __gc = function(enum)
    if enum.destructor and enum.handle then
      enum.destructor(enum.handle)
    end
    enum.destructor = nil
    enum.handle = nil
  end
}

local function EnumerateEntities(initFunc, moveFunc, disposeFunc)
  return coroutine.wrap(function()
    local iter, id = initFunc()
    if not id or id == 0 then
      disposeFunc(iter)
      return
    end

    local enum = { handle = iter, destructor = disposeFunc }
    setmetatable(enum, entityEnumerator)

    local next = true
    repeat
      coroutine.yield(id)
      next, id = moveFunc(iter)
    until not next

    enum.destructor, enum.handle = nil, nil
    disposeFunc(iter)
  end)
end

function EnumeratePeds()
  return EnumerateEntities(FindFirstPed, FindNextPed, EndFindPed)
end

RegisterNetEvent('fdg_admin:dpRadius')
AddEventHandler('fdg_admin:dpRadius', function(radius)
  for ped in EnumeratePeds() do
    if not IsPedAPlayer(ped) and GetDistanceBetweenCoords(GetEntityCoords(PlayerPedId()), GetEntityCoords(ped), false) < tonumber(radius) then
      NetworkRequestControlOfEntity(ped)
      SetEntityAsMissionEntity(ped, true, true)

      DeleteEntity(ped)
    end
  end
end)

RegisterNetEvent('fdg_admin:DeleteEntity')
AddEventHandler('fdg_admin:DeleteEntity', function(entityNetId)
  local entity = Network
end)

RegisterNetEvent('fdg_admin:news')
AddEventHandler('fdg_admin:news', function(words)
  local timer = 500 -- 30 seconds
  function Initialize(scaleform)
    local scaleform = RequestScaleformMovie(scaleform)

    while not HasScaleformMovieLoaded(scaleform) do
      Citizen.Wait(0)
    end

    PushScaleformMovieFunction(scaleform, "SET_TEXT")
    PushScaleformMovieFunctionParameterString(words)
    PushScaleformMovieFunctionParameterString("Have a Great Day.")
    PopScaleformMovieFunctionVoid()

    return scaleform
  end

  scaleform = Initialize("breaking_news")

  while timer >= 0 do
    Citizen.Wait(0)
    DrawScaleformMovieFullscreen(scaleform, 255, 255, 255, 255, 0)
    timer = timer - 1
  end
  SetScaleformMovieAsNoLongerNeeded(scaleform)
end)

RegisterNetEvent('fdg:teleport')
AddEventHandler('fdg:teleport', function(pos)
  -- heading?!
  pos.x = pos.x + 0.0
  pos.y = pos.y + 0.0

  if pos.z then
    pos.z = pos.z + 0.0
  else
    -- SetEntityCoords(GetPlayerPed(-1), pos.x, pos.y, 0.0)
    -- pos.z = GetGroundZFor_3dCoord(pos.x, pos.y, 150.0)
    local ground
    local groundCheckHeights = { 0.0, 50.0, 100.0, 150.0, 200.0, 250.0, 300.0, 350.0, 400.0, 450.0, 500.0, 550.0, 600.0, 650.0, 700.0, 750.0, 800.0 }

    for i, height in ipairs(groundCheckHeights) do
      Wait(0)
      RequestCollisionAtCoord(pos.x, pos.y, height)
      SetEntityCoordsNoOffset(PlayerPedId(), pos.x, pos.y, height, 0, 0, 1)
      ground, z = GetGroundZFor_3dCoord(pos.x, pos.y, height)
      if (ground) then
        pos.z = z + 3
        break;
      end
    end
  end

  RequestCollisionAtCoord(pos.x, pos.y, pos.z)
  SetEntityCoords(GetPlayerPed(-1), pos.x, pos.y, pos.z)
end)

RegisterNetEvent('fdg_admin:drawRoute')
AddEventHandler('fdg_admin:drawRoute', function(target)
  if trackingPlayer == target then
    trackingPlayer = nil
  else
    trackingPlayer = target
  end
end)

Citizen.CreateThread(function()
  while true do
    Wait(1000)
    if trackingPlayer then
      local coords = GetEntityCoords(GetPlayerPed(GetPlayerFromServerId(trackingPlayer)))
      SetNewWaypoint(coords.x, coords.y)
    end
  end
end)
