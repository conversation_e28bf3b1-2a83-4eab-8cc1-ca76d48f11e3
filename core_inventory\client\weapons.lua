selectedWeapon = false
local lastAmmo = nil
local selectedHotbarSlot = false
local selectedIndex = false
local reloading = false
local inventoryEnabled = true

exports("blockInv", function(state)
	inventoryEnabled = not state
	if (inventoryEnabled) then
		SetWeaponsNoAutoreload(true)
		Base.Controls.register("r", "Reload Weapon", function()
			local ped = PlayerPedId()
			local bool, ammo = GetAmmoInClip(ped, selectedWeapon)
			local maxAmmo = GetMaxAmmoInClip(ped, selectedWeapon, 1)
			local vehicle = GetVehiclePedIsUsing(ped)
			local ammoInClip = GetAmmoInPedWeapon(ped, selectedWeapon)

			if ammoInClip ~= maxAmmo and not reloading then
				reloading = true
				TriggerServerCallback("inventory:reloadWeapon", function(reload)
					if (reload) then
						SetAmmoInClip(ped, selectedWeapon, 0)
						SetPedAmmo(ped, selectedWeapon, maxAmmo)
						TriggerServerEvent("inventory:storeWeapon", selectedWeapon, maxAmmo)
						if not IsPlayerFreeAiming(ped) and not IsPedInAnyVehicle(ped, true) then
							MakePedReload(ped)
						end
						Base.Notification("Reloading...", { timeout = 2000 })
						Wait(1000)
					end
					reloading = false
				end, selectedWeapon, ammo, maxAmmo, ammoInClip)



				Wait(2000)
			end -- Don't reload if already max ammo
		end)
	else
		SetWeaponsNoAutoreload(false)
		Base.Controls.register("r", "Reload Weapon", function()
		end)
	end
end)





-- Enforcing Selected Weapon Thread
onBaseReady(function()
	SetWeaponsNoAutoreload(true)
	Base.Controls.register("r", "Reload Weapon", function()
		local ped = PlayerPedId()
		local bool, ammo = GetAmmoInClip(ped, selectedWeapon)
		local maxAmmo = GetMaxAmmoInClip(ped, selectedWeapon, 1)
		local vehicle = GetVehiclePedIsUsing(ped)
		local ammoInClip = GetAmmoInPedWeapon(ped, selectedWeapon)

		if ammoInClip ~= maxAmmo and not reloading then
			reloading = true
			TriggerServerCallback("inventory:reloadWeapon", function(reload)
				if (reload) then
					SetAmmoInClip(ped, selectedWeapon, 0)
					SetPedAmmo(ped, selectedWeapon, maxAmmo)
					TriggerServerEvent("inventory:storeWeapon", selectedWeapon, maxAmmo)
					if not IsPlayerFreeAiming(ped) and not IsPedInAnyVehicle(ped, true) then
						MakePedReload(ped)
					end

					Base.Notification("Reloading...", { timeout = 2000 })
					Wait(1000)
				end
				reloading = false
			end, selectedWeapon, ammo, maxAmmo, ammoInClip)



			Wait(2000)
		end -- Don't reload if already max ammo
	end)

	while true do
		Wait(0)
		if (inventoryEnabled) then
			-- Reload default input
			DisableControlAction(0, 45)
			DisableControlAction(1, 45)
			DisableControlAction(2, 45)


			DisableControlAction(0, 80)
			DisableControlAction(1, 80)
			DisableControlAction(2, 80)

			if selectedWeapon then
				local playerPed = PlayerPedId()
				local bool, weaponhash = GetCurrentPedWeapon(playerPed)
				-- Sets Settings to Freeaim
				SetPlayerTargetingMode(3)

				if GetHashKey(selectedWeapon) ~= weaponhash then
					SetCurrentPedWeapon(playerPed, selectedWeapon, true)
				end

				if HasPedGotWeapon(PlayerPedId(), selectedWeapon) then
					local bool, ammo = GetAmmoInClip(playerPed, GetHashKey(selectedWeapon))
					lastAmmo = ammo
				else
					local wep, i = getItem(selectedWeapon)
					TriggerServerEvent("inventory:storeWeapon", i, lastAmmo)
					selectedWeapon = false
					selectedWeaponSlot = false
				end
			end
		end
	end
end)

local autoreload = false

Citizen.CreateThread(function()
	while true do
		Citizen.Wait(100)
		local ped = PlayerPedId()
		if not IsPedReloading(ped) then
			-- check ammmo
			local wep = GetSelectedPedWeapon(ped)
			local bool, ammo = GetAmmoInClip(ped, wep)
			local ammoWeapon = GetAmmoInPedWeapon(ped, wep)

			if ammo == 0 and ammoWeapon > 0 and IsPedInAnyVehicle(ped, true) then
				if IsPedInAnyVehicle(ped, true) and IsPedDoingDriveby(ped) then
					Wait(500)
					TaskReloadWeapon(ped, true)
					Wait(10)
					ClearPedTasks(ped)
				end
			elseif ammo == 0 and autoreload and not IsPedInAnyVehicle(ped, true) then
				SetWeaponsNoAutoreload(false)
				SetWeaponsNoAutoswap(true)
				autoreload = false
			elseif ammo ~= 0 and not autoreload and not IsPedInAnyVehicle(ped, true) then
				SetWeaponsNoAutoreload(true)
				autoreload = true
			end
		end
	end
end)

RegisterNetEvent("inventory:removeWeapon", function()
	if (selectedWeapon) then
		equipWeapon(selectedWeapon)
	end
end)

RegisterNetEvent("inventory:equipWeapon")
AddEventHandler("inventory:equipWeapon", function(weaponName, weaponLabel)
	equipWeapon(weaponName, weaponLabel)
end)

RegisterNetEvent("inventory:refreshWeapon")
AddEventHandler("inventory:refreshWeapon", function(weaponName)
	local playerPed = PlayerPedId()
	local weapon, i = getItem(weaponName)
	if weapon and HasPedGotWeapon(playerPed, weapon.name) then
		applyAttachments(weapon)
	end
end)

RegisterNetEvent("inventory:deagleEngrave")
AddEventHandler("inventory:deagleEngrave", function()
	local playerPed = PlayerPedId()
	local weaponHash = `weapon_pistol50`

	exports["core_inventory"]:hideInventory()

	RemoveWeaponFromPed(playerPed, weaponHash)
	RemoveAllPedWeapons(playerPed, true)
	GiveWeaponToPed(playerPed, "WEAPON_UNARMED", 1, false, false)

	openDeagleMenu()
end)

RegisterNetEvent("inventory:akEngrave")
AddEventHandler("inventory:akEngrave", function()
	local playerPed = PlayerPedId()
	local weaponHash = `weapon_assaultrifle`

	exports["core_inventory"]:hideInventory()

	RemoveWeaponFromPed(playerPed, weaponHash)
	RemoveAllPedWeapons(playerPed, true)
	GiveWeaponToPed(playerPed, "WEAPON_UNARMED", 1, false, false)

	OpenAkMenu()
end)

RegisterNetEvent("inventory:apEngrave")
AddEventHandler("inventory:apEngrave", function()
	local playerPed = PlayerPedId()
	local weaponHash = `weapon_appistol`

	exports["core_inventory"]:hideInventory()

	RemoveWeaponFromPed(playerPed, weaponHash)
	RemoveAllPedWeapons(playerPed, true)
	GiveWeaponToPed(playerPed, "WEAPON_UNARMED", 1, false, false)

	OpenApMenu()
end)

exports("getSelectedWeapon", function()
	return selectedWeapon, selectedHotbarSlot, selectedIndex
end)

function equipWeapon(weaponName, weaponLabel, keyLabel)
	local playerPed = PlayerPedId()
	local weapon, i = getItem(weaponName)

	selectedIndex = i

	if weapon and HasPedGotWeapon(playerPed, weapon.name) then
		local bool, ammo = GetAmmoInClip(playerPed, GetHashKey(weapon.name))
		local ammo2 = GetAmmoInPedWeapon(playerPed, GetHashKey(weapon.name))
		RemoveWeaponFromPed(playerPed, weapon.name)
		RemoveAllPedWeapons(playerPed, true)
		GiveWeaponToPed(playerPed, "WEAPON_UNARMED", 1, false, false)

		if (bool or ammo2) and weapon and (ammo or ammo2) ~= weapon.data.ammo then
			if weapon.name == selectedWeapon then
				i = selectedWeaponSlot
			end
			TriggerServerEvent("inventory:storeWeapon", i, bool and ammo or ammo2)
		end

		selectedWeapon = false
		selectedWeaponSlot = false
		selectedIndex = false

		if keyLabel then
			deselectSlot(keyLabel)
		end

		LocalPlayer.state:set("equippedWeapon", nil, true)
		TriggerEvent("weaponUnequiped")
	elseif weapon then
		if selectedWeapon ~= weapon.name and HasPedGotWeapon(playerPed, selectedWeapon) then
			local oldWep, oldI = getItem(selectedWeapon)
			local bool, ammo = GetAmmoInClip(playerPed, selectedWeapon)

			if bool and weapon then
				TriggerServerEvent("inventory:storeWeapon", oldI, ammo)
			end

			if selectedHotbarSlot then
				deselectSlot(selectedHotbarSlot)
			end
		end

		RemoveAllPedWeapons(playerPed, true)
		GiveWeaponToPed(playerPed, "WEAPON_UNARMED", 1, false, false)

		local ammo = weapon.data.ammo or 0
		GiveWeaponToPed(playerPed, weapon.name, 0, false, true)
		applyAttachments(weapon)

		local maxAmmo = GetMaxAmmoInClip(playerPed, GetHashKey(weapon.name), 1)
		if (maxAmmo and ammo) and ammo > maxAmmo then
			ammo = maxAmmo
		end

		SetPedAmmo(playerPed, weapon.name, ammo)
		selectedWeapon = weapon.name
		selectedWeaponSlot = i

		if keyLabel then
			selectedHotbarSlot = keyLabel
			selectSlot(keyLabel)
		end

		LocalPlayer.state:set("equippedWeapon", i, true)
		TriggerEvent("weaponEquiped", weapon.name, weapon)

		if not weapon.data.ballisticSerial then
			TriggerServerEvent("inventory:generateSerial", i)
		end
	else
		Base.Notification("You don't have " .. weaponLabel .. " in your inventory")
	end
end

RegisterClientCallback("core_inventory:requiredObjectCheck", function(cb, modelHash, distance)
	local object = DoesObjectOfTypeExistAtCoords(GetEntityCoords(PlayerPedId()), (distance + 0.1), tonumber(modelHash))
	cb(object)
end)

AddEventHandler("vehicleEnter", function()
	local playerPed = PlayerPedId()
	if selectedWeapon then
		local weapon, i = getItem(selectedWeapon)
		if weapon then
			local ammo = GetAmmoInPedWeapon(playerPed, GetHashKey(weapon.name))
			if ammo then
				TriggerServerEvent("inventory:storeWeapon", i, ammo)
			end
		end
	end
end)

function resyncEquipedWeapon()
	for idx, item in pairs(LocalPlayer.inventory) do
		if item.name == selectedWeapon then
			selectedIndex = idx
			break
		end
	end
end
