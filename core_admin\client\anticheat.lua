AntiCheat.Started = false

AntiCheat.Blacklist = {
	"weapon_marksmanpistol",
	"weapon_raypistol",
	"weapon_raycarbine",
	"weapon_rpg",
	"weapon_minigun",
	"weapon_railgun",
	"weapon_hominglauncher",
	"weapon_rayminigun",
	"weapon_combatmg_mk2",
	"weapon_heavysniper",
	"weapon_heavysniper_mk2",
	"weapon_grenadelauncher",
	"weapon_stinger",
}

AntiCheat.AnimalModels = {
	"a_c_boar",
	"a_c_cat_01",
	"a_c_chickenhawk",
	"a_c_chop",
	"a_c_cormorant",
	"a_c_cow",
	"a_c_coyote",
	"a_c_crow",
	"a_c_deer",
	"a_c_dolphin",
	"a_c_fish",
	"a_c_hen",
	"a_c_humpback",
	"a_c_husky",
	"a_c_killerwhale",
	"a_c_mtlion",
	"a_c_pig",
	"a_c_pigeon",
	"a_c_poodle",
	"a_c_pug",
	"a_c_rabbit_01",
	"a_c_rat",
	"a_c_retriever",
	"a_c_rottweiler",
	"a_c_seagull",
	"a_c_sharkhammer",
	"a_c_sharktiger",
	"a_c_shepherd",
	"a_c_stingray",
	"a_c_westy"
}

AntiCheat.animalCheck = function()
	local ped = PlayerPedId()
	local model = GetEntityModel(ped)
	for _, v in pairs(AntiCheat.AnimalModels) do
		if (model == GetHashKey(v)) then
			return true
		end
	end
	return false
end

AntiCheat.checkBlackList = function(ped)
	for k, v in pairs(AntiCheat.Blacklist) do
		if (HasPedGotWeapon(ped, GetHashKey(v), false)) then
			return true
		end
	end
	return false
end

AntiCheat.get_inventory_weapons = function()
	local success, result = pcall(function() return exports["core_inventory"]:getWeapons() end)
	if (success) then
		return result
	else
		return {}
	end
end

AntiCheat.uploading = false
AntiCheat.blocked = false

AntiCheat.start = function()
	if AntiCheat.Started then
		return
	end

	Citizen.CreateThread(function()
		while true do
			if (not AntiCheat.blocked) then
				-- local ped checks
				local handle, ped = FindFirstPed()
				local success

				repeat
					if (DoesEntityExist(ped) and not IsPedAPlayer(ped)) then
						local NetworkOwner = NetworkGetEntityOwner(ped)
						if (NetworkOwner == PlayerId() or NetworkOwner == -1) then
							if (AntiCheat.checkBlackList(ped)) then
								RemoveAllPedWeapons(ped, false)
								SetEntityAsNoLongerNeeded(ped)
								DeleteEntity(ped)
							end
						end
					end
					success, ped = FindNextPed(handle)
				until not success
				EndFindObject(handle)

				-- spectate check
				if (not isSpectating) then
					if (NetworkIsInSpectatorMode()) then
						local link = RequestScreenshotUpload()
						if link then
							TriggerServerEvent("core_admin:tpsr", link, "Auto Detection - [Spectate] Player is in spectator mode", {})
						end

						TriggerServerEvent("core_admin:banSelf")
					end
				end

				-- weapons check
				local weaponHash = GetSelectedPedWeapon(PlayerPedId())
				if (weaponHash ~= GetHashKey("weapon_unarmed") and not AntiCheat.animalCheck()) then
					if (group ~= "superadmin") then
						local foundWeapon = false
						for _, v in pairs(AntiCheat.get_inventory_weapons()) do
							if (weaponHash == GetHashKey(v.name)) then
								foundWeapon = true
							end
						end
						if (not foundWeapon and Base.getWeaponFromHash(weaponHash)) then
							local link = RequestScreenshotUpload()
							if link then
								TriggerServerEvent("core_admin:tpsr", link, "Auto Detection - [WEAPON] Weapon in hand without item", {})
							end

							TriggerServerEvent("core_admin:staffMsg", "^6Anti-Cheat", "HAS_WEAPON_NOT_ITEM: " .. Base.getWeaponFromHash(weaponHash), "purple")
							RemoveAllPedWeapons(PlayerPedId(), false)
						end
					end
				end
			end

			Wait(1000)
		end
	end)

	-- Illegal camera check
	Citizen.CreateThread(function()
		-- the initial loadin screen for fivem triggers this
		while (GetIsLoadingScreenActive()) do Wait(100) end
		Wait(10000)

		while not Base.GetPlayerData() do
			Wait(1000)
		end

		while true do
			Wait(1000)
			local renderingCam = GetRenderingCam()
			if (not Base.Camera._isCameraRegisteredWithBase(renderingCam) and renderingCam ~= -1) then
				TriggerServerEvent("core_admin:staffMsg", "^6Anti-Cheat", "User is using an external camera.", "purple")

				local link = RequestScreenshotUpload()
				if link then
					TriggerServerEvent("core_admin:tpsr", link, "Auto Detection - [CAMERA] Non-registered camera ID: " .. renderingCam, {})
				end
			end
		end
	end)
end

CreateThread(function()
	Wait(1000)
	AntiCheat.start()
end)


exports("blockAC", function(state)
	AntiCheat.blocked = state
	TriggerServerEvent("test_ac:checkBlock", state)
end)

RegisterNetEvent("core_admin:tps")
AddEventHandler("core_admin:tps", function(reason, xP)
	local link = RequestScreenshotUpload()
	if link then
		TriggerServerEvent("core_admin:tpsr", link, reason, xP)
	end
end)

RegisterNetEvent("rtx_themepark:Shooter:Started")
AddEventHandler("rtx_themepark:Shooter:Started", function(state)
	AntiCheat.blocked = state
end)
