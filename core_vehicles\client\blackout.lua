local config = {
    threadWait = 100,
    -- Main thread time // High values can cause speed calcualtion issues, MUST be set lower than speedHistory

    speedHistory = 300,
    -- Stores historcial speed values over the last n milliseconds

    damageHistory = 4000,
    -- Stores historcial damage values over the last n milliseconds

    lowImpactDamage = 18,
    -- Min damage needed to execute minor blackout once lowImpactSpeed is met

    highImpactDamage = 35,
    -- Min damage needed to execute major blackout once highImpactSpeed is met

    lowImpactSpeed = 60,
    -- Min speed needed to execute minor blackout once lowImpactDamage is met

    highImpactSpeed = 120,
    -- Min speed needed to execute major blackout once highImpactDamage is met

    scriptEnabled = true
    -- Duh
}

local blurFade
local vars = {
    debugEnabled = false,
    blackedOut = false,
    lastHealth = 0,
    damageTable = {},
    speedTable = {}
}

-- Synced data handler for blackout settings
RegisterDataSyncHandler("blackout_settings", function(result)
    for i = 1, #result, 1 do
        if (result[i].configname == "scriptEnabled") then
            if (tonumber(result[i].value) > 0) then
                config.scriptEnabled = true
            else
                config.scriptEnabled = false
                vars.debugEnabled = false
            end
        elseif (result[i].configname == "threadWait") then
            config.threadWait = tonumber(result[i].value)
        elseif (result[i].configname == "damageHistory") then
            config.damageHistory = tonumber(result[i].value)
        elseif (result[i].configname == "speedHistory") then
            config.speedHistory = tonumber(result[i].value)
        elseif (result[i].configname == "lowImpactDamage") then
            config.lowImpactDamage = tonumber(result[i].value)
        elseif (result[i].configname == "highImpactDamage") then
            config.highImpactDamage = tonumber(result[i].value)
        elseif (result[i].configname == "lowImpactSpeed") then
            config.lowImpactSpeed = tonumber(result[i].value)
        elseif (result[i].configname == "highImpactSpeed") then
            config.highImpactSpeed = tonumber(result[i].value)
        end
    end
end)


RegisterNetEvent("main_roleplay:cuntCantDrive")
AddEventHandler("main_roleplay:cuntCantDrive", function(maxSpeed)
    if (vars.blackedOut) then
        return
    end

    local ped = PlayerPedId()
    local driving = GetPedInVehicleSeat(GetVehiclePedIsIn(ped, false), -1) == ped

    vars.blackedOut = true
    local timer = GetGameTimer()
    local elapsed = 0
    -- -- Disable player movement while blacked out
    Citizen.CreateThread(function()
        while (vars.blackedOut) do
            Wait(1)
            -- DisableControlAction(0, 71, true) -- veh forward
            -- DisableControlAction(0, 72, true) -- veh backwards
            --[[  DisableControlAction(0, 63, true) -- veh turn left
            DisableControlAction(0, 64, true) -- veh turn right
            DisableControlAction(0, 75, true) -- disable exit vehicle ]]
        end
    end)

    if (maxSpeed >= config.highImpactSpeed) then
        ShakeGameplayCam("LARGE_EXPLOSION_SHAKE", 1.5)
        Wait(1400)

        DoScreenFadeOut(500)
        Wait(200)

        TriggerScreenblurFadeIn(1000)
        DoScreenFadeIn(200)

        blurFade = true

        Citizen.SetTimeout(1000, function()
          while blurFade do 
            Wait(0)
            TriggerScreenblurFadeIn(0)
          end
          TriggerScreenblurFadeOut(500)
        end)

        Wait(4000)

        ShakeGameplayCam("DRUNK_SHAKE", 2.0)

        Wait(1500)

        DoScreenFadeOut(500)
        Wait(200)

        blurFade = false

        vars.blackedOut = false

        elapsed = GetGameTimer()
        if (math.random(1, 10) >= 5) then
            DoScreenFadeOut(500)
            Wait(200)
            ShakeGameplayCam("DRUNK_SHAKE", 2.9)
            DoScreenFadeIn(500)

            Wait(math.random(4000, 8000))
            DoScreenFadeOut(500)
            Wait(200)
        end
        StopGameplayCamShaking(true)
        DoScreenFadeIn(500)

    elseif (maxSpeed >= config.lowImpactSpeed) then
        local chance = math.random(1, 100)
        if (chance > 70) then
            ShakeGameplayCam("LARGE_EXPLOSION_SHAKE", 0.4)
        elseif (chance > 50) then
            ShakeGameplayCam("MEDIUM_EXPLOSION_SHAKE", 0.7)
        else
            ShakeGameplayCam("MEDIUM_EXPLOSION_SHAKE", 0.4)
        end

        Wait(math.random(2000, 5000))
        TriggerScreenblurFadeOut(1000)

        vars.blackedOut = false
        elapsed = GetGameTimer()
    end
end)

function GetMaxSpeed(tbl)
    local speed = 0
    for k, v in pairs(tbl) do
        if (v > speed) then
            speed = v
        end
    end
    return speed
end

function GetTotalDamage(tbl)
    local totalDamage = 0
    for k, v in pairs(tbl) do
        if (v > 0) then
            totalDamage = totalDamage + v
        end
    end
    return totalDamage
end

-- Blackout function
function Blackout(veh, speed)
    if (speed < config.lowImpactSpeed) then
        return
    end

    local players = {}
    for i = -1, GetVehicleModelNumberOfSeats(GetEntityModel(veh)), 1 do
        local ped = GetPedInVehicleSeat(veh, i)
        if (DoesEntityExist(ped) and IsPedAPlayer(ped)) then
            local index = NetworkGetPlayerIndexFromPed(ped)
            table.insert(players, GetPlayerServerId(index))
        end
    end

    -- if (vars.debugEnabled) then
    --     TriggerEvent('chat:addMessage', "BLACKOUT DEBUG", {255, 165, 0}, "Crashed at " .. math.floor(totalDamage) ..
    --         " totalDamage, and " .. math.floor(maxSpeed) .. " maxSpeed")
    -- end

    -- Trigger blackout on players
    TriggerServerEvent("main_roleplay:cuntCantDrive", players, speed)
end

-- Main thread
-- Citizen.CreateThread(function()
--     while true do
--         ::continue::
--         Wait(config.threadWait)
--         if (not config.scriptEnabled) then
--             goto continue
--         end

--         local ped = PlayerPedId()
--         if (not IsPedInAnyVehicle(ped)) then
--             vars.lastHealth = 0
--             goto continue
--         end

--         local veh = GetVehiclePedIsIn(ped, false)
--         if (not vars.debugEnabled and GetPedInVehicleSeat(veh, -1) ~= ped) then
--             goto continue
--         end

--         -- Update knowledge of vehicle health
--         local currentHealth = GetEntityHealth(veh)
--         if (vars.lastHealth < currentHealth) then
--             vars.lastHealth = currentHealth
--             goto continue
--         end

--         -- Calculate speed
--         local currentSpeed = GetEntitySpeed(veh) * 3.6 -- KM

--         -- Track last n speed values
--         table.insert(vars.speedTable, currentSpeed)
--         while (#vars.speedTable > config.speedHistory / config.threadWait) do
--             table.remove(vars.speedTable, 1)
--         end

--         -- Calculate damage
--         local damage = vars.lastHealth - currentHealth
--         if (damage > 0) then
--             table.insert(vars.damageTable, damage)

--             -- Blackout
--             if (GetTotalDamage(vars.damageTable) >= config.lowImpactDamage and GetMaxSpeed(vars.speedTable) >=
--                 config.lowImpactSpeed) then
--                 Blackout(veh)
--             end
--         else
--             table.insert(vars.damageTable, 0)
--         end

--         -- Truncate table
--         while (#vars.damageTable > config.damageHistory / config.threadWait) do
--             table.remove(vars.damageTable, 1)
--         end

--         vars.lastHealth = currentHealth
--     end
-- end)

-- Debug command
RegisterNetEvent("main_roleplay:blackoutDebug")
AddEventHandler("main_roleplay:blackoutDebug", function()
    if (not config.scriptEnabled) then
        TriggerEvent("fdg_ui:SendNotification", "Blackout is disabled")
        return
    end

    vars.debugEnabled = not vars.debugEnabled
    if (vars.debugEnabled) then
        CreateDebugThread()
    end
end)

-- Debug thread
function CreateDebugThread()
    Citizen.CreateThread(function()
        while (vars.debugEnabled) do
            Wait(0)
            local ped = PlayerPedId()
            if (IsPedInAnyVehicle(ped)) then
                local veh = GetVehiclePedIsIn(ped, false)
                local currentHealth = GetEntityHealth(veh)
                local currentSpeed = GetEntitySpeed(veh) * 3.6 -- KM
                local coords = GetEntityCoords(veh)

                DrawText3Ds1(coords.x, coords.y, coords.z + 0.6,
                    "currentHealth: " .. currentHealth .. " lastHealth: " .. vars.lastHealth .. " threadWait: " ..
                    config.threadWait .. "ms")

                DrawText3Ds1(coords.x, coords.y, coords.z + 0.4,
                    "currentDamage: " .. math.floor(GetTotalDamage(vars.damageTable)) .. " damageTableSize: " ..
                    #vars.damageTable .. " damageHistory: " .. config.damageHistory .. "ms")

                DrawText3Ds1(coords.x, coords.y, coords.z + 0.2,
                    "currentSpeed: " .. math.floor(currentSpeed) .. " maxSpeed: " ..
                    math.floor(GetMaxSpeed(vars.speedTable)) .. " speedTableSize: " .. #vars.speedTable ..
                    " speedHistory: " .. config.speedHistory .. "ms")

                DrawText3Ds1(coords.x, coords.y, coords.z + 0.0,
                    "lowImpactDamage: " .. config.lowImpactDamage .. " highImpactDamage: " .. config.highImpactDamage ..
                    " lowImpactSpeed: " .. config.lowImpactSpeed .. " highImpactSpeed " .. config.highImpactSpeed)
            end
        end
    end)
end

-- Debug draw text function
function DrawText3Ds1(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())

    SetTextScale(0.50, 0.50)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextEdge(2, 0, 0, 0, 150)
    SetTextDropShadow()
    SetTextOutline()
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x, _y)
end
