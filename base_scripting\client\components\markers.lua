Base.Markers = {}

local inMarker = false
local markers = {}
local nearbymarkers = {}
local currentBlipIdentifier = nil
local currentBlipData = nil
local currentBlipMessage = nil

Base.Markers.Create = function(identifier, coords, colour, text, data, callback, inVehicle, hold, holdMessage)
    if (coords.x == nil or coords.y == nil or coords.z == nil) then
        return
    end

    -- cleanup old markers
    Base.Markers.Delete(identifier)

    -- check and create chunk
    local blipChunk = Base.Worldgrid.getCurrentChunk(coords)
    if (markers[tostring(blipChunk)] == nil) then
        markers[tostring(blipChunk)] = {}
    end

    if colour ~= "none" then
        markers[tostring(blipChunk)][identifier] = {
            x = coords.x-0.001, y = coords.y-0.001, z = coords.z-0.001, 
            size = coords.size or 1.5,
            keyPress = coords.keyIndex or 47,
            keyLabel = coords.keyLabel or "G",
            cr = (colour.r), cg = (colour.g), cb = (colour.b), 
            text = text,
            data = data,
            callback = callback,
            veh = inVehicle or false,
            marker = coords.marker or 27,
            bobbing = coords.bobbing or false,
            noLower = coords.noLower or false,
            noAutoClose = coords.noAutoClose or false,
            hold = hold,
            holdMessage = holdMessage
        }
    else
        markers[tostring(blipChunk)][identifier] = {
            x = coords.x-0.001, y = coords.y-0.001, z = coords.z-0.001,
            size = coords.size or 1.5,
            keyPress = coords.keyIndex or 47,
            keyLabel = coords.keyLabel or "G",
            text = text,
            data = data,
            callback = callback,
            veh = inVehicle or false,
            noAutoClose = coords.noAutoClose or false,
            hold = hold,
            holdMessage = holdMessage
        }
    end

end

AddEventHandler("fdg:CreateMarker", function(identifier, coords, colour, text, data, callback, inVehicle, hold, holdMessage)
    Base.Markers.Create(identifier, coords, colour, text, data, callback, inVehicle, hold, holdMessage)
end)

Base.Markers.Delete = function(identifier)
    for chunk in pairs(markers) do
        if (markers[chunk][identifier]) then
            markers[chunk][identifier] = nil
        end
    end
end

AddEventHandler("fdg:DeleteMarker", function(identifier)
    Base.Markers.Delete(identifier)
end)

Citizen.CreateThread(function()
    while true do
        Wait(250)

        local playerPed = GetPlayerPed(-1)
        local coords    = GetEntityCoords(playerPed)
        local inVehicle = IsPedInAnyVehicle(playerPed, true)
        local closestDist = -1
        local closeChunks = Base.Worldgrid.getNearbyChunks(coords)

        nearbymarkers = {}
        currentBlipIdentifier = nil
        currentBlipData = nil
        currentBlipMessage = nil

        for _, chunk in pairs(closeChunks) do
            if (markers[tostring(chunk)]) then
                for identifier, blip in pairs(markers[tostring(chunk)]) do
                    local dist = #( coords - vector3(blip.x, blip.y, blip.z - 0.98) )
                    local size = blip.size
                    if (blip.size < 1.3) then size = 1.3 end

                    if dist < 50 then
                        if not blip.veh or ( blip.veh and inVehicle ) then
                            table.insert(nearbymarkers, blip)
                            if dist < closestDist or closestDist == -1 then
                                closestDist = dist
                                if dist < size then
                                    if not blip.noAutoClose then 
                                        inMarker = true
                                    end
                                    if blip.text then
                                        currentBlipMessage = '[~g~'..blip.keyLabel..'~s~] ' .. blip.text
                                    end
                                    currentBlipData       = blip
                                    currentBlipIdentifier = identifier
                                elseif dist < size*3 then
                                    currentBlipMessage = blip.text
                                    currentBlipData    = blip
                                end
                            end
                        end
                    end
                end
            end
        end

        if inMarker and closestDist > 1.5 then
            Base.UI.Menu.CloseAll()
            inMarker = false
            if Base.Camera.exists("job_garage_cam") or Base.Camera.exists("garage_cam") then
                -- this is a dogshit patch attempt for the garage cam stuck bug, but if it works it works
                Base.Camera.destroy("job_garage_cam", 0)
                Base.Camera.destroy("garage_cam", 0)
            end
        end
    end
end)

-- Draw Blips 
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1)

        if currentBlipIdentifier and currentBlipData then
            if IsControlJustPressed(0, tonumber(currentBlipData.keyPress)) then

                if (currentBlipData.hold) then
                    Citizen.CreateThread(function()
                        local startTime = GetGameTimer()
                        local finished, canceled = false, false
                        exports["main_progressbar"]:start(1000, currentBlipData.holdMessage or "Interacting")
                        while (IsControlPressed(0, tonumber(currentBlipData.keyPress)) and not canceled and not finished) do Wait(0)
                            if (GetGameTimer() - startTime > 1000) then
                                finished = true
                            end
                            if (currentBlipIdentifier == nil) then
                                canceled = true
                            end
                        end
                        exports["main_progressbar"]:stop()
                        if (finished) then
                            if currentBlipData.callback then
                                Citizen.CreateThread(function() currentBlipData.callback(currentBlipData.data) end)
                            end
                        end
                    end)
                else
                    if currentBlipData.callback then
                        Citizen.CreateThread(function() currentBlipData.callback(currentBlipData.data) end)
                    end
                end
            end
        end

        if currentBlipMessage and currentBlipData then
            Base.DrawText(currentBlipData.x, currentBlipData.y, currentBlipData.z, currentBlipMessage)
        end

        for i=1, #nearbymarkers, 1 do
            if nearbymarkers[i].cr then
                local z = nearbymarkers[i].z - 0.95
                if (nearbymarkers[i].noLower) then z = nearbymarkers[i].z end
                DrawMarker(nearbymarkers[i].marker or 27, nearbymarkers[i].x, nearbymarkers[i].y, z, 0.0, 0.0, 0.0, 0, 0.0, 0.0, nearbymarkers[i].size, nearbymarkers[i].size, nearbymarkers[i].size, nearbymarkers[i].cr, nearbymarkers[i].cg, nearbymarkers[i].cb, 100, nearbymarkers[i].bobbing or false, (nearbymarkers[i].bobbing == false), 2, nearbymarkers[i].bobbing or false, false, false, false)
            end
        end
    end
end)