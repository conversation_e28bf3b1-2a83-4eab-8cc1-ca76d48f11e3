#hotkeyBindMenu {
    position: relative;
    display: none;
    font-size: 12;
    list-style-type: none;
    z-index: 1000;
    padding: 0px;
    transform: translateY(-100%);
    width: 200px;
    box-shadow: 2px 2px 2px rgba(10, 10, 10, 0.8);
}

#hotkeyBindMenu li {
    height: 35px;
    line-height: 35px; 
    width: 100%;
    text-align: center;
    position: relative;
    background-color: rgba(10, 10, 10, 0.9);
    color: white;
}

#hotkeyBindMenu li:hover {
    background-color: rgba(30, 30, 30, 0.9);
}

#hotkeyBindMenu ul {
    position: relative;
    display: none;
    list-style-type: none;
    z-index: 1000;
    padding: 0px;
    transform: translateY(-100%);
    width: 200px;
    box-shadow: 2px 2px 2px rgba(10, 10, 10, 0.8);

    position: relative;
    left: 200px;
    max-height: 500px;
    overflow:hidden; 
    overflow-y:scroll;
}

#hotkeyBindMenu li:hover ul {
    display: block;
}

/* width */
::-webkit-scrollbar {
    width: 10px;
}

/* Track */
::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.9);
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: rgba(60, 60, 60, 0.9);
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: rgba(30, 30, 30, 0.9);
}