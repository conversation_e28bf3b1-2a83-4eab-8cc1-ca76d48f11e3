<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfoVarGlobal>
<Sirens>
	<Item>
      <id value="1096"/>
      <name>ukpucj</name>
      <timeMultiplier value="1.00000000"/>
      <lightFalloffMax value="100.00000000"/>
      <lightFalloffExponent value="100.00000000"/>
      <lightInnerConeAngle value="2.29061000"/>
      <lightOuterConeAngle value="70.00000000"/>
      <lightOffset value="0.00000000"/>
      <textureName>VehicleLight_sirenlight</textureName>
      <sequencerBpm value="600"/>

      <leftHeadLight>
        <sequencer value="1426392345"/>
      </leftHeadLight>

      <rightHeadLight>
        <sequencer value="2857390182"/>
      </rightHeadLight>

      <leftTailLight>
        <sequencer value="1426392345"/>
      </leftTailLight>

      <rightTailLight>
        <sequencer value="2857390182"/>
      </rightTailLight>

      <leftHeadLightMultiples value="1"/>
      <rightHeadLightMultiples value="1"/>
      <leftTailLightMultiples value="1"/>
      <rightTailLightMultiples value="1"/>
      <useRealLights value="true"/>
      <sirens>
      <!-- siren1 RED LIGHTBAR1 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="2857390182"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.30000000"/>
            <sequencer value="2857390182"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="25.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
        <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren2 BLUELIGHTBAR1 --> 
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1426392345"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1426392345"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="25.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
        <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren3 WHITELIGHTBAR1 --> 
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="44050995"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="44050995"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="25.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren4 WHITELIGHTBAR2 -->
        <Item>
          <rotation>
            <delta value="0.000000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="2819260620"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2819260620"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="25.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
        <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren5 REDLIGHTBAR2 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="2857390182"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2857390182"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="25.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren6 BLUELIGHTBAR2 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="1426392345"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1426392345"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="25.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
        <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <!-- siren7 SIDERUNNER RIGHT --> 
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2857390182"/>
            <multiples value="4"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2857390182"/>
            <multiples value="4"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
        <intensity value="0.25000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <!-- siren8 white SIDE RUNNER LEFT --> 
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1426392345"/>
            <multiples value="4"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1426392345"/>
            <multiples value="4"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
        <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
        <!-- siren9 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2819371178"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="2819371178"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="25.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFD700"/>
        <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <!-- siren10 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="44140117"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.30000000"/>
            <sequencer value="44140117"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="25.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <!-- siren11 -->
        <Item>
          <rotation>
            <delta value="0"/>
            <start value="0"/>
            <speed value="3.00000000"/>
            <sequencer value="2857390182"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0"/>
            <start value="0"/>
            <speed value="150.00000000"/>
            <sequencer value="2857390182"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
        <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <!-- siren12 -->
        <Item>
          <rotation>
            <delta value="0"/>
            <start value="0"/>
            <speed value="3.00000000"/>
            <sequencer value="1426392345"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0"/>
            <start value="0"/>
            <speed value="150.00000000"/>
            <sequencer value="1426392345"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
        <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <!--Siren 13 : Amber : Back-->
                <Item>
                    <rotation>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294901760"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294901760"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="40.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFFD700"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>                
                <!--Siren 14 : Amber : Back-->
                <Item>
                    <rotation>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="65535"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="65535"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="40.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFFD700"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>
                <!-- siren15 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="2857390182"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.30000000"/>
            <sequencer value="2857390182"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="25.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
        <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren16 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1426392345"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1426392345"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="25.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
        <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <!--Siren 17-->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="28573901829"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="28573901829"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <!--Siren 18-->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1426392345"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1426392345"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="true"/>
        </Item>
        <!--siren19-->
		<Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="1426392345"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="1426392345"/>
            <multiples value="3"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="0.00010000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
          <!--siren20-->
		<Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="28573901829"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="28573901829"/>
            <multiples value="3"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="0.00010000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        
      </sirens>
    </Item>
  </Sirens>
		<Kits>    
    <Item>
      <kitName>1096_f140rpd_modkit</kitName>
      <id value="1096" />
      <kitType>MKT_SPECIAL</kitType>
      <visibleMods>

      <Item>
					<modelName>f140rpd_callsign_b0</modelName>
					<modShopLabel>CALLSIGN_B0</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTHOLDER</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_b1</modelName>
					<modShopLabel>CALLSIGN_B1</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTHOLDER</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_b2</modelName>
					<modShopLabel>CALLSIGN_B2</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTHOLDER</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_b3</modelName>
					<modShopLabel>CALLSIGN_B3</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTHOLDER</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_b4</modelName>
					<modShopLabel>CALLSIGN_B4</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTHOLDER</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_b5</modelName>
					<modShopLabel>CALLSIGN_B5</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTHOLDER</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_b6</modelName>
					<modShopLabel>CALLSIGN_B6</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTHOLDER</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_b7</modelName>
					<modShopLabel>CALLSIGN_B7</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTHOLDER</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_b8</modelName>
					<modShopLabel>CALLSIGN_B8</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTHOLDER</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_b9</modelName>
					<modShopLabel>CALLSIGN_B9</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTHOLDER</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_c0</modelName>
					<modShopLabel>CALLSIGN_C0</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLAQUE</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_c1</modelName>
					<modShopLabel>CALLSIGN_C1</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLAQUE</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_c2</modelName>
					<modShopLabel>CALLSIGN_C2</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLAQUE</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_c3</modelName>
					<modShopLabel>CALLSIGN_C3</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLAQUE</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_c4</modelName>
					<modShopLabel>CALLSIGN_C4</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLAQUE</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_c5</modelName>
					<modShopLabel>CALLSIGN_C5</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLAQUE</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_c6</modelName>
					<modShopLabel>CALLSIGN_C6</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLAQUE</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_c7</modelName>
					<modShopLabel>CALLSIGN_C7</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLAQUE</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_c8</modelName>
					<modShopLabel>CALLSIGN_C8</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLAQUE</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>f140rpd_callsign_c9</modelName>
					<modShopLabel>CALLSIGN_C9</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLAQUE</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
        <Item>
					<modelName>f140rpd_callsign_ck</modelName>
					<modShopLabel>CALLSIGN_CK</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTVANITY</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
        <Item>
					<modelName>f140rpd_callsign_d</modelName>
					<modShopLabel>CALLSIGN_D</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTVANITY</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
        <Item>
					<modelName>f140rpd_callsign_so</modelName>
					<modShopLabel>CALLSIGN_SO</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTVANITY</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
        <Item>
					<modelName>f140rpd_callsign_su</modelName>
					<modShopLabel>CALLSIGN_SU</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTVANITY</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
        <Item>
					<modelName>f140rpd_callsign_xd</modelName>
					<modShopLabel>CALLSIGN_XD</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTVANITY</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
        <Item>
					<modelName>f140rpd_callsign_xg</modelName>
					<modShopLabel>CALLSIGN_XG</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTVANITY</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
        <Item>
					<modelName>f140rpd_callsign_xt</modelName>
					<modShopLabel>CALLSIGN_XT</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTVANITY</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
        <Item>
					<modelName>f140rpd_callsign_gc</modelName>
					<modShopLabel>CALLSIGN_GC</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_PLTVANITY</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>


        <Item>
          <modelName>f140r_bumf_a</modelName>
          <modShopLabel>f140r_bumf_a</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_KNOB</type>
          <bone>bumper_f</bone>
          <collisionBone>bumper_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140r_wing_a</modelName>
          <modShopLabel>f140r_wing_a</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_z</Item>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_wing_a</modelName>
          <modShopLabel>f140r_wing_a2</modShopLabel>
          <linkedModels>
            <Item>f140r_wing_b</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_z</Item>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_wing_ac</modelName>
          <modShopLabel>f140r_wing_ac</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_z</Item>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140r_wing_ac</modelName>
          <modShopLabel>f140r_wing_ac2</modShopLabel>
          <linkedModels>
            <Item>f140r_wing_bc</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_z</Item>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140r_wing_a</modelName>
          <modShopLabel>f140r_wing_a3</modShopLabel>
          <linkedModels>
            <Item>f140r_wing_b</Item>
            <Item>f140r_wing_c</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_z</Item>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140r_wing_ac</modelName>
          <modShopLabel>f140r_wing_ac3</modShopLabel>
          <linkedModels>
            <Item>f140r_wing_bc</Item>
            <Item>f140r_wing_cc</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_z</Item>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140r_wing_d</modelName>
          <modShopLabel>f140r_wing_d</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_z</Item>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_wing_dc</modelName>
          <modShopLabel>f140r_wing_dc</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_z</Item>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140r_wing_d</modelName>
          <modShopLabel>f140r_wing_d2</modShopLabel>
          <linkedModels>
            <Item>f140r_wing_c</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_z</Item>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_wing_dc</modelName>
          <modShopLabel>f140r_wing_dc2</modShopLabel>
          <linkedModels>
            <Item>f140r_wing_cc</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_z</Item>
          </turnOffBones>
          <type>VMT_SPOILER</type>
          <bone>boot</bone>
          <collisionBone>boot</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        

        <Item>
          <modelName>f140rpd_wing_lf</modelName>
          <modShopLabel>f140rpd_wing_lf</modShopLabel>
          <linkedModels>
            <Item>f140rpd_wing_rf</Item>
          </linkedModels>
          <turnOffBones>
            <Item>wing_lf</Item>
            <Item>wing_rf</Item>
          </turnOffBones>
          <type>VMT_TRUNK</type>
          <bone>wing_lf</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140r_wing_lfc</modelName>
          <modShopLabel>f140r_wing_lfc</modShopLabel>
          <linkedModels>
            <Item>f140r_wing_rfc</Item>
          </linkedModels>
          <turnOffBones>
            <Item>wing_lf</Item>
            <Item>wing_rf</Item>
          </turnOffBones>
          <type>VMT_TRUNK</type>
          <bone>wing_lf</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>cyph_hlite_l_1</modelName>
          <modShopLabel>CYPH_HLITE_1</modShopLabel>
          <linkedModels>
            <Item>cyph_hlite_r_1</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>headlight_l</bone>
          <collisionBone>headlight_l</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_hlite_l_2</modelName>
          <modShopLabel>CYPH_HLITE_2</modShopLabel>
          <linkedModels>
            <Item>cyph_hlite_r_2</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>headlight_l</bone>
          <collisionBone>headlight_l</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_hlite_l_3</modelName>
          <modShopLabel>CYPH_HLITE_3</modShopLabel>
          <linkedModels>
            <Item>cyph_hlite_r_3</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>headlight_l</bone>
          <collisionBone>headlight_l</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_hlite_l_4</modelName>
          <modShopLabel>CYPH_HLITE_4</modShopLabel>
          <linkedModels>
            <Item>cyph_hlite_r_4</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>headlight_l</bone>
          <collisionBone>headlight_l</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_hlite_l_5</modelName>
          <modShopLabel>CYPH_HLITE_5</modShopLabel>
          <linkedModels>
            <Item>cyph_hlite_r_5</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_WING_R</type>
          <bone>headlight_l</bone>
          <collisionBone>headlight_l</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140r_exh_a</modelName>
          <modShopLabel>f140r_exh_a</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_e</Item>
            <Item>exhaust</Item>
            <Item>exhaust_2</Item>
            <Item>exhaust_3</Item>
            <Item>exhaust_4</Item>
          </turnOffBones>
          <type>VMT_EXHAUST</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        
        <Item>
          <modelName>f140r_fin_ac</modelName>
          <modShopLabel>f140r_fin_ac</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140r_fin_b</modelName>
          <modShopLabel>f140r_fin_b</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140r_fin_bc</modelName>
          <modShopLabel>f140r_fin_bc</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_h</Item>
          </turnOffBones>
          <type>VMT_CHASSIS4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        
        <Item>
          <modelName>cyph_suns_a</modelName>
          <modShopLabel>CYPH_SUNS_A</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_suns_a1</modelName>
          <modShopLabel>CYPH_SUNS_A1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_suns_a2</modelName>
          <modShopLabel>CYPH_SUNS_A2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_suns_b</modelName>
          <modShopLabel>CYPH_SUNS_B</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_suns_b2</modelName>
          <modShopLabel>CYPH_SUNS_B2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_suns_c</modelName>
          <modShopLabel>CYPH_SUNS_C</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_suns_c2</modelName>
          <modShopLabel>CYPH_SUNS_C2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_suns_d</modelName>
          <modShopLabel>CYPH_SUNS_D</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_suns_d2</modelName>
          <modShopLabel>CYPH_SUNS_D2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR1</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_skirt_b1</modelName>
          <modShopLabel>CYPH_SKIRT_B1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_skirt_b2</modelName>
          <modShopLabel>CYPH_SKIRT_B2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_skirt_bc</modelName>
          <modShopLabel>CYPH_SKIRT_BC</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_skirt_a1</modelName>
          <modShopLabel>CYPH_SKIRT_A1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_skirt_a2</modelName>
          <modShopLabel>CYPH_SKIRT_A2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_skirt_ac</modelName>
          <modShopLabel>CYPH_SKIRT_AC</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_skirt_c1</modelName>
          <modShopLabel>CYPH_SKIRT_C1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_skirt_c2</modelName>
          <modShopLabel>CYPH_SKIRT_C2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_skirt_cc</modelName>
          <modShopLabel>CYPH_SKIRT_CC</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_skirt1_f</modelName>
          <modShopLabel>f140r_skirt1_f</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_SKIRT</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140rpd_bumr_a</modelName>
          <modShopLabel>f140rpd_bumr_a</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bumper_r</Item>
          </turnOffBones>
          <type>VMT_BUMPER_R</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>cyph_split_a1</modelName>
          <modShopLabel>CYPH_SPLIT_A1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_split_a2</modelName>
          <modShopLabel>CYPH_SPLIT_A2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_split_ac</modelName>
          <modShopLabel>CYPH_SPLIT_AC</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_split_b1</modelName>
          <modShopLabel>CYPH_SPLIT_B1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_u</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_split_b2</modelName>
          <modShopLabel>CYPH_SPLIT_B2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_u</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_split_bc</modelName>
          <modShopLabel>CYPH_SPLIT_BC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_u</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_split_c1</modelName>
          <modShopLabel>CYPH_SPLIT_C1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_u</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_split_c2</modelName>
          <modShopLabel>CYPH_SPLIT_C2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_u</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_split_cc</modelName>
          <modShopLabel>CYPH_SPLIT_CC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_u</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_split_d1</modelName>
          <modShopLabel>CYPH_SPLIT_D1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_u</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_split_d2</modelName>
          <modShopLabel>CYPH_SPLIT_D2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_u</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_split_dc</modelName>
          <modShopLabel>CYPH_SPLIT_DC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_u</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_splta</modelName>
          <modShopLabel>f140r_splta</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_u</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_spltb</modelName>
          <modShopLabel>f140r_spltb</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_u</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_splta</modelName>
          <modShopLabel>f140r_splta2</modShopLabel>
          <linkedModels>
            <Item>f140r_spltac</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_u</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_spltb</modelName>
          <modShopLabel>f140r_spltb2</modShopLabel>
          <linkedModels>
            <Item>f140r_spltac</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_u</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_spltbw</modelName>
          <modShopLabel>f140r_spltbw</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_u</Item>
          </turnOffBones>
          <type>VMT_BUMPER_F</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_hcatch_c1</modelName>
          <modShopLabel>CYPH_HCATCH_C1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_hcatch_c2</modelName>
          <modShopLabel>CYPH_HCATCH_C2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_hcatch_a</modelName>
          <modShopLabel>CYPH_HCATCH_A</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_hcatch_b</modelName>
          <modShopLabel>CYPH_HCATCH_B</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_hudpin_3</modelName>
          <modShopLabel>VTC_HACC_3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
      <Item>
          <modelName>cyph_hudpin_3a</modelName>
          <modShopLabel>VTC_HACC_4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
      <Item>
          <modelName>cyph_hudpin_3b</modelName>
          <modShopLabel>VTC_HACC_5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
      <Item>
          <modelName>cyph_hudpin_3c</modelName>
          <modShopLabel>VTC_HACC_6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS3</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_grill_ac</modelName>
          <modShopLabel>CYPH_GRILL_AC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_grill_e</modelName>
          <modShopLabel>CYPH_GRILL_E</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_grill_ec</modelName>
          <modShopLabel>CYPH_GRILL_EC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_grill_bd</modelName>
          <modShopLabel>CYPH_GRILL_BD</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_grill_bc</modelName>
          <modShopLabel>CYPH_GRILL_BC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_grill_d</modelName>
          <modShopLabel>CYPH_GRILL_D</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_grill_d1</modelName>
          <modShopLabel>CYPH_GRILL_D1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_grill_d2</modelName>
          <modShopLabel>CYPH_GRILL_D2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_grill_dc</modelName>
          <modShopLabel>CYPH_GRILL_DC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_grill_c</modelName>
          <modShopLabel>CYPH_GRILL_C</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_grill_c1</modelName>
          <modShopLabel>CYPH_GRILL_C1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_grill_c2</modelName>
          <modShopLabel>CYPH_GRILL_C2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_grill_cc</modelName>
          <modShopLabel>CYPH_GRILL_CC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_g</Item>
          </turnOffBones>
          <type>VMT_GRILL</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>cyph_vent_ac</modelName>
          <modShopLabel>CYPH_VENT_AC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_i</Item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_vent_ac</modelName>
          <modShopLabel>CYPH_VENT_AC2</modShopLabel>
          <linkedModels>
            <Item>f140r_con_ac</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_i</Item>
          </turnOffBones>
          <type>VMT_ICE</type>
          <bone>bumper_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_cage_a</modelName>
          <modShopLabel>CYPH_CAGE_A</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_cage_a1</modelName>
          <modShopLabel>CYPH_CAGE_A1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_cage_a2</modelName>
          <modShopLabel>CYPH_CAGE_A2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_cage_b</modelName>
          <modShopLabel>CYPH_CAGE_B</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_cage_b1</modelName>
          <modShopLabel>CYPH_CAGE_B1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_cage_b2</modelName>
          <modShopLabel>CYPH_CAGE_B2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_cage_c</modelName>
          <modShopLabel>CYPH_CAGE_C</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_cage_c1</modelName>
          <modShopLabel>CYPH_CAGE_C1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_cage_c2</modelName>
          <modShopLabel>CYPH_CAGE_C2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_roof_ac</modelName>
          <modShopLabel>f140r_roof_ac</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_r</Item>
          </turnOffBones>
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140rpd_roof_b</modelName>
          <modShopLabel>f140rpd_roof_b</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_r</Item>
          </turnOffBones>
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_roof_bc</modelName>
          <modShopLabel>f140r_roof_bc</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_r</Item>
          </turnOffBones>
          <type>VMT_ROOF</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_eng_a</modelName>
          <modShopLabel>CYPH_ENG_A</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_v</Item>
            <Item>misc_y</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_eng_ae</modelName>
          <modShopLabel>CYPH_ENG_AE</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_v</Item>
            <Item>misc_y</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_eng_a1</modelName>
          <modShopLabel>CYPH_ENG_A1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_v</Item>
            <Item>misc_y</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_eng_a2</modelName>
          <modShopLabel>CYPH_ENG_A2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_v</Item>
            <Item>misc_y</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_eng_ac</modelName>
          <modShopLabel>CYPH_ENG_AC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_v</Item>
            <Item>misc_y</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_eng_ad</modelName>
          <modShopLabel>CYPH_ENG_AD</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_v</Item>
            <Item>misc_y</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_eng_b</modelName>
          <modShopLabel>CYPH_ENG_B</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_v</Item>
            <Item>misc_y</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_eng_be</modelName>
          <modShopLabel>CYPH_ENG_BE</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_v</Item>
            <Item>misc_y</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_eng_b1</modelName>
          <modShopLabel>CYPH_ENG_B1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_v</Item>
            <Item>misc_y</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_eng_b2</modelName>
          <modShopLabel>CYPH_ENG_B2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_v</Item>
            <Item>misc_y</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_eng_bc</modelName>
          <modShopLabel>CYPH_ENG_BC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_v</Item>
            <Item>misc_y</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_eng_bd</modelName>
          <modShopLabel>CYPH_ENG_BD</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_v</Item>
            <Item>misc_y</Item>
          </turnOffBones>
          <type>VMT_ENGINEBAY1</type>
          <bone>engineblock</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_int1</modelName>
          <modShopLabel>CYPH_INT1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_j</Item>
          </turnOffBones>
          <type>VMT_INTERIOR2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_int2</modelName>
          <modShopLabel>CYPH_INT2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_j</Item>
          </turnOffBones>
          <type>VMT_INTERIOR2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140rpd_arch_c</modelName>
          <modShopLabel>f140rpd_arch_c</modShopLabel>
          <linkedModels>
            <Item>f140rpd_arch_f</Item>
            <Item>f140rpd_arch_r</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140rpd_arch_c2</modelName>
          <modShopLabel>f140rpd_arch_c2</modShopLabel>
          <linkedModels>
            <Item>f140rpd_arch_f2</Item>
            <Item>f140rpd_arch_r2</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_arch_c2c</modelName>
          <modShopLabel>f140r_arch_c2c</modShopLabel>
          <linkedModels>
            <Item>f140r_arch_f2c</Item>
            <Item>f140r_arch_r2c</Item>
          </linkedModels>
          <turnOffBones />
          <type>VMT_CHASSIS5</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>cyph_diff_a1</modelName>
          <modShopLabel>CYPH_DIFF_A1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_diff_a2</modelName>
          <modShopLabel>CYPH_DIFF_A2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_diff_ac</modelName>
          <modShopLabel>CYPH_DIFF_AC</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_diff_b1</modelName>
          <modShopLabel>CYPH_DIFF_B1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_diff_b2</modelName>
          <modShopLabel>CYPH_DIFF_B2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_diff_bc</modelName>
          <modShopLabel>CYPH_DIFF_BC</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_diff_c1</modelName>
          <modShopLabel>CYPH_DIFF_C1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_diff_c2</modelName>
          <modShopLabel>CYPH_DIFF_C2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_diff_cc</modelName>
          <modShopLabel>CYPH_DIFF_CC</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_CHASSIS2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_dash_ac</modelName>
          <modShopLabel>CYPH_DASH_AC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_l</Item>
          </turnOffBones>
          <type>VMT_INTERIOR3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_dash_aw</modelName>
          <modShopLabel>CYPH_DASH_AW</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_l</Item>
          </turnOffBones>
          <type>VMT_INTERIOR3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_dash_ak</modelName>
          <modShopLabel>CYPH_DASH_AK</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_l</Item>
          </turnOffBones>
          <type>VMT_INTERIOR3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_dash_af</modelName>
          <modShopLabel>CYPH_DASH_AF</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_l</Item>
          </turnOffBones>
          <type>VMT_INTERIOR3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_seats_a</modelName>
          <modShopLabel>CYPH_SEATS_A</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_seats_b3</modelName>
          <modShopLabel>SMOD_BSEAT2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_seats_b</modelName>
          <modShopLabel>SMOD_BSEAT3</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_seats_c</modelName>
          <modShopLabel>SMOD_BSEAT4</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_seats_d3</modelName>
          <modShopLabel>SMOD_BSEAT5</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_seats_d</modelName>
          <modShopLabel>SMOD_BSEAT6</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_seats_d2</modelName>
          <modShopLabel>SMOD_BSEAT7</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_seats_e3</modelName>
          <modShopLabel>SMOD_BSEAT8</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_seats_e</modelName>
          <modShopLabel>SMOD_BSEAT9</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_seats_e2</modelName>
          <modShopLabel>SMOD_BSEAT10</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_seats_f</modelName>
          <modShopLabel>CYPH_SEATS_F</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>misc_s</Item>
          </turnOffBones>
          <type>VMT_SEATS</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_mir_l_a1</modelName>
          <modShopLabel>CYPH_MIRRORS_A1</modShopLabel>
          <linkedModels>
            <Item>cyph_mir_r_a1</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_a</Item>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_WING_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_mir_l_ac</modelName>
          <modShopLabel>CYPH_MIRRORS_AC</modShopLabel>
          <linkedModels>
            <Item>cyph_mir_r_ac</Item>
          </linkedModels>
          <turnOffBones>
            <Item>misc_a</Item>
            <Item>misc_b</Item>
          </turnOffBones>
          <type>VMT_WING_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_dials</modelName>
          <modShopLabel>CYPH_DIALS</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_INTERIOR4</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace1</modelName>
          <modShopLabel>SULTAN_SBRACE1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace2</modelName>
          <modShopLabel>SULTAN_SBRACE2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace3</modelName>
          <modShopLabel>SULTAN_SBRACE3</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace4</modelName>
          <modShopLabel>SULTAN_SBRACE4</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace5</modelName>
          <modShopLabel>SULTAN_SBRACE5</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace6</modelName>
          <modShopLabel>SULTAN_SBRACE6</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace8</modelName>
          <modShopLabel>SULTAN_SBRACE8</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace9</modelName>
          <modShopLabel>SULTAN_SBRACE9</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace_b1</modelName>
          <modShopLabel>CYPH_BRACE_B1</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace_b2</modelName>
          <modShopLabel>CYPH_BRACE_B2</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace_a</modelName>
          <modShopLabel>CYPH_BRACE_A</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace_c</modelName>
          <modShopLabel>CYPH_BRACE_C</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace_d</modelName>
          <modShopLabel>CYPH_BRACE_D</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace_e</modelName>
          <modShopLabel>CYPH_BRACE_E</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace_g</modelName>
          <modShopLabel>CYPH_BRACE_G</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace_h</modelName>
          <modShopLabel>CYPH_BRACE_H</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>cyph_brace_f</modelName>
          <modShopLabel>CYPH_BRACE_F</modShopLabel>
          <linkedModels />
          <turnOffBones />
          <type>VMT_ENGINEBAY3</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel1</modelName>
          <modShopLabel>SMOD2_STEER1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel2</modelName>
          <modShopLabel>SMOD2_STEER2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel3</modelName>
          <modShopLabel>SMOD2_STEER3</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel4</modelName>
          <modShopLabel>SMOD2_STEER4</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel5</modelName>
          <modShopLabel>SMOD2_STEER5</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel6</modelName>
          <modShopLabel>SMOD2_STEER6</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel7</modelName>
          <modShopLabel>SMOD2_STEER7</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel8</modelName>
          <modShopLabel>SMOD2_STEER8</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel9</modelName>
          <modShopLabel>SMOD2_STEER9</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel10</modelName>
          <modShopLabel>SMOD2_STEER10</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel11</modelName>
          <modShopLabel>SMOD2_STEER11</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel12</modelName>
          <modShopLabel>SMOD2_STEER12</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel13</modelName>
          <modShopLabel>SMOD2_STEER13</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel14</modelName>
          <modShopLabel>SMOD2_STEER14</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel15</modelName>
          <modShopLabel>SMOD2_STEER15</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_stwheel16</modelName>
          <modShopLabel>SMOD2_STEER16</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>steeringwheel</Item>
          </turnOffBones>
          <type>VMT_STEERING</type>
          <bone>steeringwheel</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_hood_ac</modelName>
          <modShopLabel>f140r_hood_AC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140rpd_hood_d</modelName>
          <modShopLabel>f140rpd_hood_D</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_hood_dc</modelName>
          <modShopLabel>f140r_hood_DC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140rpd_hood_b</modelName>
          <modShopLabel>f140rpd_hood_B</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_hood_bc</modelName>
          <modShopLabel>f140r_hood_BC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140rpd_hood_e</modelName>
          <modShopLabel>f140rpd_hood_E</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_hood_ec</modelName>
          <modShopLabel>f140r_hood_EC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140rpd_hood_g</modelName>
          <modShopLabel>f140rpd_hood_G</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_hood_gc</modelName>
          <modShopLabel>f140r_hood_GC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140rpd_hood_f</modelName>
          <modShopLabel>f140rpd_hood_F</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_hood_fc</modelName>
          <modShopLabel>f140r_hood_FC</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140rpd_hood_c1</modelName>
          <modShopLabel>f140rpd_hood_C1</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140rpd_hood_c2</modelName>
          <modShopLabel>f140rpd_hood_C2</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140rpd_hood_h</modelName>
          <modShopLabel>f140rpd_hood_h</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140rpd_hood_i</modelName>
          <modShopLabel>f140rpd_hood_i</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        <Item>
          <modelName>f140r_hood_ic</modelName>
          <modShopLabel>f140r_hood_ic</modShopLabel>
          <linkedModels />
          <turnOffBones>
            <Item>bonnet</Item>
          </turnOffBones>
          <type>VMT_BONNET</type>
          <bone>bonnet</bone>
          <collisionBone>bonnet</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <!-- RL -->
        <Item>
          <modelName>f140r_rain_light</modelName>
          <modShopLabel>f140r_rain_light</modShopLabel>
          <linkedModels />
          <turnOffBones /> 
          <type>VMT_HYDRO</type>
          <bone>bumper_r</bone>
          <collisionBone>bumper_r</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>


        <!-- DOORS -->
        <Item>
          <modelName>f140r_door_dside_f_wd</modelName>
          <modShopLabel>f140rdoordsidefwd</modShopLabel>
          <linkedModels>
          <Item>f140r_door_pside_f_wd</Item>
          <Item>f140r_door_dside_r_wd</Item>
          <Item>f140r_door_pside_r_wd</Item>
          </linkedModels>
          <turnOffBones/>
          <type>VMT_DOOR_L</type>
          <bone>door_dside_f</bone>
          <collisionBone>door_dside_f</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <!-- INTERCOOLER -->
        <Item>
          <modelName>f140r_intercooler_1</modelName>
          <modShopLabel>fr_INTE_1</modShopLabel>
          <linkedModels />
          <turnOffBones>
             <item>misc_t</item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140r_intercooler_2</modelName>
          <modShopLabel>fr_INTE_2</modShopLabel>
          <linkedModels />
          <turnOffBones>
             <item>misc_t</item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140r_intercooler_3</modelName>
          <modShopLabel>fr_INTE_3</modShopLabel>
          <linkedModels />
          <turnOffBones>
             <item>misc_t</item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140r_intercooler_4</modelName>
          <modShopLabel>fr_INTE_4</modShopLabel>
          <linkedModels />
          <turnOffBones>
             <item>misc_t</item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>

        <Item>
          <modelName>f140r_intercooler_5</modelName>
          <modShopLabel>fr_INTE_5</modShopLabel>
          <linkedModels />
          <turnOffBones>
             <item>misc_t</item>
          </turnOffBones>
          <type>VMT_ENGINEBAY2</type>
          <bone>chassis</bone>
          <collisionBone>chassis</collisionBone>
          <cameraPos>VMCP_DEFAULT</cameraPos>
          <audioApply value="1.000000" />
          <weight value="20" />
          <turnOffExtra value="false" />
          <disableBonnetCamera value="false" />
          <allowBonnetSlide value="true" />
        </Item>
        </visibleMods>
    <linkMods>

        <Item>
          <modelName>f140r_door_pside_f_wd</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>

        <Item>
          <modelName>f140r_door_dside_r_wd</modelName>
          <bone>door_dside_r</bone>
          <turnOffExtra value="false" />
        </Item>

        <Item>
          <modelName>f140r_door_pside_r_wd</modelName>
          <bone>door_pside_r</bone>
          <turnOffExtra value="false" />
        </Item>

        <Item> 
          <modelName>f140r_wing_b</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
        </Item>

        <Item> 
          <modelName>f140r_wing_bc</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
        </Item>

        <Item> 
          <modelName>f140r_wing_c</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
        </Item>

        <Item> 
          <modelName>f140r_wing_cc</modelName>
          <bone>boot</bone>
          <turnOffExtra value="false" />
        </Item>

        <Item> 
          <modelName>f140rpd_wing_rf</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
        </Item>

        <Item> 
          <modelName>f140r_wing_rfc</modelName>
          <bone>wing_rf</bone>
          <turnOffExtra value="false" />
        </Item>

        <Item>
          <modelName>f140r_con_a</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="false" />
        </Item>

        <Item>
          <modelName>f140r_con_ac</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="false" />
        </Item>

        <Item>
          <modelName>f140r_spltac</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="false" />
        </Item>

        <Item>
          <modelName>cyph_mir_r_a1</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>cyph_mir_r_ac</modelName>
          <bone>door_pside_f</bone>
          <turnOffExtra value="false" />
        </Item>
        
        <Item>
          <modelName>cyph_hlite_r_1</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>cyph_hlite_r_2</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>cyph_hlite_r_3</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>cyph_hlite_r_4</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>cyph_hlite_r_5</modelName>
          <bone>headlight_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>f140rpd_arch_f</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>f140rpd_arch_r2</modelName>
          <bone>bumper_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>f140rpd_arch_f2</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>f140rpd_arch_r2</modelName>
          <bone>bumper_r</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>f140r_arch_f2c</modelName>
          <bone>bumper_f</bone>
          <turnOffExtra value="false" />
        </Item>
        <Item>
          <modelName>f140r_arch_r2c</modelName>
          <bone>bumper_r</bone>
          <turnOffExtra value="false" />
        </Item>

      </linkMods>
       <statMods>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="75" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ENGINE</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="30" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="65" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="120" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_BRAKES</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="50" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="200" />
          <audioApply value="1.000000" />
          <weight value="5" />
          <type>VMT_GEARBOX</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="10" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="15" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="25" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="30" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_SUSPENSION</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="20" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="40" />
          <audioApply value="1.000000" />
          <weight value="10" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="60" />
          <audioApply value="1.000000" />
          <weight value="20" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="80" />
          <audioApply value="1.000000" />
          <weight value="30" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier />
          <modifier value="100" />
          <audioApply value="1.000000" />
          <weight value="40" />
          <type>VMT_ARMOUR</type>
        </Item>
        <Item>
          <identifier>HORN_TRUCK</identifier>
          <modifier value="1766676233" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_COP</identifier>
          <modifier value="2904189469" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_CLOWN</identifier>
          <modifier value="2543206147" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_1</identifier>
          <modifier value="1732399718" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_2</identifier>
          <modifier value="2046162893" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_3</identifier>
          <modifier value="2194999691" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_4</identifier>
          <modifier value="2508304100" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_MUSICAL_5</identifier>
          <modifier value="3707223535" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HORN_SAD_TROMBONE</identifier>
          <modifier value="632950117" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_1</identifier>
          <modifier value="3628534289" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_2</identifier>
          <modifier value="3892554122" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_3</identifier>
          <modifier value="4112892878" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_4</identifier>
          <modifier value="116877169" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_5</identifier>
          <modifier value="2684983719" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_6</identifier>
          <modifier value="2982690084" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>MUSICAL_HORN_BUSINESS_7</identifier>
          <modifier value="3203290992" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
          <modifier value="771284519" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
          <modifier value="2586621229" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
          <modifier value="283386134" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
          <modifier value="3884502400" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
          <modifier value="265723083" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
          <modifier value="1746883687" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
          <modifier value="1919870950" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
          <modifier value="1085277077" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_1</identifier>
          <modifier value="444549672" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_2</identifier>
          <modifier value="1603064898" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_3</identifier>
          <modifier value="240366033" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>HIPSTER_HORN_4</identifier>
          <modifier value="960137118" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_1</identifier>
          <modifier value="3572144790" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_2</identifier>
          <modifier value="3801396714" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_3</identifier>
          <modifier value="2843657151" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>INDEP_HORN_4</identifier>
          <modifier value="3341811489" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LUXE_HORN_1</identifier>
          <modifier value="3199657341" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
    <Item>
          <identifier>LUXE_HORN_2</identifier>
          <modifier value="2900378064" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
    <Item>
          <identifier>LUXE_HORN_3</identifier>
          <modifier value="3956195248" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LUXORY_HORN_1</identifier>
          <modifier value="676333254" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LUXURY_HORN_2</identifier>
          <modifier value="2099578296" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LUXURY_HORN_3</identifier>
          <modifier value="1373384483" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>ORGAN_HORN_LOOP_01</identifier>
          <modifier value="2916775806" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
          <modifier value="3714706952" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>ORGAN_HORN_LOOP_02</identifier>
          <modifier value="2611860261" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
          <modifier value="3206770359" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LOWRIDER_HORN_1</identifier>
          <modifier value="310529291" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
          <modifier value="2965568987" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>LOWRIDER_HORN_2</identifier>
          <modifier value="55291550" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
          <modifier value="965054819" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>XM15_HORN_01</identifier>
          <modifier value="55862314" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>XM15_HORN_01_PREVIEW</identifier>
          <modifier value="2156743178" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>XM15_HORN_02</identifier>
          <modifier value="400002352" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>XM15_HORN_02_PREVIEW</identifier>
          <modifier value="897484282" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>XM15_HORN_03</identifier>
          <modifier value="560832604" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>XM15_HORN_03_PREVIEW</identifier>
          <modifier value="314232747" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_AW_Airhorn_01</identifier>
          <modifier value="3851180092" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>DLC_AW_Airhorn_01_Preview</identifier>
          <modifier value="246182814" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_AW_Airhorn_02</identifier>
          <modifier value="3412861948" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>DLC_AW_Airhorn_02_Preview</identifier>
          <modifier value="1804608241" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <Item>
          <identifier>DLC_AW_Airhorn_03</identifier>
          <modifier value="3374260066" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
        <!-- PREVIEW HORN - FOR MOD SHOP USE ONLY -->
        <Item>
          <identifier>DLC_AW_Airhorn_03_Preview</identifier>
          <modifier value="2798044638" />
          <audioApply value="1.000000" />
          <weight value="0" />
          <type>VMT_HORN</type>
        </Item>
      </statMods>
      <slotNames>

        <Item>
          <slot>VMT_TRUNK</slot>
          <name>TOP_Tail_Lights</name>
        </Item>

        <Item>
          <slot>VVMT_CHASSIS5</slot>
          <name>TOP_Wide_Body</name>
        </Item>

        <Item>
          <slot>VMT_KNOB</slot>
          <name>TOP_Bumper_F_Misc</name>
        </Item>
        <Item>
          <slot>VMT_CHASSIS</slot>
          <name>TOP_CAGE</name>
        </Item>
        <Item>
          <slot>VMT_WING_L</slot>
          <name>TOP_MIR</name>
        </Item>
        <Item>
          <slot>VMT_ICE</slot>
          <name>TOP_VENT</name>
        </Item>
        <Item>
          <slot>VMT_CHASSIS3</slot>
          <name>TOP_CATCH</name>
        </Item>
        <Item>
          <slot>VMT_INTERIOR1</slot>
          <name>TOP_SUNST</name>
        </Item>
        <Item>
          <slot>VMT_INTERIOR2</slot>
          <name>TOP_INTERTYPE</name>
        </Item>
        <Item>
          <slot>VMT_WING_R</slot>
          <name>TOP_HLT</name>
        </Item>
      </slotNames>
      <liveryNames />
    </Item>
   </Kits>
</CVehicleModelInfoVarGlobal>