local Keys = {
    ["ESC"] = 322, ["F1"] = 288, ["F2"] = 289, ["F3"] = 170, ["F5"] = 166, ["F6"] = 167, ["F7"] = 168, ["F8"] = 169, ["F9"] = 56, ["F10"] = 57,
    ["~"] = 243, ["1"] = 157, ["2"] = 158, ["3"] = 160, ["4"] = 164, ["5"] = 165, ["6"] = 159, ["7"] = 161, ["8"] = 162, ["9"] = 163, ["-"] = 84, ["="] = 83, ["BACKSPACE"] = 177,
    ["TAB"] = 37, ["Q"] = 44, ["W"] = 32, ["E"] = 38, ["R"] = 45, ["T"] = 245, ["Y"] = 246, ["U"] = 303, ["P"] = 199, ["["] = 39, ["]"] = 40, ["ENTER"] = 18,
    ["CAPS"] = 137, ["A"] = 34, ["S"] = 8, ["D"] = 9, ["F"] = 23, ["G"] = 47, ["H"] = 74, ["K"] = 311, ["L"] = 182,
    ["LEFTSHIFT"] = 21, ["Z"] = 20, ["X"] = 73, ["C"] = 26, ["V"] = 0, ["B"] = 29, ["N"] = 249, ["M"] = 244, [","] = 82, ["."] = 81,
    ["LEFTCTRL"] = 36, ["LEFTALT"] = 19, ["SPACE"] = 22, ["RIGHTCTRL"] = 70,
    ["HOME"] = 213, ["PAGEUP"] = 10, ["PAGEDOWN"] = 11, ["DELETE"] = 178,
    ["LEFT"] = 174, ["RIGHT"] = 175, ["TOP"] = 27, ["DOWN"] = 173,
    ["NENTER"] = 201, ["N4"] = 108, ["N5"] = 60, ["N6"] = 107, ["N+"] = 96, ["N-"] = 97, ["N7"] = 117, ["N8"] = 61, ["N9"] = 118
}
  
local First = vector3(0.0, 0.0, 0.0)
local Second = vector3(5.0, 5.0, 5.0)
local Vehicle = {Coords = nil, Vehicle = nil, Dimension = nil, IsInFront = false, Distance = nil}

function isBeingPushed(veh)
    if (not DecorExistOn(veh, "beingPushed")) then
        setBeingPushed(veh, false)
        return false
    end
    return DecorGetBool(veh, "beingPushed")
end 

function setBeingPushed(veh, value)
    DecorSetBool(veh, "beingPushed", value)
end

onBaseReady(function()
    while true do
        local ped = PlayerPedId()
        local closestVehicle, Distance = Base.Vehicles.GetClosestVehicle(ped, 5.0, false)
        local vehicleCoords = GetEntityCoords(closestVehicle)
        local Distance = GetDistanceBetweenCoords(GetEntityCoords(ped), vehicleCoords)
        local dimension = GetModelDimensions(GetEntityModel(closestVehicle), First, Second)
        if Distance < 3.5  and not IsPedInAnyVehicle(ped, false) then
            Vehicle.Coords = vehicleCoords
            Vehicle.Dimensions = dimension
            Vehicle.Vehicle = closestVehicle
            Vehicle.Distance = Distance
            if GetDistanceBetweenCoords(GetEntityCoords(closestVehicle) + GetEntityForwardVector(closestVehicle), GetEntityCoords(ped), true) > GetDistanceBetweenCoords(GetEntityCoords(closestVehicle) + GetEntityForwardVector(closestVehicle) * -1, GetEntityCoords(ped), true) then
                Vehicle.IsInFront = false
            else
                Vehicle.IsInFront = true
            end
        else
            Vehicle = {Coords = nil, Vehicle = nil, Dimensions = nil, IsInFront = false, Distance = nil}
        end
        Citizen.Wait(500)
    end
end)
  
local isAttached = false
RegisterCommand("push", function()
    local inVeh = IsPedInAnyVehicle(GetPlayerPed(-1), false)
    local engineOn = GetIsVehicleEngineRunning(Vehicle.Vehicle)
    
    if (isDead or isCuffed or IsPedSwimming(PlayerPedId())) then
        return
    end

    if engineOn then
        Base.Notification("<font color='red'>You are unable to move a vehicle whilst it's in gear</font>", {layout = 'bottom-right'})
    end         

    if Vehicle.Vehicle == nil or isAttached then 
        return 
    end

    if (isBeingPushed(Vehicle.Vehicle)) then
        return
    end

    -- this is ashley's fault
    local roll = GetEntityRoll(Vehicle.Vehicle)
    if (roll > 75.0 or roll < -75.0) then
        return
    end

    while true do 
        Citizen.Wait(5)
        local ped = PlayerPedId(-1)
        if Vehicle.Vehicle ~= nil and not inVeh and not engineOn then   
            if not inVeh and engineOn then return end

            -- Can enable if it becomes a problem
            --[[ if GetVehicleDoorLockStatus(Vehicle.Vehicle) == 2 then
            Base.Notification("<font color='red'>You are unable to move a locked vehicle</font>", {layout = 'bottom-right'})
            return
            end --]]

            if IsVehicleSeatFree(Vehicle.Vehicle, -1) and not IsEntityAttachedToEntity(ped, Vehicle.Vehicle) and GetVehicleEngineHealth(Vehicle.Vehicle) and not engineOn then
                NetworkRequestControlOfEntity(Vehicle.Vehicle)
                local coords = GetEntityCoords(ped)
                if Vehicle.IsInFront then
                    setBeingPushed(Vehicle.Vehicle, true)
                    AttachEntityToEntity(PlayerPedId(), Vehicle.Vehicle, GetPedBoneIndex(6286), 0.0, Vehicle.Dimensions.y * -1 + 0.1 , Vehicle.Dimensions.z + 1.0, 0.0, 0.0, 180.0, 0.0, false, false, true, false, true)
                    Base.Notification("<font color='green'>You moving a vehicle. [X] to let go</font>", {layout = 'bottom-right'})
                    isAttached = true
                else
                    setBeingPushed(Vehicle.Vehicle, true)
                    AttachEntityToEntity(PlayerPedId(), Vehicle.Vehicle, GetPedBoneIndex(6286), 0.0, Vehicle.Dimensions.y - 0.3, Vehicle.Dimensions.z  + 1.0, 0.0, 0.0, 0.0, 0.0, false, false, true, false, true)
                    Base.Notification("<font color='green'>You moving a vehicle. [X] to let go</font>", {layout = 'bottom-right'})
                    isAttached = true
                end

                RequestAnimDict('missfinale_c2ig_11')
                TaskPlayAnim(ped, 'missfinale_c2ig_11', 'pushcar_offcliff_m', 2.0, -8.0, -1, 35, 0, 0, 0, 0)
                Citizen.Wait(200)

                local currentVehicle = Vehicle.Vehicle
                while true do
                    Citizen.Wait(5)
                    if IsDisabledControlPressed(0, Keys["A"]) then
                        TaskVehicleTempAction(PlayerPedId(), currentVehicle, 11, 1000)
                    end

                    if IsDisabledControlPressed(0, Keys["D"]) then
                        TaskVehicleTempAction(PlayerPedId(), currentVehicle, 10, 1000)
                    end

                    if (IsDisabledControlPressed(0, Keys["W"])) then
                        if Vehicle.IsInFront then
                            SetVehicleForwardSpeed(currentVehicle, -1.0)
                        else
                            SetVehicleForwardSpeed(currentVehicle, 1.0)
                        end
                    else
                        SetVehicleForwardSpeed(currentVehicle, 0.0)
                    end

                    if HasEntityCollidedWithAnything(currentVehicle) then
                        SetVehicleOnGroundProperly(currentVehicle)
                    end

                    if (not IsEntityPlayingAnim(PlayerPedId(), 'missfinale_c2ig_11', 'pushcar_offcliff_m', 3)) then
                        TaskPlayAnim(ped, 'missfinale_c2ig_11', 'pushcar_offcliff_m', 2.0, -8.0, -1, 35, 0, 0, 0, 0)
                    end

                    if IsDisabledControlPressed(0, Keys["E"]) or IsDisabledControlPressed(0, Keys["X"]) or GetIsVehicleEngineRunning(currentVehicle) then
                        DetachEntity(ped, false, false)
                        StopAnimTask(ped, 'missfinale_c2ig_11', 'pushcar_offcliff_m', 2.0)
                        FreezeEntityPosition(ped, false)
                        isAttached = false
                        setBeingPushed(currentVehicle, false)
                        return
                    end
                end
            end
        end
    end
end)
  
Citizen.CreateThread(function()
    if (not DecorIsRegisteredAsType("beingPushed", 2)) then
        DecorRegister("beingPushed", 2)
    end
end)