inspectVehicle = function()
	local veh = Base.Vehicles.GetClosestVehicle(GetPlayerPed(-1), 4.0, true)

	if (veh) then
		if veh == GetVehiclePedIsIn(PlayerPedId(), false) and NetworkGetEntityIsNetworked(GetVehiclePedIsIn(PlayerPedId(), false)) then
			return Base.Notification("Please exit the vehicle to inspect it")
		end

		local props = Base.Vehicles.Props.Get(veh)
		local stats = { health, engine, brakes, trans, susps, turbo, armor, class }
		local text  = ""

		text = text .. "<h3>" .. GetDisplayNameFromVehicleModel(props.model) .. " - " .. props.plate .. " </h3>"

		local state = Entity(veh).state

		if ((props["health"] / 10) ~= 10.0) then
			stats.health = math.floor((props["health"] / 10))
			text = text .. "Health: " .. stats.health .. "%"
		else
			text = text .. "Health: 0%"
		end

		if (props["modEngine"] == 0) then
			stats.engine = "Level 1"
		elseif (props["modEngine"] == 1) then
			stats.engine = "Level 2"
		elseif (props["modEngine"] == 2) then
			stats.engine = "Level 3"
		elseif (props["modEngine"] == 3) then
			stats.engine = "Level 4"
		elseif (props["modEngine"] == -1) then
			stats.engine = "Stock Engine"
		end

		text = text .. "<br>Engine: " .. stats.engine

		if (props["modBrakes"] == 0) then
			stats.brakes = "Level 1 - Street Brakes"
		elseif (props["modBrakes"] == 1) then
			stats.brakes = "Level 2 - Sport Brakes"
		elseif (props["modBrakes"] == 2) then
			stats.brakes = "Level 3 - Race Brakes"
		elseif (props["modBrakes"] == -1) then
			stats.brakes = "Stock Brakes"
		end

		text = text .. "<br>Brakes: " .. stats.brakes

		if (props["modTransmission"] == 0) then
			stats.trans = "Level 1 - Street Transmission"

		elseif (props["modTransmission"] == 1) then
			stats.trans = "Level 2 - Sports Tramsmission"

		elseif (props["modTransmission"] == 2) then
			stats.trans = "Level 3 - Race Transmission"

		elseif (props["modTransmission"] == -1) then
			stats.trans = "Stock Transmission"
		end

		text = text .. "<br>Trans: " .. stats.trans

		if (props["modSuspension"] == 0) then
			stats.susps = "Level 1 - Lowered Suspension"

		elseif (props["modSuspension"] == 1) then
			stats.susps = "Level 2 - Street Suspension"

		elseif (props["modSuspension"] == 2) then
			stats.susps = "Level 3 - Sport Suspension"

		elseif (props["modSuspension"] == 3) then
			stats.susps = "Level 4 - Competition Suspension"

		elseif (props["modSuspension"] == -1) then
			stats.susps = "Stock Suspension"
		end

		text = text .. "<br>Susp: " .. stats.susps

		if (props["modTurbo"]) then
			stats.turbo = "Installed"
		else
			stats.turbo = "None"
		end

		text = text .. "<br>Turbo: " .. stats.turbo

		if (props["modArmor"] == 0) then
			stats.armor = "20% Armour Upgrade"

		elseif (props["modArmor"] == 1) then
			stats.armor = "40% Armour Upgrade"

		elseif (props["modArmor"] == 2) then
			stats.armor = "60% Armour Upgrade"

		elseif (props["modArmor"] == 3) then
			stats.armor = "80% Armour Upgrade"

		elseif (props["modArmor"] == 4) then
			stats.armor = "100% Armour Upgrade"

		elseif (props["modArmor"] == -1) then
			stats.armor = "None"
		end

		text = text .. "<br>Armour: " .. stats.armor

		if state.class then
			stats.class = state.class
		else
			stats.class = "None"
		end

		text = text .. "<br>Vehicle Class: " .. stats.class

		TriggerEvent('fdg_ui:SendNotification', text, { timeout = 10000 })
	end

end

RegisterCommand("inspect", function(source, args)
	inspectVehicle()
end, false)


--- ===================================== QUI Alt Menu ========================================== ---

AddVehicleInteractionAction(
	function()
		return Base.Vehicles.GetClosestVehicle(playerPed, 4.0, true) and not (NetworkGetEntityIsNetworked(Base.Vehicles.GetClosestVehicle(playerPed, 4.0, true)))
	end,

	function()
		inspectVehicle()
	end,

	{ text = "Inspect Vehicle", icon = "&#xF0349;", action = "inspect_vehicle" },

	{ requires = { Vehicle = true } }
)

--- ============================================================================================= ---
