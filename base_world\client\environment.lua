local timeTransitioning = false
local timeState = { hour = 0, minute = 0, second = 0, sync = 0, freeze = false }
local currentWeather = ""

-- This function should transition between the current time and the target time.
local function transitionToTime(hours, mins, secs)
    timeTransitioning = true
    local currentHours, currentMins, currentSecs = GetClockHours(), GetClockMinutes(), GetClockSeconds()
    local targetHours, targetMins, targetSecs = hours, mins, secs
    print("Transitioning to time", hours, mins, secs, "from", currentHours, currentMins, currentSecs)

    local currentTotalSecs = currentHours * 3600 + currentMins * 60 + currentSecs
    local targetTotalSecs = targetHours * 3600 + targetMins * 60 + targetSecs

    local diff = targetTotalSecs - currentTotalSecs
    local diffAbs = math.abs(diff)

    NetworkOverrideClockMillisecondsPerGameMinute(17) -- 1 game hour = 1 second

    -- It will take this many game hours to reach the target time
    local gameHours = math.floor(diffAbs / 60 / 60)
    Wait(gameHours * 1000)
    NetworkOverrideClockTime(targetHours, targetMins, targetSecs)
    timeTransitioning = false
end

local function getTimeFromSync(syncTime, offset)
    local epoch = GetNetworkTime() - syncTime
    local gameSecondsPassed = (epoch / 1000) / (TIME_CYCLESPEED / 60)

    local hours = (offset.hour + (math.floor(gameSecondsPassed / 3600) % 24)) % 24
    local mins = (offset.minute + (math.floor(gameSecondsPassed / 60) % 60)) % 60
    local secs = (offset.second + (math.floor(gameSecondsPassed) % 60)) % 60

    return hours, mins, secs
end

local function processWeatherSync(weather, syncOptions)
    -- Snow
    SetForceVehicleTrails(weather.snow)
    SetForcePedFootstepsTracks(weather.snow)

    if currentWeather == weather.name then
        return
    end

    currentWeather = weather.name

    if syncOptions.instant then
        ClearWeatherTypePersist()
        SetWeatherTypeNowPersist(weather.name)
        print("Changing to ", weather.name, "instantly")
    else
        print("Changing to ", weather.name, "over", WEATHER_TRANSITION_SPEED, "seconds")
        SetWeatherTypeOvertimePersist(weather.name, WEATHER_TRANSITION_SPEED)
    end
end

local function processTimeSync(time, syncOptions)
    timeState = time

    if syncOptions.instant then
        NetworkOverrideClockTime(time.hour, time.minute, time.second)
    else
        transitionToTime(time.hour, time.minute, time.second)
    end

    if time.freeze then
        NetworkOverrideClockMillisecondsPerGameMinute(99999999)
        print("Freezing time @", time.hour, time.minute, time.second)
    else
        NetworkOverrideClockMillisecondsPerGameMinute(TIME_CYCLESPEED * 1000)
        local hours, mins, secs = getTimeFromSync(time.sync, time)
        print("Syncing time @", hours, mins, secs)
    end
end

local function syncEnvironment(syncOptions, data)
    print("Syncing environment", json.encode(syncOptions), json.encode(data))

    ClearOverrideWeather()

    -- Weather
    if data.weather then
        processWeatherSync(data.weather, syncOptions)
    end

    if data.blackout ~= nil then
        SetArtificialLightsState(data.blackout)
        SetArtificialLightsStateAffectsVehicles(false)
    end

    -- Time
    if data.time then
        processTimeSync(data.time, syncOptions)
    end
end
RegisterNetEvent("world:environmentSync", syncEnvironment)

-- Thread which checks what time it should be every 500ms and updates the time if necessary
CreateThread(function()
    while true do
        Wait(500)

        -- Keep time in sync
        if not timeState.freeze and not timeTransitioning then
            local hours, mins, secs = getTimeFromSync(timeState.sync, timeState)
            -- print("Syncing time", timeState.sync, hours, mins, secs)
            NetworkOverrideClockTime(hours, mins, secs)
        end
    end
end)

local function getWeather()
    return currentWeather
end
exports("getWeather", getWeather)

-- CreateThread(function()
--     Wait(1000)
--     NetworkOverrideClockMillisecondsPerGameMinute(TIME_CYCLESPEED * 1000)

--     Wait(1000)

--     NetworkOverrideClockTime(6, 0, 0)

--     Wait(3000)

--     transitionToTime(12, 0, 0)
--     NetworkOverrideClockMillisecondsPerGameMinute(TIME_CYCLESPEED * 1000)
-- end)
