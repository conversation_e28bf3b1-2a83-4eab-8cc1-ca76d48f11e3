local makingSalami = false
local salamiDesc = nil

local deliMachines = {
    vector3(381.000, -782.233, 29.333), -- Satriale's Deli
}

CreateThread(function()
    for i, coords in ipairs(deliMachines) do
        CreateInteractableMarker(coords, function()
            if makingSalami then
                CreateThread(function()
                    Wait(20000)
                    makingSalami = false
                end)

                return Base.Notification("You are already making salami! Please wait 20 seconds")
            end

            TriggerServerCallback("business:salamijob", function(result)
                if not result.success then
                    return Base.Notification(result.message)
                end

                OpenDialog("Salami Type (blank for none)", function(desc)
                    salamiDesc = desc
                end)

                exports.main_progressbar:start(18000, "Making Salami")
                makingSalami = true

                Base.Animation.ActionAnimation("anim@scripted@npc@freemode@ig1_cook_lab@condensors@", "idle_01", true, true, false, 18,
                function()
                    TriggerServerEvent("business:makeSalami", salamiDesc)
                    exports.main_progressbar:stop()
                    makingSalami = false
                    salamiDesc = nil
                end,
                function()
                    exports.main_progressbar:stop()
                    makingSalami = false
                    salamiDesc = nil
                end)
            end)
        end, { text = "Make Salami", textRenderDistance = 1.2 }, { scale = 0.5, color = { r = 113, b = 91, g = 64, a = 255 } })
    end
end)
