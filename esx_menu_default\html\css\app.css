@import url(http://fonts.googleapis.com/css?family=Roboto:400,400italic,500,500italic,700,700italic,900,900italic,300italic,300,100italic,100);

@keyframes pulse {
	0% {opacity: 0.5;}
	50% {opacity: 0.3;}
	100% {opacity: 0.5;}
}

.menu {
	font-family: 'Roboto';
	min-width: 300px;
	color: #fff;
	/*box-shadow: 0px 0px 50px 0px #000;*/
	position: absolute;
	font-size: 18;
}

.menu.align-left {
	left: 40;
	top: 50%;
	transform: translate(0, -50%);
}

.menu.align-top-left {
	left: 40;
	top: 40;
}

.menu.align-top {
	left: 50%;
	top: 40;
	transform: translate(-50%, 0);
}

.menu.align-top-right {
	right: 10;
	top: 40;
}

.menu.align-right {
	right: 40;
	top: 50%;
	transform: translate(0, -50%);
}

.menu.align-bottom-right {
	right: 40;
	bottom: 70;
}

.menu.align-bottom {
	left: 50%;
	bottom: 70;
	transform: translate(-50%, 0);
}

.menu.align-bottom-left {
	left: 40;
	bottom: 70;
}

.menu.align-center {
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
}

.menu .head {
	background: linear-gradient(to top right, rgb(20, 65, 133), rgb(87, 133, 208));
	text-align: center;
	padding: 12px;
	height: 30px;
	line-height: 30px;
	font-size: 24;
	text-transform: uppercase;
}

.menu .desc {
	height: 27px;
	display: block;
	background: rgba(0,0,0,0.8);
	color: rgba(52, 79, 132, 0.8);

	text-align: left;
	font-weight: bold;
	line-height: 27px;
	font-size: 17px;
	padding: 0;
	padding-left: 4px;
}

.menu .menu-items {
	max-height: 400px;
	overflow-y: auto;
	padding-top: 0;
	background-color: rgba(0,0,0,0.7);
}

.menu .menu-items .menu-item {
	display: block;
	height: 32px;
	line-height: 32px;
	color: #ffffff;
	text-align: center;
	padding-left: 15px;
	padding-right: 15px;
}

.menu .menu-items .menu-item.selected {
	background: linear-gradient(to top right, rgba(250, 250, 250, 0.5), rgba(255, 255, 255, 0.5));
	color: #000000;
	animation: pulse 1.5s infinite
}

/* width */
::-webkit-scrollbar {
    width: 10px;
}

/* Track */
::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.7);
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: rgba(60, 60, 60, 0.7);
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: rgba(30, 30, 30, 0.7);
}