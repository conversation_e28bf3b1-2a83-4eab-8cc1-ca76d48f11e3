local function openLockerroomMenu(job)
    Base.UI.Menu.CloseAll()
    local elements = {
        { label = 'Access Cloakroom', value = 'cloakroom' },
        { label = 'Access Equipment', value = 'equipment' },
    }

    if LocalPlayer.state.shift and LocalPlayer.state.shift == job then
        table.insert(elements, { label = 'End Shift', value = 'shift_end' })
    else
        table.insert(elements, { label = 'Start Shift', value = 'shift_start' })
    end

    Base.UI.Menu.Open('default', GetCurrentResourceName(), 'lockeroom',
        {
            title    = 'Locker Room',
            align    = 'bottom-right',
            elements = elements
        },
        function(data, menu)
            if data.current.value == 'cloakroom' then
                OpenUniformsMenu(job, false)
            elseif data.current.value == 'equipment' then
                OpenEquipmentMenu(job)
            elseif data.current.value == 'shift_start' then
                LocalPlayer.state:set("shift", job, true)
                SendNotification("You have started your shift")
                menu.close()
            elseif data.current.value == 'shift_end' then
                LocalPlayer.state:set("shift", nil, true)
                SendNotification("You have ended your shift")
                menu.close()
            end
        end,
        function(data, menu)
            menu.close()
        end
    )
end

-- World marker interface
AddEventHandler("org:markerInteraction", function(type, marker)
    if type ~= "cloakroom" then return end
    print("Open Locker Room Menu", marker.organisation, marker.identifier)
    openLockerroomMenu(marker.organisation)
end)
