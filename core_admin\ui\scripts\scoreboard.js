var visable = false;

$(function()
{
    window.addEventListener('message', function(event)
    {
        switch (event.data.action) {
            case 'close':
                $('#wrap').hide();
                visable = false;
                break;

            case 'open':
                $('#wrap').show();
                visable = true;
                break;

            case 'updatePlayerJobs':
                var jobs = event.data.jobs;

                var mechanics = 0;
                if (jobs.mech1) {mechanics += jobs.mech1};
                if (jobs.mech2) {mechanics += jobs.mech2};
                if (jobs.mech3) {mechanics += jobs.mech3};
                if (jobs.mech4) {mechanics += jobs.mech4};
                if (jobs.mech5) {mechanics += jobs.mech5};
                if (jobs.mech6) {mechanics += jobs.mech6};

                var cardealers = 0;
                if (jobs.cardealer) {cardealers += jobs.cardealer};
                if (jobs.cardealer2) {cardealers += jobs.cardealer2};
                if (jobs.cardealer3) {cardealers += jobs.cardealer3};
                if (jobs.cardealer4) {cardealers += jobs.cardealer4};
                if (jobs.cardealer5) {cardealers += jobs.cardealer5};

        		$('#lses').html(jobs.lses || 0);
        		$('#police').html(jobs.police || 0);
        		$('#mechanic').html(mechanics || 0);
        		$('#cardealer').html(cardealers || 0);
                $('#redline').html(jobs.airlines || 0);
                $('#realestate').html(jobs.realestateagent || 0);
                break;

		    case 'updatePlayerList':
                $('#playerlist tr:gt(0)').remove();
                $('#playerlist').append(event.data.players);
                sortPlayerList();
                break;

            case 'updateServerInfo':
                if (event.data.playerCount) {
                    $('#player_count').html(event.data.playerCount);
                }

                if (event.data.maxPlayers) {
                    $('#max_players').html(event.data.maxPlayers);
                }

                if (event.data.uptime) {
                    $('#server_uptime').html(event.data.uptime);
                }

                if (event.data.playTime) {
                    $('#play_time').html(event.data.playTime);
                }
                
                if (event.data.serverName) {
                    $('#server_name').html(event.data.serverName);
                }

                break;


        }
    }, false);
});
       


function sortPlayerList() {
    var table = $('#playerlist'),
        rows = $('tr:not(.heading)', table);

    rows.sort(function(a, b) {
        var keyA = $('td', a).eq(1).html();
        var keyB = $('td', b).eq(1).html();

        return (keyA - keyB);
    });

    rows.each(function(index, row) {
        table.append(row);
    });
}