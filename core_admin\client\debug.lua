local dickheaddebug = false
local debugsphere = false
local debugVariant = false

local Keys = {
    ["ESC"] = 322,
    ["F1"] = 288,
    ["F2"] = 289,
    ["F3"] = 170,
    ["F5"] = 166,
    ["F6"] = 167,
    ["F7"] = 168,
    ["F8"] = 169,
    ["F9"] = 56,
    ["F10"] = 57,
    ["~"] = 243,
    ["1"] = 157,
    ["2"] = 158,
    ["3"] = 160,
    ["4"] = 164,
    ["5"] = 165,
    ["6"] = 159,
    ["7"] = 161,
    ["8"] = 162,
    ["9"] = 163,
    ["-"] = 84,
    ["="] = 83,
    ["BACKSPACE"] = 177,
    ["TAB"] = 37,
    ["Q"] = 44,
    ["W"] = 32,
    ["E"] = 38,
    ["R"] = 45,
    ["T"] = 245,
    ["Y"] = 246,
    ["U"] = 303,
    ["P"] = 199,
    ["["] = 39,
    ["]"] = 40,
    ["ENTER"] = 18,
    ["CAPS"] = 137,
    ["A"] = 34,
    ["S"] = 8,
    ["D"] = 9,
    ["F"] = 23,
    ["G"] = 47,
    ["H"] = 74,
    ["K"] = 311,
    ["L"] = 182,
    ["LEFTSHIFT"] = 21,
    ["Z"] = 20,
    ["X"] = 73,
    ["C"] = 26,
    ["V"] = 0,
    ["B"] = 29,
    ["N"] = 249,
    ["M"] = 244,
    [","] = 82,
    ["."] = 81,
    ["LEFTCTRL"] = 36,
    ["LEFTALT"] = 19,
    ["SPACE"] = 22,
    ["RIGHTCTRL"] = 70,
    ["HOME"] = 213,
    ["PAGEUP"] = 10,
    ["PAGEDOWN"] = 11,
    ["DELETE"] = 178,
    ["LEFT"] = 174,
    ["RIGHT"] = 175,
    ["TOP"] = 27,
    ["DOWN"] = 173,
    ["NENTER"] = 201,
    ["N4"] = 108,
    ["N5"] = 60,
    ["N6"] = 107,
    ["N+"] = 96,
    ["N-"] = 97,
    ["N7"] = 117,
    ["N8"] = 61,
    ["N9"] = 118
}

local function round(num, numDecimalPlaces)
    local mult = 10 ^ (numDecimalPlaces or 0)
    return math.floor(num * mult + 0.5) / mult
end

RegisterNetEvent("hud:enabledebug")
AddEventHandler("hud:enabledebug", function(args)
    dickheaddebug = not dickheaddebug
    displayDebug()
    if dickheaddebug then
        print("Debug: Enabled")
        debugVariant = args[1] or false
    else
        print("Debug: Disabled")
    end
end)

RegisterNetEvent("hud:enablesphere")
AddEventHandler("hud:enablesphere", function(args)
    if (#args == 0) or (#args == 1 and tonumber(args[1]) == 0) then
        debugsphere = false
        return
    end

    local x, y, z, size
    if (#args == 4) then
        x, y, z, size = tonumber(args[1]) + 0.0, tonumber(args[2]) + 0.0, tonumber(args[3]) + 0.0, tonumber(args[4]) + 0.0
    end

    debugsphere = true
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(0)
            if not debugsphere then return end
            local playerPos = GetEntityCoords(PlayerPedId())
            DrawMarker(28, vector3(x or playerPos.x, y or playerPos.y, z or playerPos.z), 0.0, 0.0, 0.0, 0, 0.0, 0.0,
                tonumber(size) or tonumber(args[1]) + 0.0001, tonumber(size) or tonumber(args[1]) + 0.0001, tonumber(size) or tonumber(args[1]) + 0.0001, 255, 0,
                0, 100, false, true, 2, false, false,
                false, false)
        end
    end)
end)

local inFreeze = false
local lowGrav = false

RegisterNetEvent("fdg_debug:playAnim")
AddEventHandler("fdg_debug:playAnim", function(lib, anim)
    Citizen.CreateThread(function()
        RequestAnimDict(lib)

        while not HasAnimDictLoaded(lib) do
            Citizen.Wait(0)
        end

        TaskPlayAnim(GetPlayerPed(-1), lib, anim, 8.0, -8.0, -1, 0, 0, false, false, false)
    end)
end)

function drawTxt(x, y, width, height, scale, text, r, g, b, a)
    SetTextFont(0)
    SetTextProportional(0)
    SetTextScale(0.25, 0.25)
    SetTextColour(r, g, b, a)
    SetTextDropShadow(0, 0, 0, 0, 255)
    SetTextEdge(1, 0, 0, 0, 255)
    SetTextDropShadow()
    SetTextOutline()
    SetTextEntry("STRING")
    AddTextComponentString(text)
    DrawText(x - width / 2, y - height / 2 + 0.005)
end

function DrawText3Ds(x, y, z, text)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())

    SetTextScale(0.35, 0.35)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(0, 255, 0, 215)
    SetTextEntry("STRING")
    SetTextOutline()
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x, _y)
end

function GetVehicle()
    local playerped = GetPlayerPed(-1)
    local playerCoords = GetEntityCoords(playerped)
    local handle, ped = FindFirstVehicle()
    local success
    local rped = nil
    local distanceFrom
    repeat
        local ownerId = GetPlayerServerId(NetworkGetEntityOwner(ped))
        local ownerName = GetPlayerName(NetworkGetEntityOwner(ped))
        local pos = GetEntityCoords(ped)
        local distance = GetDistanceBetweenCoords(playerCoords, pos, true)
        if canPedBeUsed(ped) and distance < 30.0 and (distanceFrom == nil or distance < distanceFrom) then
            distanceFrom = distance
            rped = ped

            local netid = "N/A"
            if NetworkGetEntityIsNetworked(ped) then
                netid = NetworkGetNetworkIdFromEntity(ped)
            end

            -- FreezeEntityPosition(ped, inFreeze)
            if IsEntityTouchingEntity(GetPlayerPed(-1), ped) then
                DrawText3Ds(pos["x"], pos["y"], pos["z"] + 1,
                    "Veh: " .. ped .. "(" .. netid .. ") NetOwner: " .. ownerName .. " (" .. ownerId .. ")" .. " Model: " .. GetEntityModel(ped) .. " IN CONTACT")
            else
                DrawText3Ds(pos["x"], pos["y"], pos["z"] + 1,
                    "Veh: " .. ped .. "(" .. netid .. ") NetOwner: " .. ownerName .. " (" .. ownerId .. ")" .. " Model: " .. GetEntityModel(ped))
            end
            if lowGrav then
                SetEntityCoords(ped, pos["x"], pos["y"], pos["z"] + 5.0)
            end
        end
        success, ped = FindNextVehicle(handle)
    until not success
    EndFindVehicle(handle)
    return rped
end

function GetObject()
    local playerped = GetPlayerPed(-1)
    local playerCoords = GetEntityCoords(playerped)
    local handle, ped = FindFirstObject()
    local success
    local rped = nil
    local distanceFrom
    repeat
        local pos = GetEntityCoords(ped)
        local distance = GetDistanceBetweenCoords(playerCoords, pos, true)
        if distance < 10.0 then
            distanceFrom = distance
            rped = ped

            local subText = "x: " ..
                round(pos["x"], 2) .. " y: " .. round(pos["y"], 2) .. " z: " .. round(pos["z"], 2) .. " r: " .. round(GetEntityHeading(ped), 2)

            --FreezeEntityPosition(ped, inFreeze)
            if NetworkGetEntityIsNetworked(ped) then
                DrawText3Ds(pos["x"], pos["y"], pos["z"],
                    "Obj: " ..
                    ped ..
                    " (" ..
                    NetworkGetNetworkIdFromEntity(ped) .. ") Model: " .. GetEntityModel(ped) .. "\n" .. subText)
            else
                DrawText3Ds(pos["x"], pos["y"], pos["z"] + 1,
                    "Obj: " .. ped .. " Model: " .. GetEntityModel(ped) .. "\n" .. subText)
            end

            if lowGrav then
                --ActivatePhysics(ped)
                SetEntityCoords(ped, pos["x"], pos["y"], pos["z"] + 0.1)
                FreezeEntityPosition(ped, false)
            end
        end

        success, ped = FindNextObject(handle)
    until not success
    EndFindObject(handle)
    return rped
end

local relationshipLabels = {
    [0] = "Companion",
    [1] = "Respect",
    [2] = "Like",
    [3] = "Neutral",
    [4] = "Dislike",
    [5] = "Hate",
    [255] = "Pedestrians"
}

function getNPC()
    local playerped = GetPlayerPed(-1)
    local playerCoords = GetEntityCoords(playerped)
    local handle, ped = FindFirstPed()
    local success
    local rped = nil
    local distanceFrom
    repeat
        local pos = GetEntityCoords(ped)
        local distance = GetDistanceBetweenCoords(playerCoords, pos, true)
        if canPedBeUsed(ped) and distance < 30.0 and (distanceFrom == nil or distance < distanceFrom) then
            distanceFrom = distance
            rped = ped

            if IsPedAPlayer(ped) then
                DrawText3Ds(pos["x"], pos["y"], pos["z"],
                    "Player: " ..
                    NetworkGetPlayerIndexFromPed(ped) .. " (Entity: " .. ped .. ") [SID:" .. GetPlayerServerId(NetworkGetPlayerIndexFromPed(ped)) .. "]")
            elseif NetworkGetEntityIsNetworked(ped) then
                DrawText3Ds(pos["x"], pos["y"], pos["z"],
                    "Ped: " ..
                    ped .. " (" .. NetworkGetNetworkIdFromEntity(ped) .. ") [PID:" .. NetworkGetPlayerIndexFromPed(ped) .. "] Model: " .. GetEntityModel(ped))

                if debugVariant == "relationships" then
                    DrawText3Ds(pos["x"], pos["y"], pos["z"] + 0.5,
                        "Group: " ..
                        GetPedRelationshipGroupHash(ped) ..
                        " Actual: " ..
                        relationshipLabels[GetRelationshipBetweenPeds(ped, PlayerPedId())] ..
                        " | " .. relationshipLabels[GetRelationshipBetweenPeds(PlayerPedId(), ped)] .. " | Flee: " .. (IsPedFleeing(ped) and "true" or "false"))
                end
            else
                DrawText3Ds(pos["x"], pos["y"], pos["z"],
                    "Ped: " .. ped .. " Model: " .. GetEntityModel(ped) .. " [PID:" .. NetworkGetPlayerIndexFromPed(ped) .. "]")

                if debugVariant == "relationships" then
                    DrawText3Ds(pos["x"], pos["y"], pos["z"] + 0.5,
                        "Group: " ..
                        GetPedRelationshipGroupHash(ped) ..
                        " Actual: " ..
                        relationshipLabels[GetRelationshipBetweenPeds(ped, PlayerPedId())] ..
                        " | " .. relationshipLabels[GetRelationshipBetweenPeds(PlayerPedId(), ped)] .. " | Flee: " .. (IsPedFleeing(ped) and "true" or "false"))
                end
            end

            FreezeEntityPosition(ped, inFreeze)
            if lowGrav then
                SetPedToRagdoll(ped, 511, 511, 0, 0, 0, 0)
                SetEntityCoords(ped, pos["x"], pos["y"], pos["z"] + 0.1)
            end
        end
        success, ped = FindNextPed(handle)
    until not success
    EndFindPed(handle)
    return rped
end

function canPedBeUsed(ped)
    if ped == nil then
        return false
    end
    if ped == GetPlayerPed(-1) then
        return false
    end
    if not DoesEntityExist(ped) then
        return false
    end
    return true
end

function displayDebug()
    Citizen.CreateThread(function()
        while dickheaddebug do
            Citizen.Wait(1)
            local ped = GetPlayerPed(-1)
            local pos = GetEntityCoords(ped)

            local forPos = GetOffsetFromEntityInWorldCoords(ped, 0, 1.0, 0.0)
            local backPos = GetOffsetFromEntityInWorldCoords(ped, 0, -1.0, 0.0)
            local LPos = GetOffsetFromEntityInWorldCoords(ped, 1.0, 0.0, 0.0)
            local RPos = GetOffsetFromEntityInWorldCoords(ped, -1.0, 0.0, 0.0)

            local forPos2 = GetOffsetFromEntityInWorldCoords(ped, 0, 2.0, 0.0)
            local backPos2 = GetOffsetFromEntityInWorldCoords(ped, 0, -2.0, 0.0)
            local LPos2 = GetOffsetFromEntityInWorldCoords(ped, 2.0, 0.0, 0.0)
            local RPos2 = GetOffsetFromEntityInWorldCoords(ped, -2.0, 0.0, 0.0)

            local x, y, z = table.unpack(pos)
            local currentStreetHash, intersectStreetHash = GetStreetNameAtCoord(x, y, z, currentStreetHash, intersectStreetHash)
            local currentStreetName = GetStreetNameFromHashKey(currentStreetHash)

            local cameraPitch = GetGameplayCamRelativePitch()

            drawTxt(0.8, 0.50, 0.4, 0.4, 0.30, "Heading: " .. GetEntityHeading(ped), 55, 155, 55, 255)
            drawTxt(0.8, 0.52, 0.4, 0.4, 0.30, "Coords: " .. pos, 55, 155, 55, 255)
            drawTxt(0.8, 0.54, 0.4, 0.4, 0.30, "Camera Tilt (Pitch): " .. cameraPitch, 55, 155, 55, 255)
            drawTxt(0.8, 0.56, 0.4, 0.4, 0.30, "Health: " .. GetEntityHealth(ped), 55, 155, 55, 255)
            drawTxt(0.8, 0.60, 0.4, 0.4, 0.30, "Model: " .. GetEntityModel(ped), 55, 155, 55, 255)
            drawTxt(0.8, 0.62, 0.4, 0.4, 0.30, "Speed: " .. GetEntitySpeed(ped), 55, 155, 55, 255)
            drawTxt(0.8, 0.66, 0.4, 0.4, 0.30, "Interior: " .. GetInteriorFromEntity(ped), 55, 155, 55, 255)
            drawTxt(0.9, 0.66, 0.4, 0.4, 0.30, "Interior (Coords): " .. GetInteriorAtCoords(GetEntityCoords(PlayerPedId())), 55, 155, 55, 255)
            drawTxt(0.8, 0.68, 0.4, 0.4, 0.30, "Room: " .. GetRoomKeyFromEntity(ped), 55, 155, 55, 255)
            drawTxt(0.8, 0.70, 0.4, 0.4, 0.30, "Player Count: " .. #GetActivePlayers(), 55, 155, 55, 255)
            drawTxt(0.8, 0.74, 0.4, 0.4, 0.30, "Instance: " .. (LocalPlayer.state.instanceID or "base"), 55, 155, 55, 255)

            DrawLine(pos, forPos, 255, 0, 0, 115)
            DrawLine(pos, backPos, 255, 0, 0, 115)
            DrawLine(pos, LPos, 255, 255, 0, 115)
            DrawLine(pos, RPos, 255, 255, 0, 115)
            DrawLine(forPos, forPos2, 255, 0, 255, 115)
            DrawLine(backPos, backPos2, 255, 0, 255, 115)
            DrawLine(LPos, LPos2, 255, 255, 255, 115)
            DrawLine(RPos, RPos2, 255, 255, 255, 115)

            local nearped = getNPC()
            local veh = GetVehicle()
            local nearobj = GetObject()
        end
    end)
end

local flags = {}
local debugFlags = false

RegisterCommand("debug_flags", function()
    flags = {}
    for i = 0, 512 do
        flags[i] = GetPedConfigFlag(PlayerPedId(), i, 1)
        print(i, flags[i])
    end
    debugFlags = not debugFlags

    while debugFlags do
        Wait(0)
        for i = 0, 512 do
            local val = GetPedConfigFlag(PlayerPedId(), i, 1)
            if flags[i] ~= val then
                print('fuck')
                print("Flag updated", i, flags[i], val)
                flags[i] = val
            end
        end
    end
end)

RegisterCommand("testcuff", function()
    SetEnableHandcuffs(GetPlayerPed(-1), true)
end)
RegisterCommand("testuncuff", function()
    SetEnableHandcuffs(GetPlayerPed(-1), false)
end)
