local function startPatDown(player, type)
    local playerPed = GetPlayerPed(-1)
    local targetPed = GetPlayerPed(player)

    if DoesEntityExist(targetPed) and player ~= PlayerId() and targetPed ~= playerPed then
        local playerCoords = GetEntityCoords(playerPed)
        local targetCoords = GetEntityCoords(targetPed)
        local distance = #(playerCoords - targetCoords)

        if distance >= 2 then
            Base.Notification("The person you are trying to pat down is too far away!")
            return
        end

        Base.Animation.ActionAnimation("custom@police", "police", true, true, true, 7, function()
            TriggerServerEvent("security:patDown", GetPlayerServerId(player), type)
            TriggerServerEvent("security.finish", GetPlayerServerId(player), type)
        end, function()
            Base.Animation.StopForcedAnim(targetPed)
            TriggerServerEvent("security.finish", GetPlayerServerId(player), type)
        end)
        Base.Animation.StartForcedAnim(targetPed, "misscarsteal2chad_garage", "chad_parking_garage_handsuploop_chad", true, true, true)
        Base.Player.attachPlayerToEntity(targetPed, playerPed, 11816, 0.0, 0.52, 0.0, 0.0, 0.0, 0.0)
    end
end

RegisterInteraction("player", "patdown.weapons", { label = "Pat Down (Weapons)" }, function(player)
    startPatDown(player, "weapons")
end, "patdown.weapons")

RegisterInteraction("player", "patdown.comms", { label = "Pat Down (Comms)" }, function(player)
    startPatDown(player, "comms")
end, "patdown.comms")

RegisterInteraction("player", "patdown.all", { label = "Pat Down" }, function(player)
    startPatDown(player, "all")
end, "patdown.all")

RegisterNetEvent("security.detach", function()
    DetachEntity(PlayerPedId(), true, false)
    Base.Animation.StopForcedAnim(PlayerPedId())
end)