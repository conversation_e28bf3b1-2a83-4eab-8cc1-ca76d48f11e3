window.addEventListener(
  "message",
  (event) => {
    const data = event.data;

    if (data.type === "image") {
      setImage(data.image);
    } else if (data.type === "image-url") {
      setImageURL(data.image);
    }
  },
  false
);

function setImage(image) {
  const renderer = document.getElementById("renderer");
  const img = document.createElement("img");
  img.src = `assets/images/${image}`;
  renderer.innerHTML = "";
  renderer.appendChild(img);
}

function setImageURL(image) {
  const renderer = document.getElementById("renderer");
  const img = document.createElement("img");
  img.src = image;
  renderer.innerHTML = "";
  renderer.appendChild(img);
}
