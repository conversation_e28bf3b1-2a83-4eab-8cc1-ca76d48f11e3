local STATIC_CAMS = {
    {
        pos = vector3(275.3966, -1806.219, 26.93659),
        rot = vector3(0.0, 0.0, 229.80),
        fov = 60.0,
        range = 10.0,
    }
}

local function getCamInRange()
    local ped = PlayerPedId()
    local pos = GetEntityCoords(ped)

    for i, cam in ipairs(STATIC_CAMS) do
        if #(pos - cam.pos) <= cam.range then
            return cam
        end
    end
end

RegisterCommand("staticcam", function()
    local cam = getCamInRange()

    if not cam then
        Base.Notification("No static cam in range.")
    end

    Base.Camera.create("staticcam", 1, cam.pos, cam.rot, cam.fov)
    Base.Camera.render("staticcam", true, false)


    while true do
        Wait(0)

        -- Disable E and ESC
        DisableControlAction(0, 38, true)
        DisableControlAction(0, 177, true)

        -- Did the player press E or ESC
        if IsDisabledControlJustReleased(0, 38) or IsDisabledControlJustReleased(0, 177) then
            break
        end
    end

    Base.Camera.destroy("staticcam")
end, false)
