local GRID_VOICE_TARGET = 2
local spectatingVoiceOverride = false
local isMuted = false

Voice = {
    chunkID              = 0,

    showRangeCircle      = true,
    showChatMessage      = false,
    showNotification     = true,
    circleDisplayTime    = 450,
    microphone           = false,

    ranges               = {
        Whisper = 2.5,
        Normal  = 4.0,
        Shout   = 8.5,
        Phone   = 0.0,
    },

    itemRanges           = {
        microphone_handheld = 25.0,
        microphone_clipon = 25.0,
        microphone_megaphone = 30.0,
        microphone_megaphone_staff = 200.0,
    },

    voiceRangeIdentifier = "Normal",
    voiceRange           = 6.0,
    forcedRange          = false,
    showCircle           = false,
    shoutRangeOverride   = false,

    playerVolumes        = {},
    forcedVolumes        = {}
}

if GetConvar("voice_useNativeAudio", "false") == "true" then
    print("Native Audio Enabled: Changing voice ranges")
    Voice.ranges = {
        Whisper = 1.25,
        Normal  = 3.5,
        Shout   = 6.0,
        Phone   = 0.0,
    }
end

-- Talker distance / range
Voice.setTalkerDistance = function(range)
    if Voice.forcedRange then
        MumbleSetAudioInputDistance(Voice.forcedRange)
    else
        -- Preset voice ranges
        if type(range) == "number" then
            MumbleSetAudioInputDistance(range)
            Voice.voiceRange = range
        elseif Voice.ranges[range] then
            Voice.alertVoiceRangeChange(range, Voice.ranges[range])
            MumbleSetAudioInputDistance(Voice.ranges[range])
            Voice.voiceRange = Voice.ranges[range]
        end

        -- Used to amplify when the player is shouting
        if (range == "Shout" or range == Voice.ranges["Shout"]) and Voice.shoutRangeOverride then
            MumbleSetAudioInputDistance(Voice.shoutRangeOverride)
        end
    end
end

Voice.forcedTalkerDistance = function(range)
    if range == -1 then
        Voice.forcedRange = false
        Voice.setTalkerDistance(Voice.voiceRange)
    else
        Voice.forcedRange = range
        Voice.setTalkerDistance(range)
    end
end

Voice.alertVoiceRangeChange = function(label, distance)
    local diameter = distance * 2

    if Voice.showChatMessage then
        TriggerEvent('chat:addMessage', "Voice Range", { 0, 124, 35 }, "Voice range set to " .. label)
    end

    if Voice.showRangeCircle then
        Voice.showCircle = true
        local playerPed  = PlayerPedId()

        Citizen.CreateThread(function()
            Wait(Voice.circleDisplayTime)
            Voice.showCircle = false
        end)

        Citizen.CreateThread(function()
            while Voice.showCircle do
                Wait(0)
                local coords = GetEntityCoords(playerPed)
                DrawMarker(1, coords.x, coords.y, coords.z - 0.95, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, diameter, diameter, 0.25, 0, 124, 35, 50, false, false, 2, true,
                    false, false, false)
            end
        end)
    end

    if Voice.showNotification then
        TriggerEvent("fdg_ui:SendNotification", "Voice range set to " .. label, { layout = "top-right", timeout = 500 })
    end
end

-- Forced volume changing
Voice.setPlayerVolume = function(source, volume)
    if volume < 0 then
        Voice.playerVolumes[source] = nil
        if not Voice.forcedVolumes[source] then MumbleSetVolumeOverrideByServerId(source, -1.0) end
    else
        Voice.playerVolumes[source] = volume
    end

    Voice.enforceVolumes()
end

Voice.forcePlayerVolume = function(source, volume)
    if volume < 0 then
        Voice.forcedVolumes[source] = nil
        MumbleSetVolumeOverrideByServerId(source, -1.0)
    else
        Voice.forcedVolumes[source] = volume
    end

    Voice.enforceVolumes()
end

Voice.enforceVolumes = function()
    for source, volume in pairs(Voice.playerVolumes) do
        if not Voice.forcedVolumes[source] then
            MumbleSetVolumeOverrideByServerId(source, volume)
        end
    end

    for source, volume in pairs(Voice.forcedVolumes) do
        MumbleSetVolumeOverrideByServerId(source, volume)
    end
end

Voice.resetAllVolumes = function()
    Voice.resetForcedVolumes()
    Voice.resetPlayerVolumes()
end

Voice.resetPlayerVolumes = function()
    for source, volume in pairs(Voice.playerVolumes) do
        if not Voice.forcedVolumes[source] then MumbleSetVolumeOverrideByServerId(source, -1.0) end
    end

    Voice.playerVolumes = {}
end

Voice.resetForcedVolumes = function()
    for source, volume in pairs(Voice.forcedVolumes) do
        MumbleSetVolumeOverrideByServerId(source, -1.0)
    end

    Voice.forcedVolumes = {}
end

Voice.applyVoiceTargets = function(targets)
    MumbleClearVoiceTargetPlayers(GRID_VOICE_TARGET)
    vlog("Clearing Voice Targets...")


    if targets and #targets > 0 then
        vlog("Applying Voice Targets: " .. json.encode(targets))
        for i, source in ipairs(targets) do
            vlog(i, source, GetPlayerFromServerId(source), GetPlayerName(GetPlayerFromServerId(source)))
            MumbleAddVoiceTargetPlayerByServerId(GRID_VOICE_TARGET, source)
        end
    end
end

-- Disables voice activation
AddStateBagChangeHandler("forceMute", nil, function(bagName, key, value, _unused, replicated)
    if GetPlayerFromStateBagName(bagName) == PlayerId() then
        isMuted = value
        if not isMuted then
            Voice.forcedTalkerDistance(-1)
            return
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Wait(0)
        if isMuted then
            Voice.forcedTalkerDistance(0.1)
        end
    end
end)

-- Initial proximity / channel setup
-- DO NOT MAKE THIS ON BASE READY
Citizen.CreateThread(function()
    MumbleSetAudioInputDistance(Voice.voiceRange)

    -- Reset voice target
    MumbleClearVoiceTarget(GRID_VOICE_TARGET)
    MumbleSetVoiceTarget(GRID_VOICE_TARGET)
end)

Citizen.CreateThread(function()
    local connected = false
    while true do
        if not MumbleIsConnected() then
            connected = false
            print("Lost connection to voice server...")
            Wait(1000)
        elseif not connected then
            connected = true
            print("Reconnected to voice.")
            Wait(1000)

            -- Reset voice target
            MumbleClearVoiceTarget(GRID_VOICE_TARGET)
            MumbleSetVoiceTarget(GRID_VOICE_TARGET)
            MumbleSetAudioInputDistance(Voice.voiceRange)
            print("Set voice targets")
        end

        Wait(1000)
    end
end)

-- Controls
onBaseReady(function()
    Base.Controls.register("grave", "Voice (Adjust Range)", function()
        if Voice.voiceRange == Voice.ranges["Whisper"] then
            Voice.setTalkerDistance("Normal")
            TriggerEvent("setRange", 1)
        elseif Voice.voiceRange == Voice.ranges["Normal"] then
            Voice.setTalkerDistance("Shout")
            TriggerEvent("setRange", 2)
        elseif Voice.voiceRange == Voice.ranges["Shout"] then
            Voice.setTalkerDistance("Whisper")
            TriggerEvent("setRange", 3)
        end
    end)
end)


RegisterNetEvent("core_voice:setVoiceGrid")
AddEventHandler("core_voice:setVoiceGrid", function(chunkID)
    spectatingVoiceOverride = true
    NetworkSetVoiceChannel(chunkID)
    vlog("Switching to Channel: " .. chunkID)
    Voice.chunkID = chunkID

    local coords = GetEntityCoords(PlayerPedId())

    local nearbyChunks = Base.Worldgrid.getNearbyChunks(coords, Voice.voiceRange + 8.5)
    MumbleClearVoiceTargetChannels(GRID_VOICE_TARGET)
    for i, channel in ipairs(nearbyChunks) do
        MumbleAddVoiceTargetChannel(GRID_VOICE_TARGET, channel)
    end
end)

RegisterNetEvent("core_voice:cancelSpectateOverride")
AddEventHandler("core_voice:cancelSpectateOverride", function()
    if spectatingVoiceOverride then
        spectatingVoiceOverride = false
    end
end)

local peopleInCar = {}
-- Handle who we should listen
onBaseReady(function()
    while true do
        Wait(50)

        -- -- Unrestrict all people at max volume
        -- for sid, _ in pairs(peopleInCar) do
        --     Voice.setPlayerVolume(sid, -1.0)
        -- end
        -- peopleInCar = {}

        -- -- Loop through players in car, and override volume levels
        -- local playerPed = PlayerPedId()
        -- local vehicle = GetVehiclePedIsUsing(playerPed)
        -- if DoesEntityExist(vehicle) then
        --     local maxSeats = GetVehicleModelNumberOfSeats(GetEntityModel(vehicle))
        --     for seat=-1, maxSeats-2 do
        --         local ped = GetPedInVehicleSeat(vehicle, seat)

        --         if DoesEntityExist(ped) and ped ~= playerPed then
        --             local sid = GetPlayerServerId(NetworkGetPlayerIndexFromPed(ped))
        --             peopleInCar[sid] = true
        --             Voice.setPlayerVolume(sid, 1.0)
        --         end
        --     end
        -- end

        -- Set voice channel to the current chunk
        if not (spectatingVoiceOverride) then
            local coords = GetFinalRenderedCamCoord()

            local instance = LocalPlayer.state.instanceID

            if instance then
                if instance ~= Voice.chunkID then
                    Voice.chunkID = instance
                    NetworkSetVoiceChannel(instance)
                    vlog("Switching to Channel: " .. instance)
                end
                MumbleClearVoiceTargetChannels(GRID_VOICE_TARGET)
                MumbleAddVoiceTargetChannel(GRID_VOICE_TARGET, instance)
            else
                local chunkID = Base.Worldgrid.getCurrentChunk(coords)
                if chunkID ~= Voice.chunkID then
                    if chunkID == 0 then
                        NetworkClearVoiceChannel()
                        vlog("Switching to Channel: Global")
                    else
                        NetworkSetVoiceChannel(chunkID)
                        vlog("Switching to Channel: " .. chunkID)
                        Voice.chunkID = chunkID
                    end
                end

                -- Broadcast to chunks within range of current voice distance
                -- TODO: Add some sort of check to avoid adding the voice targets if nothing changed?
                local nearbyChunks = Base.Worldgrid.getNearbyChunks(coords, Voice.voiceRange + 5.0)
                MumbleClearVoiceTargetChannels(GRID_VOICE_TARGET)
                for i, channel in ipairs(nearbyChunks) do
                    -- vlog("Sending to channel: " .. channel)
                    MumbleAddVoiceTargetChannel(GRID_VOICE_TARGET, channel)
                end
            end
        end
    end
end)

local propId

RegisterNetEvent("voice:disableMegaphone")
AddEventHandler("voice:disableMegaphone", function()
    if Voice.forcedRange then
        Voice.forcedTalkerDistance(-1)
        Voice.setTalkerDistance(Voice.voiceRange)
        TriggerServerEvent("voice:toggleMicEffect", false)
        Base.Notification("Microphone Disabled.")
        Base.Objects.Delete(propId)
        Base.Animation.Stop(PlayerPedId(), "veh@driveby@first_person@passenger_left_handed@1h", "intro_0")
    end
end)

RegisterNetEvent("voice:setMicrophone")
AddEventHandler("voice:setMicrophone", function(micType)
    if micType and Voice.itemRanges[micType] then
        local range = Voice.itemRanges[micType]
        Voice.forcedTalkerDistance(range)
        Base.Notification("Microphone Enabled.")
        TriggerServerEvent("voice:toggleMicEffect", true)

        local pos = GetEntityCoords(PlayerPedId())
        local attachedSettings = { target = GetPlayerServerId(PlayerId()), bone = 60309, x = 0.04, y = 0.01, z = 0.0, xr = -90.0, yr = 0.0, zr = 0.0, p9 = true, useSoftPinning = true, collision = false, isPed = true, vertexIndex = 1, fixedRot = true }
        propId = Base.Objects.Create("prop_megaphone_01", pos.x, pos.y, pos.z, 0.0, "", false, attachedSettings, true)

        Citizen.CreateThread(function()
            while Voice.forcedRange do
                Wait(0)
                AddTextEntry("MICROPHONE_ACTIVE", "Press ~INPUT_PICKUP~ to stop using the microphone.")
                BeginTextCommandDisplayHelp("MICROPHONE_ACTIVE")
                EndTextCommandDisplayHelp(false, false, false, -1)

                if (not IsEntityPlayingAnim(PlayerPedId(), "veh@driveby@first_person@passenger_left_handed@1h", "intro_0", 3)) then
                    Base.Animation.Start(PlayerPedId(), "veh@driveby@first_person@passenger_left_handed@1h", "intro_0", false, false, false, -8.0, 50)
                end

                -- Toggle radio button
                if IsControlJustReleased(1, 38) then
                    TriggerEvent("voice:disableMegaphone")
                end
            end
        end)
    else
        Voice.forcedTalkerDistance(-1)
        Voice.setTalkerDistance(Voice.voiceRange)
    end
end)

RegisterNetEvent("voice:toggleMicEffect", function(target, toggle)
    if (toggle) then
        MumbleSetSubmixForServerId(target, Voice.Radio.radioSubmix)
    else
        MumbleSetSubmixForServerId(target, -1)
    end
end)

function GetVoice()
    return Voice
end

function GetGridChunk(x)
    return math.floor((x + 8192) / 128)
end

function GetGridBase(x)
    return (x * 128) - 8192
end

-- Microphone Props
local MICROPHONE_PROPS = {
    [`v_ilev_fos_mic`] = true
}
local nearbyMics = {}
local microphoneEnabled = false

-- Slow running loop to find nearby microphones
CreateThread(function()
    while true do
        Wait(0)
        local nearbyObjs = {}

        for obj in EnumerateObjects() do
            if MICROPHONE_PROPS[GetEntityModel(obj)] then
                table.insert(nearbyObjs, obj)
            end
            Wait(0)
        end

        nearbyMics = nearbyObjs
    end
end)

local STAGE_ZONES = {
    {
        name = "courtroom_mainstage",
        coords = {
            vector2(-525.01983642578, -193.29835510254),
            vector2(-515.50225830078, -187.68644714355),
            vector2(-519.31634521484, -180.88201904297),
            vector2(-528.84307861328, -186.67134094238)
        },
        details = {
            label = "Courtroom",
            minZ = 35.0,
            maxZ = 41.0,
            debugGrid = false,
            gridDivisions = 25,
        }
    },
}

CreateThread(function()
    Wait(1000)
    for _, zone in ipairs(STAGE_ZONES) do
        local pz = PolyZone:Create(zone.coords, zone.details)

        pz:onPlayerInOut(function(isPointInside)
            if isPointInside then
                print("Nearby microphone zone - adjusting voice range")
                Base.Notification("You are in a stage area. Shouting distance increased.")
                Voice.shoutRangeOverride = 100.0
                Voice.setTalkerDistance(Voice.voiceRange)
            else
                print("No nearby microphone zone - resetting voice range")
                Base.Notification("You have left the stage area. Shouting distance reduced.")
                Voice.shoutRangeOverride = false
                Voice.setTalkerDistance(Voice.voiceRange)
            end
        end)
    end
end)



-- Distance check for nearby microphones and adjust voice range
CreateThread(function()
    while true do
        Wait(0)
        local playerPos = GetEntityCoords(PlayerPedId())
        local nearMic = false

        DrawLightWithRange()

        for i, obj in ipairs(nearbyMics) do
            local objPos = GetEntityCoords(obj)
            local dist = #(playerPos - objPos)

            if dist < 2.0 then
                nearMic = true
                if not microphoneEnabled then
                    print("Nearby microphone - adjusting voice range")
                    Voice.forcedTalkerDistance(200.0)
                    microphoneEnabled = true
                end
            end
        end

        if not nearMic and microphoneEnabled then
            print("No nearby microphone - resetting voice range")
            Voice.forcedTalkerDistance(-1)
            microphoneEnabled = false
        end
    end
end)
