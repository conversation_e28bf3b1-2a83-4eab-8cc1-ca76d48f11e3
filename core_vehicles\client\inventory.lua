local interactingVehicle
local bootName
local savedHealth
local checkVehicleBlown = true

onBaseReady(function()
	RegisterClientCallback("vehicles:getActiveBoot", function(cb)
		cb(NetworkGetNetworkIdFromEntity(interactingVehicle))
	end)
end)

RegisterNetEvent("inventory:clientSync")
AddEventHandler('inventory:clientSync', function(name, inv, money, dirty, capacity)
	if string.sub(name, 1, 4) == "boot" then
		exports["core_inventory"]:loadExternalInventory("Boot for " .. GetVehicleNumberPlateText(interactingVehicle), inv,
			money, dirty, capacity,
			function(itemIndex, count)
				TriggerServerEvent("core_vehicles:addBootItem", NetworkGetNetworkIdFromEntity(interactingVehicle),
					itemIndex, count, false)
			end,

			function(itemIndex, count)
				TriggerServerEvent("core_vehicles:removeBootItem", NetworkGetNetworkIdFromEntity(interactingVehicle),
					itemIndex, count, false)
			end,

			function()
				Base.Animation.StopForcedAnim(PlayerPedId())
				TriggerServerEvent("core_vehicles:closeBoot", bootName)
				bootName = nil
				if interactingVehicle then
					TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(interactingVehicle),
						"setDoorState", 5, { false, true })
					interactingVehicle = nil
				end
			end
		)
	elseif (string.sub(name, 1, 8) == "glovebox") then
		exports["core_inventory"]:loadExternalInventory("Glovebox for " .. GetVehicleNumberPlateText(interactingVehicle),
			inv, money, dirty, capacity,
			function(itemIndex, count)
				TriggerServerEvent("core_vehicles:addBootItem", NetworkGetNetworkIdFromEntity(interactingVehicle),
					itemIndex, count, true)
			end,

			function(itemIndex, count)
				TriggerServerEvent("core_vehicles:removeBootItem", NetworkGetNetworkIdFromEntity(interactingVehicle),
					itemIndex, count, true)
			end,

			function()
				TriggerServerEvent("core_vehicles:closeGlovebox", bootName)
				bootName = nil
				interactingVehicle = nil
			end
		)
	end
end)

-- Open the boot menu after receiving server data
RegisterNetEvent("core_vehicles:openBoot")
AddEventHandler("core_vehicles:openBoot", function(vehicle, name)
	interactingVehicle = NetworkGetEntityFromNetworkId(vehicle)
	bootName = name
end)

function isVehicleLocked(vehicle)
	return Entity(vehicle).state.locked or false
end

function isVehicleBootLocked(vehicle)
	return Entity(vehicle).state.bootlocked or false
end

function closeBoot()
	exports["core_inventory"]:hideInventory()
	interactingVehicle = nil
	bootName = nil
	Base.Animation.StopForcedAnim(PlayerPedId())
end

function canPlayerAccessBoot()
	return not isDead and not isCuffed and not isInTrunk
end

function isBootVehicleValid(vehicle)
	return canPlayerAccessBoot() and vehicle and DoesEntityExist(vehicle) and
		#(GetEntityCoords(PlayerPedId()) - GetEntityCoords(vehicle)) < 6.0
end

function isBootValid(vehicle)
	local ped = PlayerPedId()
	return isBootVehicleValid(vehicle) and not IsPedRagdoll(ped) and
		IsEntityPlayingAnim(ped, "amb@prop_human_bum_bin@idle_a", "idle_a", 3) and
		not (GetEntityHealth(ped) < savedHealth) and
		not isVehicleBootLocked(vehicle)
end

function isGloveboxValid(vehicle)
	return isBootVehicleValid(vehicle) and IsPedInVehicle(PlayerPedId(), vehicle, false)
end

function openBoot(veh, glovebox)
	Citizen.CreateThread(function()
		local playerPed = GetPlayerPed(-1)
		local vehicle = veh or Base.Vehicles.GetClosestVehicle(playerPed, 4.5, false)
		local isInVehicle = IsPedInAnyVehicle(GetPlayerPed(-1))

		--Check if the vehicle has been in an MVA
		local state = Entity(vehicle).state
		local MVAstatus = state.MVA
		if MVAstatus then
			return
		end

		Citizen.Wait(50)
		if IsPedRagdoll(playerPed) or IsPedGettingUp(playerPed) then
			return
		end

		if IsPedSwimming(playerPed) then
			return TriggerEvent("fdg_ui:SendNotification", "You cannot access boots while swimming")
		end

		if not DoesEntityExist(vehicle) then
			return print("Vehicle not found!")
		end

		if not glovebox then
			if not canPlayerAccessBoot() or isInVehicle then
				return print("Boot not accessible")
			end

			if isVehicleBootLocked(vehicle) then
				return TriggerEvent("fdg_ui:SendNotification", "This boot is locked")
			end

			savedHealth = GetEntityHealth(playerPed)
			Base.Animation.StartForcedAnim(playerPed, "amb@prop_human_bum_bin@idle_a", "idle_a", true, true, true)
			Wait(100)

			if exports["main_phone_2"]:isPhoneOpen() then
				exports["main_phone_2"]:PhonePlayAnim("text")
			end

			exports["main_progressbar"]:start(3000, "Opening boot...",
				function()
					if isVehicleBootLocked(vehicle) then
						TriggerEvent("fdg_ui:SendNotification", "This boot is locked")
						Base.Animation.StopForcedAnim(PlayerPedId())
						return
					end
					if (GetVehicleBodyHealth(vehicle) == 0.0 or IsEntityOnFire(vehicle)) and checkVehicleBlown then
						TriggerEvent("fdg_ui:SendNotification", "This car had been blown up")
						Base.Animation.StopForcedAnim(PlayerPedId())
						return
					end
					TriggerServerEvent("core_vehicles:openBoot", NetworkGetNetworkIdFromEntity(vehicle))
					TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(vehicle),
						"setDoorState", 5, { true, true })
					while isBootValid(vehicle) do
						Wait(500)
					end
					closeBoot()
				end,
				closeBoot,
				function()
					return isBootValid(vehicle)
				end
			)
		else
			if not canPlayerAccessBoot() or not isInVehicle then
				return print("Boot not accessible")
			end

			TriggerServerEvent("core_vehicles:openGlovebox", NetworkGetNetworkIdFromEntity(vehicle))

			Wait(1000)

			while isGloveboxValid(vehicle) and interactingVehicle do
				Wait(500)
			end
			closeBoot()
		end
	end)
end

RegisterCommand("boot", function(source, args)
	openBoot()
end)

RegisterNetEvent("core_vehicles:checkVehicleBlown")
AddEventHandler("core_vehicles:checkVehicleBlown", function(state)
	checkVehicleBlown = state
end)