-- This enables specific weapons to switch between semi auto, burst and full auto

local FIREMODE = {
    semi = 1,
    burst = 2,
    auto = 3,
}

local WEAPONS = {
    -- [`weapon_name`] = {
    --     semi = 100 (delay in ms between shots),
    --     burst = 350 (burst time),
    --     auto = true or false
    -- }

    [`weapon_glock`] = {
        semi = 300,
        burst = false,
        auto = true,
    },

    [`weapon_raptorglock`] = {
        semi = 300,
        burst = false,
        auto = true,
    },

    [`weapon_specialcarbine_mk2`] = {
        semi = 200,
        burst = 250,
        auto = true,
    },

    [`weapon_sig516`] = {
        semi = 300,
        burst = 300,
    },

    [`weapon_hk416`] = {
        semi = 300,
        burst = 300,
    },

    [`weapon_bullpuprifle`] = {
        semi = 200,
        burst = 250,
        auto = true,
    },

    [`weapon_militaryrifle`] = {
        semi = 200,
        burst = 250,
        auto = true,
    },

    [`weapon_m6ic`] = {
        semi = 200,
        burst = 350,
        auto = true,
    },

    [`weapon_heavyrifle`] = {
        semi = 250,
        auto = true,
    },

    [`weapon_ar15`] = {
        semi = 200,
        burst = 250,
        auto = true,
    },

    [`weapon_carbinerifle_mk2`] = {
        semi = 350,
        burst = 250,
        auto = true,
    },

    [`weapon_mk47`] = {
        semi = 300,
        burst = 300,
        auto = true,
    },

    [`weapon_carbinerifle`] = { semi = 250 },
    [`weapon_ak971`] = { semi = 250 },
    [`weapon_grenadelauncher_bz`] = { semi = 2000 },
    [`weapon_grenadelauncher_smoke`] = { semi = 2000 },
}

local function getWeaponType(weapon)
    local weaponType = WEAPONS[weapon]

    if not weaponType then
        return false
    end

    return weaponType
end

local fireMode = FIREMODE.semi

-- Fire mode swapping
RegisterCommand("changefiremode", function()
    local weapon = GetSelectedPedWeapon(PlayerPedId())
    local weaponType = getWeaponType(weapon)

    if not weaponType then return end

    if fireMode == FIREMODE.semi then
        if weaponType.burst then
            fireMode = FIREMODE.burst
            Base.Notification("Fire Mode: Burst")
        elseif weaponType.auto then
            fireMode = FIREMODE.auto
            Base.Notification("Fire Mode: Full-Auto")
        end
    elseif fireMode == FIREMODE.burst then
        if weaponType.auto then
            fireMode = FIREMODE.auto
            Base.Notification("Fire Mode: Full-Auto")
        elseif weaponType.semi then
            fireMode = FIREMODE.semi
            Base.Notification("Fire Mode: Semi-Auto")
        end
    elseif fireMode == FIREMODE.auto then
        if weaponType.semi then
            fireMode = FIREMODE.semi
            Base.Notification("Fire Mode: Semi-Auto")
        elseif weaponType.burst then
            fireMode = FIREMODE.burst
            Base.Notification("Fire Mode: Burst")
        end
    end

    PlaySoundFrontend(-1, "Faster_Click", "RESPAWN_ONLINE_SOUNDSET")
    weaponType.fireMode = fireMode
end, false)
RegisterKeyMapping('changefiremode', 'Firemode', 'keyboard', 'h')

-- Fire mode enforcement
local nextShot = 0
local lastWeapon = 0
local disableSemi = false

onBaseReady(function()
    while true do
        Wait(0)
        local weapon = GetSelectedPedWeapon(PlayerPedId())
        local weaponType = getWeaponType(weapon)

        -- Restore the fire rate on the weapon when it's equipped
        if weaponType and lastWeapon ~= weapon then
            lastWeapon = weapon
            if weaponType.fireMode then
                fireMode = weaponType.fireMode

                if fireMode == FIREMODE.semi then
                    Base.Notification("Fire Mode: Semi-Auto")
                elseif fireMode == FIREMODE.burst then
                    Base.Notification("Fire Mode: Burst")
                elseif fireMode == FIREMODE.auto then
                    Base.Notification("Fire Mode: Full-Auto")
                end
            else
                fireMode = FIREMODE.semi
                Base.Notification("Fire Mode: Semi-Auto")
            end
        end
        
        -- Disable the player from firing too fast in semi and burst modes
        if weaponType then
            if nextShot > GetGameTimer() or disableSemi then
                if  disableSemi and IsControlReleased(0, 24) and IsControlReleased(0, 92) and IsControlReleased(0, 257) and IsControlReleased(0, 69) and IsDisabledControlReleased(0, 24) and IsDisabledControlReleased(0, 92) and IsDisabledControlReleased(0, 257) and IsDisabledControlReleased(0, 69) then
                    disableSemi = false
                else
                    DisableControlAction(0, 24, true)
                    DisableControlAction(0, 69, true)
                    DisableControlAction(0, 92, true)
                    DisableControlAction(0, 257, true)
                end
                
                
            elseif IsControlPressed(0, 24) or IsControlPressed(0, 92) or IsControlPressed(0, 257) or IsControlPressed(0, 69) then
                if fireMode == FIREMODE.semi then
                    disableSemi = true
                    nextShot = GetGameTimer() + weaponType.semi
                    
                elseif fireMode == FIREMODE.burst then
                    Wait(weaponType.burst)
                    nextShot = GetGameTimer() + 350
                end
            end
        else
            lastWeapon = 0
        end
    end
end)
