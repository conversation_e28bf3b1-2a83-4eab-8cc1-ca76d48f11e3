@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

#hotbar {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 0.5vh;
  height: 7vh;
  max-height: 7vh;

  display: flex;
  flex-direction: row;
}

.hotbarslot {
  position: relative;
  height: 7vh;
  width: 7vh;
  /* padding: 5px; */
  /* margin: 2px; */  
  font-family: 'Inter', sans-serif;
  font-size: 12;
  border: 0.1vh solid black;
  /* border-radius: 5px; */
  background-color: rgba(10, 10, 10, 0.5);
  color: white;
  z-index: -2;
  vertical-align: top;

  margin-left: 2px;
  margin-right: 1px;
}

.hotbarslot:hover {
  background: linear-gradient(to top, rgba(79, 119, 156, 0.678), rgba(10, 10, 10, 0.805));
}

.hotbarItem {
  z-index: 900;
}

.hotbarslot div {
  overflow-x: visible;
}

.hotbarslot.selected {
  background-color: rgba(100, 100, 100, 0.6);
}

.hotbarslot img {
  position: relative;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  max-width: 6vh;
  max-height: 6vh;
}

.hotbarslot.selected img {
  opacity: 100%;
}

.slotNumber {
  font-family: 'Inter', sans-serif;
  position: absolute;
  right: 4px;
  color: white;
  font-size: 1.0vh;
  z-index: 1000;
}

#hotbar-visibility-container {
  position: absolute;
  bottom: 2.5vh;
  right: 50vh;
  height: 3vh;
  width: 3vh;
  background-color: rgba(10, 10, 10, 0.6);
  border-radius: 50%;
}

#hotbar-visibility {
  height: auto;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  max-width: 20px;
  max-height: 20px;
  vertical-align: middle;
}

#hotbarClearZone {
  display: none;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 100;
  height: 150px;
  line-height: 150px;
  background-color: rgba(150, 0, 0, 0.15);
  border: 0px;
  width: 645px;
  text-align: center;
  font-family: 'Inter', sans-serif;
  color: rgba(255, 255, 255, 0.6);
  font-size: 36;
}
