local sounds = {}
local currentSiren = 1

RequestScriptAudioBank("DLC_WMSIRENS\\SIRENPACK_ONE", false)

AddEventHandler("vehicleDriverTick", function(veh)
    -- If emergency vehicle, disabled sirens and controls
    if (GetVehicleClass(veh) == 18) then
        DisableControlAction(0, 85, true) -- INPUT_VEH_RADIO_WHEEL (Q & DPAD LEFT)
        DisableControlAction(0, 19, true) -- INPUT_CHARACTER_WHEEL (LALT)
        DisableControlAction(0, 80, true) -- INPUT_VEH_CIN_CAM
        -- DisableControlAction(0, 86, true) -- INPUT_VEH_HORN
        DisableControlAction(0, 84, true) -- INPUT_VEH_PREV_RADIO_TRACK
        DisableControlAction(0, 83, true) -- INPUT_VEH_NEXT_RADIO_TRACK
        SetVehicleRadioEnabled(veh, false)
        SetVehRadioStation(veh, "OFF")
    end
end)

function CanPlayerControlSirens(veh)
    return GetPedInVehicleSeat(veh, -1) == PlayerPedId()
end

-- Required for nos
function registerVehicleDecor(name, type)
    if (not DecorIsRegisteredAsType(name, type)) then
        DecorRegister(name, type)
    end
end

function GetVehicleSirenId(veh)
    local state = Entity(veh).state
    if (state.job == nil) then
        return false
    end

    if (state.job == "police" or state.job == "hwbranch" or state.job == "tac" or state.job == "sbranch" or state.job == "dogsquad" or state.job == "ds" or state.job == "sars" or state.job == "homicide") then
        return "VEHICLES_HORNS_SIREN_1", "VEHICLES_HORNS_SIREN_2", "VEHICLES_HORNS_POLICE_WARNING", 0
    end

    if (state.job == "afp") then
        return "SIREN_ALPHA", "SIREN_BRAVO", false, "DLC_WMSIRENS_SOUNDSET"
    end

    if (state.job == "lses") then
        return "RESIDENT_VEHICLES_SIREN_FIRETRUCK_WAIL_01", "RESIDENT_VEHICLES_SIREN_FIRETRUCK_QUICK_01", "VEHICLES_HORNS_AMBULANCE_WARNING", 0
    end

    return false
end

onBaseReady(function()
    -- IF LIGHTS ON THEN POLICE HORN AND TOGGLE SIRENS ELSE JUST POLICE HORN
    Base.Controls.register("E", "Police Horn",
        function() -- Start pressing E
            if (not isDead) and (IsPedInAnyVehicle(PlayerPedId(), false)) then
                local veh = GetVehiclePedIsIn(PlayerPedId(), false)
                local ent = Entity(veh)

                if not CanPlayerControlSirens(veh) then return end
                if not GetVehicleSirenId(veh) then return end

                --if (GetVehicleClass(veh) == 18) then
                DisableControlAction(0, 86, true)
                if (ent.state.emergencySirens == 1) then
                    TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencySirens', 2)
                    TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencyHorn', 1)
                    currentSiren = 2
                elseif (ent.state.emergencySirens == 2) then
                    TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencySirens', 1)
                    TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencyHorn', 1)
                    currentSiren = 1
                elseif (ent.state.emergencyLights == true) then
                    TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencyHorn', 1)
                elseif (ent.state.emergencySirens == 3) then
                    TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencyHorn', 1)
                end
                --end
            end
        end,

        function() -- Releasing E
            if (not isDead) then
                if (IsPedInAnyVehicle(PlayerPedId(), false)) then
                    local veh = GetVehiclePedIsIn(PlayerPedId(), false)
                    local ent = Entity(veh)

                    if not CanPlayerControlSirens(veh) then return end
                    if not GetVehicleSirenId(veh) then return end

                    DisableControlAction(0, 86, false)
                    --if (GetVehicleClass(veh) == 18) then
                    TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencyHorn', false)
                    --end
                end
            end
        end,

        function() -- While E
            local veh = GetVehiclePedIsIn(PlayerPedId(), false)
            local ent = Entity(veh)

            if ent.state.emergencyHorn == 1 or ent.state.emergencyLights == true then
                DisableControlAction(0, 86, false)
                DisableControlAction(0, 86, true)
            end
        end
    )

    -- EMERGENCY LIGHTS
    Base.Controls.register("Q", "Emergency Lights", function()
        if (not isDead) then
            if (IsPedInAnyVehicle(PlayerPedId(), false)) then
                local veh = GetVehiclePedIsIn(PlayerPedId(), false)
                local ent = Entity(veh)

                if not CanPlayerControlSirens(veh) then return end
                if not GetVehicleSirenId(veh) then return end

                if (ent.state.emergencyLights == true) then
                    PlaySoundFrontend(-1, "NAV_LEFT_RIGHT", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
                    TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencyLights', false)
                    TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencySirens', false)
                    currentSiren = 1
                else
                    PlaySoundFrontend(-1, "NAV_UP_DOWN", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
                    TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencyLights', true)
                end
            end
        end
    end)

    -- TOGGLE BETWEEN DIFFERENT TYPES OF SIRENS
    Base.Controls.register("R", "Siren Whoop / Siren Mode",
        function()
            if (not isDead) then
                if (IsPedInAnyVehicle(PlayerPedId(), false)) then
                    local veh = GetVehiclePedIsIn(PlayerPedId(), false)
                    local ent = Entity(veh)

                    if not CanPlayerControlSirens(veh) then return end
                    if not GetVehicleSirenId(veh) then return end

                    --if (GetVehicleClass(veh) == 18) then
                    if (ent.state.emergencyLights == true) then
                        if currentSiren == 1 then
                            TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencySirens', 2)
                        elseif currentSiren == 2 then
                            TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencySirens', 3)
                        elseif currentSiren == 3 then
                            TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencySirens', 1)
                        end

                        currentSiren = currentSiren + 1
                        if currentSiren > 3 then
                            currentSiren = 1
                        end

                        PlaySoundFrontend(-1, "NAV_LEFT_RIGHT", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1) -- on
                    end
                    --end
                end
            end
        end)

    -- EMERGENCY SIRENS
    Base.Controls.register("z", "Emergency Sirens", function()
        if (not isDead) then
            if (IsPedInAnyVehicle(PlayerPedId(), false)) then
                local veh = GetVehiclePedIsIn(PlayerPedId(), false)
                local ent = Entity(veh)

                if not CanPlayerControlSirens(veh) then return end
                if not GetVehicleSirenId(veh) then return end

                --if (GetVehicleClass(veh) == 18) then
                if (ent.state.emergencyLights == true) then
                    if (sounds[tostring(veh) .. "es"]) then
                        PlaySoundFrontend(-1, "NAV_UP_DOWN", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
                        TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencySirens', false)
                        currentSiren = 1
                    else
                        PlaySoundFrontend(-1, "NAV_LEFT_RIGHT", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1)
                        TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencySirens', 1)
                    end
                end
                --end
            end
        end
    end)
end)

-- Cleanup for SoundIDs and to stop memory leaks
Citizen.CreateThread(function()
    while (true) do
        DistantCopCarSirens(false)
        for k, v in pairs(sounds) do
            Wait(0)
            local cleanedStr = k:gsub("%D", "")
            local cleanedNumber = tonumber(cleanedStr)
            if not DoesEntityExist(cleanedNumber) then
                ReleaseSoundId(v)
                sounds[k] = nil
            end
        end
        Wait(500)
    end
end)

-- Debug command to print number of active sounds (remove in futre)
RegisterCommand("debugsounds", function(source, args)
    for k, v in pairs(sounds) do
        print(k, v)
    end
end)

---------------------------------
-- Vehicle Radial Menu Options --
---------------------------------
AddVehicleInteractionAction(function() -- Condition
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
        return (IsPedInAnyVehicle(PlayerPedId(), false) and (GetVehicleClass(veh) == 18 or GetVehicleClass(veh) == 17))
    end,

    function() -- Action
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
        if (GetPedInVehicleSeat(veh, -1) ~= PlayerPedId()) then
            return
        end

        if not IsVehicleSirenOn(veh) then
            PlaySoundFrontend(-1, "NAV_LEFT_RIGHT", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1) -- on
            TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencyLights', true)
            TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencySirens', 1)
        else
            PlaySoundFrontend(-1, "NAV_LEFT_RIGHT", "HUD_FRONTEND_DEFAULT_SOUNDSET", 1) -- on
            TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencyLights', false)
            TriggerServerEvent("core_vehicles:setEmergencyVehState", NetworkGetNetworkIdFromEntity(veh), 'emergencySirens', false)
        end
    end,

    { text = "Toggle Sirens", icon = "&#xF0BEA;", action = "toggle_sirens" }, -- Menu Item

    { requires = { Vehicle = true } }                                         -- Requires
)

-- Disable sirens when exiting vehicle
AddEventHandler("vehicleExit", function(lastVehicle)
    if (IsVehicleSirenOn(lastVehicle)) then
        Entity(lastVehicle).state:set("emergencySirens", false, true)
    end
end)

----
-- Neon Lights
DecorRegister("areNeonsOn", 2)

local function HasNeonsInstalled(veh)
    for i = 0, 3 do
        if IsVehicleNeonLightEnabled(veh, i) then
            return true
        end
    end
    return false
end

AddVehicleInteractionAction( -- Disable Neons
    function()               -- Condition
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
        local ent = Entity(veh).state

        return HasNeonsInstalled(veh) and ent.neons
    end,

    function() -- Action
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
        local ent = Entity(veh).state

        if (GetPedInVehicleSeat(veh, -1) ~= PlayerPedId()) then
            return
        end

        DisableVehicleNeonLights(veh, true)
        ent:set('neons', false, true)
    end,

    { text = "Disable Neon Lights", icon = "&#xF0BEA;", action = "disable_neons" }, -- Menu Item

    { requires = { Vehicle = true } }                                               -- Requires
)

AddVehicleInteractionAction( -- Enable Neons
    function()               -- Condition
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
        local ent = Entity(veh).state

        return HasNeonsInstalled(veh) and not ent.neons
    end,

    function() -- Action
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
        local ent = Entity(veh).state

        if (GetPedInVehicleSeat(veh, -1) ~= PlayerPedId()) then
            return
        end

        DisableVehicleNeonLights(veh, false)
        ent:set('neons', true, true)
    end,

    { text = "Enable Neon Lights", icon = "&#xF0BEA;", action = "enable_neons" }, -- Menu Item

    { requires = { Vehicle = true } }                                             -- Requires
)

-----------------------
-- Statebag Handlers --
-----------------------
AddStateBagChangeHandler("emergencyLights", "", function(bagName, key, value)
    local entNet = tonumber(bagName:gsub('entity:', ''), 10)

    Wait(0)
    if not NetworkDoesEntityExistWithNetworkId(entNet) then
        Wait(100)
        if not NetworkDoesEntityExistWithNetworkId(entNet) then return end
    end

    if not NetworkDoesEntityExistWithNetworkId(entNet) then return end

    local vehicle = NetworkGetEntityFromNetworkId(entNet)

    --if (GetVehicleClass(vehicle) == 18) or (GetEntityModel(vehicle) == `dotspeedo`) then
    if GetVehicleSirenId(vehicle) then
        SetVehicleHasMutedSirens(vehicle, true)
        SetVehicleSiren(vehicle, value)
    end

    if value == false then
        if sounds[tostring(vehicle) .. "eh"] then
            StopSound(sounds[tostring(vehicle) .. "eh"])
            ReleaseSoundId(sounds[tostring(vehicle) .. "eh"])
            sounds[tostring(vehicle) .. "eh"] = nil
        end
    end
end)

AddStateBagChangeHandler("emergencySirens", "", function(bagName, key, value)
    local entNet = tonumber(bagName:gsub('entity:', ''), 10)

    Wait(0)
    if not NetworkDoesEntityExistWithNetworkId(entNet) then
        Wait(100)
        if not NetworkDoesEntityExistWithNetworkId(entNet) then return end
    end

    if not NetworkDoesEntityExistWithNetworkId(entNet) then return end

    local vehicle = NetworkGetEntityFromNetworkId(entNet)
    local baseSiren, secondarySiren, warningSiren, dlcName = GetVehicleSirenId(vehicle)

    local sirenList = {
        [1] = baseSiren,
        [2] = secondarySiren,
        [3] = warningSiren
    }

    if value == false then -- No siren
        if sounds[tostring(vehicle) .. "es"] then
            StopSound(sounds[tostring(vehicle) .. "es"])
            ReleaseSoundId(sounds[tostring(vehicle) .. "es"])
            sounds[tostring(vehicle) .. "es"] = nil
        end
    else -- Play siren
        local sound = sirenList[value]
        if sound then
            if sounds[tostring(vehicle) .. "es"] then
                StopSound(sounds[tostring(vehicle) .. "es"])
                ReleaseSoundId(sounds[tostring(vehicle) .. "es"])
                sounds[tostring(vehicle) .. "es"] = nil
            end
            sounds[tostring(vehicle) .. "es"] = GetSoundId()
            PlaySoundFromEntity(sounds[tostring(vehicle) .. "es"], sound, vehicle, dlcName, 0, 0)
        end
    end
end)

AddStateBagChangeHandler("emergencyHorn", "", function(bagName, key, value)
    local entNet = tonumber(bagName:gsub('entity:', ''), 10)

    Wait(0)
    if not NetworkDoesEntityExistWithNetworkId(entNet) then
        Wait(100)
        if not NetworkDoesEntityExistWithNetworkId(entNet) then return end
    end

    local vehicle = NetworkGetEntityFromNetworkId(entNet)

    if value == false then -- No siren
        if sounds[tostring(vehicle) .. "eh"] then
            StopSound(sounds[tostring(vehicle) .. "eh"])
            ReleaseSoundId(sounds[tostring(vehicle) .. "eh"])
            sounds[tostring(vehicle) .. "eh"] = nil
        end
    elseif value == 1 then -- Play siren
        local sound = "SIRENS_AIRHORN"

        if sounds[tostring(vehicle) .. "eh"] then
            StopSound(sounds[tostring(vehicle) .. "eh"])
            ReleaseSoundId(sounds[tostring(vehicle) .. "eh"])
            sounds[tostring(vehicle) .. "eh"] = nil
        end
        sounds[tostring(vehicle) .. "eh"] = GetSoundId()
        PlaySoundFromEntity(sounds[tostring(vehicle) .. "eh"], sound, vehicle, dlcName, 0, 0)
    end
end)
