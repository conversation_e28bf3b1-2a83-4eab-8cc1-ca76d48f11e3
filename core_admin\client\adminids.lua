overheadIdsActive = false
local overheadIdMacro = false
local gamertagCache = {}
local overhead_id_active = {}
local storedStreamers = {}

local tagStars = {
    ["tmod"]       = 1,
    ["mod"]        = 2,
    ["admin"]      = 3,
    ["superadmin"] = 4
}

local accountOverrides = {
    ["1"] = {
        tagColor = 214,
        starColor = 280,
        stars = 5,
        hideHealth = true,
    },
    ["2"] = {
        tagColor = 214,
        starColor = 280,
        stars = 5,
        hideHealth = true,
    },
    ["50"] = {
        tagColor = 214,
        starColor = 280,
        stars = 5,
        hideHealth = true,
    },
    ["4368"] = {
        tagColor = 214,
        starColor = 280,
        stars = 5,
        hideHealth = true,
    },
    ["3830"] = {
        tagColor = 214,
        starColor = 280,
        stars = 5,
        hideHealth = true,
    },
    ["69"] = {
        tagColor = 214,
        starColor = 280,
        stars = 5,
        hideHealth = true,
    },
    --<PERSON><PERSON> is a managed account in AD, yellow account so he stands out when / if being actioned.
    ["145543"] = {
        tagColor = 12,
        starColor = 12,
        stars = 0,
        hideHealth = false,
    },
    ["9"] = { rainbow = true }
}

RegisterNetEvent("core_admin:overheadToggle", function(target, toggle)
    overhead_id_active[tostring(target)] = toggle
end)

RegisterNetEvent("core_admin:syncOverhead", function(staff_overhead)
    for k, v in pairs(staff_overhead) do
        overhead_id_active[k] = v
    end
end)

RegisterNetEvent("admin:overhead:macro", function(toggle)
    if (group ~= "user") then
        if not toggle and overheadIdMacro then
            TriggerServerEvent("core_admin:overheadToggle", toggle, true)
        elseif toggle then
            TriggerServerEvent("core_admin:overheadToggle", toggle, true)
        end
        overheadIdMacro = toggle
        overheadIdsActive = toggle
    end
end)

function GetPlayerDetails(id)
    for _, v in ipairs(playerList) do
        if (GetPlayerFromServerId(v.source) == id) then
            return v
        end
    end
    return { source = GetPlayerServerId(id), name = "NaN", accountid = 0, group = "user" }
end

local threadRunning = false
local function InvisibleChecker() --Prevent exploit
    if not threadRunning then
        threadRunning = true
        Citizen.CreateThread(function()
            while true do
                if (not overheadIdsActive) or IsEntityVisible(PlayerPedId()) then
                    overheadIdsActive = false
                    TriggerServerEvent("core_admin:overheadToggle", overheadIdsActive)
                    threadRunning = false
                    return
                end
                Wait(5000)
            end
        end)
    end
end

Citizen.CreateThread(function()
    while true do
        Wait(0)
        -- control handler
        DisableControlAction(0, 118, true)
        if (GetLastInputMethod(2) and group ~= "user" and not overheadIdMacro) then
            if (IsDisabledControlJustPressed(0, 118)) then
                if (isSpectating or featurePlayerInvisible) and overheadIdsActive then
                    overheadIdsActive = false
                else
                    overheadIdsActive = true
                    if featurePlayerInvisible then
                        InvisibleChecker()
                    end
                end
                TriggerServerEvent("core_admin:overheadToggle", overheadIdsActive)
            elseif (IsDisabledControlJustReleased(0, 118)) and (not isSpectating) and (not featurePlayerInvisible) then
                overheadIdsActive = false
                TriggerServerEvent("core_admin:overheadToggle", overheadIdsActive)
            end
        end
    end
end)

Citizen.CreateThread(function()
    while (true) do
        Wait(200)

        -- cleans up old tags
        for i, player in ipairs(gamertagCache) do
            if (not NetworkIsPlayerActive(player.id) or not overheadIdsActive) then
                RemoveMpGamerTag(player.gamertag)
                table.remove(gamertagCache, i)
                overhead_id_refresh = false
            end
        end

        if (overheadIdsActive and not streamer and group ~= "user") then
            -- creates new tags
            for _, id in ipairs(GetActivePlayers()) do
                local info = GetPlayerDetails(id)
                local overrides = accountOverrides[tostring(info.accountid)]
                local gamertag = nil

                if overrides and overrides.customName then
                    gamertag = CreateMpGamerTag(GetPlayerPed(id), (info.accountid) .. " - " .. overrides.customName,
                        false, false, "", false)
                elseif storedStreamers[tostring(info.accountid)] then
                    gamertag = CreateMpGamerTag(GetPlayerPed(id), (info.accountid) .. " - " .. GetPlayerName(id) .. "",
                        false, false, "", false)
                else
                    gamertag = CreateMpGamerTag(GetPlayerPed(id), (info.accountid) .. " - " .. GetPlayerName(id), false,
                        false, "", false)
                end

                -- Speaking Icon
                if NetworkIsPlayerTalking(id) then
                    SetMpGamerTagVisibility(gamertag, 9, true)
                else
                    SetMpGamerTagVisibility(gamertag, 9, false)
                end

                -- staff icon
                if overrides then
                    SetMpGamerTagWantedLevel(gamertag, overrides.stars or tagStars[info.group])
                    SetMpGamerTagColour(gamertag, 7, overrides.starColor or 0)
                    SetMpGamerTagVisibility(gamertag, 7, true)
                elseif info.accountid == 6 then
                    SetMpGamerTagWantedLevel(gamertag, 4)
                    SetMpGamerTagColour(gamertag, 7, 90)
                    SetMpGamerTagVisibility(gamertag, 7, true)
                elseif (info.group ~= "user" and info.group ~= nil) then
                    SetMpGamerTagWantedLevel(gamertag, tagStars[info.group])
                    SetMpGamerTagVisibility(gamertag, 7, true)
                else
                    SetMpGamerTagWantedLevel(gamertag, 0)
                    SetMpGamerTagVisibility(gamertag, 7, false)
                end

                -- health
                SetMpGamerTagVisibility(gamertag, 2, true)
                SetMpGamerTagAlpha(gamertag, 2, 255)
                if OverHealthCheck(GetPlayerPed(id)) then
                    SetMpGamerTagHealthBarColour(gamertag, 18)
                else
                    SetMpGamerTagHealthBarColour(gamertag, 6)
                end

                if overrides and group ~= "superadmin" then
                    SetMpGamerTagVisibility(gamertag, 2, not overrides.hideHealth)
                    SetMpGamerTagAlpha(gamertag, 2, 0)
                end

                if overrides and overrides.rainbow then
                    local curTime = GetGameTimer() / 1000
                    local rainbow = {}
                    rainbow.r = math.floor(math.sin(curTime * 0.6 + 0) * 127 + 128)
                    rainbow.g = math.floor(math.sin(curTime * 0.6 + 2) * 127 + 128)
                    rainbow.b = math.floor(math.sin(curTime * 0.6 + 4) * 127 + 128)
                    ReplaceHudColourWithRgba(233, rainbow.r, rainbow.g, rainbow.b, 255)
                end

                if storedStreamers[tostring(info.accountid)] then
                    SetMpGamerTagColour(gamertag, 0, 21)
                    SetMpGamerTagColour(gamertag, 3, 21)
                    SetMpGamerTagColour(gamertag, 7, 21)
                    SetMpGamerTagHealthBarColour(gamertag, 21)
                elseif (info.group ~= "superadmin" or group == "superadmin") and (overhead_id_active[tostring(info.source)]) then
                    SetMpGamerTagColour(gamertag, 0, (overrides and overrides.tagColor) or 6)
                else
                    SetMpGamerTagColour(gamertag, 0, (overrides and overrides.tagColor) or 0)
                end

                -- storage
                local found = false
                for _, v in pairs(gamertagCache) do
                    if (v.id == id) then
                        found = true
                        v.gamertag = gamertag
                    end
                end

                if (not found) then
                    table.insert(gamertagCache, { id = id, gamertag = gamertag })
                end
            end
        end
    end
end)

RegisterNetEvent("admin:updateStreamers")
AddEventHandler("admin:updateStreamers", function(streamers)
    storedStreamers = {}
    for k, streamer in pairs(streamers) do
        storedStreamers[tostring(streamer.accountid)] = streamer
    end
end)

function OverHealthCheck(ped)
    if GetPedMaxHealth(ped) > 200 and (GetEntityHealth(ped) > 200 and GetEntityHealth(ped) < 300) then
        return true
    end
    return false
end
