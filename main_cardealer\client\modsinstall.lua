-- Mechanic Integration
-- Headlight List
CreateThread(function()
    Wait(500)
    for i, zone in ipairs(MECH_ZONES_ILLEGALMODS) do
        local polyzone = PolyZone:Create(zone.coords, zone.details)
        zone.polyzone = polyzone
    end
end)

local headlights = {
    ["headlight_-1"] = { label = "Headlight Kit - Default", id = -1, colourValue = "default" },
    ["headlight_0"] = { label = "Headlight Kit - White", id = 0, colourValue = "white" },
    ["headlight_1"] = { label = "Headlight Kit - Blue", id = 1, colourValue = "blue1" },
    ["headlight_2"] = { label = "Headlight Kit - Electric Blue", id = 2, colourValue = "blue2" },
    ["headlight_3"] = { label = "Headlight Kit - Mint Green", id = 3, colourValue = "green1" },
    ["headlight_4"] = { label = "Headlight Kit - Lime Green", id = 4, colourValue = "green2" },
    ["headlight_5"] = { label = "Headlight Kit - Yellow", id = 5, colourValue = "yellow1" },
    ["headlight_6"] = { label = "Headlight Kit - Golden Shower", id = 6, colourValue = "yellow2" },
    ["headlight_7"] = { label = "Headlight Kit - Orange", id = 7, colourValue = "orange" },
    ["headlight_8"] = { label = "Headlight Kit - Red", id = 8, colourValue = "red" },
    ["headlight_9"] = { label = "Headlight Kit - Pony Pink", id = 9, colourValue = "pink1" },
    ["headlight_10"] = { label = "Headlight Kit - Hot Pink", id = 10, colourValue = "pink2" },
    ["headlight_11"] = { label = "Headlight Kit - Purple", id = 11, colourValue = "purple" },
    ["headlight_12"] = { label = "Headlight Kit - Blacklight", id = 12, colourValue = "blacklight" },
}

local function playerHasHeadlightItem()
    for i = -1, 30 do
        if exports["core_inventory"]:getItem("headlight_" .. i) then
            return "headlight_" .. i
        end
    end

    return false
end

function illegalModZone()
    local coords = GetEntityCoords(PlayerPedId())

    for i, garage in ipairs(MECH_ZONES_ILLEGALMODS) do
        if garage.polyzone and garage.polyzone:isPointInside(coords) and DoesPlayerHaveOrg(garage.name) then
            currentOrg = garage.name
            return true
        end
    end

    return false, "Unknown"
end

local function hasWbToolkit()
    for i = 1, 3 do
        if exports["core_inventory"]:getItem("toolkit_" .. i) then
            return true
        end
    end
    
    return false
end

local function headlightMenuItems()
    local menuItems = {}
    for headlight, colour in pairs(headlights) do
        if exports["core_inventory"]:getItem(headlight) then
            menuItems[headlight] = {
                displayData = { label = colour.label },
                onInteract = function(vehicle)
                    TriggerServerEvent("install:headlightKit", NetworkGetNetworkIdFromEntity(vehicle), colour, currentOrg)
                end
            }
        end
    end

    return menuItems
end

-- Headlight Menu
RegisterInteraction("vehicle", "headlight_menu", { label = "Install Headlights" }, function(vehicle)
    local menuItems = headlightMenuItems()
    exports.base_interaction:OpenInteractMenu(menuItems, vehicle)
end, function(entity)
    return playerHasHeadlightItem() and illegalModZone() and DoesPlayerHaveOrgPermission("mechanic.headlights")
end)

-- Register the menu option to install NOS
RegisterInteraction("vehicle", "nos_install", { label = "Install NOS" }, function(vehicle)
    TriggerServerEvent("nos:install", NetworkGetNetworkIdFromEntity(vehicle), currentOrg)
end, function(entity)
    return exports["core_inventory"]:getItem("nos_bottle") and illegalModZone() and DoesPlayerHaveOrgPermission("mechanic.nos")
end)


-- Register the menu option to refill NOS
RegisterInteraction("vehicle", "nos_refill", { label = "Refill NOS" }, function(vehicle)
    TriggerServerEvent("nos:refill", NetworkGetNetworkIdFromEntity(vehicle))
end, function(entity)
    local state = Entity(entity).state
    local hasNosBottle = exports["core_inventory"]:getItem("nos_bottle") ~= false
    return state.nosEnabled and hasNosBottle
end)

-- Register the menu option to install Underglow RGB
RegisterInteraction("vehicle", "underglowRGB_install", { label = "Install Underglow RGB Kit" }, function(vehicle)
    TriggerServerEvent("install:underglowRGB", NetworkGetNetworkIdFromEntity(vehicle), currentOrg)
end, function(entity)
    return exports["core_inventory"]:getItem("underglowrgbkit") and illegalModZone() and DoesPlayerHaveOrgPermission("mechanic.rgb")
end)

-- Register the menu option to install Headlight RGB
RegisterInteraction("vehicle", "headlightRGB_install", { label = "Install Headlight RGB Kit" }, function(vehicle)
    TriggerServerEvent("install:headlightRGB", NetworkGetNetworkIdFromEntity(vehicle), currentOrg)
end, function(entity)
    return exports["core_inventory"]:getItem("headlightrgbkit") and illegalModZone() and DoesPlayerHaveOrgPermission("mechanic.rgb")
end)

RegisterInteraction("vehicle", "driftmode_install", { label = "Install Drift Mode Upgrade" }, function(vehicle)
    TriggerServerEvent("install:driftTyres", NetworkGetNetworkIdFromEntity(vehicle), currentOrg)
end, function(entity)
    return exports["core_inventory"]:getItem("driftmodekit") and illegalModZone() and DoesPlayerHaveOrgPermission("mechanic.driftmode")
end)

-- Register the menu option to transform WB
RegisterInteraction("vehicle", "widebody_transformation", { label = "Prepare Widebody" }, function(vehicle)
    TriggerServerEvent("widebody:transform", NetworkGetNetworkIdFromEntity(vehicle), currentOrg)
end, function(entity)
    return hasWbToolkit() and illegalModZone() and DoesPlayerHaveOrgPermission("mechanic.widebody")
end)

RegisterNetEvent("install:setDriftTyres")
AddEventHandler("install:setDriftTyres", function(vehNetId, amount)
    local vehicle = NetworkGetEntityFromNetworkId(vehNetId)
    local start = GetGameTimer()
    while (not NetworkHasControlOfEntity(vehicle)) and GetGameTimer() - start < 5000 do
        Wait(0)
        NetworkRequestControlOfEntity(vehicle)
    end
    DecorSetBool(vehicle, "driftTyres", true)
end)

RegisterNetEvent("install:setHeadlights")
AddEventHandler("install:setHeadlights", function(vehNetId, color)
    local targetVeh = NetworkGetEntityFromNetworkId(vehNetId)
    if DoesEntityExist(targetVeh) then
        ToggleVehicleMod(targetVeh, 22, true)
        SetVehicleXenonLightsColor(targetVeh, color)
    end
end)
