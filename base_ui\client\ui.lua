function sendQUIEvent(eventName, ...)
    SendNUIMessage({ nuiEvent = eventName, data = table.pack(...) })
end

exports("sendQUIEvent", sendQUIEvent)

function toggleQUIState(keyboardFocus, mouseInput, passthrough)
    SetNuiFocus(keyboardFocus, mouseInput)
    SetNuiFocusKeepInput(passthrough)
end

exports("toggleQUIState", toggleQUIState)

function registerQUICallback(eventName, callback)
    RegisterNUICallback(
        eventName,
        function(data, cb)
            callback(data)
            cb("OK")
        end
    )
end

exports("registerQUICallback", registerQUICallback)
