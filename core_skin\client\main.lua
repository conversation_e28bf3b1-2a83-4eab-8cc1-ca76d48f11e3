local CAMERAS = {
    cos = {
        heightOff = 0.62,
        distance = 0.8,
    },
    basic = {
        heightOff = 0.62,
        distance = 0.8,
    },
    adv = {
        heightOff = 0.62,
        distance = 0.8,
    },
    clothing = {
        heightOff = 0.0,
        distance = 2.0,
    },
    ["clothing-restricted"] = {
        heightOff = 0.0,
        distance = 2.0,
    },
    charCreate = {
        heightOff = 0.0,
        distance = 2.0,
    },
}

local CLOTHING_CAMERAS = {
    torso = {
        heightOff = 0.3,
        distance = 1.0
    },

    bproof = {
        heightOff = 0.3,
        distance = 1.0
    },

    bag = {
        heightOff = 0.3,
        distance = 1.1
    },

    tshirt = {
        heightOff = 0.3,
        distance = 1.1
    },

    pants = {
        heightOff = -0.3,
        distance = 1.1
    },

    decalTexture = {
        heightOff = 0.50,
        distance = 0.4
    },

    decals = {
        heightOff = 0.3,
        distance = 0.7
    },

    shoes = {
        heightOff = -0.4,
        distance = 1.1
    },

    watch = {
        heightOff = 0.3,
        distance = 1.1
    },

    bracelet = {
        heightOff = 0.3,
        distance = 1.1
    },

    chain = {
        heightOff = 0.0,
        distance = 2.0,
    },

    lower_torso = {
        heightOff = -0.5,
        distance = 0.8,
    },

    upper_torso = {
        heightOff = 0.5,
        distance = 0.8,
    },

    whole_torso = {
        heightOff = 0.0,
        distance = 2.0,
    },

    head = {
        heightOff = 0.67,
        distance = 0.5,
    }
}

local COSMETIC_CAMERAS = {
    head = {
        heightOff = 0.67,
        distance = 0.5,
    }
}

local oldSkin = nil
local cam = nil
local playerHeading = 0.0
local playerForward = nil
local menuOpen = false
local lastPosition = nil
local onMenuSave = nil
local onMenuReset = nil

local function renderSkinCam(camData)
    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    if not cam then cam = Base.Camera.create("skin_cam", 1) end
    if not playerForward then playerForward = GetEntityForwardVector(playerPed) end

    if camData then
        local x, y, z = table.unpack(coords + playerForward * camData.distance)
        SetCamCoord(cam, x, y, z + camData.heightOff)
        PointCamAtCoord(cam, coords.x, coords.y, coords.z + camData.heightOff)
        SetCamActive(cam, true)
        RenderScriptCams(true, true, 500, 0, 0)
    end
end

local function destroySkinCam()
    Base.Camera.destroy("skin_cam", 500)
    cam = nil
end

local function getMaxValues()
    local maxValues = {}
    local minValues = {}
    local clothing = Base.Skin.getCurrentSkin().clothing

    maxValues.clothing = {}
    minValues.clothing = {}

    for feature, details in pairs(clothing) do
        maxValues.clothing[feature] = {}
        maxValues.clothing[feature].model = Base.Skin.getMaxValue("clothing", feature, false)
        maxValues.clothing[feature].texture = Base.Skin.getMaxValue("clothing", feature, true)

        minValues.clothing[feature] = {}
        minValues.clothing[feature].model = Base.Skin.getMinValue(feature)
        minValues.clothing[feature].texture = 0
    end

    return maxValues, minValues
end

local function closeSkinMenu()
    SetNuiFocus(false, false)
    destroySkinCam()
    menuOpen = false
    playerForward = nil
    ClearPedTasks(PlayerPedId())

    if lastPosition then
        Base.Util.fadeScreen(true, 1000)
        local ped = PlayerPedId()
        SetEntityCoords(ped, lastPosition.x, lastPosition.y, lastPosition.z)
        TriggerServerEvent("core_skin:skin_loaded")
        lastPosition = nil
        FreezeEntityPosition(ped, false)
        Wait(800)
        Base.Util.fadeScreen(false, 1000)
    end
end

RegisterNUICallback("setSkinValue", function(data)
    if data.category == "skin" then
        Base.Skin.setSkinItem(data.feature, data.key or false, tonumber(data.value), false)
    elseif data.category == "cosmetics" then
        Base.Skin.setCosmeticItem(data.feature, data.key or false, tonumber(data.value), false)
    elseif data.category == "clothing" then
        Base.Skin.setClothingItem(data.feature, data.key or false, tonumber(data.value), false)
    end
    SendNUIMessage({ maxValues = getMaxValues() })
end)

RegisterNUICallback("setModel", function(model)
    Base.Skin.setModel(model, false)
end)

RegisterNUICallback("resetSkin", function()
    closeSkinMenu()
    Base.Skin.setSkin(oldSkin)

    if onMenuReset then
        onMenuReset()
    end

    onMenuSave = nil
    onMenuReset = nil
end)

RegisterNUICallback("saveSkin", function()
    closeSkinMenu()

    if not onMenuSave then
        Base.Skin.saveCurrSkin()
    else
        onMenuSave()
    end

    onMenuSave = nil
    onMenuReset = nil

    -- TriggerServerEvent("core_jobs:jobUniform", false)
end)

local handsUp = false

RegisterNUICallback("handsUp", function()
    local ped = PlayerPedId()
    if not handsUp then
        handsUp = true
        Base.Animation.Start(ped, "random@mugging3", "handsup_standing_base", true, false, true, -10.0)
    else
        Base.Animation.Stop(ped)
        handsUp = false
    end
end)

RegisterNUICallback("menuChanged", function(menuType)
    local camData = CAMERAS[string.gsub(menuType, "skin%-", "")]
    if camData then renderSkinCam(camData) end
end)

RegisterNUICallback("setRotation", function(offset)
    SetEntityHeading(PlayerPedId(), playerHeading + offset)
end)

RegisterNUICallback("toggleClothing", function(item)
    Base.Skin.toggleItem(item)
end)

RegisterNUICallback("clothingCamera", function(item)
    renderSkinCam(CLOTHING_CAMERAS[item])
end)

local restricted_clothing2 = {}

onBaseReady(function()
    TriggerServerCallback("core_skin:getRestrictedClothing", function(result)
        for k, v in pairs(result) do
            table.insert(restricted_clothing2, v.organisation)
        end
    end)
end)

function openSkinMenu(menus, skin, blockReset, onSave, onReset)
    local playerPed = PlayerPedId()

    SetNuiFocus(true, true)
    TaskStandStill(playerPed, -1)

    for k, v in pairs(restricted_clothing2) do
        if DoesPlayerHaveOrg(v) then
            for i, menu in ipairs(menus) do
                if menu == "clothing" then
                    menus[i] = "clothing-restricted"
                end
            end
        end
    end

    local camData = CAMERAS[menus[1]]
    oldSkin = skin or Base.Skin.getCurrentSkin()
    playerHeading = GetEntityHeading(playerPed)
    menuOpen = true

    local maxValues, minValues = getMaxValues()

    onMenuSave = onSave
    onMenuReset = onReset

    SendNUIMessage({
        skin = oldSkin,
        showMenu = menus,
        blockReset = blockReset,
        maxValues = maxValues,
        minValues =
            minValues
    })
    if camData then renderSkinCam(camData) end
end

function getMaxValues()
    local maxValues = {}
    local minValues = {}
    local clothing = Base.Skin.getCurrentSkin().clothing

    maxValues.clothing = {}
    minValues.clothing = {}

    for feature, details in pairs(clothing) do
        maxValues.clothing[feature] = {}
        maxValues.clothing[feature].model = Base.Skin.getMaxValue("clothing", feature, false)
        maxValues.clothing[feature].texture = Base.Skin.getMaxValue("clothing", feature, true)

        minValues.clothing[feature] = {}
        minValues.clothing[feature].model = Base.Skin.getMinValue(feature)
        minValues.clothing[feature].texture = 0
    end

    return maxValues, minValues
end

RegisterNetEvent("base:characterLoaded", function(data)
    local skin = data.skin

    if skin then
        -- Load defaults in case of DB corruption
        if not skin.model then skin.model = "mp_m_freemode_01" end
        if not skin.skin then skin.skin = Base.Skin.getDefaultSkin() end
        if not skin.clothing then skin.clothing = Base.Skin.getDefaultClothing() end
        if not skin.cosmetics then skin.cosmetics = Base.Skin.getDefaultCosmetics() end
        if not skin.tattoos then skin.tattoos = {} end

        -- Load the skin
        Base.Skin.setSkin(skin, false)
        Base.Skin.saveCurrSkin()
    else
        skin = Base.Skin.getDefault()
        lastPosition = { x = -542.57, y = -208.55, z = 37.65, r = 207.34 }

        Citizen.CreateThread(function()
            local pid = PlayerId()
            while lastPosition do
                Wait(0)
                for i, p in pairs(GetActivePlayers()) do
                    if p ~= pid then NetworkConcealPlayer(p, true, 0) end
                end
            end

            for i, p in pairs(GetActivePlayers()) do
                if p ~= pid then NetworkConcealPlayer(p, false, 0) end
            end
        end)

        -- Set the skin
        Base.Skin.setSkin(skin)

        -- Set the ped position etc for char create
        Base.Util.fadeScreen(true, 1000)
        local ped = PlayerPedId()
        SetEntityCoords(ped, 402.74, -996.51, -99.9, 0.0, 0.0, 0.0, false)
        SetEntityHeading(ped, 180.0)
        SetEntityCollision(ped, true)
        Base.Player.setEntityVisible(true)
        FreezeEntityPosition(ped, true)
        renderSkinCam(CAMERAS["charCreate"])
        Wait(800)
        Base.Util.fadeScreen(false, 1000)
        openSkinMenu({ "charCreate", "basic", "adv", "cos", "clothing" }, skin, true)
    end
end)

RegisterNetEvent("skin:openSkinMenu", function()
    openSkinMenu({ "charCreate", "basic", "adv", "cos", "clothing-restricted" })
end)

local clothingFreezeEnabled = false

RegisterCommand("freezeclothing", function(source, args)
    local playerPed = PlayerPedId()
    clothingFreezeEnabled = not clothingFreezeEnabled

    if clothingFreezeEnabled then
        TriggerEvent("chat:addMessage", "", { 255, 0, 0 }, "Clothing Freeze has been ENABLED")
        SetPedCanLosePropsOnDamage(playerPed, false, 0)
    else
        TriggerEvent("chat:addMessage", "", { 255, 0, 0 }, "Clothing Freeze has been DISABLED")
        SetPedCanLosePropsOnDamage(playerPed, true, 0)
    end
end)

Citizen.CreateThread(function()
    while true do
        Wait(0)
        if menuOpen then
            SetNuiFocus(true, true)
        end
    end
end)
