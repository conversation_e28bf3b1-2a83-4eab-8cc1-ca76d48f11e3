SHOWROOMS = {
    {
        label = "Premium Deluxe Motorsports",
        type = "car_standard",
        blipLabel = "Sell Vehicle",
        purchaseable = true,
        menu = { coords = vector3(-55.0318, -1096.175, 26.42245), heading = 161.90545654297 },
        purchaseSpawn = { coords = vector3(-45.18348, -1098.392, 26.42246), heading = 279.23797607422 },
        previewSpawn = { coords = vector3(-45.18348, -1098.392, 26.42246), heading = 279.23797607422 },
        blip = {
            sprite = 426,
            color = 26,
            scale = 0.6,
        }
    },
    {
        label = "Harmony Offroad Sales",
        type = "car_offroad",
        blipLabel = "Sell Vehicle",
        purchaseable = false,
        menu = { coords = vector3(1224.634, 2728.119, 38.00488), heading = 179.40097045898 },
        purchaseSpawn = { coords = vector3(1224.186, 2711.209, 38.00578), heading = 235.17854309082 },
        previewSpawn = { coords = vector3(1224.186, 2711.209, 38.00578), heading = 235.17854309082 },
        blip = {
            sprite = 559,
            color = 5,
            scale = 0.6,
        }
    },
    {
        label = "Benny's Automotives (NEW)",
        type = "car_dealership",
        blipLabel = "Sell Vehicle",
        purchaseable = true,
        assignable = true,
        visibleShowroom = true,
        restricted = "mech3",
        menu = { coords = vector3(-209.9456, -1330.567, 30.91339), heading = 89.93 },
        purchaseSpawn = { coords = vector3(-213.697, -1329.789, 30.60205), heading = 357.30 },
        previewSpawn = { coords = vector3(-213.697, -1329.789, 30.60205), heading = 357.30 },
    },
    {
        label = "Benny's Automotives (NEW)",
        type = "usedcar",
        blipLabel = "Sell Vehicle",
        purchaseable = true,
        assignable = true,
        visibleShowroom = true,
        restricted = "mech3",
        menu = { coords = vector3(-209.9286, -1332.083, 30.89037), heading = 90.66 },
        purchaseSpawn = { coords = vector3(-213.697, -1329.789, 30.60205), heading = 357.30 },
        previewSpawn = { coords = vector3(-213.697, -1329.789, 30.60205), heading = 357.30 },
    },
    {
        label = "Haye's Autos",
        type = "car_dealership",
        blipLabel = "Sell Vehicle",
        purchaseable = true,
        assignable = true,
        visibleShowroom = true,
        restricted = "mech4",
        menu = { coords = vector3(-1647.04, -818.26, 10.18), heading = 320.09 },
        purchaseSpawn = { coords = vector3(-1650.4173583984, -826.45941162109, 9.6355495452881), heading = 228.8832244873 },
        previewSpawn = { coords = vector3(-1650.4173583984, -826.45941162109, 9.6355495452881), heading = 228.8832244873 },
    },
    {
        label = "Haye's Autos",
        type = "usedcar",
        blipLabel = "Sell Vehicle",
        purchaseable = true,
        assignable = true,
        visibleShowroom = true,
        restricted = "mech4",
        menu = { coords = vector3(-1644.83, -820.27, 10.17), heading = 1320.09 },
        purchaseSpawn = { coords = vector3(-1650.4173583984, -826.45941162109, 9.6355495452881), heading = 228.8832244873 },
        previewSpawn = { coords = vector3(-1650.4173583984, -826.45941162109, 9.6355495452881), heading = 228.8832244873 },
    },
    {
        label = "LS Customs",
        type = "car_dealership",
        blipLabel = "Sell Vehicle",
        purchaseable = true,
        assignable = true,
        visibleShowroom = true,
        restricted = "mech1",
        menu = { coords = vector3(545.51049804688, -204.03691101074, 54.347545623779), heading = 182.20663452148 },
        purchaseSpawn = { coords = vector3(548.282, -205.8655, 53.7525), heading = 178.47180175781 },
        previewSpawn = { coords = vector3(548.282, -205.8655, 53.7525), heading = 178.47180175781 },
    },
    {
        label = "LS Customs",
        type = "usedcar",
        blipLabel = "Sell Vehicle",
        purchaseable = true,
        assignable = true,
        visibleShowroom = true,
        restricted = "mech1",
        menu = { coords = vector3(551.43157958984, -203.42515563965, 54.390197998209), heading = 64.015365600586 },
        purchaseSpawn = { coords = vector3(548.282, -205.8655, 53.7525), heading = 178.47180175781 },
        previewSpawn = { coords = vector3(548.282, -205.8655, 53.7525), heading = 178.47180175781 },
    },
    -- {
    --     label = "Flywheels Paleto",
    --     type = "car_dealership",
    --     blipLabel = "Sell Vehicle",
    --     purchaseable = true,
    --     assignable = true,
    --     visibleShowroom = true,
    --     restricted = "mech6",
    --     menu = { coords = vector3(113.6002, 6532.828, 31.75057), heading = 42.475849151611 },
    --     purchaseSpawn = { coords = vector3(112.9882, 6525.404, 31.32635), heading = 61.402824401855 },
    --     previewSpawn = { coords = vector3(112.9882, 6525.404, 31.32635), heading = 61.402824401855 },
    -- },
    {
        label = "Flywheels Sandy",
        type = "car_dealership",
        blipLabel = "Sell Vehicle",
        purchaseable = true,
        assignable = true,
        visibleShowroom = true,
        restricted = "mech6",
        menu = { coords = vector3(1744.142, 3299.116, 41.22359), heading = 105.53785705566 },
        purchaseSpawn = { coords = vector3(1740.047, 3305.751, 40.79951), heading = 152.04370117188 },
        previewSpawn = { coords = vector3(1740.047, 3305.751, 40.79951), heading = 152.04370117188 },
    },
    {
        label = "Flywheels Sandy",
        type = "usedcar",
        blipLabel = "Sell Vehicle",
        purchaseable = true,
        assignable = true,
        visibleShowroom = true,
        restricted = "mech6",
        menu = { coords = vector3(1745.735, 3299.48, 41.22359), heading = 10.041151046753 },
        purchaseSpawn = { coords = vector3(1740.047, 3305.751, 40.79951), heading = 152.04370117188 },
        previewSpawn = { coords = vector3(1740.047, 3305.751, 40.79951), heading = 152.04370117188 },
    },
    {
        label = "Flywheels Sandy (Aircraft)",
        type = "aircraft_dealership",
        blipLabel = "Sell Aircraft",
        purchaseable = true,
        assignable = true,
        visibleShowroom = true,
        restricted = "mech6",
        menu = { coords = vector3(1744.6217041016, 3301.0476074219, 41.223541259766), heading = 105.53785705566 },
        purchaseSpawn = { coords = vector3(1740.1463623047, 3277.697265625, 41.107265472412), heading = 152.04370117188 },
        previewSpawn = { coords = vector3(1740.1463623047, 3277.697265625, 41.107265472412), heading = 152.04370117188 },
    },
    {
        label = "The District",
        type = "car_racing",
        blipLabel = "Buy Vehicle",
        purchaseable = true,
        --assignable = true,
        --visibleShowroom = true,
        --restricted = "umbrella",
        menu = { coords = vector3(-358.4075012207, 7413.6201171875, 6.4103803634644), heading = 311.87 },
        purchaseSpawn = { coords = vector3(-334.06655883789, 7407.8833007812, 5.8561191558838), heading = 359.59 },
        previewSpawn = { coords = vector3(-354.32824707031, 7415.6411132812, 5.45), heading = 133.68 },
    },
    {
        label = "Redline Motors",
        type = "usedcar",
        blipLabel = "Sell Vehicle",
        purchaseable = true,
        assignable = true,
        visibleShowroom = true,
        restricted = "usedcar3",
        menu = { coords = vector3(-223.02, 6215.23, 31.51), heading = 49.940200805664 },
        purchaseSpawn = { coords = vector3(-222.54, 6220.67, 30.88), heading = 44.787937164307 },
        previewSpawn = { coords = vector3(-222.54, 6220.67, 30.88), heading = 44.787937164307 },
    },
    {
        label = "About the Art",
        type = "car_ata",
        blipLabel = "Sell Vehicle",
        purchaseable = true,
        assignable = true,
        visibleShowroom = true,
        restricted = "paint",
        menu = { coords = vector3(270.98, -219.89, 53.97), heading = 307.53533935547 },
        purchaseSpawn = { coords = vector3(273.07, -224.66, 53.93), heading = 192.5 },
        previewSpawn = { coords = vector3(273.07, -224.66, 53.93), heading = 192.5 },
    }
}

DEFAULT_COLORS = {
    {
        label = "Black",
        primary = 0,
        secondary = 0,
        pearl = 0
    },
    {
        label = "Red",
        primary = 27,
        secondary = 27,
        pearl = 27
    },
    {
        label = "Blue",
        primary = 53,
        secondary = 53,
        pearl = 53
    },
    {
        label = "Yellow",
        primary = 88,
        secondary = 88,
        pearl = 88
    },
    {
        label = "Green",
        primary = 55,
        secondary = 55,
        pearl = 55
    },
    {
        label = "Orange",
        primary = 36,
        secondary = 36,
        pearl = 36
    },
    {
        label = "Purple",
        primary = 145,
        secondary = 145,
        pearl = 145
    },
    {
        label = "Pink",
        primary = 141,
        secondary = 141,
        pearl = 141
    },
    {
        label = "Gray",
        primary = 1,
        secondary = 1,
        pearl = 1
    },
    {
        label = "Silver",
        primary = 4,
        secondary = 4,
        pearl = 4
    },
    {
        label = "White",
        primary = 111,
        secondary = 111,
        pearl = 111
    },
}

ALLOWLISTED_JOBS = {
    staff = true
}

-- Mechanics
MECHANIC_JOBS = {
    mech1 = true,
    mech2 = true,
    mech3 = true,
    mech4 = true,
    mech5 = true,
    mech6 = true
}

--Perms for headlights as Bennys wanted different perms.
ALLOWED_GRADES_HEADLIGHTS = {

    --LSC
    mech1 = {
        [9] = true,  -- Executive
        [10] = true, -- Head Mech
        [11] = true, -- Head Dealer
        [12] = true, -- Head Tuner
        [13] = true, -- Senior Management
        [14] = true, -- Management
        [21] = true, -- Tuner Specialist
        [23] = true  -- Master Mech
    },

    --Bennys
    mech3 = {
        [100] = true, -- Executive
        [15] = true,  -- HR Manager
        [14] = true,  -- General Manager
        [13] = true,  -- Senior Specialist
        [12] = true,  -- Advanced Technician
        [11] = true,  -- Sales Technician
        [6] = true    -- Mod Specialist
    },

    --Hayes
    mech4 = {
        [100] = true, -- Executives
        [6] = true,   -- Management
        [5] = true,   -- Floor Supervisor
        [4] = true,   -- Senior Mech
        [3] = true    -- Qualified
    },

    --Flywheels
    mech6 = {
        [100] = true, -- Boss
        [1] = true,   -- Manager
        [5] = true,   -- Flywheels Specialist
        [2] = true    -- Master Mechanic
    },

    --Staffmech
    staffmech = {
        [100] = true -- Comp claim staff
    }
}

--Perms for All Tuner Mods
ALLOWED_GRADES_ALL_MODS = {

    --LSC
    mech1 = {
        [9] = true,  -- Executive
        [10] = true, -- Head Mech
        [11] = true, -- Head Dealer
        [12] = true, -- Head Tuner
        [13] = true, -- Senior Management
        [14] = true, -- Management
        [15] = true, -- HR Assistant
        [18] = true, -- Floor Supervisor
        [21] = true, -- Tuner Specialist
        [23] = true  -- Master Mech
    },

    --Bennys
    mech3 = {
        [100] = true, -- Executive
        [15] = true,  -- HR Manager
        [14] = true,  -- General Manager
        [13] = true   -- Senior Specialist
    },

    --Hayes
    mech4 = {
        [100] = true, -- Executives
        [6] = true,   -- Management
        [5] = true,   -- Floor Supervisor
        [4] = true,   -- Senior Mech
        [3] = true    -- Qualified
    },

    --Flywheels
    mech6 = {
        [100] = true, -- Boss
        [1] = true,   -- Manager
        [5] = true,   -- Flywheels Specialist
        [2] = true    -- Master Mechanic
    },

    --Staffmech
    staffmech = {
        [100] = true -- Comp claim staff
    }
}

--Perms for Widebodies
ALLOWED_GRADES_WIDEBODIES = {

    --LSC
    mech1 = {
        [9] = true,  -- Executive
        [10] = true, -- Head Mech
        [11] = true, -- Head Dealer
        [12] = true, -- Head Tuner
        [13] = true, -- Senior Management
        [14] = true, -- Management
        [15] = true, -- HR Assistant
        [18] = true, -- Floor Supervisor
        [21] = true  -- Tuner Specialist
    },

    --Bennys
    mech3 = {
        [100] = true, -- Executive
        [15] = true,  -- HR Manager
        [14] = true,  -- General Manager
        [13] = true   -- Senior Specialist
    },

    --Hayes
    mech4 = {
        [100] = true, -- Executives
        [6] = true,   -- Management
        [5] = true,   -- Floor Supervisor
        [4] = true,   -- Senior Mech
        [3] = true    -- Qualified
    },

    --Flywheels
    mech6 = {
        [100] = true, -- Boss
        [1] = true,   -- Manager
        [5] = true    -- Flywheels Specialist
    }
}

MECH_ZONES_ILLEGALMODS = {
    {
        name = "mech1",
        coords = {
            vector2(525.00, -165.91),
            vector2(556.06, -165.53),
            vector2(556.06, -196.21),
            vector2(540.53, -243.94),
            vector2(531.82, -244.32),
            vector2(532.58, -222.73),
            vector2(541.67, -222.35),
            vector2(537.50, -204.17),
            vector2(536.36, -194.70),
            vector2(526.14, -195.83),
            vector2(520.45, -180.68),
            vector2(518.94, -168.18)
        },
        details = {
            label = "LSC",
            minZ = 45.0,
            maxZ = 60.0,
            debugGrid = false,
            gridDivisions = 25,
        }
    },

    {
        name = "mech3",
        coords = {
            vector2(-242.05, -1312.50),
            vector2(-242.05, -1341.29),
            vector2(-219.32, -1338.64),
            vector2(-218.56, -1326.52),
            vector2(-213.64, -1326.52),
            vector2(-211.36, -1325.38),
            vector2(-207.20, -1326.14),
            vector2(-203.03, -1326.14),
            vector2(-199.24, -1339.77),
            vector2(-189.77, -1338.26),
            vector2(-188.64, -1310.23)
        },
        details = {
            label = "BennysCustoms",
            minZ = 30.0,
            maxZ = 36.0,
            debugGrid = false,
            gridDivisions = 25,
        }
    },

    {
        name = "mech4",
        coords = {
            vector2(-1573.11, -840.91),
            vector2(-1580.68, -848.86),
            vector2(-1589.39, -859.47),
            vector2(-1614.39, -839.02),
            vector2(-1639.02, -816.67),
            vector2(-1621.21, -798.11)
        },
        details = {
            label = "HayesAutos",
            minZ = 9.0,
            maxZ = 15.0,
            debugGrid = false,
            gridDivisions = 25,
        }
    },

    {
        name = "mech6",
        coords = {
            vector2(1773.11, 3350.00),
            vector2(1785.61, 3329.17),
            vector2(1762.88, 3307.95),
            vector2(1756.44, 3316.67),
            vector2(1744.32, 3309.85),
            vector2(1739.39, 3306.06),
            vector2(1745.83, 3289.77),
            vector2(1722.35, 3288.26),
            vector2(1712.88, 3313.64),
            vector2(1735.61, 3322.35)
        },
        details = {
            label = "FlywheelsMechanics",
            minZ = 40.0,
            maxZ = 48.0,
            debugGrid = false,
            gridDivisions = 25,
        }
    },

    {
        name = "staffmech",
        coords = {
            vector2(-970.45, -2060.23),
            vector2(-955.30, -2046.21),
            vector2(-944.32, -2057.20),
            vector2(-959.85, -2070.83)
        },
        details = {
            label = "StaffMech",
            minZ = 8.0,
            maxZ = 16.0,
            debugGrid = false,
            gridDivisions = 25,
        }
    }
}
