local restedEffect = TimedStatusEffect(StatusEffect("rested", "Rested",
    {
        onStart = function()
            SetPlayerHealthRegenBuff("rested", 0.5)
            SetPlayerHealthRegenCap("rested", 0.75)
        end,
        onStop = function()
            SetPlayerHealthRegenBuff("rested", 0.0)
            SetPlayerHealthRegenCap("rested", 0.75)
        end
    },
    {
        desc = "You feel rested after visiting your home",
        subdesc = "Your health is slowly regenerating"
    }
), { maxTime = 30 * 60 })

local insideHomeEffect = StatusEffect("insideHome", "Inside Home",
    {
        onStart = function()
            SetPlayerHealthRegenCap("insideHome", 0.9)
        end,
        onStop = function()
            SetPlayerHealthRegenCap("insideHome", 0.0)
        end
    },
    {
        desc = "You feel safe inside your home",
        subdesc = "You are recovering health"
    }
)

local restedTruckerEffect = TimedStatusEffect(StatusEffect("restedtrucker", "Rested Trucker",
    {},
    {
        desc = "You're feeling rested and energised!",
        subdesc = "Extra XP for trucking"
    }
), { maxTime = 30 * 60 })

local insideProperty = false

AddEventHandler("properties:enterProperty", function(hasKeys)
    insideProperty = hasKeys
    if insideProperty then
        insideHomeEffect.start()
    end

    while insideProperty do
        Wait(10000)

        if insideProperty then
            restedEffect.addTime(120)
        end
    end
end)

AddEventHandler("properties:exitProperty", function()
    insideProperty = false
    insideHomeEffect.stop()
end)


RegisterNetEvent("core_effects:clearStatusEffects")
AddEventHandler("core_effects:clearStatusEffects", function()
    insideProperty = false
    insideHomeEffect.stop()
    restedEffect.setTime(0)
    restedEffect.stop()
    TriggerEvent("overhealth:off")
end)

RegisterNetEvent("core_effects:restedTrucker")
AddEventHandler("core_effects:restedTrucker", function()
    -- Stop so it doesn't stack
    restedTruckerEffect.stop()
    restedTruckerEffect.addTime(3600)
    restedTruckerEffect.start()
end)

-- Citizen.CreateThread(function()
--     local churchCoords = vector3(-775.0, -2.0, -140.0)
--     while true do
--         local coords = GetEntityCoords(PlayerPedId())

--         if #(coords - churchCoords) < 100.0 then
--             Wait(10000)

--             if #(coords - churchCoords) < 100.0 then
--                 blessedEffect.addTime(120)
--             end
--         end

--         Wait(5000)
--     end
-- end)
