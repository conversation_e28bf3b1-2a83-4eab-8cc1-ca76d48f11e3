local kenoDui = nil

RegisterNetEvent("keno:displayNumbers")
AddEventHandler(
	"keno:displayNumbers",
	function(numbers, _currentGame, _waitTime)
		SendDuiMessage(kenoDui, json.encode({ numbersDrawn = numbers, currentGame = _currentGame }))
		SendNUIMessage({ currentGame = _currentGame, waitTime = _waitTime })
	end
)

RegisterNetEvent("keno:displayWinner")
AddEventHandler(
	"keno:displayWinner",
	function(_winningSide)
		SendDuiMessage(kenoDui, json.encode({ winningSide = _winningSide }))
	end
)

RegisterNetEvent("keno:pastGameSync")
AddEventHandler(
	"keno:pastGameSync",
	function(pastGamesData)
		SendDuiMessage(kenoDui, json.encode({ pastGamesData = pastGamesData }))
	end
)

RegisterNetEvent("keno:syncRates")
AddEventHandler(
	"keno:syncRates",
	function(_LOTTORATES, _MINBET, _MAXBET)
		SendNUIMessage({ LOTTORATES = _LOTTORATES, minBetAmount = _MINBET, maxBetAmount = _MAXBET })
	end
)

RegisterNetEvent("keno:sendOutcomeFail")
AddEventHandler(
	"keno:sendOutcomeFail",
	function(_betType, _msg)
		SendNUIMessage({ betType = _betType, msg = _msg })
	end
)

RegisterNetEvent("keno:sendOutcomeSuccess")
AddEventHandler(
	"keno:sendOutcomeSuccess",
	function(_betType)
		SendNUIMessage({ betSlip = _betType })
	end
)

RegisterNUICallback(
	"placeNumbersBet",
	function(data)
		TriggerServerEvent("keno:placeNumbersBet", data.amount, data.numbers)
	end
)

RegisterNUICallback(
	"placeSidesBet",
	function(data)
		TriggerServerEvent("keno:placeSidesBet", data.amount, data.side)
	end
)

RegisterNUICallback(
	"closeTerminal",
	function()
		hideTerminal()
	end
)

function showTerminal()
	SendNUIMessage({ showTerminal = true })
	SetNuiFocus(true, true)
end

function hideTerminal()
	SendNUIMessage({ showTerminal = false })
	SetNuiFocus(false, false)
end

local BOOTHS = {
	{ x = 1116.44,   y = 221.71,   z = -49.51 },
	{ x = 1116.09,   y = 219.95,   z = -49.51 },
	{ x = 1116.49,   y = 218.24,   z = -49.51 },
	{ x = 1099.05,   y = 262.75,   z = -51.32 },
	-- {x = 1229.12, y = -420.68, z = 67.5},
	{ x = -559.89,   y = 289.50,   z = 82.0 },
	{ x = -563.45,   y = 284.85,   z = 85.0 },
	{ x = -563.45,   y = 284.85,   z = 85.0 },
	{ x = 144.95,    y = -3014.43, z = 6.95 },
	{ x = -2179.645, y = 4293.148, z = 49.11 }
}

onBaseReady(function()
	for i, coords in ipairs(BOOTHS) do
		Base.Markers.Create("kenobooth_" .. i, coords, { r = 255, g = 255, b = 255 }, "Play KENO", false, showTerminal)
	end
end)


local currentID = nil
RegisterNetEvent("setZedRun", function(raceId)
	if not raceId then
		currentID = nil
		SetDuiUrl(zedRunDuiWidescreen, "nui://main_casino/ui/black.html")
	elseif currentID ~= raceId then
		currentID = raceId
		SetDuiUrl(zedRunDuiWidescreen, "nui://main_casino/racedisplay/dist/index.html")
	end
end)

Citizen.CreateThread(
	function()
		kenoDui = CreateDui("nui://main_casino/ui/index.html", 1920, 1080)
		local kenoDuiHandle = GetDuiHandle(kenoDui)

		-- billboardDui = CreateDui("https://player.vimeo.com/external/449537892.sd.mp4?s=8961277b27566649e1d33b01fadf559a33fd1343&profile_id=165", 960, 540)
		-- local billboardDuiHandle = GetDuiHandle(billboardDui)

		Wait(1000)
		local interiorId = GetInteriorAtCoords(1100.00, 220.00, -50.00)
		DUI.registerTxd(
			"xm_prop_x17_tv_flat_01_casino_01",
			"script_rt_tv_flat_01_casino_01",
			"casino",
			"keno",
			kenoDuiHandle,
			interiorId
		)
		DUI.registerTxd(
			"xm_prop_x17_tv_flat_01_casino_02",
			"script_rt_tv_flat_01_casino_02",
			"casino",
			"keno",
			kenoDuiHandle,
			interiorId
		)
		DUI.registerTxd(
			"xm_prop_x17_tv_flat_01_casino_03",
			"script_rt_tv_flat_01_casino_03",
			"casino",
			"keno",
			kenoDuiHandle,
			interiorId
		)
		DUI.registerTxd(
			"prop_tv_flat_02",
			"script_rt_tvscreen",
			"casino",
			"keno",
			kenoDuiHandle,
			GetInteriorAtCoords(-558.27, 289.80, 82.176)
		)
		DUI.registerTxd(
			"ex_prop_ex_tv_flat_01_zoom_mutiny",
			"script_rt_ex_tvscreen",
			"casino",
			"keno",
			kenoDuiHandle,
			GetInteriorAtCoords(-2196.435, 4285.396, 50.0721)
		)
		DUI.registerTxd(
			"csu",
			"1",
			"casino",
			"keno",
			kenoDuiHandle
		)
		-- DUI.registerTxd("sc1_30_billboard", "rsg_mh_sc1_30_billboard1", "billboard", "video1", billboardDuiHandle)
		-- DUI.registerTxd("sc1_14_build1", "joses_logo_only", "casino", "keno", kenoDuiHandle)

		--Syndicate Hub
		DUI.registerTxd(
			"prop_tv_flat_michael",
			"script_rt_tvscreen",
			"casino",
			"keno",
			kenoDuiHandle,
			GetInteriorAtCoords(137.91, -3016.31, 7.04)
		)
	end
)
