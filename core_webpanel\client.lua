RegisterCommand("ad", function(source, args, raw)
	if (PlayerData.group ~= "user") and (not Tablet.isOpen) then
		Tablet.open(true)
		Tablet.navigate("https://staff.fatduckgaming.com/account/" .. args[1] .. "/info")
	else
		TriggerEvent("fdg_ui:SendNotification", "You do not have the correct permissions to access this resource.")
		Tablet.close()
	end
end)

RegisterNUICallback("updatePassword", function(data)
	TriggerScreenblurFadeOut(1000)
	TriggerServerEvent("webpanel:updatePassword", data.password)
	SendNUIMessage({ showLoginDialog = false })
	SetNuiFocus(false, false)
end)

RegisterNUICallback("close", function()
	SetNuiFocus(false, false)
	SetNuiFocusKeepInput(false)
end)

RegisterNUICallback("setFocus", function(state)
	SetNuiFocus(state, state)
end)
