<html>
    <head>
        <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
        <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
        <script src="js/script.js" type="text/javascript"></script>
        <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">

        <link rel="stylesheet" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">
        <link href="css/main.css" rel="stylesheet" type="text/css" />
        <link href="css/hotbar.css" rel="stylesheet" type="text/css" />
        <link href="css/inventory.css" rel="stylesheet" type="text/css" />
        <link href="css/animmenu.css" rel="stylesheet" type="text/css" />
		<link href="css/countdialog.css" rel="stylesheet" type="text/css" />
        <link href="css/infodisplay.css" rel="stylesheet" type="text/css" />
        <link href="css/tooltip.css" rel="stylesheet" type="text/css" />
        <link href="css/itemmenu.css" rel="stylesheet" type="text/css" />
        <link href="css/notification.css" rel="stylesheet" type="text/css" />
    </head>
    <body>
        <ul id="hotkeyBindMenu"></ul>

        <ul id="itemMenu">
            <li id="useItem">Use Item</li>
        </ul>

        <span id="itemTooltip"></span>

        <div id="countDialog" class="dialog-container">
            <header>Transfer Amount</header>
            <input id="dialogInput" type="text" placeholder="All">
            <b id="dialogExc" style="color: red">!</b>
            <button class="dialogButton" id="dialogCancel">Cancel</button>
            <button class="dialogButton" id="dialogConfirm">Transfer</button>
        </div>


        <div id="itemTagDialog" class="dialog-container">
            <header>Item Tag</header>
            <input type="text" placeholder="Label Text" id="tagDialogInput">
            <button class="dialogButton" id="tagDialogCancel">Cancel</button>
            <button class="dialogButton" id="tagDialogConfirm">Apply</button>
        </div>

        <div id="hotbarClearZone">
            Drag here to unbind
        </div>

        <div id="notification">
            <p>Used</p>
        </div>

        <section id="hotbar">
            <div id="slot_1" control="157" class="hotbarslot"><img class="hotbarItem" src=""><label class="slotNumber">1</label></div>
            <div id="slot_2" control="158" class="hotbarslot"><img class="hotbarItem" src=""><label class="slotNumber">2</label></div>
            <div id="slot_3" control="160" class="hotbarslot"><img class="hotbarItem" src=""><label class="slotNumber">3</label></div>
            <div id="slot_4" control="164" class="hotbarslot"><img class="hotbarItem" src=""><label class="slotNumber">4</label></div>
            <div id="slot_5" control="165" class="hotbarslot"><img class="hotbarItem" src=""><label class="slotNumber">5</label></div>
            <div id="slot_6" control="159" class="hotbarslot"><img class="hotbarItem" src=""><label class="slotNumber">6</label></div>
            <div id="slot_7" control="161" class="hotbarslot"><img class="hotbarItem" src=""><label class="slotNumber">7</label></div>
            <div id="slot_8" control="162" class="hotbarslot"><img class="hotbarItem" src=""><label class="slotNumber">8</label></div>
            <div id="slot_9" control="163" class="hotbarslot"><img class="hotbarItem" src=""><label class="slotNumber">9</label></div>
        </section>

        <div id="inventory-infobar">
        </div>

        <div id="inventory-window">
            <table id="inventory-container" cellspacing="10">
                <tr>
                    <td>
						<table class="infodisplay infoLeft">
                            <tr>
                                <td class="infoIcon" rowspan="2"><img src="avatar.png"></td>
								<td id="nameDisplay"></td>
							</tr>
							<tr><td id="bankDisplay"></td></tr>
						</table>
						<table id="subjobdisplay" class="infodisplay infoRight">
                            <tr>
								<td class="infoIcon" rowspan="2"><img src="calendar.png"></td>
								<td id="dateDisplay"></td>  
							</tr>
							<tr><td id="timeDisplay"></td></tr>
                        </table>
					</td>
                    <td></td>
                    <td>
						<table id="jobdisplay" class="infodisplay infoLeft">
                            <tr>
                                <td class="infoIcon" rowspan="2"><img id="jobIcon" class="jobDisplayIcon" src=""></td>
								<td id="jobDisplay"></td>
							</tr>
							<tr><td id="jobGradeDisplay"></td></tr>
						</table>
						<table id="subjobdisplay" class="infodisplay infoRight">
                            <tr>
								<td class="infoIcon" rowspan="2"><img id="subjobIcon" class="jobDisplayIcon" src=""></td>
								<td id="subjobDisplay"></td>
							</tr>
							<tr><td id="subjobGradeDisplay"></td></tr>
                        </table>
					</td>
                </tr>
                <tr>
                    <td class="inventory-container-pane" id="inventory-container-left">
                        <table class="inventory" id="player-inventory">
                            <thead>
                                <tr>
                                    <td colspan="6" class="inventory-header">
                                        <div class="inventory-label">Player Inventory</div>
                                        <div class="copy-inventory">
                                            <span class="material-icons">content_copy</span>
                                        </div>
                                        <div class="inventory-capacity"> / 20KG</div>
                                        <div class="inventory-weight">0</div>
                                    </td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="inventory-itembar">
                                    <td class="inventoryslot staticslot">
                                        <div class="inventoryitem staticitem" label="Cash" account="money" count="0">
                                            <img src="nui://stream_graphics/data/inventory/cash.png">
                                            <label class="countLabel cashDisplay">$0</label>
                                        </div>
                                    </td>
                                    <td class="inventoryslot staticslot">
                                        <div class="inventoryitem staticitem" label="Casino Chips" account="dirty" count="0">
                                            <img src="nui://stream_graphics/data/inventory/dirty.png">
                                            <label class="countLabel dirtyDisplay">0</label>
                                        </div>
                                    </td>
                                    <td class="inventoryslot" id="test"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                </tr>
                                <tr class="inventory-itembar">
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                </tr>
                                <tr class="inventory-itembar">
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                </tr>
                                <tr class="inventory-itembar">
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                </tr>
                                <tr class="inventory-itembar">
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                    <td class="inventory-container-pane" id="inventory-container-center">
                    </td>
                    <td class="inventory-container-pane" id="inventory-container-right">
                        <table class="inventory" id="external-inventory">
                            <thead>
                                <tr>
                                    <td colspan="6" class="inventory-header">
                                        <div class="inventory-label">Proximity</div>
                                        <div class="copy-inventory">
                                            <span class="material-icons">content_copy</span>
                                        </div>
                                        <div class="inventory-capacity"> / 10KG</div>
                                        <div class="inventory-weight">0</div>
                                    </td>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="inventory-itembar">
                                    <td class="inventoryslot staticslot">
                                        <div class="inventoryitem staticitem" label="Cash" account="money" count="0">
                                            <img src="nui://stream_graphics/data/inventory/cash.png">
                                            <label class="countLabel cashDisplay">$0</label>
                                        </div>
                                    </td>
                                    <td class="inventoryslot staticslot">
                                        <div class="inventoryitem staticitem" label="Casino Chips" account="dirty" count="0">
                                            <img src="nui://stream_graphics/data/inventory/dirty.png">
                                            <label class="countLabel dirtyDisplay">0</label>
                                        </div>
                                    </td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                </tr>
                                <tr class="inventory-itembar">
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                </tr>
                                <tr class="inventory-itembar">
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                </tr>
                                <tr class="inventory-itembar">
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                </tr>
                                <tr class="inventory-itembar">
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                    <td class="inventoryslot"></td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </table>
            <div id="hotbar-visibility-container">
                <img id="hotbar-visibility" src="nui://stream_graphics/data/inventory/icons/hotbar_visible.png"></img>
            </div>
        </div>
    </body>
</html>