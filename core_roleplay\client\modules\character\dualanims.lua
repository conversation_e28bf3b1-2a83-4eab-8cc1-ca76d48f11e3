local ANIMATIONS = {
    handshake = {
        label = "Handshake",
        lib = "mp_common",
        hash = "givetake1_a"
    },
    hifive = {
        label = "High Five",
        lib = "anim@mp_player_intcelebrationmale@hifive",
        hash = "hifive"
    },
    broshake = {
        label = "Bro Shake",
        lib = "anim@mp_player_intcelebrationmale@bro_shake",
        hash = "bro_shake"
    },
    hug = {
        label = "Hug",
        lib = "mp_ped_interaction",
        hash = "hugs_guy_a"
    },
    kiss = {
        label = "Kiss",
        lib = "mp_ped_interaction",
        hash = "kisses_guy_a"
    }
}

-- Menu
local function requestDualAnim(player, animID)
    local anim = ANIMATIONS[animID]
    if not anim then return end

    TriggerServerEvent("dualanims:invitePlayerToAnim", GetPlayerServerId(player), animID)
end

local function openAnimMenu(player)
    local elements = {}

    for k, v in pairs(ANIMATIONS) do
        elements[k] = {
            displayData = {
                label = v.label
            },
            onInteract = function(player)
                requestDualAnim(player, k)
            end
        }
    end

    exports.base_interaction:OpenInteractMenu(elements, player)
end

RegisterInteraction("player", "dualanims", { label = "Animations", emoji = "🎭" },
    function(entity)
        openAnimMenu(entity)
    end, function(player)
        return not Player(GetPlayerServerId(player)).state.wounded
    end
)

-- Logic for handling invite
local requestingAnim = false
local requestingAnimID = false

RegisterNetEvent("dualanims:onAnimInvite", function(source, animID)
    requestingAnim = source
    requestingAnimID = animID

    local label = ANIMATIONS[animID].label
    TriggerEvent("fdg_ui:SendNotification", "Press E to accept " .. label, { timeout = 5000 })
    Wait(5000)

    requestingAnim = false
    requestingAnimID = false
end)

AddEventHandler("interact", function()
    if requestingAnim then
        TriggerServerEvent("dualanims:acceptAnimInvite", requestingAnim, requestingAnimID)
        requestingAnim = false
        requestingAnimID = false
    end
end)

RegisterNetEvent("dualanims:playAnim", function(animID)
    local anim = ANIMATIONS[animID]
    if not anim then return end

    Base.Animation.Start(PlayerPedId(), anim.lib, anim.hash, false, false, true)
end)
