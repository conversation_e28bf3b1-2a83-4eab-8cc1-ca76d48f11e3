-- This file contains threads to make small gameplay tweaks

-- Make the tail rotor of helicopters invincible to ensure they don't break off
-- Purpose: makes more helicopters viable in combat situations, and ensures an even playing field for all helicopters
CreateThread(function()
    while true do
        Wait(0)

        local ped = PlayerPedId()
        local veh = GetVehiclePedIsIn(ped, false)

        if DoesEntityExist(veh) then
            local model = GetEntityModel(veh)
            local class = GetVehicleClass(veh)

            if class == 15 then
                local driver = GetPedInVehicleSeat(veh, -1)
                if driver == ped then
                    SetHeliTailRotorHealth(veh, 1000.0)
                end
            end

        end
    end
end)