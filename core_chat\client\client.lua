-- TODO: Message templates
-- TODO: Markdown or raw html support for add message (will this cause issues if user types in raw html | escape?)
-- TODO: Replace add messages

--[[

  NET EVENTS AND FUNCTIONALITY

]]

-- checks if a value is a command with a leading '/'
local function isCommand(value)
  -- check valid value
  if value == nil or (value ~= nil and string.len(value) <= 1) then return false end
  -- check valid command
  return value:sub(1, 1) == "/"
end

-- gets a commands value if it has a leading '/'
local function getCommandValue(value)
  -- check valid value
  if not isCommand(value) then return nil end
  return value:sub(2)
end

local accounts = {}
local nameTargeting = GetResourceKvpInt("chat_nameTargeting") or 0

RegisterNetEvent("core_admin:updateLocalInfo", function(data)
  if nameTargeting == 0 then return end
  accounts = {}
  for i, player in pairs(data) do
    table.insert(accounts, player)
  end
end)

local function findPlayer(name)
  local playerMatches = {}
  for _, player in pairs(accounts) do
    -- find player name with partial match
    if string.find(string.lower(player.name), string.lower(name)) then
      table.insert(playerMatches, player)
      if #playerMatches > 1 then
        return TriggerEvent('chat:addMessage', "SYSTEM", { 255, 0, 0 }, "Multiple players found, please refine your search")
      end
    end
  end
  if #playerMatches == 0 then
    return TriggerEvent('chat:addMessage', "SYSTEM", { 255, 0, 0 }, "No player found")
  end
  return playerMatches
end

-- client sends message from frontend chatbox
RegisterNUICallback("chat:sendMessage", function(data, cb)
  local value = data.value or ""

  -- get command value then execute the command
  local command = getCommandValue(value)
  -- return early if no command value found
  if not command then
    if PlayerData.group == "user" then
      TriggerEvent('chat:addMessage', "Report", { 255, 0, 0 }, "Please use /report to contact staff")
    end
    return
  end

  -- execute the valid command
  -- if command has @@name then replace with player id
  local name = nil
  if nameTargeting == 1 and string.find(command, "@@") then
    name = string.match(command, "@@([^%s]+)") -- gets name after @@ stopping if there is a space
    if not name then
      return TriggerEvent('chat:addMessage', "SYSTEM", { 255, 0, 0 }, "Invalid name")
    end

    local foundPlayer = findPlayer(string.lower(name))
    if not foundPlayer then return end

    local playerID = foundPlayer[1].accountid

    -- replace command name with playerID
    command = string.gsub(command, "@@" .. name, playerID)
  end
  ExecuteCommand(command)

  cb("ok")
end)

RegisterNetEvent("core_chat:toggleNameTargeting", function()
  nameTargeting = nameTargeting == 1 and 0 or 1
  TriggerEvent('chat:addMessage', "SYSTEM", { 255, 0, 0 }, "Name targeting is now " .. (nameTargeting == 1 and "on" or "off"))
  SetResourceKvpInt("chat_nameTargeting", nameTargeting)
end)

RegisterNetEvent('chat:addMessage', function(title, titleColor, msg, systemMessage, options)
  -- create temporary table
  local message = {}

  -- print(title, titleColor, msg, systemMessage)

  -- message defaults
  message.title = title or ""
  message.value = msg or ""

  -- check type of color
  if type(titleColor) == "table" then
    message.titleColor = titleColor
  else
    message.titleColor = { 255, 255, 255 }
  end

  message.options = {}
  -- set to true by default
  message.options.category = "system"
  message.options.sanitizeTitle = false
  message.options.sanitizeValue = true
  message.options.fontSize = "normal"
  -- apply options if they exist
  if options then
    if options.category ~= nil then
      message.options.category = options.category
    end
    if options.sanitizeTitle ~= nil then
      message.options.sanitizeTitle = options.sanitizeTitle
    end
    if options.sanitizeValue ~= nil then
      message.options.sanitizeValue = options.sanitizeValue
    end
    if options.fontSize ~= nil then
      message.options.fontSize = options.fontSize
    end
  end

  -- sanitize input values
  if message.options.sanitizeTitle then
    message.title = SanitizeHtml(message.title)
  end

  if message.options.sanitizeValue then
    message.value = SanitizeHtml(message.value)
  end

  -- print(message.options.category or "none" .. " - " .. options.category or "none", message.title, message.value)

  -- send message to frontend
  SendNUIMessage({
    type = "ON_ADD_MESSAGE",
    payload = {
      message = message
    }
  })
end)

RegisterNUICallback("chat:enableCursor", function(data, cb)
  local cursorActive = data.value
  SetNuiFocus(true, cursorActive)
  cb("ok")
end)

local chatInputActive = false
RegisterNUICallback("chat:chatInputNotActive", function(data, cb)
  SetNuiFocus(false)
  chatInputActive = false
  cb("ok")
end)

local function handleAddSuggestions(suggestions)
  -- create temporary table for valid suggestions
  local validSuggestions = {}

  -- loop through all suggestions and get command values
  for index, suggestion in ipairs(suggestions) do
    local value = suggestion.name
    -- check if the value is a command
    if isCommand(value) then
      suggestion.name = getCommandValue(value)
    end
    -- add suggestions to valid suggestions table
    table.insert(validSuggestions, suggestion)
  end

  SendNUIMessage({
    type = "ON_ADD_SUGGESTIONS",
    payload = {
      suggestions = validSuggestions
    }
  })
end

RegisterNetEvent("chat:addSuggestion", function(name, help, params)
  -- print("Add suggestion ->", name, help, params)
  -- backwards compatability
  -- create a suggestion
  local suggestion = {
    name = name,
    help = help,
    params = params
  }

  -- add suggestion to a table
  local suggestions = {}
  table.insert(suggestions, suggestion)

  -- handle suggestions
  handleAddSuggestions(suggestions)
end)

RegisterNetEvent("chat:addSuggestions", handleAddSuggestions)

AddEventHandler("chat:removeSuggestion", function(name)
  if isCommand(name) then
    name = getCommandValue(name)
  end
  SendNUIMessage({
    type = "ON_REMOVE_SUGGESTION",
    payload = {
      name = name
    }
  })
end)

-- clear client chatbox
RegisterNetEvent("chat:clear", function()
  SendNUIMessage({
    type = "ON_CLEAR"
  })
end)

-- overhead typing admin command
RegisterNUICallback("chat:typingAdminCommand", function(data, cb)
  TriggerEvent("admin:overhead:macro", data.isAdminCommand)
  cb('ok')
end)

-- initialize chat data
local function initChat()
  local accountId = PlayerData.identifier
  local name = PlayerData.logName
  local group = PlayerData.group
  -- print(accountId, name, group)
  SendNUIMessage({
    type = "ON_INIT",
    payload = {
      -- client
      player = {
        id = accountId,
        name = name,
        group = group
      }
    }
  })
end

-- fetch clientside commands
local function refreshCommands()
  if GetRegisteredCommands then
    local registeredCommands = GetRegisteredCommands()
    local suggestions = {}

    for _, command in ipairs(registeredCommands) do
      if IsAceAllowed(("command.%s"):format(command.name)) then
        -- remove leading forward slash
        if command.name:sub(1, 1) == "/" then
          command.name = command.name:sub(2)
        end
        table.insert(suggestions, {
          name = command.name,
          help = ""
        })
      end
    end

    handleAddSuggestions(suggestions)
  end
end

local chatLoaded = false
local chatHidden = false

Citizen.CreateThread(function()
  SetTextChatEnabled(false)
  while true do
    Wait(0)
    -- set active input
    if not chatInputActive and chatLoaded then
      if IsControlPressed(0, 245) then
        SetNuiFocus(true)
        chatInputActive = true
        SendNUIMessage({ type = "ON_OPEN" })
      end

      local shouldBeHidden = false
      if IsScreenFadedOut() or IsPauseMenuActive() then
        shouldBeHidden = true
      end

      if (shouldBeHidden and not chatHidden) or (not shouldBeHidden and chatHidden) then
        chatHidden = shouldBeHidden
        SendNUIMessage({
          type = "ON_HIDE",
          payload = {
            chatHidden = chatHidden
          }
        })
      end
    end
  end
end)


RegisterNUICallback("chat:loaded", function(data, cb)
  chatLoaded = true
  cb('ok')
end)

RegisterNetEvent("base:characterLoaded", function()
  -- initial wait
  Wait(500)

  -- make sure chat is ready
  while chatLoaded == false do
    Wait(500)
  end

  -- initialize chat with PlayerData
  initChat()
end)

onBaseReady(function()
  -- initial wait
  Wait(500)

  -- make sure chat is ready
  while chatLoaded == false do
    Wait(500)
  end

  -- get server side commands
  TriggerServerEvent("chat:init")

  -- get clientside commands
  refreshCommands()
end)
