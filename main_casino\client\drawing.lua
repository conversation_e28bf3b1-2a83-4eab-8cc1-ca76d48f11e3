local duiList = {}
local txdList = {}
local txnList = {}
local runtimeReplacements = {}

DUI = {}

DUI.registerTxd = function(origTxd, origTxn, newTxd, newTxn, dui, interior)
	-- Create the runtime txd
	if not txdList[newTxd] then
		txdList[newTxd] = CreateRuntimeTxd(newTxd)
	end

	if not txnList[newTxd..newTxn] then
		txnList[newTxd..newTxn] = CreateRuntimeTextureFromDuiHandle(txdList[newTxd], newTxn, dui)
	end

	-- Remove any replace texture
	RemoveReplaceTexture(origTxd, origTxn)

	-- Add the replacement to the list
	table.insert(runtimeReplacements, {
		newTxd = newTxd,
		newTxn = newTxn,
		origTxd = origTxd,
		origTxn = origTxn,
		interior = interior or 0,
	})

	return #runtimeReplacements
end

Citizen.CreateThread(function()
	while true do
		Wait(500)

		local interiorId = GetInteriorFromEntity(PlayerPedId())
		for i, data in ipairs(runtimeReplacements) do
			if data.interior ~= 0 then

				-- If the player is in the interior, and the texture has not been replaced, replace it!
				if data.interior == interiorId then
					AddReplaceTexture(data.origTxd, data.origTxn, data.newTxd, data.newTxn)
					-- data.hasBeenReplaced = true
					
				-- If the player is not in the interior, and it has been replaced, remove it!
				elseif data.interior ~= interiorId then
					RemoveReplaceTexture(data.origTxd, data.origTxn)
					-- data.hasBeenReplaced = false
				end

			else--if not data.hasBeenReplaced then
				AddReplaceTexture(data.origTxd, data.origTxn, data.newTxd, data.newTxn)
				data.hasBeenReplaced = true

			end
		end
	end
end)