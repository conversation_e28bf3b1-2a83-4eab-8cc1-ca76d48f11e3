local function openColoursMenu(type, option)
    local playerPed = GetPlayerPed(-1)
    local vehicle = GetVehiclePedIsIn(playerPed, false)

    local colours = {
        { value = 0,   label = 'Black' },
        { value = 147, label = 'Carbon Black' },
        { value = 1,   label = 'Graphite' },
        { value = 11,  label = 'Anhracite Black' },
        { value = 2,   label = 'Black Steel' },
        { value = 3,   label = 'Dark Steel' },
        { value = 4,   label = 'Silver' },
        { value = 5,   label = 'Bluish Silver' },
        { value = 6,   label = 'Rolled Steel' },
        { value = 7,   label = 'Shadow Silver' },
        { value = 8,   label = 'Stone Silver' },
        { value = 9,   label = 'Midnight Silver' },
        { value = 10,  label = 'Cast Iron Silver' },
        { value = 27,  label = 'Red' },
        { value = 28,  label = 'Torino Red' },
        { value = 29,  label = 'Formula Red' },
        { value = 150, label = 'Lava Red' },
        { value = 30,  label = 'Blaze Red' },
        { value = 31,  label = 'Grace Red' },
        { value = 32,  label = 'Garnet Red' },
        { value = 33,  label = 'Sunset Red' },
        { value = 34,  label = 'Cabernet Red' },
        { value = 143, label = 'Wine Red' },
        { value = 35,  label = 'Candy Red' },
        { value = 135, label = 'Hot Pink' },
        { value = 137, label = 'Pfsiter Pink' },
        { value = 136, label = 'Salmon Pink' },
        { value = 36,  label = 'Sunrise Orange' },
        { value = 38,  label = 'Orange' },
        { value = 138, label = 'Bright Orange' },
        { value = 99,  label = 'Gold' },
        { value = 90,  label = 'Bronze' },
        { value = 88,  label = 'Yellow' },
        { value = 89,  label = 'Race Yellow' },
        { value = 91,  label = 'Dew Yellow' },
        { value = 49,  label = 'Dark Green' },
        { value = 50,  label = 'Racing Green' },
        { value = 51,  label = 'Sea Green' },
        { value = 52,  label = 'Olive Green' },
        { value = 53,  label = 'Bright Green' },
        { value = 54,  label = 'Gasoline Green' },
        { value = 92,  label = 'Lime Green' },
        { value = 141, label = 'Midnight Blue' },
        { value = 61,  label = 'Galaxy Blue' },
        { value = 62,  label = 'Dark Blue' },
        { value = 63,  label = 'Saxon Blue' },
        { value = 64,  label = 'Blue' },
        { value = 65,  label = 'Mariner Blue' },
        { value = 66,  label = 'Harbor Blue' },
        { value = 67,  label = 'Diamond Blue' },
        { value = 68,  label = 'Surf Blue' },
        { value = 69,  label = 'Nautical Blue' },
        { value = 73,  label = 'Racing Blue' },
        { value = 70,  label = 'Ultra Blue' },
        { value = 74,  label = 'Light Blue' },
        { value = 96,  label = 'Chocolate Brown' },
        { value = 101, label = 'Bison Brown' },
        { value = 95,  label = 'Creeen Brown' },
        { value = 94,  label = 'Feltzer Brown' },
        { value = 97,  label = 'Maple Brown' },
        { value = 103, label = 'Beechwood Brown' },
        { value = 104, label = 'Sienna Brown' },
        { value = 98,  label = 'Saddle Brown' },
        { value = 100, label = 'Moss Brown' },
        { value = 102, label = 'Woodbeech Brown' },
        { value = 105, label = 'Sandy Brown' },
        { value = 106, label = 'Bleached Brown' },
        { value = 71,  label = 'Schafter Purple' },
        { value = 72,  label = 'Spinnaker Purple' },
        { value = 142, label = 'Midnight Purple' },
        { value = 145, label = 'Bright Purple' },
        { value = 107, label = 'Cream' },
        { value = 111, label = 'Ice White' },
        { value = 112, label = 'Frost White' },
    }

    Base.UI.Menu.Open(
        'default', GetCurrentResourceName(), 'vehicle_colour3_changer',
        {
            title    = 'Colour Options',
            align    = 'bottom-right',
            elements = colours
        },
        function(colour, menu)
            if option == 0 then -- Primary
                local cur1, cur2 = GetVehicleColours(vehicle)
                SetVehicleColours(vehicle, colour.current.value, cur2)
            elseif option == 1 then -- Secondary
                local cur1, cur2 = GetVehicleColours(vehicle)
                SetVehicleColours(vehicle, cur1, colour.current.value)
            elseif option == 2 then -- Primary & Secondary
                SetVehicleColours(vehicle, colour.current.value, colour.current.value)
            elseif option == 3 then -- Pearl
                local cur1, cur2 = GetVehicleExtraColours(vehicle)
                SetVehicleExtraColours(vehicle, colour.current.value, cur2)
            elseif option == 4 then -- Wheels
                local cur1, cur2 = GetVehicleExtraColours(vehicle)
                SetVehicleExtraColours(vehicle, cur1, colour.current.value)
            end
        end,
        function(data, menu)
            menu.close()
        end
    )
end

local function openMatteColoursMenu(type, option)
    local playerPed = GetPlayerPed(-1)
    local vehicle = GetVehiclePedIsIn(playerPed, false)
    local cur1, cur2 = GetVehicleColours(vehicle)

    local matte = {
        { value = 12,  label = 'Black' },
        { value = 13,  label = 'Gray' },
        { value = 14,  label = 'Light Gray' },
        { value = 131, label = 'Ice White' },
        { value = 83,  label = 'Blue' },
        { value = 82,  label = 'Dark Blue' },
        { value = 84,  label = 'Midnight Blue' },
        { value = 149, label = 'Midnight Purple' },
        { value = 148, label = 'Schafter Purple' },
        { value = 39,  label = 'Red' },
        { value = 40,  label = 'Dark Red' },
        { value = 41,  label = 'Orange' },
        { value = 42,  label = 'Yellow' },
        { value = 55,  label = 'Lime Green' },
        { value = 128, label = 'Green' },
        { value = 151, label = 'Frost Green' },
        { value = 155, label = 'Foliage Green' },
        { value = 152, label = 'Olive Darb' },
        { value = 153, label = 'Dark Earth' },
        { value = 154, label = 'Desert Tan' },
    }

    Base.UI.Menu.Open(
        'default', GetCurrentResourceName(), 'vehicle_colour4_changer',
        {
            title    = 'Colour Options',
            align    = 'bottom-right',
            elements = matte
        },
        function(colour, menu)
            if option == 0 then -- Primary
                local cur1, cur2 = GetVehicleColours(vehicle)
                SetVehicleColours(vehicle, colour.current.value, cur2)
            elseif option == 1 then -- Secondary
                local cur1, cur2 = GetVehicleColours(vehicle)
                SetVehicleColours(vehicle, cur1, colour.current.value)
            elseif option == 2 then -- Primary & Secondary
                SetVehicleColours(vehicle, colour.current.value, colour.current.value)
            elseif option == 3 then -- Pearl
                local cur1, cur2 = GetVehicleExtraColours(vehicle)
                SetVehicleExtraColours(vehicle, colour.current.value, cur2)
            elseif option == 4 then -- Wheels
                local cur1, cur2 = GetVehicleExtraColours(vehicle)
                SetVehicleExtraColours(vehicle, cur1, colour.current.value)
            end
        end,
        function(data, menu)
            menu.close()
        end
    )
end

local function openColourOptionsMenu(label, option)
    local playerPed = GetPlayerPed(-1)
    local vehicle = GetVehiclePedIsIn(playerPed, false)
    local type = {
        { label = "Normal", value = 0 },
        { label = "Matte",  value = 2 },
    }


    Base.UI.Menu.Open(
        'default', GetCurrentResourceName(), 'vehicle_colour2_changer',
        {
            title    = 'Colour Options',
            align    = 'bottom-right',
            elements = type
        },
        function(data, menu)
            if data.current.value == 0 then
                openColoursMenu(data.current.value, option)
            elseif data.current.value == 2 then
                openMatteColoursMenu(data.current.value, option)
            end
        end,
        function(data, menu)
            menu.close()
        end
    )
end

local function openCarChangeMenu()
    local playerPed = GetPlayerPed(-1)
    local vehicle = GetVehiclePedIsIn(playerPed, false)

    Base.UI.Menu.Open(
        'default', GetCurrentResourceName(), 'vehicle_changer',
        {
            title    = 'Vehicle Options',
            align    = 'bottom-right',
            elements = {
                { label = "Livery", value = 1 },
                { label = "Colour", value = 2 }
            },
        },
        function(data, menu)
            if data.current.value == 1 then
                menu.close()
                local modCount = GetVehicleLiveryCount(vehicle)
                local liveries = {}
                for i = 1, modCount, 1 do
                    local realIndex = i - 1
                    local modName = GetLiveryName(vehicle, realIndex)
                    local realModName = GetLabelText(modName) or "Unknown"
                    table.insert(liveries, { label = realModName, value = realIndex })
                end

                Base.UI.Menu.Open(
                    'default', GetCurrentResourceName(), 'vehicle_livery_changer',
                    {
                        title    = 'Livery Options',
                        align    = 'bottom-right',
                        elements = liveries,
                    },
                    function(data2, menu2)
                        SetVehicleLivery(vehicle, data2.current.value)
                        SetVehicleMod(vehicle, 48, data2.current.value, false)
                    end,

                    function(data2, menu2)
                        menu2.close()
                    end
                )
            elseif data.current.value == 2 then
                menu.close()
                Base.UI.Menu.Open(
                    'default', GetCurrentResourceName(), 'vehicle_colour_changer',
                    {
                        title    = 'Colour Options',
                        align    = 'bottom-right',
                        elements = {
                            { label = "Primary Colour",             value = 0 },
                            { label = "Secondary Colour",           value = 1 },
                            { label = "Primary & Secondary Colour", value = 2 },
                            { label = "Pearl",                      value = 3 },
                            { label = "Wheel Colour",               value = 4 },
                        },
                    },
                    function(data2, menu2)
                        openColourOptionsMenu(data2.current.label, data2.current.value)
                    end,
                    function(data2, menu2)
                        menu2.close()
                    end
                )
            end
        end,
        function(data, menu)
            menu.close()
        end
    )
end

AddEventHandler("org:markerInteraction", function(type, marker)
    if type ~= "carchange" then return end
    print("Car Change Menu", marker.organisation, marker.identifier)
    openCarChangeMenu()
end)
