Base.Ped = {}

Base.Ped.registered_local_peds = {}

-- stores info on ids
local ped_creation_info = {}

-- ped store
local created_local_peds = {}

-- store for the info of what peds are currently being displayed
local peds_being_displayed = {}

Base.Ped.registerLocalPed = function(id, model, coords, heading, onEnterProx, onExitProx)
    local modelHash = ( type(model) == "string" and GetHashKey(model) or model )

    -- if the ped is being re-registered then delete the old ped
    if (created_local_peds[id]) then
        if (DoesEntityExist(created_local_peds[id])) then
            DeleteEntity(created_local_peds[id])
        end
    end

    -- check if the model exists in the gta 5 files
    if ( not IsModelInCdimage(modelHash) ) then
        print("No Model Exists by ID : " .. model)
        return
    end

    local gridCrunk = Base.Worldgrid.getCurrentChunk(coords)
    if ( Base.Ped.registered_local_peds[gridCrunk] == nil ) then
        Base.Ped.registered_local_peds[gridCrunk] = {}
    end

    Base.Ped.registered_local_peds[gridCrunk][id] = true

    ped_creation_info[id] = {
        model = model,
        coords = coords,
        heading = heading,
        onEnterProx = onEnterProx,
        onExitProx = onExitProx
    }
end

Base.Ped.deregister = function(id)
    if (created_local_peds[id]) then
        if (DoesEntityExist(created_local_peds[id])) then
            DeleteEntity(created_local_peds[id])
        end
    end

    if (peds_being_displayed[id]) then
        peds_being_displayed[id] = nil
    end

    for gridChunk, data in pairs(Base.Ped.registered_local_peds) do
        if (data[id]) then
            Base.Ped.registered_local_peds[gridChunk][id] = nil
        end
    end

    if (ped_creation_info[id]) then
        ped_creation_info[id] = nil
    end
end

local ped_doesPedExist = function(id)
    return created_local_peds[id] ~= nil
end

local ped_isPedBeingDisplayed = function(id)
    return peds_being_displayed[id] ~= nil
end

local ped_requestModel = function(model)
    if (not HasModelLoaded(model)) then
        RequestModel(model)
        while not HasModelLoaded(model) do Wait(1)
            RequestModel(model)
        end
    end
end

local ped_createLocalPedFromId = function(id)
    local data = ped_creation_info[id]
    if (data) then
        local hash = ( type(data.model) == "string" and GetHashKey(data.model) or data.model )
        ped_requestModel(hash)
        local ped = CreatePed(1, hash, data.coords, data.heading, false, false)
        SetModelAsNoLongerNeeded(hash)
        return ped
    end
end

local ped_displayPed = function(id, toggle)
    if (toggle) then
        if ( not ped_isPedBeingDisplayed(id) ) then
            peds_being_displayed[id] = true
            local ped = ped_createLocalPedFromId(id)
            local pedData = ped_creation_info[id]
            created_local_peds[id] = ped
            if (pedData) then
                if ( pedData.onEnterProx ) then
                    pedData.onEnterProx(ped)
                end
            end
        end
    else
        if ( ped_isPedBeingDisplayed(id) ) then
            local pedData = ped_creation_info[id]
            if (pedData) then
                if ( pedData.onExitProx ) then
                    pedData.onExitProx(created_local_peds[id])
                end
            end
            DeleteEntity(created_local_peds[id])
            created_local_peds[id] = nil
            peds_being_displayed[id] = nil
        end
    end
end

Citizen.CreateThread(function()
    while true do Wait(1000)
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local closeChunks = Base.Worldgrid.getNearbyChunks(coords)
        local pedsToDisplay = {}

        for _, gridChunk in pairs(closeChunks) do
            if ( Base.Ped.registered_local_peds[gridChunk] ) then
                for id in pairs(Base.Ped.registered_local_peds[gridChunk]) do
                    pedsToDisplay[id] = true
                end
            end
        end

        for id in pairs(peds_being_displayed) do
            if (not pedsToDisplay[id]) then
                ped_displayPed(id, false)
            end
        end

        for id in pairs(pedsToDisplay) do
            ped_displayPed(id, true)
        end

    end
end)