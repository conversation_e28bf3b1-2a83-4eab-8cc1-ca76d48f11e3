.tablecard{
    display: flex;
    justify-content: flex-end;
    
}

.topSection{
    float:right;
}
.tablecard >img{
    width: 125px;
    max-width: 100%;
    margin: 10px;
}

.pot{
    float: right;
    padding-right: 10px;
    color: white;
	-webkit-text-stroke: 1px black;
    font-size: 14px;
    line-height: 30px;
    display: flex;
}

.pot .amount{
    color: white;
	-webkit-text-stroke: 1px black;
    padding-left:10px;
    font-size: 30px;
    

}

.timer{
    padding-right: 10px;
    color: white;
	-webkit-text-stroke: 1px black;
    font-size: 14px;
    line-height: 30px;
    display: flex;
}

.timer .amount{
    color: white;
	-webkit-text-stroke: 1px black;
    padding-left:10px;
    font-size: 30px;
    

}

.player {
    margin-top: 1%;
}
/* 
body {
    background-image: url("https://media.discordapp.net/attachments/******************/1093140287276191905/FiveM_b2372_GTAProcess_yk7nwxXBBo.png?width=1920&height=1080");
   } */
.playerHand{
    position: fixed;
    bottom:10%;
    width: 100%;
    font-size: large

}

.bottomSection{
    position: fixed;
    right: 1%;
    bottom: 10%; 
}
.playerCardGrid{
    display: grid;
}
.playerCard{
    display: flex;
}
.playerCard >img{
    height: 100px;

}
.logmain{
    color: white;
    -webkit-text-stroke: 1px black;
    font-size: 24px; 
}
.log{
    color: white;
    -webkit-text-stroke: 1px black;
    font-size: 18px;
}
.oneline{
display: flex;
color: white;
-webkit-text-stroke: 1px black;
font-size: 16px; 
}
.money{
    color: white;
	-webkit-text-stroke: 1px black;
    font-size: 26px;
}
.seat{
    color: black;
	-webkit-text-stroke: 0.5px white;
    font-size: 22px;

}
.seat.active{
    color:red
}

.action{
    color: white;
	-webkit-text-stroke: 1px black;
    font-size: 20px;
}
.bet{
    color: white;
	-webkit-text-stroke: 1px black;
    font-size: 20px;
}
.comma{
    color: white;
	-webkit-text-stroke: 1px black;
    font-size: 20px; 
}

.dealer{
    font-size: 14px; 
    color: black;
    -webkit-text-stroke: 0.5px white;
}
.rank{
    color: white;
    text-align: center;
	-webkit-text-stroke: 1px black;
    font-size: 22px; 
}
.playerWrap{
    display: flex;
}

.infoText{
    width: 200px;
}

div { font-family: Tahoma, Verdana, Segoe, sans-serif; font-size: 24px; font-style: normal; font-variant: normal; font-weight: 700; line-height: 26.4px; } h3 { font-family: Tahoma, Verdana, Segoe, sans-serif; font-size: 14px; font-style: normal; font-variant: normal; font-weight: 700; line-height: 15.4px; } p { font-family: Tahoma, Verdana, Segoe, sans-serif; font-size: 14px; font-style: normal; font-variant: normal; font-weight: 400; line-height: 20px; } blockquote { font-family: Tahoma, Verdana, Segoe, sans-serif; font-size: 21px; font-style: normal; font-variant: normal; font-weight: 400; line-height: 30px; } pre { font-family: Tahoma, Verdana, Segoe, sans-serif; font-size: 13px; font-style: normal; font-variant: normal; font-weight: 400; line-height: 18.5714px; }