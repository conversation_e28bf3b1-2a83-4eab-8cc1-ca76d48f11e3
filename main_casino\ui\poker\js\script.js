// $( document.body ).css("display", "show");
// console.log("test")

// $("#card1").show()
// setSeat(1,"money",to<PERSON><PERSON>(0))
// setSeat(1,"bet",to<PERSON><PERSON>(0))
// setSeat(1,"action","Fold")

for (let index = 1; index < 7; index++) {
  displaySeat(index,false)
  $("#player"+index).find('span.dealer').css("display", "none");
  $("#player"+index).find('img.hand1' ).css("display", "none");
  $("#player"+index).find('img.hand2').css("display", "none");
}

for (let index = 1; index < 7; index++) {
  hideTableCard(index)
}

//dummy render 
// $( document.body ).css("display", "block");
// $("#card1").show()
// displaySeat(1,true)
// $("#player1").find('img.hand1').css("display", "flex");
// $("#player1").find('img.hand2').css("display", "flex");
// $("#player1").find('img.hand1').attr("src","playercards/"+"DummyCard"+".svg");
// $("#player1").find('img.hand2').attr("src","playercards/"+"DummyCard"+".svg");
// $("#player1").find('div.rank').text("test")
// setTableCard(1,"5Clubs");
// setTableCard(2,"2Clubs");
// setTableCard(3,"3Clubs");
// setTableCard(4,"4Clubs");
// setTableCard(5,"5Clubs");
// $("#card"+1).show()
// $("#card"+2).show()
// $("#card"+3).show()
// $("#card"+4).show()
// $("#card"+5).show()
// displaySeat(1,true)
// displaySeat(2,true)
// $("#potValue").text(toMoney(100));
// $("#player"+1).find('span.dealer').css("display", "contents");
// $("#player"+1).find('div.seat').addClass("active");
// setSeat(1,"money",toMoney(10000))
// setSeat(1,"bet",toMoney(10000))
// setSeat(1,"action","Call")
// setSeat(1,"info","Winner")
var count
let intervalID;
// console.log("test")

// console.log($("#player1").find('div.money'))
window.parent.addEventListener('message', function(event){

	var item = event.data;

	if (item.toggleDisplay == true) {
    // console.log("show")
		$( document.body ).css("display", "block");
	}

  if(item.showSeat){
    // console.log("showseat")
    console.log(item.showSeat)
  }

  if(item.roundTime){
    let count = item.roundTime.amount
    startTime(count)
  }

  if(item.newLog){
    $("#log1").text($("#logmain").text())
    for (let index = 5; index !=0; index--) {
      $("#log"+index.toString()).text($("#log"+(index-1).toString()).text())
      
    }
    $("#logmain").text(item.newLog.text)
  }

  if(item.pot){
    $("#potValue").text(toMoney(item.pot.amount));
  }

  if (item.active ){
    // console.log("show",item.active.seatNum)
    $("div").removeClass("active");
    $("#player"+item.active.seatNum).find('div.seat').addClass("active");
  }

  if (item.dealer ){
    $(".dealer").css("display", "none");
    $("#player"+item.dealer.seatNum).find('span.dealer').css("display", "contents");
  }

  if (item.showSeat?.display==true ){
    // console.log("show",item.showSeat.seatNum)
    $("#player"+item.showSeat.seatNum).css("display", "block");
  }

  if (item.showSeat?.display==false ){
    
    $("#player"+item.showSeat.seatNum).css("display", "none");

  }
  
  // console.log("test")
  if ( item.rank ){
    // console.log(item.rank)
    $("#player"+item.rank.seatNum).find('div.rank').text(item.rank.amount)
  }

  if(item.cleanUpPokerUi){
    for (let index = 1; index < 7; index++) {
      displaySeat(index,false)
      $("#player"+index).find('span.dealer').css("display", "none");
      $("#player"+index).find('img.hand1' ).css("display", "none");
      $("#player"+index).find('img.hand2').css("display", "none");
      setSeat(index,"bet",toMoney(0))
      setSeat(index,"action","")
    }
    cleanUpTable()
  }

  if (item.setHand){
    if(item.setHand.card=="hide"){
      $("#player"+item.setHand.seatNum).find('img.hand'+item.setHand.cardPos).css("display", "none");
    }else{
      $("#player"+item.setHand.seatNum).find('img.hand'+item.setHand.cardPos).css("display", "flex");
      $("#player"+item.setHand.seatNum).find('img.hand'+item.setHand.cardPos).attr("src","playercards/"+item.setHand.card+".svg");
    }
  }

	if (item.toggleDisplay == false) {
    // console.log("hide")
		$( document.body).css("display", "none");
	}

  if (item.cleanUpTable == true) {
    cleanUpTable()
	}

  if (item.setSeat){
    if(item.setSeat.action =="bet" || item.setSeat.action == "money"){
      setSeat(item.setSeat.seatNum,item.setSeat.action,toMoney(item.setSeat.amount))

    }else{
      setSeat(item.setSeat.seatNum,item.setSeat.action,item.setSeat.amount)

    }
  }

	if (item.tableCard != null) {
    // console.log(item.tableCard)
    $("#card"+item.tableCard.cardpos).show()
		setTableCard(item.tableCard.cardpos,item.tableCard.card);
	}

	if (item.status != null) {
		setStatus(item.status);
	}
});

function displaySeat(seatNum,show){
  if(show){
    $("#player"+seatNum).css("display", "block");
  }else{
    $("#player"+seatNum).css("display", "none");

  }

}

function startTime(count){
  let tempCount=count

  if (intervalID) {
    clearTimeout(intervalID);
  }
  intervalID = setTimeout(function() {
    // Code to be executed after delay

    tempCount=tempCount-1;
      if (tempCount <= 0)
      {
         clearInterval(intervalID);
         return;
      }
      // console.log(tempCount,"wtf")
     $("#timer").text(tempCount + " secs"); // watch for spelling
     startTime(tempCount)
  }, 1000);

}

function cleanUpTable(){
  // console.log("CleanupTable")
  for (let index = 1; index < 7; index++) {
    hideTableCard(index)
    setSeat(index,"money",toMoney(0))
    setSeat(index,"bet",toMoney(0))
    displaySeat(index,false)
    $("#player"+index).find('span.dealer').css("display", "none");
    $("#player"+index).find('img.hand1' ).css("display", "none");
    $("#player"+index).find('img.hand2').css("display", "none");
  }
  
}

function hideTableCard(cardNum){
  $("#card"+cardNum).hide()
}

function setTableCard(cardNum,card) {
    //playercards/9_of_clubs.svg
	$("#card"+cardNum).attr("src","playercards/"+card+".svg");
}

function setSeat(seatNum,action,value){

    $("#player"+seatNum).find('div.'+action).text(value) 

};


function toMoney(amount){
    return (amount).toLocaleString('en-US', {
        style: 'currency',
        currency: 'USD',
        maximumFractionDigits: 0, 
      })
}
