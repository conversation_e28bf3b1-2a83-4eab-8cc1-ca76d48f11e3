-- Anticheat init
AntiCheat = {}

function getAntiCheat()
    if (AntiCheat.Blacklist) then
        return AntiCheat
    end
    return nil
end

-- main
group = "user"
adminStatus = false
playerList = {}
accountIds = {}
streamer = false

local updateCooldown = false
local currentPlayerCount = 0

RegisterNetEvent("core_admin:streamer", function(toggle)
    streamer = toggle
end)

RegisterNetEvent("core_admin:updateLocalInfo", function(players)
    playerList = players
    currentPlayerCount = 0
    updateCooldown = true
    accountIds = {}
    for i, player in ipairs(playerList) do
        currentPlayerCount = currentPlayerCount + 1
        accountIds[tostring(player.source)] = player.accountid
    end
end)

CreateThread(function()
    SetDiscordAppId(621608630818570260)
    SetDiscordRichPresenceAsset("duck")
    SetDiscordRichPresenceAssetText("Los Santos Life Australia")
    SetDiscordRichPresenceAssetSmallText("www.fatduckgaming.com")
    SetRichPresence("Aussie RP Community")
end)

RegisterNetEvent('fdg:setGroup', function(g, mt)
    group = g
    adminStatus = mt
end)

-- Admin Blips Config
idKey = 118           -- But<PERSON> used to display IDs
playerNamesDist = 150 -- Range you can see player IDs
displayIDHeight = 2.0 -- Height of IDs above players head(starts at center body mass)

-- AFK Kick Config
secondsUntilKick = 1200
kickWarning = true -- Warn players if 3/4 of the Time Limit ran up

TriggerEvent('chat:addSuggestion', '/addjobblip', 'Add Job Blip',
    {
        { name = "job",   help = "Job Identifier" },
        { name = "ident", help = "Identifier" },
        { name = "type",  help = "Type of Blip" },
        { name = "label", help = "Label" },
        { name = "size",  help = "Size" },
    }
)

-- Functions
function DrawText3D(x, y, z, text, scaleSetting)
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    local px, py, pz = table.unpack(GetGameplayCamCoords())
    local dist = GetDistanceBetweenCoords(px, py, pz, x, y, z, 1)

    local scale = (1 / dist) * (scaleSetting or 2)
    local fov = (1 / GetGameplayCamFov()) * 100
    local scale = scale * fov

    if onScreen then
        SetTextScale(1 * scale, 2 * scale)
        SetTextFont(0)
        SetTextProportional(1)
        SetTextColour(255, 255, 255, 255)
        SetTextDropshadow(0, 0, 0, 0, 255)
        SetTextEdge(2, 0, 0, 0, 150)
        SetTextDropShadow()
        SetTextOutline()
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        World3dToScreen2d(x, y, z, 0) --Added Here
        DrawText(_x, _y)
    end
end

RegisterNetEvent("fdg_admin:getVehicle", function(actionType, item, count)
    local ped = GetPlayerPed(-1)
    if IsPedInAnyVehicle(ped, true) then
        local vehicle = GetVehiclePedIsUsing(ped)
        local model = GetEntityModel(vehicle)
        local plate = GetVehicleNumberPlateText(vehicle)
        TriggerServerEvent("fdg_admin:vehicleBootAction", model, plate, actionType, item, count)
    end
end)

RegisterNetEvent("core_admin:setHealth", function(value)
    local ped = GetPlayerPed(-1)
    SetEntityHealth(ped, value)
end)

RegisterNetEvent("core_admin:setArmour", function(value)
    local ped = GetPlayerPed(-1)
    SetPedArmour(ped, value)
end)

RegisterNetEvent("core_admin:resetPed", function()
    Base.Skin.setSkin(Base.Skin.getCharSkin(), false)
end)

RegisterNetEvent("core_admin:setFuel", function(fuel)
    local ped = GetPlayerPed(-1)
    local fuel = tonumber(fuel) + 0.0
    if IsPedInAnyVehicle(ped, true) then
        local vehicle = GetVehiclePedIsUsing(ped)
        exports['core_vehicles']:setVehicleFuel(vehicle, fuel)
    end
end)

RegisterNetEvent('base:characterLoaded', function(data)
    local accountid = data.identifier
    local accs = json.decode(GetResourceKvpString('core_admin:localAccounts')) or {}
    local alreadyAdded, foundAccount = false, false
    for _, v in pairs(accs) do
        if accountid ~= v then
            foundAccount = true
        else
            alreadyAdded = true
        end
    end

    if foundAccount then
        TriggerServerEvent("core_admin:localAccounts", accs)
    end

    if not alreadyAdded then
        table.insert(accs, accountid)
        SetResourceKvp('core_admin:localAccounts', json.encode(accs))
    end
end)
