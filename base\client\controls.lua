Controls = {}

Controls.disabledKeys = {}
Controls.controlList = {}

Controls.register = function(key, label, onPressed, onReleased, onHeld)
    local id = "lsla_"..string.lower(string.gsub(label, " ", ""))

    Controls.disabledKeys[key] = false

    Controls.controlList[id] = false

    RegisterCommand("+"..id, function()
        if (not Controls.disabledKeys[key]) then
            Controls.controlList[id] = true

            if onPressed then onPressed() end

            if onHeld then
                while Controls.controlList[id] and onHeld do
                    Wait(0)
                    onHeld()
                end
            end
        end
    end)

    RegisterCommand("-"..id, function()
        if (not Controls.disabledKeys[key]) then
            Controls.controlList[id] = false
            if onReleased then onReleased() end
        end
    end)

    RegisterKeyMapping("+"..id, label, "keyboard", key)
end

Controls.disable = function(key, toggle)
    TriggerEvent("controls:disable", key, toggle)
end

Controls.disableAll = function(toggle)
    TriggerEvent("controls:disableAll", toggle)
end

AddEventHandler("controls:disable", function(key, toggle)
    Controls.disabledKeys[key] = toggle
end)

AddEventHandler("controls:disableAll", function(toggle)
    for key in pairs(Controls.disabledKeys) do
        Controls.disabledKeys[key] = toggle
    end
end)