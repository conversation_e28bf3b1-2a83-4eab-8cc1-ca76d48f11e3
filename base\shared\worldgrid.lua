local OFFSET = 8192   -- Determines where the grid starts in each direction
local GRID_SIZE = 128 -- Determines the width and height of each chunk
local GRID_ROW_SIZE = math.ceil(OFFSET * 2 / GRID_SIZE)

local deltas = {
    vector2(-1, -1),
    vector2(-1, 0),
    vector2(-1, 1),
    vector2(0, -1),
    vector2(0, 0),
    vector2(0, 1),
    vector2(1, -1),
    vector2(1, 0),
    vector2(1, 1),
}

local function stringifyList(list)
    local tbl = {}
    for k, v in pairs(list) do
        tbl[tostring(k)] = v
    end
    return tbl
end

local function getGridChunk(x)
    return math.floor((x + OFFSET) / GRID_SIZE)
end

local function getChunkId(v)
    if v.x <= 0 or v.y <= 0 or v.x > OFFSET * 2 or v.y > OFFSET * 2 then
        return 0
    else
        return math.ceil((v.x * GRID_ROW_SIZE) + v.y)
    end
end

-- get the center of a chunk
-- local function getChunkCenter(chunk)
--     return vector2((chunk.x * GRID_SIZE) - OFFSET + (GRID_SIZE / 2), (chunk.y * GRID_SIZE) - OFFSET + (GRID_SIZE / 2))
-- end

---Gets the chunk ID given a vector
---@param pos vector3
---@return integer
function WorldgridGetChunk(pos)
    local chunk = vector2(getGridChunk(pos.x), getGridChunk(pos.y))
    local chunkId = getChunkId(chunk)

    return chunkId
end

---Gets nearby chunk IDs within a specified range of the player
---@param pos vector3 | vector2
---@param range number Range of nearby chunk search (max 128)
---@return table -- Array of nearby chunkIDs
function WorldgridGetNearbyChunks(pos, range)
    local nearbyChunksList = {}
    local nearbyChunks = {}

    for i = 1, #deltas do                                                           -- Get nearby chunks
        local chunkSize = pos.xy + (deltas[i] * (range or 20.0))                    -- edge size
        local chunk = vector2(getGridChunk(chunkSize.x), getGridChunk(chunkSize.y)) -- get nearby chunk
        local chunkId = getChunkId(chunk)                                           -- Get id for chunk

        if not nearbyChunksList[chunkId] then
            nearbyChunks[#nearbyChunks + 1] = chunkId
            nearbyChunksList[chunkId] = true
        end
    end

    -- Ensure the current chunk is included
    local chunkId = WorldgridGetChunk(pos)
    if not nearbyChunksList[chunkId] then
        nearbyChunks[#nearbyChunks + 1] = chunkId
        nearbyChunksList[chunkId] = true
    end

    return nearbyChunks
end

function WorldgridGetAdjacentChunks(chunk)
    local chunk = tonumber(chunk)
    if not chunk then error("Invalid chunk ID") end
    return {
        [chunk] = true,
        [chunk + 1] = true,
        [chunk - 1] = true,
        [chunk + GRID_ROW_SIZE] = true,
        [chunk + GRID_ROW_SIZE + 1] = true,
        [chunk + GRID_ROW_SIZE - 1] = true,
        [chunk - GRID_ROW_SIZE] = true,
        [chunk - GRID_ROW_SIZE + 1] = true,
        [chunk - GRID_ROW_SIZE - 1] = true,
    }
end

-- Server Code
if IsDuplicityVersion() then
    ---Returns an array of player sources within a chunk
    ---@param chunkid number
    ---@return table
    function WorldgridGetChunkPlayers(chunkid)
        return exports.base:getPlayersInChunk(chunkid)
    end

    ---Returns a list of players given a list of chunks
    ---@param chunks table List of chunk IDs
    ---@return table
    function WorldgridGetChunkListPlayers(chunks)
        local players = {}

        for _, chunk in ipairs(chunks) do
            table.merge(players, WorldgridGetChunkPlayers(chunk))
        end

        return players
    end

    ---Triggers a client event for all players within the chunk
    ---@param eventName string
    ---@param chunk number Chunk ID
    ---@param ... unknown
    function TriggerChunkClientEvent(eventName, chunk, ...)
        for _, playerId in ipairs(WorldgridGetChunkPlayers(chunk)) do
            TriggerClientEvent(eventName, playerId, ...)
        end
    end

    ---Triggers a client event for all players within a list of chunk IDs
    ---@param eventName string
    ---@param chunks table
    ---@param ... unknown
    function TriggerChunkListClientEvent(eventName, chunks, ...)
        for _, playerId in ipairs(WorldgridGetChunkListPlayers(chunks)) do
            TriggerClientEvent(eventName, playerId, ...)
        end
    end

    ---Triggers a client events for all players within nearby chunks given coords and range
    ---@param eventName string
    ---@param coords vector3
    ---@param range number
    ---@param ... unknown
    function TriggerNearbyChunkClientEvent(eventName, coords, range, ...)
        local chunks = WorldgridGetNearbyChunks(coords, range)
        TriggerChunkListClientEvent(eventName, chunks, ...)
    end

    -- Chunk Data
    function RegisterSyncedChunkData(identifier)
        local self = {}

        self.data = {}
        self.dataChunk = {}
        self.chunkKeys = {}
        self.subscribers = {}
        self.chunkSubscribers = {}

        self.set = function(key, value, coords)
            self.data[key] = value

            local chunkId = tostring(WorldgridGetChunk(coords))
            self.chunkKeys[chunkId] = self.chunkKeys[chunkId] or {}
            self.chunkKeys[chunkId][key] = true
            self.dataChunk[key] = chunkId

            if self.chunkSubscribers[chunkId] then
                for source in pairs(self.chunkSubscribers[chunkId]) do
                    TriggerClientEvent("chunkDataSet:" .. identifier, source, key, value, chunkId)
                end
            end
        end

        self.remove = function(key)
            if not self.data[key] then return end
            
            local chunkId = self.dataChunk[key]
            self.chunkKeys[chunkId] = self.chunkKeys[chunkId] or {}
            self.chunkKeys[chunkId][key] = nil
            self.data[key] = nil

            if self.chunkSubscribers[chunkId] then
                for source in pairs(self.chunkSubscribers[chunkId]) do
                    TriggerClientEvent("chunkDataRemove:" .. identifier, source, key, chunkId)
                end
            end
        end

        self.addSubscriber = function(source)
            local source = tostring(source)

            local chunks = {}
            if DoesEntityExist(GetPlayerPed(source)) then
                local coords = GetEntityCoords(GetPlayerPed(source))
                local chunk = tostring(WorldgridGetChunk(coords))
                chunks = stringifyList(WorldgridGetAdjacentChunks(chunk))

                self.subscribers[source] = chunk
                for chunk in pairs(chunks) do
                    self.chunkSubscribers[chunk] = self.chunkSubscribers[chunk] or {}
                    self.chunkSubscribers[chunk][source] = true
                end
            else
                self.subscribers[source] = 0
            end
            self.sync(source, chunks)
        end

        self.removeSubscriber = function(source)
            self.subscribers[source] = nil
            TriggerClientEvent("chunkDataClear:" .. identifier, source)
        end

        -- SYNC any of the data within the new chunks, and remove any data from the old chunks
        self.sync = function(source, chunks, removeChunks)
            local newData = {}

            for chunk in pairs(chunks) do
                if self.chunkKeys[chunk] then
                    for key, _ in pairs(self.chunkKeys[chunk]) do
                        newData[key] = { data = self.data[key], chunk = chunk }
                    end
                end
            end

            TriggerClientEvent("chunkDataSync:" .. identifier, source, newData, removeChunks)
        end

        RegisterNetEvent("chunkUpdated", function(newChunk)
            local source = tostring(source)
            if self.subscribers[source] and self.subscribers[source] ~= newChunk then
                local oldChunk = self.subscribers[source]
                local oldChunks = stringifyList(WorldgridGetAdjacentChunks(oldChunk))
                local chunks = stringifyList(WorldgridGetAdjacentChunks(newChunk))

                local removeChunks = {}
                self.subscribers[source] = newChunk

                -- remove the old chunks
                for chunk in pairs(oldChunks) do
                    if chunks[chunk] then
                        chunks[chunk] = nil
                    else
                        removeChunks[chunk] = true
                        if self.chunkSubscribers[chunk] then
                            self.chunkSubscribers[chunk][source] = nil
                        end
                    end
                end

                -- add the new chunks
                for chunk in pairs(chunks) do
                    self.chunkSubscribers[chunk] = self.chunkSubscribers[chunk] or {}
                    self.chunkSubscribers[chunk][source] = true
                end

                self.sync(source, chunks, removeChunks)
            end
        end)

        return self
    end
end

-- Client Code
if not IsDuplicityVersion() then
    function RegisterChunkDataHandler(identifier)
        local self = {}

        self.data = {}
        self.chunkKeys = {}

        -- Handlers
        self.onSet = false
        self.onRemove = false
        self.onClear = false

        local function setData(k, v, c)
            v.id = k
            v.chunk = c
            self.data[k] = v
            self.chunkKeys[c] = self.chunkKeys[c] or {}
            self.chunkKeys[c][k] = v ~= nil
            if self.onSet then self.onSet(k, v, c) end
        end

        local function removeData(k, c)
            self.data[k] = nil
            self.chunkKeys[c][k] = nil
            if self.onRemove then self.onRemove(k, c) end
        end

        local function clearChunkData(chunk)
            if self.chunkKeys[chunk] then
                for k in pairs(self.chunkKeys[chunk]) do
                    removeData(k, chunk)
                end
            end
            self.chunkKeys[chunk] = nil
        end

        RegisterNetEvent("chunkDataSync:" .. identifier, function(data, removeChunks)
            for chunk in pairs(removeChunks or {}) do
                clearChunkData(chunk)
            end

            for k, v in pairs(data or {}) do
                setData(k, v.data, v.chunk)
            end
        end)

        RegisterNetEvent("chunkDataSet:" .. identifier, function(k, v, c)
            if v ~= nil then
                setData(k, v, c)
            else
                removeData(k, c)
            end
        end)

        RegisterNetEvent("chunkDataRemove:" .. identifier, function(key, chunkId)
            self.data[key] = nil
            self.chunkKeys[chunkId][key] = nil
            if self.onRemove then self.onRemove() end
        end)

        RegisterNetEvent("chunkDataClear:" .. identifier, function()
            for k in pairs(self.data) do
                self.data[k] = nil
                if self.onRemove then self.onRemove(k) end
            end
            self.chunkKeys = {}
            if self.onClear then self.onClear() end
        end)

        return self
    end
end
