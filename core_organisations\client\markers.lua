local MARKERS = {}

local activeMarkers = {}

local function handleMarkerInteraction(marker)
    -- do stuff with the marker
    print("Interacted with Org Marker", marker.organisation, marker.type, json.encode(marker.data))
    TriggerEvent("org:markerInteraction", marker.type, marker)
end

local function createMarkers()
    for _, markerID in ipairs(activeMarkers) do
        RemoveInteractableMarker(markerID)
    end

    activeMarkers = {}

    for _, marker in pairs(MARKERS) do
        local scale = marker.scale or 1.0
        local markerID = CreateInteractableMarker(marker.coords + vector3(0, 0, 0.02), function()
            handleMarkerInteraction(marker)
        end, { text = marker.label, interactionDist = scale + 0.5 }, { scale = vector3(scale, scale, scale), instance = marker.instance })
        table.insert(activeMarkers, markerID)
    end
end

RegisterNetEvent("syncOrganisationData", function(orgs)
    MARKERS = {}

    for org, data in pairs(orgs) do
        for _, marker in pairs(data.markers) do
            MARKERS[marker.identifier] = marker
        end
    end

    createMarkers()
end)
