RESPAWN_TIMER = 500 -- how many seconds after becoming wounded before the player can respawn at hospital
HOSPITALS = {
    { label = 'Mount Zonah Medical - Rockford Hills', coords = vector3(-497.7, -342.39, 35.0),    heading = 266.39 },
    { label = 'St Fiarce Hospital - East Los Santos', coords = vector3(1152.84, -1526.66, 34.94), heading = 330.91 },
    { label = 'Sandy Shores Medical Center',          coords = vector3(1766.3, 3664.13, 34.48),  heading = 35.21 }
    --{ label = 'The Bay Care Center',                  coords = vector3(-205.15933227539, 6313.**********, 31.448699951172),  heading = 220.62 }
}

-- <PERSON><PERSON>ert config
EMS_ALERT_TIMER = 30           -- how many seconds after becoming wounded before <PERSON><PERSON> can be alerted
EMS_ALERT_UPDATE_INTERVAL = 15 -- how many seconds between updates to the EMS dispatch call

-- Revive config
HOSPITALISE_TIME = 180 -- how many seconds the player is hospitalised for when being revived

WEAPON_TYPE_DAMAGE = {
    MELEE_STAB = 2,
    MELEE_STAB_ZONE_HEAD = 1,
    GSW_LIGHT = 1,
    GSW_MEDIUM = 2,
    GSW_HEAVY = 3,
    GSW_SHOTGUN = 2,
}

zoneHP = {
    ZONE_HEAD = 2,
    ZONE_TORSO = 5,
    ZONE_LIMB = 8,
}

-- Knockout config
KNOCKOUT_TIME = 20 -- how many seconds the player is knocked out for
