local needsToHotwire = false
local needsLockpick = true
local possibleNumbers = {
    { label = "1", keyValue = 157 },
    { label = "2", keyValue = 158 },
    { label = "3", keyValue = 160 },
    { label = "4", keyValue = 164 },
    { label = "5", keyValue = 165 },
    { label = "6", keyValue = 159 },
    { label = "7", keyValue = 161 },
    { label = "8", keyValue = 162 },
    { label = "9", keyValue = 163 }
}
local detectEntry = true
local rounds = 4
local wins = 0
local keyValue = 0
local controlPressed = false
local hotwireSystem = true
local width = 0
local startAttempt = 0
local time = 0
local inMinigame = false
local clicked = false

------------------------
-- Vehicle Enter Handlers


local lastVehicle = false
hotwireSetup = false

-- NOTE: The tick system has been deprecated in favor of the new event system.
-- This code will no longer work but is kept here for reference.

-- table.insert(
--     vehicleGetInHandlers,
--     function(veh)
--         local plate = GetVehicleNumberPlateText(veh)
--         local model = GetEntityModel(veh)
--         isVehicleOwned(veh, plate, model, function(isOwner, hasKeys, startSequence, needsLockpick)
--             local state = Entity(veh).state

--             if state.hasBeenHotwired then return end
--             if state.ignoreHotwire then return end
--             if state.job_vehicle then return end
--             if state.admin_spawned then return end
--             if state.isMissionVehicle then return end
--             if veh == GetHashKey("taxi") then return end

--             needsLockpick = needsLockpick
--             if not (isOwner or hasKeys) and startSequence then

--                 if lastVehicle ~= veh then
--                     lastVehicle = veh
--                     local lockChance = math.random(4, 10) >= 6
--                     if lockChance then
--                         SetVehicleDoorsLocked(veh, 2)
--                     else
--                         if hotwireRequired() then
--                             hotwirePreConditions(veh)
--                             needsToHotwire = true
--                         else
--                             needsToHotwire = false
--                             removeHotwirePreconditions(veh)
--                         end
--                     end
--                 end
--                 return
--             end
--         end)
--     end
-- )

--- ===================================== QUI Alt Menu ========================================== ---

-- AddVehicleInteractionAction(
--     function() -- Condition
--         return (needsToHotwire)
--     end,

--     function() -- Action
--         local veh = GetVehiclePedIsIn(GetPlayerPed(-1))
--         local plate = GetVehicleNumberPlateText(veh)
--         local model = GetEntityModel(veh)
--         TriggerServerCallback("core_vehicles:attemptEnterVehicle", function(start, lockpick)
--             if not lockpick and start then
--                 startMinigame()
--             elseif lockpick then
--                 Base.Notification("You need a lockpick to do this!")
--             end
--         end, veh, plate, model)
--     end,

--     { text = "Hotwire Engine", icon = "&#xF0341;", action = "hotwire_engine"},  -- Menu Item

--     {requires = {Vehicle = true}}  -- Requires
-- )

--- ============================================================================================= ---


------------------------
-- Functions

function hotwireRequired()
    local ret = math.random(1, 10)
    return ret > 2
end

function isVehicleOwned(veh, plate, model, cb)
    TriggerServerCallback("core_vehicles:attemptEnterVehicle", function(start, lockpick)
        exports["base_vehicle"]:isOwner(veh, function(isOwner, hasKeys)
            cb(isOwner, hasKeys, start, lockpick)
        end)
    end, veh, plate, model)
end

function hotwirePreConditions(veh)
    DecorSetBool(veh, "engineForceOff", true)
    DecorSetBool(veh, "engineOn", false)
    SetVehicleEngineOn(veh, false, false, true)

    wins = 0
    attempt = 0
    detectEntry = false
    inMinigame = false

    SendNUIMessage({
        type = "chalui",
        display = false
    })
end

function removeHotwirePreconditions(veh)
    DecorSetBool(veh, "engineForceOff", false)
    DecorSetBool(veh, "engineOn", true)
    SetVehicleEngineOn(veh, true, false, true)
end

function startHotwire(data, boxMargin)
    exports["core_inventory"]:toggleKeys(false)
    exports["core_inventory"]:toggleHotbar(false)

    SendNUIMessage({
        type = "chalui",
        display = false,
        time = 0,
        text = nil,
        box = 0,
    })
    inMinigame = true
    startAttempt = startAttempt + 1

    if startAttempt == 4 then
        time = math.random(2000, 4000)
        boxMargin = math.random(50, 90)
    else
        time = math.random(3000, 7000)
    end

    width = boxMargin
    SendNUIMessage({
        type = "chalui",
        display = true,
        time = time,
        text = data.label,
        box = boxMargin,
    })

    Citizen.CreateThread(function()
        while inMinigame do
            Citizen.Wait(0)
            if (IsDisabledControlJustReleased(0, data.keyValue)) then
                SendNUIMessage({
                    type = "chalui",
                    click = true
                })
                clicked = true
                keyValue = 0
                return
            end
        end
    end)
end

RegisterNUICallback("clicked-chalbar", function(data)
    local clickedValue = (data.value):gsub("%%", "")
    local dif = tonumber(clickedValue) - 0.5

    local veh = GetVehiclePedIsIn(PlayerPedId(), false)
    if veh ~= 0 then
        if ((dif > width) and (dif < width + 10)) then
            wins = wins + 1
            Base.Notification("Progress: " .. wins .. " / " .. rounds)
            if (wins >= rounds) then
                -- If they complete 4 rounds
                wonHotwire(veh)
                TriggerServerEvent("main_crime:wonCarHotwire")
                Wait(1500)
                dispatchCall(veh, 20)
                Entity(veh).state.hasBeenHotwired = true
            else
                SendNUIMessage({
                    type = "chalui",
                    display = false,
                })
                startMinigame()
            end
        else
            Base.Notification("You lost!")
            Wait(50)
            failedHotwire(veh)
        end
    else
        resetInfo()
    end
end)

function startMinigame()
    local index = possibleNumbers[math.random(#possibleNumbers)]
    startHotwire(index, math.random(10, 90))
end

function failedHotwire(veh)
    wins = 0
    startAttempt = 0
    detectEntry = true
    inMinigame = false

    SendNUIMessage({
        type = "chalui",
        display = false,
        time = 0,
        text = nil,
        box = 0,
    })

    exports["core_inventory"]:toggleKeys(true)

    SetVehicleAlarm(veh, true)
    StartVehicleAlarm(veh)
    DecorSetBool(veh, "engineForceOff", true)
    DecorSetBool(veh, "engineOn", false)
    SetVehicleEngineOn(veh, false, false, true)
end

function dispatchCall(veh, max)
    local chance = math.random(1, 100)
    if chance < max then
        local vehModel = Base.Vehicles.GetLabel(GetEntityModel(veh))
        local vehPlate = GetVehicleNumberPlateText(veh)
        local message = "My car just got stolen! It's a " .. vehModel .. " and the plate is " .. vehPlate

        TriggerEvent("main_phone:dispatchMessage", message, "police")
    end
end

function wonHotwire(veh)
    wins = 0
    startAttempt = 0
    detectEntry = true
    inMinigame = false
    needsToHotwire = false

    SendNUIMessage({
        type = "chalui",
        display = false,
        click = false,
    })

    exports["core_inventory"]:toggleKeys(true)

    DecorSetBool(veh, "engineForceOff", false)
    DecorSetBool(veh, "engineOn", true)
    DecorSetBool(veh, "hasBeenLockpicked", true)
    SetVehicleEngineOn(veh, true, false, true)
end
