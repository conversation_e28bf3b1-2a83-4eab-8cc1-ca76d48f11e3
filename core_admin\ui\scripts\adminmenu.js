let selected = 0;
var playername;
var adminname;
var jobs;
var weapons;

let calcs = {
  hour: 60,
  day: 1440,
  week: 10080,
  month: 20160,
};

function compare(a, b) {
  if (a.id < b.id) return -1;
  if (a.id > b.id) return 1;
  return 0;
}

// Search Stuff
var current_players;

function IsLike(_first, _second) {
  _first = _first.toLowerCase();
  _second = _second.toLowerCase();
  if (_first.includes(_second)) {
    return true;
  }
  return false;
}

function UpdateSearch() {
  var search_string = $("#player-search").val();
  if (search_string != "" && search_string != " ") {
    $("#list").empty();
    for (let i in current_players) {
      let player = current_players[i];
      let id_str = player.accountid;
      if (IsLike(player.name, search_string) || IsLike(id_str.toString(), search_string)) {
        $("#list").append(
          '<div class="player" id="player_' +
            player.accountid +
            '" data="' +
            player.name +
            '">Account: ' +
            player.accountid +
            " | " +
            player.name +
            "</div>"
        );
      }
    }
  } else {
    $("#list").empty();
    for (let i in current_players) {
      let player = current_players[i];
      $("#list").append(
        '<div class="player" id="player_' +
          player.accountid +
          '" data="' +
          player.name +
          '">Account: ' +
          player.accountid +
          " | " +
          player.name +
          "</div>"
      );
    }
  }

  $(".player").click(function () {
    let id = $(this).attr("id").replace("player_", "");
    selected = parseInt(id);
    playername = $(this).attr("data");

    $(".player").removeClass("selected");
    $(this).addClass("selected");

    $("#exist")
      .find("h1")
      .text("Account ID: " + id + " | " + playername);

    $("#notexist").css("display", "none");
    $("#exist").css("display", "block");
    $("#historyList").css("display", "none");
    $("#reason").css("display", "none");
  });
}

// End Search Stuff

$(function () {
  $(document).keyup(function (e) {
    if (e.keyCode == 27) {
      $("#admin").css("display", "none");
      $("#container").css("display", "none");
      $.post("http://core_admin/close", JSON.stringify({}));
    }
  });
  window.addEventListener("message", function (event) {
    if (event.data.type == "open") {
      $("#container").css("display", "block");
      $("#notexist").css("display", "block");
      $("#exist").css("display", "none");
      $("#reason").css("display", "none");
      $("#historyList").css("display", "none");
      $("#admin").css("display", "block");
      $("#users").css("display", "block");
      $("input").val("");
      adminname = event.data.admin;
      $("#list").empty();
      $("#weaponselect").empty();
      event.data.players.sort(compare);
      $("#select_users").html("Users (" + event.data.players.length + ")");

      event.data.players.sort((a, b) => (a.accountid > b.accountid ? 1 : -1)); // Sorts the list of players
      event.data.weapons.sort((a, b) => (a.label > b.label ? 1 : -1)); // Sorts the list of weapons

      current_players = event.data.players;
      for (let i in event.data.players) {
        let player = event.data.players[i];
        $("#list").append(
          '<div class="player" id="player_' +
            player.accountid +
            '" data="' +
            player.name +
            '">Account: ' +
            player.accountid +
            " | " +
            player.name +
            "</div>"
        );
      }

      var weapon_list = document.getElementById("weaponselect");
      for (let i in event.data.weapons) {
        if (event.data.weapons[i].name != "WEAPON_UNARMED") {
          var opt = document.createElement("option");
          opt.value = event.data.weapons[i].name;
          opt.innerHTML = event.data.weapons[i].label;
          weapon_list.appendChild(opt);
        }
      }

      jobs = event.data.jobs;

      var job_names = [];
      for (var job in event.data.jobs) {
        job_names.push(job);
      }

      job_names.sort((a, b) => (a.toLowerCase() > b.toLowerCase() ? 1 : -1)); // Sorts the list of jobs

      $("#job").find("option").remove();
      for (var i in job_names) {
        $("#job").append('<option value="' + job_names[i] + '">' + job_names[i] + "</option>");
      }

      $(".player").click(function () {
        let id = $(this).attr("id").replace("player_", "");
        selected = parseInt(id);
        playername = $(this).attr("data");

        $(".player").removeClass("selected");
        $(this).addClass("selected");

        $("#exist")
          .find("h1")
          .text("Account ID: " + id + " | " + playername);

        $("#notexist").css("display", "none");
        $("#exist").css("display", "block");
        $("#historyList").css("display", "none");
        $("#reason").css("display", "none");
      });
    }
    if (event.data.type == "startspectating") {
      $("#stopSpectating").css("display", "block");
    }
    if (event.data.type == "stopspectating") {
      $("#stopSpectating").css("display", "none");
    }
  });

  $("#job").on("change", function (e) {
    var selectedJob = this.value;
    var jobGrades = jobs[selectedJob];

    jobGrades.sort((a, b) => (a.label.toLowerCase() > b.label.toLowerCase() ? 1 : -1)); // Sorts the list of jobgrades

    $("#grade").find("option").remove();
    for (let i in jobGrades) {
      var grade = jobGrades[i];
      $("#grade").append('<option value="' + grade.grade + '">' + grade.label + "</option>");
    }
  });

  $("#stopSpectating").click(() => {
    $.post("http://core_admin/stopspectating", JSON.stringify({}));
    $("#stopSpectating").css("display", "none");
  });

  $("#close").click(() => {
    $("#admin").css("display", "none");
    $("#historyList").css("display", "none");
    $("#reason").css("display", "none");
    $("#reasontype").val("");
    $("textarea#reason").val("");
    $("#spacer").css("display", "none");
    $("#users").css("display", "none");
    $("#container").css("display", "none");
    $.post("http://core_admin/close", JSON.stringify({}));
  });

  $("#select_users").click(() => {
    $("#server").css("display", "none");
    $("#users").css("display", "block");
    $("#historyList").css("display", "none");
    $("#reason").css("display", "none");
    $("#reasontype").val("");
    $("textarea#reason").val("");
    $("#spacer").css("display", "none");
    $(".tab").removeClass("selected");
    $("#select_users").addClass("selected");
    $("#select_users").blur();
  });

  $("#select_server").click(() => {
    $("#users").css("display", "none");
    $("#server").css("display", "block");
    $("#reason").css("display", "none");
    $("#historyList").css("display", "none");
    $(".tab").removeClass("selected");
    $("#select_server").addClass("selected");
    $("#select_server").blur();
  });

  $("#note").click(() => {
    $("#reason").css("display", "inline-block");
    $("#exist").css("display", "none");
    $("#reasontype").val("note");
    $("#playerid").val(selected);
    $("#playername").val(playername);
    $("#adminName").val(adminname);
    $("#reason")
      .find("h1")
      .text("NOTE " + playername);
    var d = new Date();
    if (d.getHours() < 10) {
      hr = "0" + d.getHours();
    } else {
      hr = d.getHours();
    }
    if (d.getMinutes() < 10) {
      min = "0" + d.getMinutes();
    } else {
      min = d.getMinutes();
    }
    $("#timedate").val(hr + ":" + min + " " + d.getDate() + "/" + d.getMonth() + "/" + d.getFullYear());
    $("#spacer").css("display", "block");
    $("#bandiv").css("display", "none");
  });

  $("#kick").click(() => {
    $("#reason").css("display", "inline-block");
    $("#exist").css("display", "none");
    $("#reasontype").val("kick");
    $("#playerid").val(selected);
    $("#playername").val(playername);
    $("#adminName").val(adminname);
    $("#reason")
      .find("h1")
      .text("KICK " + playername);
    var d = new Date();
    if (d.getHours() < 10) {
      hr = "0" + d.getHours();
    } else {
      hr = d.getHours();
    }
    if (d.getMinutes() < 10) {
      min = "0" + d.getMinutes();
    } else {
      min = d.getMinutes();
    }
    if (d.getDate() < 10) {
      date = "0" + d.getDate();
    } else {
      date = d.getDate();
    }
    if (d.getMonth() < 10) {
      month = "0" + (d.getMonth() + 1);
    } else {
      month = d.getMonth() + 1;
    }
    $("#timedate").val(hr + ":" + min + " " + date + "/" + month + "/" + d.getFullYear());
    $("#spacer").css("display", "block");
    $("#bandiv").css("display", "none");
  });

  $("#ban").click(() => {
    $("#reason").css("display", "inline-block");
    $("#exist").css("display", "none");
    $("#reasontype").val("ban");
    $("#playerid").val(selected);
    $("#playername").val(playername);
    $("#adminName").val(adminname);
    $("#reason")
      .find("h1")
      .text("BAN " + playername);
    var d = new Date();
    if (d.getHours() < 10) {
      hr = "0" + d.getHours();
    } else {
      hr = d.getHours();
    }
    if (d.getMinutes() < 10) {
      min = "0" + d.getMinutes();
    } else {
      min = d.getMinutes();
    }
    $("#timedate").val(hr + ":" + min + " " + d.getDate() + "/" + d.getMonth() + "/" + d.getFullYear());
    $("#spacer").css("display", "block");
    $("#bandiv").css("display", "block");
  });

  $("#warn").click(() => {
    $("#reason").css("display", "inline-block");
    $("#exist").css("display", "none");
    $("#reasontype").val("warn");
    $("#playerid").val(selected);
    $("#playername").val(playername);
    $("#adminName").val(adminname);
    $("#reason")
      .find("h1")
      .text("WARN " + playername);
    var d = new Date();
    if (d.getHours() < 10) {
      hr = "0" + d.getHours();
    } else {
      hr = d.getHours();
    }
    if (d.getMinutes() < 10) {
      min = "0" + d.getMinutes();
    } else {
      min = d.getMinutes();
    }
    $("#timedate").val(hr + ":" + min + " " + d.getDate() + "/" + d.getMonth() + "/" + d.getFullYear());
    $("#spacer").css("display", "block");
    $("#bandiv").css("display", "none");
  });

  $("#commend").click(() => {
    $("#reason").css("display", "inline-block");
    $("#exist").css("display", "none");
    $("#reasontype").val("commend");
    $("#playerid").val(selected);
    $("#playername").val(playername);
    $("#adminName").val(adminname);
    $("#reason")
      .find("h1")
      .text("COMMEND " + playername);
    var d = new Date();
    if (d.getHours() < 10) {
      hr = "0" + d.getHours();
    } else {
      hr = d.getHours();
    }
    if (d.getMinutes() < 10) {
      min = "0" + d.getMinutes();
    } else {
      min = d.getMinutes();
    }
    $("#timedate").val(hr + ":" + min + " " + d.getDate() + "/" + d.getMonth() + "/" + d.getFullYear());
    $("#spacer").css("display", "block");
    $("#bandiv").css("display", "none");
  });

  $("#commendplus").click(() => {
    $("#reason").css("display", "inline-block");
    $("#exist").css("display", "none");
    $("#reasontype").val("commendplus");
    $("#playerid").val(selected);
    $("#playername").val(playername);
    $("#adminName").val(adminname);
    $("#reason")
      .find("h1")
      .text("commendplus " + playername);
    var d = new Date();
    if (d.getHours() < 10) {
      hr = "0" + d.getHours();
    } else {
      hr = d.getHours();
    }
    if (d.getMinutes() < 10) {
      min = "0" + d.getMinutes();
    } else {
      min = d.getMinutes();
    }
    $("#timedate").val(hr + ":" + min + " " + d.getDate() + "/" + d.getMonth() + "/" + d.getFullYear());
    $("#spacer").css("display", "block");
    $("#bandiv").css("display", "none");
  });

  $("#afk").click(() => {
    $("#reason").css("display", "inline-block");
    $("#exist").css("display", "none");
    $("#reasontype").val("afk");
    $("#playerid").val(selected);
    $("#playername").val(playername);
    $("#adminName").val(adminname);
    $("#reason")
      .find("h1")
      .text("afk " + playername);
    var d = new Date();
    if (d.getHours() < 10) {
      hr = "0" + d.getHours();
    } else {
      hr = d.getHours();
    }
    if (d.getMinutes() < 10) {
      min = "0" + d.getMinutes();
    } else {
      min = d.getMinutes();
    }
    $("#timedate").val(hr + ":" + min + " " + d.getDate() + "/" + d.getMonth() + "/" + d.getFullYear());
    $("#spacer").css("display", "block");
    $("#bandiv").css("display", "none");
  });

  $("#donereason").click(function () {
    var time_set = $("#bantime").val();
    var time_scale = $("#timeselect").val();

    var _time = 0;
    if (time_set == 0 || time_set == null) {
      _time = 0;
    } else if (time_scale == "minute") {
      _time = parseInt(time_set);
    } else {
      _time = parseInt(time_set) * calcs[time_scale];
    }

    $.post(
      "http://core_admin/singleaction",
      JSON.stringify({
        id: selected,
        time: _time,
        type: $("#reasontype").val(),
        reason: $("textarea#reason").val(),
      })
    );

    $("textarea#reason").val("");
    $("#admin").css("display", "none");
    $("#reason").css("display", "none");
    $("#users").css("display", "none");
    $("#spacer").css("display", "none");
    $.post("http://core_admin/close", JSON.stringify({}));
    $(this).blur();
  });

  $("#cancelreason").click(() => {
    $("#users").css("display", "block");
    $("#exist").css("display", "inline-block");
    $("#reason").css("display", "none");
    $("#reasontype").val("");
    $("textarea#reason").val("");
    $("#spacer").css("display", "none");
  });

  $("#slay").click(function () {
    $.post("http://core_admin/singleaction", JSON.stringify({ id: selected, type: "slay" }));
    $(this).blur();
  });
  $("#revive").click(function () {
    $.post("http://core_admin/singleaction", JSON.stringify({ id: selected, type: "revive" }));
    $(this).blur();
  });
  $("#freeze").click(function () {
    $.post("http://core_admin/singleaction", JSON.stringify({ id: selected, type: "freeze" }));
    $(this).blur();
  });
  $("#bring").click(function () {
    $.post("http://core_admin/singleaction", JSON.stringify({ id: selected, type: "bring" }));
    $(this).blur();
  });
  $("#goto").click(function () {
    $.post("http://core_admin/singleaction", JSON.stringify({ id: selected, type: "gotoplayer" }));
    $(this).blur();
  });
  $("#slap").click(function () {
    $.post("http://core_admin/singleaction", JSON.stringify({ id: selected, type: "slap" }));
    $(this).blur();
  });
  $("#crash").click(function () {
    $.post("http://core_admin/singleaction", JSON.stringify({ id: selected, type: "crash" }));
    $(this).blur();
  });
  $("#info").click(function () {
    $.post("http://core_admin/singleaction", JSON.stringify({ id: selected, type: "info" }));
    $(this).blur();
  });
  $("#explode").click(function () {
    $.post("http://core_admin/singleaction", JSON.stringify({ id: selected, type: "explode" }));
    $(this).blur();
  });
  $("#spectate").click(() => {
    $.post("http://core_admin/singleaction", JSON.stringify({ id: selected, type: "spectate" }));
    $(this).blur();
    $("#admin").css("display", "none");
    $("#historyList").css("display", "none");
    $("#reason").css("display", "none");
    $("#reasontype").val("");
    $("textarea#reason").val("");
    $("#spacer").css("display", "none");
    $("#users").css("display", "none");
    $("#container").css("display", "none");
    $.post("http://core_admin/close", JSON.stringify({}));
  });

  $("#history").click(function () {
    $.post("http://core_admin/singleaction", JSON.stringify({ id: selected, type: "history" }));
    $(this).blur();
    $("#historyList").css("display", "block");
    $("#users").css("display", "block");
    $("#exist").css("display", "none");
    $("#reason").css("display", "none");
  });
  $("#setgroup").click(function () {
    $.post("http://core_admin/set", JSON.stringify({ user: selected, type: "setgroup", param: $("#newgroup").val() }));
    $(this).blur();
  });
  $("#setmoney").click(function () {
    $.post("http://core_admin/set", JSON.stringify({ user: selected, type: "setmoney", param: $("#newmoney").val() }));
    $(this).blur();
  });
  $("#givemoney").click(function () {
    $.post("http://core_admin/set", JSON.stringify({ user: selected, type: "givemoney", param: $("#newmoney").val() }));
    $(this).blur();
  });
  $("#setbank").click(function () {
    $.post("http://core_admin/set", JSON.stringify({ user: selected, type: "setbank", param: $("#newbank").val() }));
    $(this).blur();
  });
  $("#givebank").click(function () {
    $.post("http://core_admin/set", JSON.stringify({ user: selected, type: "givebank", param: $("#newbank").val() }));
    $(this).blur();
  });
  $("#setblack").click(function () {
    $.post("http://core_admin/set", JSON.stringify({ user: selected, type: "setblack", param: $("#newblack").val() }));
    $(this).blur();
  });
  $("#giveblack").click(function () {
    $.post("http://core_admin/set", JSON.stringify({ user: selected, type: "giveblack", param: $("#newblack").val() }));
    $(this).blur();
  });
  $("#giveitem").click(function () {
    $.post("http://core_admin/set", JSON.stringify({ user: selected, type: "giveitem", param: $("#newitem").val() }));
    $(this).blur();
  });
  $("#setjob").click(() => {
    var selectedJob = $("#job").children("option:selected").val();
    var selectedGrade = $("#grade").children("option:selected").val();

    if (selectedJob !== undefined && selectedGrade !== undefined) {
      $.post(
        "http://core_admin/set",
        JSON.stringify({ user: selected, type: "setjob", param: { job: selectedJob, grade: selectedGrade } })
      );
    }
  });
  $("#announce1").click(function () {
    $.post(
      "http://core_admin/set",
      JSON.stringify({ user: selected, type: "announce", param: $("#newannounce").val() })
    );
    $(this).blur();
  });

  $("button").click(function () {
    $(this).blur();
  });
  $("#backHistory").click(() => {
    $("#users").css("display", "block");
    $("#exist").css("display", "inline-block");
    $("#reason").css("display", "none");
    $("#historyList").css("display", "none");
    $("#reasontype").val("");
    $("textarea#reason").val("");
    $("#spacer").css("display", "none");
    // $('#hisList').empty();
  });

  $("#bring_all").click(() => {
    $.post("http://core_admin/singleaction", JSON.stringify({ type: "bring_all" }));
  });

  // Weapon
  $("#weaponadd").click(function () {
    var _weapon = $("#weaponselect").val();
    var _ammo = $("#weaponammo").val();
    if (_ammo > 0) {
      $.post(
        "http://core_admin/set",
        JSON.stringify({
          user: selected,
          type: "giveweapon",
          param: { weapon: _weapon, ammo: parseInt(_ammo) },
        })
      );
      $(this).blur();
    }
  });

  $("#weaponremove").click(function () {
    var _weapon = $("#weaponselect").val();

    $.post(
      "http://core_admin/set",
      JSON.stringify({
        user: selected,
        type: "removeweapon",
        param: _weapon,
      })
    );

    $(this).blur();
  });

  $("#weaponremoveall").click(function () {
    $.post(
      "http://core_admin/set",
      JSON.stringify({
        user: selected,
        type: "removeweaponall",
      })
    );

    $(this).blur();
  });
});
