(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const o of i.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&s(o)}).observe(document,{childList:!0,subtree:!0});function n(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function s(r){if(r.ep)return;r.ep=!0;const i=n(r);fetch(r.href,i)}})();/**
* @vue/shared v3.5.12
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Ds(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const q={},Pt=[],Ke=()=>{},Uo=()=>!1,Fn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Cs=e=>e.startsWith("onUpdate:"),ae=Object.assign,Ms=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Wo=Object.prototype.hasOwnProperty,U=(e,t)=>Wo.call(e,t),R=Array.isArray,$t=e=>Rn(e)==="[object Map]",Qr=e=>Rn(e)==="[object Set]",$=e=>typeof e=="function",re=e=>typeof e=="string",ut=e=>typeof e=="symbol",ee=e=>e!==null&&typeof e=="object",ei=e=>(ee(e)||$(e))&&$(e.then)&&$(e.catch),ti=Object.prototype.toString,Rn=e=>ti.call(e),Yo=e=>Rn(e).slice(8,-1),ni=e=>Rn(e)==="[object Object]",Os=e=>re(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,zt=Ds(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Pn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ko=/-(\w)/g,ct=Pn(e=>e.replace(Ko,(t,n)=>n?n.toUpperCase():"")),zo=/\B([A-Z])/g,Dt=Pn(e=>e.replace(zo,"-$1").toLowerCase()),si=Pn(e=>e.charAt(0).toUpperCase()+e.slice(1)),zn=Pn(e=>e?`on${si(e)}`:""),at=(e,t)=>!Object.is(e,t),Gn=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ri=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},Go=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let qs;const $n=()=>qs||(qs=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ee(e){if(R(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=re(s)?Xo(s):Ee(s);if(r)for(const i in r)t[i]=r[i]}return t}else if(re(e)||ee(e))return e}const Zo=/;(?![^(]*\))/g,Jo=/:([^]+)/,qo=/\/\*[^]*?\*\//g;function Xo(e){const t={};return e.replace(qo,"").split(Zo).forEach(n=>{if(n){const s=n.split(Jo);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Fe(e){let t="";if(re(e))t=e;else if(R(e))for(let n=0;n<e.length;n++){const s=Fe(e[n]);s&&(t+=s+" ")}else if(ee(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Qo="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",el=Ds(Qo);function ii(e){return!!e||e===""}const oi=e=>!!(e&&e.__v_isRef===!0),ne=e=>re(e)?e:e==null?"":R(e)||ee(e)&&(e.toString===ti||!$(e.toString))?oi(e)?ne(e.value):JSON.stringify(e,li,2):String(e),li=(e,t)=>oi(t)?li(e,t.value):$t(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],i)=>(n[Zn(s,i)+" =>"]=r,n),{})}:Qr(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Zn(n))}:ut(t)?Zn(t):ee(t)&&!R(t)&&!ni(t)?String(t):t,Zn=(e,t="")=>{var n;return ut(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.12
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let ye;class ai{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=ye,!t&&ye&&(this.index=(ye.scopes||(ye.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=ye;try{return ye=this,t()}finally{ye=n}}}on(){ye=this}off(){ye=this.parent}stop(t){if(this._active){let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.scopes)for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0,this._active=!1}}}function tl(e){return new ai(e)}function nl(){return ye}function sl(e,t=!1){ye&&ye.cleanups.push(e)}let J;const Jn=new WeakSet;class ci{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,ye&&ye.active&&ye.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Jn.has(this)&&(Jn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||fi(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Xs(this),di(this);const t=J,n=Ne;J=this,Ne=!0;try{return this.fn()}finally{hi(this),J=t,Ne=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Es(t);this.deps=this.depsTail=void 0,Xs(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Jn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){as(this)&&this.run()}get dirty(){return as(this)}}let ui=0,Gt,Zt;function fi(e,t=!1){if(e.flags|=8,t){e.next=Zt,Zt=e;return}e.next=Gt,Gt=e}function Is(){ui++}function As(){if(--ui>0)return;if(Zt){let t=Zt;for(Zt=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Gt;){let t=Gt;for(Gt=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function di(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function hi(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),Es(s),rl(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function as(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(mi(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function mi(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Qt))return;e.globalVersion=Qt;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!as(e)){e.flags&=-3;return}const n=J,s=Ne;J=e,Ne=!0;try{di(e);const r=e.fn(e._value);(t.version===0||at(r,e._value))&&(e._value=r,t.version++)}catch(r){throw t.version++,r}finally{J=n,Ne=s,hi(e),e.flags&=-3}}function Es(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let i=n.computed.deps;i;i=i.nextDep)Es(i,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function rl(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Ne=!0;const gi=[];function ft(){gi.push(Ne),Ne=!1}function dt(){const e=gi.pop();Ne=e===void 0?!0:e}function Xs(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=J;J=void 0;try{t()}finally{J=n}}}let Qt=0;class il{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Fs{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!J||!Ne||J===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==J)n=this.activeLink=new il(J,this),J.deps?(n.prevDep=J.depsTail,J.depsTail.nextDep=n,J.depsTail=n):J.deps=J.depsTail=n,pi(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=J.depsTail,n.nextDep=void 0,J.depsTail.nextDep=n,J.depsTail=n,J.deps===n&&(J.deps=s)}return n}trigger(t){this.version++,Qt++,this.notify(t)}notify(t){Is();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{As()}}}function pi(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)pi(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const wn=new WeakMap,_t=Symbol(""),cs=Symbol(""),en=Symbol("");function me(e,t,n){if(Ne&&J){let s=wn.get(e);s||wn.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new Fs),r.map=s,r.key=n),r.track()}}function tt(e,t,n,s,r,i){const o=wn.get(e);if(!o){Qt++;return}const l=a=>{a&&a.trigger()};if(Is(),t==="clear")o.forEach(l);else{const a=R(e),d=a&&Os(n);if(a&&n==="length"){const c=Number(s);o.forEach((h,g)=>{(g==="length"||g===en||!ut(g)&&g>=c)&&l(h)})}else switch((n!==void 0||o.has(void 0))&&l(o.get(n)),d&&l(o.get(en)),t){case"add":a?d&&l(o.get("length")):(l(o.get(_t)),$t(e)&&l(o.get(cs)));break;case"delete":a||(l(o.get(_t)),$t(e)&&l(o.get(cs)));break;case"set":$t(e)&&l(o.get(_t));break}}As()}function ol(e,t){const n=wn.get(e);return n&&n.get(t)}function It(e){const t=B(e);return t===e?t:(me(t,"iterate",en),Re(e)?t:t.map(ge))}function kn(e){return me(e=B(e),"iterate",en),e}const ll={__proto__:null,[Symbol.iterator](){return qn(this,Symbol.iterator,ge)},concat(...e){return It(this).concat(...e.map(t=>R(t)?It(t):t))},entries(){return qn(this,"entries",e=>(e[1]=ge(e[1]),e))},every(e,t){return Qe(this,"every",e,t,void 0,arguments)},filter(e,t){return Qe(this,"filter",e,t,n=>n.map(ge),arguments)},find(e,t){return Qe(this,"find",e,t,ge,arguments)},findIndex(e,t){return Qe(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Qe(this,"findLast",e,t,ge,arguments)},findLastIndex(e,t){return Qe(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Qe(this,"forEach",e,t,void 0,arguments)},includes(...e){return Xn(this,"includes",e)},indexOf(...e){return Xn(this,"indexOf",e)},join(e){return It(this).join(e)},lastIndexOf(...e){return Xn(this,"lastIndexOf",e)},map(e,t){return Qe(this,"map",e,t,void 0,arguments)},pop(){return Ut(this,"pop")},push(...e){return Ut(this,"push",e)},reduce(e,...t){return Qs(this,"reduce",e,t)},reduceRight(e,...t){return Qs(this,"reduceRight",e,t)},shift(){return Ut(this,"shift")},some(e,t){return Qe(this,"some",e,t,void 0,arguments)},splice(...e){return Ut(this,"splice",e)},toReversed(){return It(this).toReversed()},toSorted(e){return It(this).toSorted(e)},toSpliced(...e){return It(this).toSpliced(...e)},unshift(...e){return Ut(this,"unshift",e)},values(){return qn(this,"values",ge)}};function qn(e,t,n){const s=kn(e),r=s[t]();return s!==e&&!Re(e)&&(r._next=r.next,r.next=()=>{const i=r._next();return i.value&&(i.value=n(i.value)),i}),r}const al=Array.prototype;function Qe(e,t,n,s,r,i){const o=kn(e),l=o!==e&&!Re(e),a=o[t];if(a!==al[t]){const h=a.apply(e,i);return l?ge(h):h}let d=n;o!==e&&(l?d=function(h,g){return n.call(this,ge(h),g,e)}:n.length>2&&(d=function(h,g){return n.call(this,h,g,e)}));const c=a.call(o,d,s);return l&&r?r(c):c}function Qs(e,t,n,s){const r=kn(e);let i=n;return r!==e&&(Re(e)?n.length>3&&(i=function(o,l,a){return n.call(this,o,l,a,e)}):i=function(o,l,a){return n.call(this,o,ge(l),a,e)}),r[t](i,...s)}function Xn(e,t,n){const s=B(e);me(s,"iterate",en);const r=s[t](...n);return(r===-1||r===!1)&&$s(n[0])?(n[0]=B(n[0]),s[t](...n)):r}function Ut(e,t,n=[]){ft(),Is();const s=B(e)[t].apply(e,n);return As(),dt(),s}const cl=Ds("__proto__,__v_isRef,__isVue"),vi=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(ut));function ul(e){ut(e)||(e=String(e));const t=B(this);return me(t,"has",e),t.hasOwnProperty(e)}class yi{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){const r=this._isReadonly,i=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return i;if(n==="__v_raw")return s===(r?i?_l:wi:i?xi:_i).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=R(t);if(!r){let a;if(o&&(a=ll[n]))return a;if(n==="hasOwnProperty")return ul}const l=Reflect.get(t,n,oe(t)?t:s);return(ut(n)?vi.has(n):cl(n))||(r||me(t,"get",n),i)?l:oe(l)?o&&Os(n)?l:l.value:ee(l)?r?Ln(l):Ae(l):l}}class bi extends yi{constructor(t=!1){super(!1,t)}set(t,n,s,r){let i=t[n];if(!this._isShallow){const a=St(i);if(!Re(s)&&!St(s)&&(i=B(i),s=B(s)),!R(t)&&oe(i)&&!oe(s))return a?!1:(i.value=s,!0)}const o=R(t)&&Os(n)?Number(n)<t.length:U(t,n),l=Reflect.set(t,n,s,oe(t)?t:r);return t===B(r)&&(o?at(s,i)&&tt(t,"set",n,s):tt(t,"add",n,s)),l}deleteProperty(t,n){const s=U(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&tt(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!ut(n)||!vi.has(n))&&me(t,"has",n),s}ownKeys(t){return me(t,"iterate",R(t)?"length":_t),Reflect.ownKeys(t)}}class fl extends yi{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const dl=new bi,hl=new fl,ml=new bi(!0);const us=e=>e,dn=e=>Reflect.getPrototypeOf(e);function gl(e,t,n){return function(...s){const r=this.__v_raw,i=B(r),o=$t(i),l=e==="entries"||e===Symbol.iterator&&o,a=e==="keys"&&o,d=r[e](...s),c=n?us:t?fs:ge;return!t&&me(i,"iterate",a?cs:_t),{next(){const{value:h,done:g}=d.next();return g?{value:h,done:g}:{value:l?[c(h[0]),c(h[1])]:c(h),done:g}},[Symbol.iterator](){return this}}}}function hn(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function pl(e,t){const n={get(r){const i=this.__v_raw,o=B(i),l=B(r);e||(at(r,l)&&me(o,"get",r),me(o,"get",l));const{has:a}=dn(o),d=t?us:e?fs:ge;if(a.call(o,r))return d(i.get(r));if(a.call(o,l))return d(i.get(l));i!==o&&i.get(r)},get size(){const r=this.__v_raw;return!e&&me(B(r),"iterate",_t),Reflect.get(r,"size",r)},has(r){const i=this.__v_raw,o=B(i),l=B(r);return e||(at(r,l)&&me(o,"has",r),me(o,"has",l)),r===l?i.has(r):i.has(r)||i.has(l)},forEach(r,i){const o=this,l=o.__v_raw,a=B(l),d=t?us:e?fs:ge;return!e&&me(a,"iterate",_t),l.forEach((c,h)=>r.call(i,d(c),d(h),o))}};return ae(n,e?{add:hn("add"),set:hn("set"),delete:hn("delete"),clear:hn("clear")}:{add(r){!t&&!Re(r)&&!St(r)&&(r=B(r));const i=B(this);return dn(i).has.call(i,r)||(i.add(r),tt(i,"add",r,r)),this},set(r,i){!t&&!Re(i)&&!St(i)&&(i=B(i));const o=B(this),{has:l,get:a}=dn(o);let d=l.call(o,r);d||(r=B(r),d=l.call(o,r));const c=a.call(o,r);return o.set(r,i),d?at(i,c)&&tt(o,"set",r,i):tt(o,"add",r,i),this},delete(r){const i=B(this),{has:o,get:l}=dn(i);let a=o.call(i,r);a||(r=B(r),a=o.call(i,r)),l&&l.call(i,r);const d=i.delete(r);return a&&tt(i,"delete",r,void 0),d},clear(){const r=B(this),i=r.size!==0,o=r.clear();return i&&tt(r,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=gl(r,e,t)}),n}function Rs(e,t){const n=pl(e,t);return(s,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(U(n,r)&&r in s?n:s,r,i)}const vl={get:Rs(!1,!1)},yl={get:Rs(!1,!0)},bl={get:Rs(!0,!1)};const _i=new WeakMap,xi=new WeakMap,wi=new WeakMap,_l=new WeakMap;function xl(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function wl(e){return e.__v_skip||!Object.isExtensible(e)?0:xl(Yo(e))}function Ae(e){return St(e)?e:Ps(e,!1,dl,vl,_i)}function Sl(e){return Ps(e,!1,ml,yl,xi)}function Ln(e){return Ps(e,!0,hl,bl,wi)}function Ps(e,t,n,s,r){if(!ee(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const o=wl(e);if(o===0)return e;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function kt(e){return St(e)?kt(e.__v_raw):!!(e&&e.__v_isReactive)}function St(e){return!!(e&&e.__v_isReadonly)}function Re(e){return!!(e&&e.__v_isShallow)}function $s(e){return e?!!e.__v_raw:!1}function B(e){const t=e&&e.__v_raw;return t?B(t):e}function Tl(e){return!U(e,"__v_skip")&&Object.isExtensible(e)&&ri(e,"__v_skip",!0),e}const ge=e=>ee(e)?Ae(e):e,fs=e=>ee(e)?Ln(e):e;function oe(e){return e?e.__v_isRef===!0:!1}function Se(e){return Si(e,!1)}function nt(e){return Si(e,!0)}function Si(e,t){return oe(e)?e:new Dl(e,t)}class Dl{constructor(t,n){this.dep=new Fs,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:B(t),this._value=n?t:ge(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||Re(t)||St(t);t=s?t:B(t),at(t,n)&&(this._rawValue=t,this._value=s?t:ge(t),this.dep.trigger())}}function Ti(e){return oe(e)?e.value:e}const Cl={get:(e,t,n)=>t==="__v_raw"?e:Ti(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return oe(r)&&!oe(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function Di(e){return kt(e)?e:new Proxy(e,Cl)}function Ci(e){const t=R(e)?new Array(e.length):{};for(const n in e)t[n]=Mi(e,n);return t}class Ml{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return ol(B(this._object),this._key)}}class Ol{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function Il(e,t,n){return oe(e)?e:$(e)?new Ol(e):ee(e)&&arguments.length>1?Mi(e,t,n):Se(e)}function Mi(e,t,n){const s=e[t];return oe(s)?s:new Ml(e,t,n)}class Al{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Fs(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Qt-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&J!==this)return fi(this,!0),!0}get value(){const t=this.dep.track();return mi(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function El(e,t,n=!1){let s,r;return $(e)?s=e:(s=e.get,r=e.set),new Al(s,r,n)}const mn={},Sn=new WeakMap;let bt;function Fl(e,t=!1,n=bt){if(n){let s=Sn.get(n);s||Sn.set(n,s=[]),s.push(e)}}function Rl(e,t,n=q){const{immediate:s,deep:r,once:i,scheduler:o,augmentJob:l,call:a}=n,d=E=>r?E:Re(E)||r===!1||r===0?lt(E,1):lt(E);let c,h,g,p,T=!1,x=!1;if(oe(e)?(h=()=>e.value,T=Re(e)):kt(e)?(h=()=>d(e),T=!0):R(e)?(x=!0,T=e.some(E=>kt(E)||Re(E)),h=()=>e.map(E=>{if(oe(E))return E.value;if(kt(E))return d(E);if($(E))return a?a(E,2):E()})):$(e)?t?h=a?()=>a(e,2):e:h=()=>{if(g){ft();try{g()}finally{dt()}}const E=bt;bt=c;try{return a?a(e,3,[p]):e(p)}finally{bt=E}}:h=Ke,t&&r){const E=h,se=r===!0?1/0:r;h=()=>lt(E(),se)}const I=nl(),P=()=>{c.stop(),I&&Ms(I.effects,c)};if(i&&t){const E=t;t=(...se)=>{E(...se),P()}}let V=x?new Array(e.length).fill(mn):mn;const X=E=>{if(!(!(c.flags&1)||!c.dirty&&!E))if(t){const se=c.run();if(r||T||(x?se.some((W,te)=>at(W,V[te])):at(se,V))){g&&g();const W=bt;bt=c;try{const te=[se,V===mn?void 0:x&&V[0]===mn?[]:V,p];a?a(t,3,te):t(...te),V=se}finally{bt=W}}}else c.run()};return l&&l(X),c=new ci(h),c.scheduler=o?()=>o(X,!1):X,p=E=>Fl(E,!1,c),g=c.onStop=()=>{const E=Sn.get(c);if(E){if(a)a(E,4);else for(const se of E)se();Sn.delete(c)}},t?s?X(!0):V=c.run():o?o(X.bind(null,!0),!0):c.run(),P.pause=c.pause.bind(c),P.resume=c.resume.bind(c),P.stop=P,P}function lt(e,t=1/0,n){if(t<=0||!ee(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,oe(e))lt(e.value,t,n);else if(R(e))for(let s=0;s<e.length;s++)lt(e[s],t,n);else if(Qr(e)||$t(e))e.forEach(s=>{lt(s,t,n)});else if(ni(e)){for(const s in e)lt(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&lt(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.12
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function ln(e,t,n,s){try{return s?e(...s):e()}catch(r){Nn(r,t,n)}}function Ze(e,t,n,s){if($(e)){const r=ln(e,t,n,s);return r&&ei(r)&&r.catch(i=>{Nn(i,t,n)}),r}if(R(e)){const r=[];for(let i=0;i<e.length;i++)r.push(Ze(e[i],t,n,s));return r}}function Nn(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||q;if(t){let l=t.parent;const a=t.proxy,d=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let h=0;h<c.length;h++)if(c[h](e,a,d)===!1)return}l=l.parent}if(i){ft(),ln(i,null,10,[e,a,d]),dt();return}}Pl(e,n,r,s,o)}function Pl(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const be=[];let Ue=-1;const Lt=[];let it=null,Ft=0;const Oi=Promise.resolve();let Tn=null;function Ii(e){const t=Tn||Oi;return e?t.then(this?e.bind(this):e):t}function $l(e){let t=Ue+1,n=be.length;for(;t<n;){const s=t+n>>>1,r=be[s],i=tn(r);i<e||i===e&&r.flags&2?t=s+1:n=s}return t}function ks(e){if(!(e.flags&1)){const t=tn(e),n=be[be.length-1];!n||!(e.flags&2)&&t>=tn(n)?be.push(e):be.splice($l(t),0,e),e.flags|=1,Ai()}}function Ai(){Tn||(Tn=Oi.then(Fi))}function kl(e){R(e)?Lt.push(...e):it&&e.id===-1?it.splice(Ft+1,0,e):e.flags&1||(Lt.push(e),e.flags|=1),Ai()}function er(e,t,n=Ue+1){for(;n<be.length;n++){const s=be[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;be.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function Ei(e){if(Lt.length){const t=[...new Set(Lt)].sort((n,s)=>tn(n)-tn(s));if(Lt.length=0,it){it.push(...t);return}for(it=t,Ft=0;Ft<it.length;Ft++){const n=it[Ft];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}it=null,Ft=0}}const tn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Fi(e){try{for(Ue=0;Ue<be.length;Ue++){const t=be[Ue];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),ln(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Ue<be.length;Ue++){const t=be[Ue];t&&(t.flags&=-2)}Ue=-1,be.length=0,Ei(),Tn=null,(be.length||Lt.length)&&Fi()}}let ke=null,Ri=null;function Dn(e){const t=ke;return ke=e,Ri=e&&e.type.__scopeId||null,t}function ds(e,t=ke,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&ar(-1);const i=Dn(t);let o;try{o=e(...r)}finally{Dn(i),s._d&&ar(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function pt(e,t,n,s){const r=e.dirs,i=t&&t.dirs;for(let o=0;o<r.length;o++){const l=r[o];i&&(l.oldValue=i[o].value);let a=l.dir[s];a&&(ft(),Ze(a,n,8,[e.el,l,e,t]),dt())}}const Ll=Symbol("_vte"),Nl=e=>e.__isTeleport;function Ls(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Ls(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Hl(e,t){return $(e)?ae({name:e.name},t,{setup:e}):e}function Pi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function hs(e,t,n,s,r=!1){if(R(e)){e.forEach((T,x)=>hs(T,t&&(R(t)?t[x]:t),n,s,r));return}if(Jt(s)&&!r)return;const i=s.shapeFlag&4?Us(s.component):s.el,o=r?null:i,{i:l,r:a}=e,d=t&&t.r,c=l.refs===q?l.refs={}:l.refs,h=l.setupState,g=B(h),p=h===q?()=>!1:T=>U(g,T);if(d!=null&&d!==a&&(re(d)?(c[d]=null,p(d)&&(h[d]=null)):oe(d)&&(d.value=null)),$(a))ln(a,l,12,[o,c]);else{const T=re(a),x=oe(a);if(T||x){const I=()=>{if(e.f){const P=T?p(a)?h[a]:c[a]:a.value;r?R(P)&&Ms(P,i):R(P)?P.includes(i)||P.push(i):T?(c[a]=[i],p(a)&&(h[a]=c[a])):(a.value=[i],e.k&&(c[e.k]=a.value))}else T?(c[a]=o,p(a)&&(h[a]=o)):x&&(a.value=o,e.k&&(c[e.k]=o))};o?(I.id=-1,Oe(I,n)):I()}}}$n().requestIdleCallback;$n().cancelIdleCallback;const Jt=e=>!!e.type.__asyncLoader,$i=e=>e.type.__isKeepAlive;function Vl(e,t){ki(e,"a",t)}function Bl(e,t){ki(e,"da",t)}function ki(e,t,n=pe){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Hn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)$i(r.parent.vnode)&&jl(s,t,n,r),r=r.parent}}function jl(e,t,n,s){const r=Hn(t,e,s,!0);Ni(()=>{Ms(s[t],r)},n)}function Hn(e,t,n=pe,s=!1){if(n){const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{ft();const l=cn(n),a=Ze(t,n,e,o);return l(),dt(),a});return s?r.unshift(i):r.push(i),i}}const rt=e=>(t,n=pe)=>{(!sn||e==="sp")&&Hn(e,(...s)=>t(...s),n)},Ul=rt("bm"),Ns=rt("m"),Wl=rt("bu"),Yl=rt("u"),Li=rt("bum"),Ni=rt("um"),Kl=rt("sp"),zl=rt("rtg"),Gl=rt("rtc");function Zl(e,t=pe){Hn("ec",e,t)}const Jl=Symbol.for("v-ndc");function yn(e,t,n,s){let r;const i=n,o=R(e);if(o||re(e)){const l=o&&kt(e);let a=!1;l&&(a=!Re(e),e=kn(e)),r=new Array(e.length);for(let d=0,c=e.length;d<c;d++)r[d]=t(a?ge(e[d]):e[d],d,void 0,i)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,i)}else if(ee(e))if(e[Symbol.iterator])r=Array.from(e,(l,a)=>t(l,a,void 0,i));else{const l=Object.keys(e);r=new Array(l.length);for(let a=0,d=l.length;a<d;a++){const c=l[a];r[a]=t(e[c],c,a,i)}}else r=[];return r}const ms=e=>e?io(e)?Us(e):ms(e.parent):null,qt=ae(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ms(e.parent),$root:e=>ms(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Hs(e),$forceUpdate:e=>e.f||(e.f=()=>{ks(e.update)}),$nextTick:e=>e.n||(e.n=Ii.bind(e.proxy)),$watch:e=>va.bind(e)}),Qn=(e,t)=>e!==q&&!e.__isScriptSetup&&U(e,t),ql={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:i,accessCache:o,type:l,appContext:a}=e;let d;if(t[0]!=="$"){const p=o[t];if(p!==void 0)switch(p){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(Qn(s,t))return o[t]=1,s[t];if(r!==q&&U(r,t))return o[t]=2,r[t];if((d=e.propsOptions[0])&&U(d,t))return o[t]=3,i[t];if(n!==q&&U(n,t))return o[t]=4,n[t];gs&&(o[t]=0)}}const c=qt[t];let h,g;if(c)return t==="$attrs"&&me(e.attrs,"get",""),c(e);if((h=l.__cssModules)&&(h=h[t]))return h;if(n!==q&&U(n,t))return o[t]=4,n[t];if(g=a.config.globalProperties,U(g,t))return g[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:i}=e;return Qn(r,t)?(r[t]=n,!0):s!==q&&U(s,t)?(s[t]=n,!0):U(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:i}},o){let l;return!!n[o]||e!==q&&U(e,o)||Qn(t,o)||(l=i[0])&&U(l,o)||U(s,o)||U(qt,o)||U(r.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:U(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function tr(e){return R(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let gs=!0;function Xl(e){const t=Hs(e),n=e.proxy,s=e.ctx;gs=!1,t.beforeCreate&&nr(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:o,watch:l,provide:a,inject:d,created:c,beforeMount:h,mounted:g,beforeUpdate:p,updated:T,activated:x,deactivated:I,beforeDestroy:P,beforeUnmount:V,destroyed:X,unmounted:E,render:se,renderTracked:W,renderTriggered:te,errorCaptured:j,serverPrefetch:ie,expose:Q,inheritAttrs:ue,components:Te,directives:Ve,filters:qe}=t;if(d&&Ql(d,s,null),o)for(const G in o){const Y=o[G];$(Y)&&(s[G]=Y.bind(n))}if(r){const G=r.call(n,n);ee(G)&&(e.data=Ae(G))}if(gs=!0,i)for(const G in i){const Y=i[G],Pe=$(Y)?Y.bind(n,n):$(Y.get)?Y.get.bind(n,n):Ke,Ct=!$(Y)&&$(Y.set)?Y.set.bind(n):Ke,Xe=N({get:Pe,set:Ct});Object.defineProperty(s,G,{enumerable:!0,configurable:!0,get:()=>Xe.value,set:de=>Xe.value=de})}if(l)for(const G in l)Hi(l[G],s,n,G);if(a){const G=$(a)?a.call(n):a;Reflect.ownKeys(G).forEach(Y=>{Vn(Y,G[Y])})}c&&nr(c,e,"c");function fe(G,Y){R(Y)?Y.forEach(Pe=>G(Pe.bind(n))):Y&&G(Y.bind(n))}if(fe(Ul,h),fe(Ns,g),fe(Wl,p),fe(Yl,T),fe(Vl,x),fe(Bl,I),fe(Zl,j),fe(Gl,W),fe(zl,te),fe(Li,V),fe(Ni,E),fe(Kl,ie),R(Q))if(Q.length){const G=e.exposed||(e.exposed={});Q.forEach(Y=>{Object.defineProperty(G,Y,{get:()=>n[Y],set:Pe=>n[Y]=Pe})})}else e.exposed||(e.exposed={});se&&e.render===Ke&&(e.render=se),ue!=null&&(e.inheritAttrs=ue),Te&&(e.components=Te),Ve&&(e.directives=Ve),ie&&Pi(e)}function Ql(e,t,n=Ke){R(e)&&(e=ps(e));for(const s in e){const r=e[s];let i;ee(r)?"default"in r?i=ze(r.from||s,r.default,!0):i=ze(r.from||s):i=ze(r),oe(i)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>i.value,set:o=>i.value=o}):t[s]=i}}function nr(e,t,n){Ze(R(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Hi(e,t,n,s){let r=s.includes(".")?Xi(n,s):()=>n[s];if(re(e)){const i=t[e];$(i)&&Ge(r,i)}else if($(e))Ge(r,e.bind(n));else if(ee(e))if(R(e))e.forEach(i=>Hi(i,t,n,s));else{const i=$(e.handler)?e.handler.bind(n):t[e.handler];$(i)&&Ge(r,i,e)}}function Hs(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,l=i.get(t);let a;return l?a=l:!r.length&&!n&&!s?a=t:(a={},r.length&&r.forEach(d=>Cn(a,d,o,!0)),Cn(a,t,o)),ee(t)&&i.set(t,a),a}function Cn(e,t,n,s=!1){const{mixins:r,extends:i}=t;i&&Cn(e,i,n,!0),r&&r.forEach(o=>Cn(e,o,n,!0));for(const o in t)if(!(s&&o==="expose")){const l=ea[o]||n&&n[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const ea={data:sr,props:rr,emits:rr,methods:Yt,computed:Yt,beforeCreate:ve,created:ve,beforeMount:ve,mounted:ve,beforeUpdate:ve,updated:ve,beforeDestroy:ve,beforeUnmount:ve,destroyed:ve,unmounted:ve,activated:ve,deactivated:ve,errorCaptured:ve,serverPrefetch:ve,components:Yt,directives:Yt,watch:na,provide:sr,inject:ta};function sr(e,t){return t?e?function(){return ae($(e)?e.call(this,this):e,$(t)?t.call(this,this):t)}:t:e}function ta(e,t){return Yt(ps(e),ps(t))}function ps(e){if(R(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ve(e,t){return e?[...new Set([].concat(e,t))]:t}function Yt(e,t){return e?ae(Object.create(null),e,t):t}function rr(e,t){return e?R(e)&&R(t)?[...new Set([...e,...t])]:ae(Object.create(null),tr(e),tr(t??{})):t}function na(e,t){if(!e)return t;if(!t)return e;const n=ae(Object.create(null),e);for(const s in t)n[s]=ve(e[s],t[s]);return n}function Vi(){return{app:null,config:{isNativeTag:Uo,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let sa=0;function ra(e,t){return function(s,r=null){$(s)||(s=ae({},s)),r!=null&&!ee(r)&&(r=null);const i=Vi(),o=new WeakSet,l=[];let a=!1;const d=i.app={_uid:sa++,_component:s,_props:r,_container:null,_context:i,_instance:null,version:Na,get config(){return i.config},set config(c){},use(c,...h){return o.has(c)||(c&&$(c.install)?(o.add(c),c.install(d,...h)):$(c)&&(o.add(c),c(d,...h))),d},mixin(c){return i.mixins.includes(c)||i.mixins.push(c),d},component(c,h){return h?(i.components[c]=h,d):i.components[c]},directive(c,h){return h?(i.directives[c]=h,d):i.directives[c]},mount(c,h,g){if(!a){const p=d._ceVNode||k(s,r);return p.appContext=i,g===!0?g="svg":g===!1&&(g=void 0),h&&t?t(p,c):e(p,c,g),a=!0,d._container=c,c.__vue_app__=d,Us(p.component)}},onUnmount(c){l.push(c)},unmount(){a&&(Ze(l,d._instance,16),e(null,d._container),delete d._container.__vue_app__)},provide(c,h){return i.provides[c]=h,d},runWithContext(c){const h=Nt;Nt=d;try{return c()}finally{Nt=h}}};return d}}let Nt=null;function Vn(e,t){if(pe){let n=pe.provides;const s=pe.parent&&pe.parent.provides;s===n&&(n=pe.provides=Object.create(s)),n[e]=t}}function ze(e,t,n=!1){const s=pe||ke;if(s||Nt){const r=Nt?Nt._context.provides:s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&$(t)?t.call(s&&s.proxy):t}}const Bi={},ji=()=>Object.create(Bi),Ui=e=>Object.getPrototypeOf(e)===Bi;function ia(e,t,n,s=!1){const r={},i=ji();e.propsDefaults=Object.create(null),Wi(e,t,r,i);for(const o in e.propsOptions[0])o in r||(r[o]=void 0);n?e.props=s?r:Sl(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function oa(e,t,n,s){const{props:r,attrs:i,vnode:{patchFlag:o}}=e,l=B(r),[a]=e.propsOptions;let d=!1;if((s||o>0)&&!(o&16)){if(o&8){const c=e.vnode.dynamicProps;for(let h=0;h<c.length;h++){let g=c[h];if(jn(e.emitsOptions,g))continue;const p=t[g];if(a)if(U(i,g))p!==i[g]&&(i[g]=p,d=!0);else{const T=ct(g);r[T]=vs(a,l,T,p,e,!1)}else p!==i[g]&&(i[g]=p,d=!0)}}}else{Wi(e,t,r,i)&&(d=!0);let c;for(const h in l)(!t||!U(t,h)&&((c=Dt(h))===h||!U(t,c)))&&(a?n&&(n[h]!==void 0||n[c]!==void 0)&&(r[h]=vs(a,l,h,void 0,e,!0)):delete r[h]);if(i!==l)for(const h in i)(!t||!U(t,h))&&(delete i[h],d=!0)}d&&tt(e.attrs,"set","")}function Wi(e,t,n,s){const[r,i]=e.propsOptions;let o=!1,l;if(t)for(let a in t){if(zt(a))continue;const d=t[a];let c;r&&U(r,c=ct(a))?!i||!i.includes(c)?n[c]=d:(l||(l={}))[c]=d:jn(e.emitsOptions,a)||(!(a in s)||d!==s[a])&&(s[a]=d,o=!0)}if(i){const a=B(n),d=l||q;for(let c=0;c<i.length;c++){const h=i[c];n[h]=vs(r,a,h,d[h],e,!U(d,h))}}return o}function vs(e,t,n,s,r,i){const o=e[n];if(o!=null){const l=U(o,"default");if(l&&s===void 0){const a=o.default;if(o.type!==Function&&!o.skipFactory&&$(a)){const{propsDefaults:d}=r;if(n in d)s=d[n];else{const c=cn(r);s=d[n]=a.call(null,t),c()}}else s=a;r.ce&&r.ce._setProp(n,s)}o[0]&&(i&&!l?s=!1:o[1]&&(s===""||s===Dt(n))&&(s=!0))}return s}const la=new WeakMap;function Yi(e,t,n=!1){const s=n?la:t.propsCache,r=s.get(e);if(r)return r;const i=e.props,o={},l=[];let a=!1;if(!$(e)){const c=h=>{a=!0;const[g,p]=Yi(h,t,!0);ae(o,g),p&&l.push(...p)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!i&&!a)return ee(e)&&s.set(e,Pt),Pt;if(R(i))for(let c=0;c<i.length;c++){const h=ct(i[c]);ir(h)&&(o[h]=q)}else if(i)for(const c in i){const h=ct(c);if(ir(h)){const g=i[c],p=o[h]=R(g)||$(g)?{type:g}:ae({},g),T=p.type;let x=!1,I=!0;if(R(T))for(let P=0;P<T.length;++P){const V=T[P],X=$(V)&&V.name;if(X==="Boolean"){x=!0;break}else X==="String"&&(I=!1)}else x=$(T)&&T.name==="Boolean";p[0]=x,p[1]=I,(x||U(p,"default"))&&l.push(h)}}const d=[o,l];return ee(e)&&s.set(e,d),d}function ir(e){return e[0]!=="$"&&!zt(e)}const Ki=e=>e[0]==="_"||e==="$stable",Vs=e=>R(e)?e.map(We):[We(e)],aa=(e,t,n)=>{if(t._n)return t;const s=ds((...r)=>Vs(t(...r)),n);return s._c=!1,s},zi=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Ki(r))continue;const i=e[r];if($(i))t[r]=aa(r,i,s);else if(i!=null){const o=Vs(i);t[r]=()=>o}}},Gi=(e,t)=>{const n=Vs(t);e.slots.default=()=>n},Zi=(e,t,n)=>{for(const s in t)(n||s!=="_")&&(e[s]=t[s])},ca=(e,t,n)=>{const s=e.slots=ji();if(e.vnode.shapeFlag&32){const r=t._;r?(Zi(s,t,n),n&&ri(s,"_",r,!0)):zi(t,s)}else t&&Gi(e,t)},ua=(e,t,n)=>{const{vnode:s,slots:r}=e;let i=!0,o=q;if(s.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:Zi(r,t,n):(i=!t.$stable,zi(t,r)),o=t}else t&&(Gi(e,t),o={default:1});if(i)for(const l in r)!Ki(l)&&o[l]==null&&delete r[l]},Oe=Ta;function fa(e){return da(e)}function da(e,t){const n=$n();n.__VUE__=!0;const{insert:s,remove:r,patchProp:i,createElement:o,createText:l,createComment:a,setText:d,setElementText:c,parentNode:h,nextSibling:g,setScopeId:p=Ke,insertStaticContent:T}=e,x=(u,f,m,b=null,v=null,y=null,D=void 0,S=null,w=!!f.dynamicChildren)=>{if(u===f)return;u&&!Wt(u,f)&&(b=gt(u),de(u,v,y,!0),u=null),f.patchFlag===-2&&(w=!1,f.dynamicChildren=null);const{type:_,ref:A,shapeFlag:C}=f;switch(_){case an:I(u,f,m,b);break;case Tt:P(u,f,m,b);break;case ns:u==null&&V(f,m,b,D);break;case we:Te(u,f,m,b,v,y,D,S,w);break;default:C&1?se(u,f,m,b,v,y,D,S,w):C&6?Ve(u,f,m,b,v,y,D,S,w):(C&64||C&128)&&_.process(u,f,m,b,v,y,D,S,w,Bt)}A!=null&&v&&hs(A,u&&u.ref,y,f||u,!f)},I=(u,f,m,b)=>{if(u==null)s(f.el=l(f.children),m,b);else{const v=f.el=u.el;f.children!==u.children&&d(v,f.children)}},P=(u,f,m,b)=>{u==null?s(f.el=a(f.children||""),m,b):f.el=u.el},V=(u,f,m,b)=>{[u.el,u.anchor]=T(u.children,f,m,b,u.el,u.anchor)},X=({el:u,anchor:f},m,b)=>{let v;for(;u&&u!==f;)v=g(u),s(u,m,b),u=v;s(f,m,b)},E=({el:u,anchor:f})=>{let m;for(;u&&u!==f;)m=g(u),r(u),u=m;r(f)},se=(u,f,m,b,v,y,D,S,w)=>{f.type==="svg"?D="svg":f.type==="math"&&(D="mathml"),u==null?W(f,m,b,v,y,D,S,w):ie(u,f,v,y,D,S,w)},W=(u,f,m,b,v,y,D,S)=>{let w,_;const{props:A,shapeFlag:C,transition:M,dirs:F}=u;if(w=u.el=o(u.type,y,A&&A.is,A),C&8?c(w,u.children):C&16&&j(u.children,w,null,b,v,es(u,y),D,S),F&&pt(u,null,b,"created"),te(w,u,u.scopeId,D,b),A){for(const Z in A)Z!=="value"&&!zt(Z)&&i(w,Z,null,A[Z],y,b);"value"in A&&i(w,"value",null,A.value,y),(_=A.onVnodeBeforeMount)&&je(_,b,u)}F&&pt(u,null,b,"beforeMount");const H=ha(v,M);H&&M.beforeEnter(w),s(w,f,m),((_=A&&A.onVnodeMounted)||H||F)&&Oe(()=>{_&&je(_,b,u),H&&M.enter(w),F&&pt(u,null,b,"mounted")},v)},te=(u,f,m,b,v)=>{if(m&&p(u,m),b)for(let y=0;y<b.length;y++)p(u,b[y]);if(v){let y=v.subTree;if(f===y||eo(y.type)&&(y.ssContent===f||y.ssFallback===f)){const D=v.vnode;te(u,D,D.scopeId,D.slotScopeIds,v.parent)}}},j=(u,f,m,b,v,y,D,S,w=0)=>{for(let _=w;_<u.length;_++){const A=u[_]=S?ot(u[_]):We(u[_]);x(null,A,f,m,b,v,y,D,S)}},ie=(u,f,m,b,v,y,D)=>{const S=f.el=u.el;let{patchFlag:w,dynamicChildren:_,dirs:A}=f;w|=u.patchFlag&16;const C=u.props||q,M=f.props||q;let F;if(m&&vt(m,!1),(F=M.onVnodeBeforeUpdate)&&je(F,m,f,u),A&&pt(f,u,m,"beforeUpdate"),m&&vt(m,!0),(C.innerHTML&&M.innerHTML==null||C.textContent&&M.textContent==null)&&c(S,""),_?Q(u.dynamicChildren,_,S,m,b,es(f,v),y):D||Y(u,f,S,null,m,b,es(f,v),y,!1),w>0){if(w&16)ue(S,C,M,m,v);else if(w&2&&C.class!==M.class&&i(S,"class",null,M.class,v),w&4&&i(S,"style",C.style,M.style,v),w&8){const H=f.dynamicProps;for(let Z=0;Z<H.length;Z++){const K=H[Z],De=C[K],he=M[K];(he!==De||K==="value")&&i(S,K,De,he,v,m)}}w&1&&u.children!==f.children&&c(S,f.children)}else!D&&_==null&&ue(S,C,M,m,v);((F=M.onVnodeUpdated)||A)&&Oe(()=>{F&&je(F,m,f,u),A&&pt(f,u,m,"updated")},b)},Q=(u,f,m,b,v,y,D)=>{for(let S=0;S<f.length;S++){const w=u[S],_=f[S],A=w.el&&(w.type===we||!Wt(w,_)||w.shapeFlag&70)?h(w.el):m;x(w,_,A,null,b,v,y,D,!0)}},ue=(u,f,m,b,v)=>{if(f!==m){if(f!==q)for(const y in f)!zt(y)&&!(y in m)&&i(u,y,f[y],null,v,b);for(const y in m){if(zt(y))continue;const D=m[y],S=f[y];D!==S&&y!=="value"&&i(u,y,S,D,v,b)}"value"in m&&i(u,"value",f.value,m.value,v)}},Te=(u,f,m,b,v,y,D,S,w)=>{const _=f.el=u?u.el:l(""),A=f.anchor=u?u.anchor:l("");let{patchFlag:C,dynamicChildren:M,slotScopeIds:F}=f;F&&(S=S?S.concat(F):F),u==null?(s(_,m,b),s(A,m,b),j(f.children||[],m,A,v,y,D,S,w)):C>0&&C&64&&M&&u.dynamicChildren?(Q(u.dynamicChildren,M,m,v,y,D,S),(f.key!=null||v&&f===v.subTree)&&Ji(u,f,!0)):Y(u,f,m,A,v,y,D,S,w)},Ve=(u,f,m,b,v,y,D,S,w)=>{f.slotScopeIds=S,u==null?f.shapeFlag&512?v.ctx.activate(f,m,b,D,w):qe(f,m,b,v,y,D,w):fn(u,f,w)},qe=(u,f,m,b,v,y,D)=>{const S=u.component=Aa(u,b,v);if($i(u)&&(S.ctx.renderer=Bt),Fa(S,!1,D),S.asyncDep){if(v&&v.registerDep(S,fe,D),!u.el){const w=S.subTree=k(Tt);P(null,w,f,m)}}else fe(S,u,f,m,v,y,D)},fn=(u,f,m)=>{const b=f.component=u.component;if(wa(u,f,m))if(b.asyncDep&&!b.asyncResolved){G(b,f,m);return}else b.next=f,b.update();else f.el=u.el,b.vnode=f},fe=(u,f,m,b,v,y,D)=>{const S=()=>{if(u.isMounted){let{next:C,bu:M,u:F,parent:H,vnode:Z}=u;{const Ce=qi(u);if(Ce){C&&(C.el=Z.el,G(u,C,D)),Ce.asyncDep.then(()=>{u.isUnmounted||S()});return}}let K=C,De;vt(u,!1),C?(C.el=Z.el,G(u,C,D)):C=Z,M&&Gn(M),(De=C.props&&C.props.onVnodeBeforeUpdate)&&je(De,H,C,Z),vt(u,!0);const he=ts(u),$e=u.subTree;u.subTree=he,x($e,he,h($e.el),gt($e),u,v,y),C.el=he.el,K===null&&Sa(u,he.el),F&&Oe(F,v),(De=C.props&&C.props.onVnodeUpdated)&&Oe(()=>je(De,H,C,Z),v)}else{let C;const{el:M,props:F}=f,{bm:H,m:Z,parent:K,root:De,type:he}=u,$e=Jt(f);if(vt(u,!1),H&&Gn(H),!$e&&(C=F&&F.onVnodeBeforeMount)&&je(C,K,f),vt(u,!0),M&&Gs){const Ce=()=>{u.subTree=ts(u),Gs(M,u.subTree,u,v,null)};$e&&he.__asyncHydrate?he.__asyncHydrate(M,u,Ce):Ce()}else{De.ce&&De.ce._injectChildStyle(he);const Ce=u.subTree=ts(u);x(null,Ce,m,b,u,v,y),f.el=Ce.el}if(Z&&Oe(Z,v),!$e&&(C=F&&F.onVnodeMounted)){const Ce=f;Oe(()=>je(C,K,Ce),v)}(f.shapeFlag&256||K&&Jt(K.vnode)&&K.vnode.shapeFlag&256)&&u.a&&Oe(u.a,v),u.isMounted=!0,f=m=b=null}};u.scope.on();const w=u.effect=new ci(S);u.scope.off();const _=u.update=w.run.bind(w),A=u.job=w.runIfDirty.bind(w);A.i=u,A.id=u.uid,w.scheduler=()=>ks(A),vt(u,!0),_()},G=(u,f,m)=>{f.component=u;const b=u.vnode.props;u.vnode=f,u.next=null,oa(u,f.props,b,m),ua(u,f.children,m),ft(),er(u),dt()},Y=(u,f,m,b,v,y,D,S,w=!1)=>{const _=u&&u.children,A=u?u.shapeFlag:0,C=f.children,{patchFlag:M,shapeFlag:F}=f;if(M>0){if(M&128){Ct(_,C,m,b,v,y,D,S,w);return}else if(M&256){Pe(_,C,m,b,v,y,D,S,w);return}}F&8?(A&16&&mt(_,v,y),C!==_&&c(m,C)):A&16?F&16?Ct(_,C,m,b,v,y,D,S,w):mt(_,v,y,!0):(A&8&&c(m,""),F&16&&j(C,m,b,v,y,D,S,w))},Pe=(u,f,m,b,v,y,D,S,w)=>{u=u||Pt,f=f||Pt;const _=u.length,A=f.length,C=Math.min(_,A);let M;for(M=0;M<C;M++){const F=f[M]=w?ot(f[M]):We(f[M]);x(u[M],F,m,null,v,y,D,S,w)}_>A?mt(u,v,y,!0,!1,C):j(f,m,b,v,y,D,S,w,C)},Ct=(u,f,m,b,v,y,D,S,w)=>{let _=0;const A=f.length;let C=u.length-1,M=A-1;for(;_<=C&&_<=M;){const F=u[_],H=f[_]=w?ot(f[_]):We(f[_]);if(Wt(F,H))x(F,H,m,null,v,y,D,S,w);else break;_++}for(;_<=C&&_<=M;){const F=u[C],H=f[M]=w?ot(f[M]):We(f[M]);if(Wt(F,H))x(F,H,m,null,v,y,D,S,w);else break;C--,M--}if(_>C){if(_<=M){const F=M+1,H=F<A?f[F].el:b;for(;_<=M;)x(null,f[_]=w?ot(f[_]):We(f[_]),m,H,v,y,D,S,w),_++}}else if(_>M)for(;_<=C;)de(u[_],v,y,!0),_++;else{const F=_,H=_,Z=new Map;for(_=H;_<=M;_++){const Me=f[_]=w?ot(f[_]):We(f[_]);Me.key!=null&&Z.set(Me.key,_)}let K,De=0;const he=M-H+1;let $e=!1,Ce=0;const jt=new Array(he);for(_=0;_<he;_++)jt[_]=0;for(_=F;_<=C;_++){const Me=u[_];if(De>=he){de(Me,v,y,!0);continue}let Be;if(Me.key!=null)Be=Z.get(Me.key);else for(K=H;K<=M;K++)if(jt[K-H]===0&&Wt(Me,f[K])){Be=K;break}Be===void 0?de(Me,v,y,!0):(jt[Be-H]=_+1,Be>=Ce?Ce=Be:$e=!0,x(Me,f[Be],m,null,v,y,D,S,w),De++)}const Zs=$e?ma(jt):Pt;for(K=Zs.length-1,_=he-1;_>=0;_--){const Me=H+_,Be=f[Me],Js=Me+1<A?f[Me+1].el:b;jt[_]===0?x(null,Be,m,Js,v,y,D,S,w):$e&&(K<0||_!==Zs[K]?Xe(Be,m,Js,2):K--)}}},Xe=(u,f,m,b,v=null)=>{const{el:y,type:D,transition:S,children:w,shapeFlag:_}=u;if(_&6){Xe(u.component.subTree,f,m,b);return}if(_&128){u.suspense.move(f,m,b);return}if(_&64){D.move(u,f,m,Bt);return}if(D===we){s(y,f,m);for(let C=0;C<w.length;C++)Xe(w[C],f,m,b);s(u.anchor,f,m);return}if(D===ns){X(u,f,m);return}if(b!==2&&_&1&&S)if(b===0)S.beforeEnter(y),s(y,f,m),Oe(()=>S.enter(y),v);else{const{leave:C,delayLeave:M,afterLeave:F}=S,H=()=>s(y,f,m),Z=()=>{C(y,()=>{H(),F&&F()})};M?M(y,H,Z):Z()}else s(y,f,m)},de=(u,f,m,b=!1,v=!1)=>{const{type:y,props:D,ref:S,children:w,dynamicChildren:_,shapeFlag:A,patchFlag:C,dirs:M,cacheIndex:F}=u;if(C===-2&&(v=!1),S!=null&&hs(S,null,m,u,!0),F!=null&&(f.renderCache[F]=void 0),A&256){f.ctx.deactivate(u);return}const H=A&1&&M,Z=!Jt(u);let K;if(Z&&(K=D&&D.onVnodeBeforeUnmount)&&je(K,f,u),A&6)Vt(u.component,m,b);else{if(A&128){u.suspense.unmount(m,b);return}H&&pt(u,null,f,"beforeUnmount"),A&64?u.type.remove(u,f,m,Bt,b):_&&!_.hasOnce&&(y!==we||C>0&&C&64)?mt(_,f,m,!1,!0):(y===we&&C&384||!v&&A&16)&&mt(w,f,m),b&&Mt(u)}(Z&&(K=D&&D.onVnodeUnmounted)||H)&&Oe(()=>{K&&je(K,f,u),H&&pt(u,null,f,"unmounted")},m)},Mt=u=>{const{type:f,el:m,anchor:b,transition:v}=u;if(f===we){Kn(m,b);return}if(f===ns){E(u);return}const y=()=>{r(m),v&&!v.persisted&&v.afterLeave&&v.afterLeave()};if(u.shapeFlag&1&&v&&!v.persisted){const{leave:D,delayLeave:S}=v,w=()=>D(m,y);S?S(u.el,y,w):w()}else y()},Kn=(u,f)=>{let m;for(;u!==f;)m=g(u),r(u),u=m;r(f)},Vt=(u,f,m)=>{const{bum:b,scope:v,job:y,subTree:D,um:S,m:w,a:_}=u;or(w),or(_),b&&Gn(b),v.stop(),y&&(y.flags|=8,de(D,u,f,m)),S&&Oe(S,f),Oe(()=>{u.isUnmounted=!0},f),f&&f.pendingBranch&&!f.isUnmounted&&u.asyncDep&&!u.asyncResolved&&u.suspenseId===f.pendingId&&(f.deps--,f.deps===0&&f.resolve())},mt=(u,f,m,b=!1,v=!1,y=0)=>{for(let D=y;D<u.length;D++)de(u[D],f,m,b,v)},gt=u=>{if(u.shapeFlag&6)return gt(u.component.subTree);if(u.shapeFlag&128)return u.suspense.next();const f=g(u.anchor||u.el),m=f&&f[Ll];return m?g(m):f};let xe=!1;const Ot=(u,f,m)=>{u==null?f._vnode&&de(f._vnode,null,null,!0):x(f._vnode||null,u,f,null,null,null,m),f._vnode=u,xe||(xe=!0,er(),Ei(),xe=!1)},Bt={p:x,um:de,m:Xe,r:Mt,mt:qe,mc:j,pc:Y,pbc:Q,n:gt,o:e};let zs,Gs;return{render:Ot,hydrate:zs,createApp:ra(Ot,zs)}}function es({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function vt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function ha(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Ji(e,t,n=!1){const s=e.children,r=t.children;if(R(s)&&R(r))for(let i=0;i<s.length;i++){const o=s[i];let l=r[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[i]=ot(r[i]),l.el=o.el),!n&&l.patchFlag!==-2&&Ji(o,l)),l.type===an&&(l.el=o.el)}}function ma(e){const t=e.slice(),n=[0];let s,r,i,o,l;const a=e.length;for(s=0;s<a;s++){const d=e[s];if(d!==0){if(r=n[n.length-1],e[r]<d){t[s]=r,n.push(s);continue}for(i=0,o=n.length-1;i<o;)l=i+o>>1,e[n[l]]<d?i=l+1:o=l;d<e[n[i]]&&(i>0&&(t[s]=n[i-1]),n[i]=s)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}function qi(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:qi(t)}function or(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ga=Symbol.for("v-scx"),pa=()=>ze(ga);function Bn(e,t){return Bs(e,null,t)}function Ge(e,t,n){return Bs(e,t,n)}function Bs(e,t,n=q){const{immediate:s,deep:r,flush:i,once:o}=n,l=ae({},n),a=t&&s||!t&&i!=="post";let d;if(sn){if(i==="sync"){const p=pa();d=p.__watcherHandles||(p.__watcherHandles=[])}else if(!a){const p=()=>{};return p.stop=Ke,p.resume=Ke,p.pause=Ke,p}}const c=pe;l.call=(p,T,x)=>Ze(p,c,T,x);let h=!1;i==="post"?l.scheduler=p=>{Oe(p,c&&c.suspense)}:i!=="sync"&&(h=!0,l.scheduler=(p,T)=>{T?p():ks(p)}),l.augmentJob=p=>{t&&(p.flags|=4),h&&(p.flags|=2,c&&(p.id=c.uid,p.i=c))};const g=Rl(e,t,l);return sn&&(d?d.push(g):a&&g()),g}function va(e,t,n){const s=this.proxy,r=re(e)?e.includes(".")?Xi(s,e):()=>s[e]:e.bind(s,s);let i;$(t)?i=t:(i=t.handler,n=t);const o=cn(this),l=Bs(r,i.bind(s),n);return o(),l}function Xi(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const ya=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${ct(t)}Modifiers`]||e[`${Dt(t)}Modifiers`];function ba(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||q;let r=n;const i=t.startsWith("update:"),o=i&&ya(s,t.slice(7));o&&(o.trim&&(r=n.map(c=>re(c)?c.trim():c)),o.number&&(r=n.map(Go)));let l,a=s[l=zn(t)]||s[l=zn(ct(t))];!a&&i&&(a=s[l=zn(Dt(t))]),a&&Ze(a,e,6,r);const d=s[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Ze(d,e,6,r)}}function Qi(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const i=e.emits;let o={},l=!1;if(!$(e)){const a=d=>{const c=Qi(d,t,!0);c&&(l=!0,ae(o,c))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!i&&!l?(ee(e)&&s.set(e,null),null):(R(i)?i.forEach(a=>o[a]=null):ae(o,i),ee(e)&&s.set(e,o),o)}function jn(e,t){return!e||!Fn(t)?!1:(t=t.slice(2).replace(/Once$/,""),U(e,t[0].toLowerCase()+t.slice(1))||U(e,Dt(t))||U(e,t))}function ts(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[i],slots:o,attrs:l,emit:a,render:d,renderCache:c,props:h,data:g,setupState:p,ctx:T,inheritAttrs:x}=e,I=Dn(e);let P,V;try{if(n.shapeFlag&4){const E=r||s,se=E;P=We(d.call(se,E,c,h,p,g,T)),V=l}else{const E=t;P=We(E.length>1?E(h,{attrs:l,slots:o,emit:a}):E(h,null)),V=t.props?l:_a(l)}}catch(E){Xt.length=0,Nn(E,e,1),P=k(Tt)}let X=P;if(V&&x!==!1){const E=Object.keys(V),{shapeFlag:se}=X;E.length&&se&7&&(i&&E.some(Cs)&&(V=xa(V,i)),X=Ht(X,V,!1,!0))}return n.dirs&&(X=Ht(X,null,!1,!0),X.dirs=X.dirs?X.dirs.concat(n.dirs):n.dirs),n.transition&&Ls(X,n.transition),P=X,Dn(I),P}const _a=e=>{let t;for(const n in e)(n==="class"||n==="style"||Fn(n))&&((t||(t={}))[n]=e[n]);return t},xa=(e,t)=>{const n={};for(const s in e)(!Cs(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function wa(e,t,n){const{props:s,children:r,component:i}=e,{props:o,children:l,patchFlag:a}=t,d=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return s?lr(s,o,d):!!o;if(a&8){const c=t.dynamicProps;for(let h=0;h<c.length;h++){const g=c[h];if(o[g]!==s[g]&&!jn(d,g))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===o?!1:s?o?lr(s,o,d):!0:!!o;return!1}function lr(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const i=s[r];if(t[i]!==e[i]&&!jn(n,i))return!0}return!1}function Sa({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const eo=e=>e.__isSuspense;function Ta(e,t){t&&t.pendingBranch?R(e)?t.effects.push(...e):t.effects.push(e):kl(e)}const we=Symbol.for("v-fgt"),an=Symbol.for("v-txt"),Tt=Symbol.for("v-cmt"),ns=Symbol.for("v-stc"),Xt=[];let Ie=null;function L(e=!1){Xt.push(Ie=e?null:[])}function Da(){Xt.pop(),Ie=Xt[Xt.length-1]||null}let nn=1;function ar(e){nn+=e,e<0&&Ie&&(Ie.hasOnce=!0)}function to(e){return e.dynamicChildren=nn>0?Ie||Pt:null,Da(),nn>0&&Ie&&Ie.push(e),e}function z(e,t,n,s,r,i){return to(O(e,t,n,s,r,i,!0))}function xt(e,t,n,s,r){return to(k(e,t,n,s,r,!0))}function Mn(e){return e?e.__v_isVNode===!0:!1}function Wt(e,t){return e.type===t.type&&e.key===t.key}const no=({key:e})=>e??null,bn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?re(e)||oe(e)||$(e)?{i:ke,r:e,k:t,f:!!n}:e:null);function O(e,t=null,n=null,s=0,r=null,i=e===we?0:1,o=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&no(t),ref:t&&bn(t),scopeId:Ri,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:ke};return l?(js(a,n),i&128&&e.normalize(a)):n&&(a.shapeFlag|=re(n)?8:16),nn>0&&!o&&Ie&&(a.patchFlag>0||i&6)&&a.patchFlag!==32&&Ie.push(a),a}const k=Ca;function Ca(e,t=null,n=null,s=0,r=null,i=!1){if((!e||e===Jl)&&(e=Tt),Mn(e)){const l=Ht(e,t,!0);return n&&js(l,n),nn>0&&!i&&Ie&&(l.shapeFlag&6?Ie[Ie.indexOf(e)]=l:Ie.push(l)),l.patchFlag=-2,l}if(ka(e)&&(e=e.__vccOpts),t){t=Ma(t);let{class:l,style:a}=t;l&&!re(l)&&(t.class=Fe(l)),ee(a)&&($s(a)&&!R(a)&&(a=ae({},a)),t.style=Ee(a))}const o=re(e)?1:eo(e)?128:Nl(e)?64:ee(e)?4:$(e)?2:0;return O(e,t,n,s,r,o,i,!0)}function Ma(e){return e?$s(e)||Ui(e)?ae({},e):e:null}function Ht(e,t,n=!1,s=!1){const{props:r,ref:i,patchFlag:o,children:l,transition:a}=e,d=t?ro(r||{},t):r,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:d,key:d&&no(d),ref:t&&t.ref?n&&i?R(i)?i.concat(bn(t)):[i,bn(t)]:bn(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==we?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ht(e.ssContent),ssFallback:e.ssFallback&&Ht(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&s&&Ls(c,a.clone(c)),c}function so(e=" ",t=0){return k(an,null,e,t)}function le(e="",t=!1){return t?(L(),xt(Tt,null,e)):k(Tt,null,e)}function We(e){return e==null||typeof e=="boolean"?k(Tt):R(e)?k(we,null,e.slice()):Mn(e)?ot(e):k(an,null,String(e))}function ot(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ht(e)}function js(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(R(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),js(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!Ui(t)?t._ctx=ke:r===3&&ke&&(ke.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else $(t)?(t={default:t,_ctx:ke},n=32):(t=String(t),s&64?(n=16,t=[so(t)]):n=8);e.children=t,e.shapeFlag|=n}function ro(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Fe([t.class,s.class]));else if(r==="style")t.style=Ee([t.style,s.style]);else if(Fn(r)){const i=t[r],o=s[r];o&&i!==o&&!(R(i)&&i.includes(o))&&(t[r]=i?[].concat(i,o):o)}else r!==""&&(t[r]=s[r])}return t}function je(e,t,n,s=null){Ze(e,t,7,[n,s])}const Oa=Vi();let Ia=0;function Aa(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||Oa,i={uid:Ia++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new ai(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Yi(s,r),emitsOptions:Qi(s,r),emit:null,emitted:null,propsDefaults:q,inheritAttrs:s.inheritAttrs,ctx:q,data:q,props:q,attrs:q,slots:q,refs:q,setupState:q,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=ba.bind(null,i),e.ce&&e.ce(i),i}let pe=null;const Ea=()=>pe||ke;let On,ys;{const e=$n(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),i=>{r.length>1?r.forEach(o=>o(i)):r[0](i)}};On=t("__VUE_INSTANCE_SETTERS__",n=>pe=n),ys=t("__VUE_SSR_SETTERS__",n=>sn=n)}const cn=e=>{const t=pe;return On(e),e.scope.on(),()=>{e.scope.off(),On(t)}},cr=()=>{pe&&pe.scope.off(),On(null)};function io(e){return e.vnode.shapeFlag&4}let sn=!1;function Fa(e,t=!1,n=!1){t&&ys(t);const{props:s,children:r}=e.vnode,i=io(e);ia(e,s,i,t),ca(e,r,n);const o=i?Ra(e,t):void 0;return t&&ys(!1),o}function Ra(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,ql);const{setup:s}=n;if(s){ft();const r=e.setupContext=s.length>1?$a(e):null,i=cn(e),o=ln(s,e,0,[e.props,r]),l=ei(o);if(dt(),i(),(l||e.sp)&&!Jt(e)&&Pi(e),l){if(o.then(cr,cr),t)return o.then(a=>{ur(e,a,t)}).catch(a=>{Nn(a,e,0)});e.asyncDep=o}else ur(e,o,t)}else oo(e,t)}function ur(e,t,n){$(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ee(t)&&(e.setupState=Di(t)),oo(e,n)}let fr;function oo(e,t,n){const s=e.type;if(!e.render){if(!t&&fr&&!s.render){const r=s.template||Hs(e).template;if(r){const{isCustomElement:i,compilerOptions:o}=e.appContext.config,{delimiters:l,compilerOptions:a}=s,d=ae(ae({isCustomElement:i,delimiters:l},o),a);s.render=fr(r,d)}}e.render=s.render||Ke}{const r=cn(e);ft();try{Xl(e)}finally{dt(),r()}}}const Pa={get(e,t){return me(e,"get",""),e[t]}};function $a(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Pa),slots:e.slots,emit:e.emit,expose:t}}function Us(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Di(Tl(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in qt)return qt[n](e)},has(t,n){return n in t||n in qt}})):e.proxy}function ka(e){return $(e)&&"__vccOpts"in e}const N=(e,t)=>El(e,t,sn);function La(e,t,n){const s=arguments.length;return s===2?ee(t)&&!R(t)?Mn(t)?k(e,null,[t]):k(e,t):k(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&Mn(n)&&(n=[n]),k(e,t,n))}const Na="3.5.12";/**
* @vue/runtime-dom v3.5.12
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let bs;const dr=typeof window<"u"&&window.trustedTypes;if(dr)try{bs=dr.createPolicy("vue",{createHTML:e=>e})}catch{}const lo=bs?e=>bs.createHTML(e):e=>e,Ha="http://www.w3.org/2000/svg",Va="http://www.w3.org/1998/Math/MathML",et=typeof document<"u"?document:null,hr=et&&et.createElement("template"),Ba={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?et.createElementNS(Ha,e):t==="mathml"?et.createElementNS(Va,e):n?et.createElement(e,{is:n}):et.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>et.createTextNode(e),createComment:e=>et.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>et.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,i){const o=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===i||!(r=r.nextSibling)););else{hr.innerHTML=lo(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=hr.content;if(s==="svg"||s==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},ja=Symbol("_vtc");function Ua(e,t,n){const s=e[ja];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const mr=Symbol("_vod"),Wa=Symbol("_vsh"),Ya=Symbol(""),Ka=/(^|;)\s*display\s*:/;function za(e,t,n){const s=e.style,r=re(n);let i=!1;if(n&&!r){if(t)if(re(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();n[l]==null&&_n(s,l,"")}else for(const o in t)n[o]==null&&_n(s,o,"");for(const o in n)o==="display"&&(i=!0),_n(s,o,n[o])}else if(r){if(t!==n){const o=s[Ya];o&&(n+=";"+o),s.cssText=n,i=Ka.test(n)}}else t&&e.removeAttribute("style");mr in e&&(e[mr]=i?s.display:"",e[Wa]&&(s.display="none"))}const gr=/\s*!important$/;function _n(e,t,n){if(R(n))n.forEach(s=>_n(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=Ga(e,t);gr.test(n)?e.setProperty(Dt(s),n.replace(gr,""),"important"):e[s]=n}}const pr=["Webkit","Moz","ms"],ss={};function Ga(e,t){const n=ss[t];if(n)return n;let s=ct(t);if(s!=="filter"&&s in e)return ss[t]=s;s=si(s);for(let r=0;r<pr.length;r++){const i=pr[r]+s;if(i in e)return ss[t]=i}return t}const vr="http://www.w3.org/1999/xlink";function yr(e,t,n,s,r,i=el(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(vr,t.slice(6,t.length)):e.setAttributeNS(vr,t,n):n==null||i&&!ii(n)?e.removeAttribute(t):e.setAttribute(t,i?"":ut(n)?String(n):n)}function br(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?lo(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const l=i==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let o=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=ii(n):n==null&&l==="string"?(n="",o=!0):l==="number"&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(r||t)}function Za(e,t,n,s){e.addEventListener(t,n,s)}function Ja(e,t,n,s){e.removeEventListener(t,n,s)}const _r=Symbol("_vei");function qa(e,t,n,s,r=null){const i=e[_r]||(e[_r]={}),o=i[t];if(s&&o)o.value=s;else{const[l,a]=Xa(t);if(s){const d=i[t]=tc(s,r);Za(e,l,d,a)}else o&&(Ja(e,l,o,a),i[t]=void 0)}}const xr=/(?:Once|Passive|Capture)$/;function Xa(e){let t;if(xr.test(e)){t={};let s;for(;s=e.match(xr);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Dt(e.slice(2)),t]}let rs=0;const Qa=Promise.resolve(),ec=()=>rs||(Qa.then(()=>rs=0),rs=Date.now());function tc(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;Ze(nc(s,n.value),t,5,[s])};return n.value=e,n.attached=ec(),n}function nc(e,t){if(R(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const wr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,sc=(e,t,n,s,r,i)=>{const o=r==="svg";t==="class"?Ua(e,s,o):t==="style"?za(e,n,s):Fn(t)?Cs(t)||qa(e,t,n,s,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):rc(e,t,s,o))?(br(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&yr(e,t,s,o,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!re(s))?br(e,ct(t),s,i,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),yr(e,t,s,o))};function rc(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&wr(t)&&$(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return wr(t)&&re(n)?!1:t in e}const ic=ae({patchProp:sc},Ba);let Sr;function oc(){return Sr||(Sr=fa(ic))}const lc=(...e)=>{const t=oc().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=cc(s);if(!r)return;const i=t._component;!$(i)&&!i.render&&!i.template&&(i.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const o=n(r,!1,ac(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),o},t};function ac(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function cc(e){return re(e)?document.querySelector(e):e}function uc(e,t){let n;function s(){n=tl(),n.run(()=>t.length?t(()=>{n==null||n.stop(),s()}):t())}Ge(e,r=>{r&&!n?s():r||(n==null||n.stop(),n=void 0)},{immediate:!0}),sl(()=>{n==null||n.stop()})}const Le=typeof window<"u",fc=Le&&("ontouchstart"in window||window.navigator.maxTouchPoints>0);function dc(e,t,n){const s=t.length-1;if(s<0)return e===void 0?n:e;for(let r=0;r<s;r++){if(e==null)return n;e=e[t[r]]}return e==null||e[t[s]]===void 0?n:e[t[s]]}function Tr(e,t,n){return e==null||!t||typeof t!="string"?n:e[t]!==void 0?e[t]:(t=t.replace(/\[(\w+)\]/g,".$1"),t=t.replace(/^\./,""),dc(e,t.split("."),n))}function ao(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return Array.from({length:e},(n,s)=>t+s)}function _e(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"px";if(!(e==null||e===""))return isNaN(+e)?String(e):isFinite(+e)?`${Number(e)}${t}`:void 0}function Dr(e){let t;return e!==null&&typeof e=="object"&&((t=Object.getPrototypeOf(e))===Object.prototype||t===null)}function hc(e){if(e&&"$el"in e){const t=e.$el;return(t==null?void 0:t.nodeType)===Node.TEXT_NODE?t.nextElementSibling:t}return e}function is(e,t){return t.every(n=>e.hasOwnProperty(n))}function mc(e,t){const n={},s=new Set(Object.keys(e));for(const r of t)s.has(r)&&(n[r]=e[r]);return n}function gc(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:1;return Math.max(t,Math.min(n,e))}function Cr(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"0";return e+n.repeat(Math.max(0,t-e.length))}function Mr(e,t){return(arguments.length>2&&arguments[2]!==void 0?arguments[2]:"0").repeat(Math.max(0,t-e.length))+e}function pc(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1;const n=[];let s=0;for(;s<e.length;)n.push(e.substr(s,t)),s+=t;return n}function st(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;const s={};for(const r in e)s[r]=e[r];for(const r in t){const i=e[r],o=t[r];if(Dr(i)&&Dr(o)){s[r]=st(i,o,n);continue}if(n&&Array.isArray(i)&&Array.isArray(o)){s[r]=n(i,o);continue}s[r]=o}return s}function co(e){return e.map(t=>t.type===we?co(t.children):t).flat()}function wt(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"";if(wt.cache.has(e))return wt.cache.get(e);const t=e.replace(/[^a-z]/gi,"-").replace(/\B([A-Z])/g,"-$1").toLowerCase();return wt.cache.set(e,t),t}wt.cache=new Map;function Kt(e,t){if(!t||typeof t!="object")return[];if(Array.isArray(t))return t.map(n=>Kt(e,n)).flat(1);if(t.suspense)return Kt(e,t.ssContent);if(Array.isArray(t.children))return t.children.map(n=>Kt(e,n)).flat(1);if(t.component){if(Object.getOwnPropertySymbols(t.component.provides).includes(e))return[t.component];if(t.component.subTree)return Kt(e,t.component.subTree).flat(1)}return[]}function uo(e){const t=Ae({}),n=N(e);return Bn(()=>{for(const s in n.value)t[s]=n.value[s]},{flush:"sync"}),Ci(t)}function vc(e,t){return e.includes(t)}function yc(){const e=nt(),t=n=>{e.value=n};return Object.defineProperty(t,"value",{enumerable:!0,get:()=>e.value,set:n=>e.value=n}),Object.defineProperty(t,"el",{enumerable:!0,get:()=>hc(e.value)}),t}const At=2.4,Or=.2126729,Ir=.7151522,Ar=.072175,bc=.55,_c=.58,xc=.57,wc=.62,gn=.03,Er=1.45,Sc=5e-4,Tc=1.25,Dc=1.25,Fr=.078,Rr=12.82051282051282,pn=.06,Pr=.001;function $r(e,t){const n=(e.r/255)**At,s=(e.g/255)**At,r=(e.b/255)**At,i=(t.r/255)**At,o=(t.g/255)**At,l=(t.b/255)**At;let a=n*Or+s*Ir+r*Ar,d=i*Or+o*Ir+l*Ar;if(a<=gn&&(a+=(gn-a)**Er),d<=gn&&(d+=(gn-d)**Er),Math.abs(d-a)<Sc)return 0;let c;if(d>a){const h=(d**bc-a**_c)*Tc;c=h<Pr?0:h<Fr?h-h*Rr*pn:h-pn}else{const h=(d**wc-a**xc)*Dc;c=h>-Pr?0:h>-Fr?h-h*Rr*pn:h+pn}return c*100}const In=.20689655172413793,Cc=e=>e>In**3?Math.cbrt(e):e/(3*In**2)+4/29,Mc=e=>e>In?e**3:3*In**2*(e-4/29);function fo(e){const t=Cc,n=t(e[1]);return[116*n-16,500*(t(e[0]/.95047)-n),200*(n-t(e[2]/1.08883))]}function ho(e){const t=Mc,n=(e[0]+16)/116;return[t(n+e[1]/500)*.95047,t(n),t(n-e[2]/200)*1.08883]}const Oc=[[3.2406,-1.5372,-.4986],[-.9689,1.8758,.0415],[.0557,-.204,1.057]],Ic=e=>e<=.0031308?e*12.92:1.055*e**(1/2.4)-.055,Ac=[[.4124,.3576,.1805],[.2126,.7152,.0722],[.0193,.1192,.9505]],Ec=e=>e<=.04045?e/12.92:((e+.055)/1.055)**2.4;function mo(e){const t=Array(3),n=Ic,s=Oc;for(let r=0;r<3;++r)t[r]=Math.round(gc(n(s[r][0]*e[0]+s[r][1]*e[1]+s[r][2]*e[2]))*255);return{r:t[0],g:t[1],b:t[2]}}function Ws(e){let{r:t,g:n,b:s}=e;const r=[0,0,0],i=Ec,o=Ac;t=i(t/255),n=i(n/255),s=i(s/255);for(let l=0;l<3;++l)r[l]=o[l][0]*t+o[l][1]*n+o[l][2]*s;return r}function _s(e){return!!e&&/^(#|var\(--|(rgb|hsl)a?\()/.test(e)}function Fc(e){return _s(e)&&!/^((rgb|hsl)a?\()?var\(--/.test(e)}const kr=/^(?<fn>(?:rgb|hsl)a?)\((?<values>.+)\)/,Rc={rgb:(e,t,n,s)=>({r:e,g:t,b:n,a:s}),rgba:(e,t,n,s)=>({r:e,g:t,b:n,a:s}),hsl:(e,t,n,s)=>Lr({h:e,s:t,l:n,a:s}),hsla:(e,t,n,s)=>Lr({h:e,s:t,l:n,a:s}),hsv:(e,t,n,s)=>rn({h:e,s:t,v:n,a:s}),hsva:(e,t,n,s)=>rn({h:e,s:t,v:n,a:s})};function Ye(e){if(typeof e=="number")return{r:(e&16711680)>>16,g:(e&65280)>>8,b:e&255};if(typeof e=="string"&&kr.test(e)){const{groups:t}=e.match(kr),{fn:n,values:s}=t,r=s.split(/,\s*/).map(i=>i.endsWith("%")&&["hsl","hsla","hsv","hsva"].includes(n)?parseFloat(i)/100:parseFloat(i));return Rc[n](...r)}else if(typeof e=="string"){let t=e.startsWith("#")?e.slice(1):e;return[3,4].includes(t.length)?t=t.split("").map(n=>n+n).join(""):[6,8].includes(t.length),$c(t)}else if(typeof e=="object"){if(is(e,["r","g","b"]))return e;if(is(e,["h","s","l"]))return rn(go(e));if(is(e,["h","s","v"]))return rn(e)}throw new TypeError(`Invalid color: ${e==null?e:String(e)||e.constructor.name}
Expected #hex, #hexa, rgb(), rgba(), hsl(), hsla(), object or number`)}function rn(e){const{h:t,s:n,v:s,a:r}=e,i=l=>{const a=(l+t/60)%6;return s-s*n*Math.max(Math.min(a,4-a,1),0)},o=[i(5),i(3),i(1)].map(l=>Math.round(l*255));return{r:o[0],g:o[1],b:o[2],a:r}}function Lr(e){return rn(go(e))}function go(e){const{h:t,s:n,l:s,a:r}=e,i=s+n*Math.min(s,1-s),o=i===0?0:2-2*s/i;return{h:t,s:o,v:i,a:r}}function vn(e){const t=Math.round(e).toString(16);return("00".substr(0,2-t.length)+t).toUpperCase()}function Pc(e){let{r:t,g:n,b:s,a:r}=e;return`#${[vn(t),vn(n),vn(s),r!==void 0?vn(Math.round(r*255)):""].join("")}`}function $c(e){e=kc(e);let[t,n,s,r]=pc(e,2).map(i=>parseInt(i,16));return r=r===void 0?r:r/255,{r:t,g:n,b:s,a:r}}function kc(e){return e.startsWith("#")&&(e=e.slice(1)),e=e.replace(/([^0-9a-f])/gi,"F"),(e.length===3||e.length===4)&&(e=e.split("").map(t=>t+t).join("")),e.length!==6&&(e=Cr(Cr(e,6),8,"F")),e}function Lc(e,t){const n=fo(Ws(e));return n[0]=n[0]+t*10,mo(ho(n))}function Nc(e,t){const n=fo(Ws(e));return n[0]=n[0]-t*10,mo(ho(n))}function Hc(e){const t=Ye(e);return Ws(t)[1]}function po(e){const t=Math.abs($r(Ye(0),Ye(e)));return Math.abs($r(Ye(16777215),Ye(e)))>Math.min(t,50)?"#fff":"#000"}function He(e,t){return n=>Object.keys(e).reduce((s,r)=>{const o=typeof e[r]=="object"&&e[r]!=null&&!Array.isArray(e[r])?e[r]:{type:e[r]};return n&&r in n?s[r]={...o,default:n[r]}:s[r]=o,t&&!s[r].source&&(s[r].source=t),s},{})}const Ys=He({class:[String,Array,Object],style:{type:[String,Array,Object],default:null}},"component");function ht(e,t){const n=Ea();if(!n)throw new Error(`[Vuetify] ${e} must be called from inside a setup function`);return n}function Vc(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"composables";const t=ht(e).type;return wt((t==null?void 0:t.aliasName)||(t==null?void 0:t.name))}let vo=0,xn=new WeakMap;function yo(){const e=ht("getUid");if(xn.has(e))return xn.get(e);{const t=vo++;return xn.set(e,t),t}}yo.reset=()=>{vo=0,xn=new WeakMap};function Bc(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ht("injectSelf");const{provides:n}=t;if(n&&e in n)return n[e]}const on=Symbol.for("vuetify:defaults");function jc(e){return Se(e)}function bo(){const e=ze(on);if(!e)throw new Error("[Vuetify] Could not find defaults instance");return e}function Uc(e,t){var n,s;return typeof((n=e.props)==null?void 0:n[t])<"u"||typeof((s=e.props)==null?void 0:s[wt(t)])<"u"}function Wc(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:bo();const s=ht("useDefaults");if(t=t??s.type.name??s.type.__name,!t)throw new Error("[Vuetify] Could not determine component name");const r=N(()=>{var a;return(a=n.value)==null?void 0:a[e._as??t]}),i=new Proxy(e,{get(a,d){var h,g,p,T,x,I,P;const c=Reflect.get(a,d);return d==="class"||d==="style"?[(h=r.value)==null?void 0:h[d],c].filter(V=>V!=null):typeof d=="string"&&!Uc(s.vnode,d)?((g=r.value)==null?void 0:g[d])!==void 0?(p=r.value)==null?void 0:p[d]:((x=(T=n.value)==null?void 0:T.global)==null?void 0:x[d])!==void 0?(P=(I=n.value)==null?void 0:I.global)==null?void 0:P[d]:c:c}}),o=nt();Bn(()=>{if(r.value){const a=Object.entries(r.value).filter(d=>{let[c]=d;return c.startsWith(c[0].toUpperCase())});o.value=a.length?Object.fromEntries(a):void 0}else o.value=void 0});function l(){const a=Bc(on,s);Vn(on,N(()=>o.value?st((a==null?void 0:a.value)??{},o.value):a==null?void 0:a.value))}return{props:i,provideSubDefaults:l}}function un(e){if(e._setup=e._setup??e.setup,!e.name)return e;if(e._setup){e.props=He(e.props??{},e.name)();const t=Object.keys(e.props).filter(n=>n!=="class"&&n!=="style");e.filterProps=function(s){return mc(s,t)},e.props._as=String,e.setup=function(s,r){const i=bo();if(!i.value)return e._setup(s,r);const{props:o,provideSubDefaults:l}=Wc(s,s._as??e.name,i),a=e._setup(o,r);return l(),a}}return e}function Un(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return t=>(e?un:Hl)(t)}function Ks(e){const t=ht("useRender");t.render=e}function Yc(e,t,n){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:h=>h,r=arguments.length>4&&arguments[4]!==void 0?arguments[4]:h=>h;const i=ht("useProxiedModel"),o=Se(e[t]!==void 0?e[t]:n),l=wt(t),d=N(l!==t?()=>{var h,g,p,T;return e[t],!!(((h=i.vnode.props)!=null&&h.hasOwnProperty(t)||(g=i.vnode.props)!=null&&g.hasOwnProperty(l))&&((p=i.vnode.props)!=null&&p.hasOwnProperty(`onUpdate:${t}`)||(T=i.vnode.props)!=null&&T.hasOwnProperty(`onUpdate:${l}`)))}:()=>{var h,g;return e[t],!!((h=i.vnode.props)!=null&&h.hasOwnProperty(t)&&((g=i.vnode.props)!=null&&g.hasOwnProperty(`onUpdate:${t}`)))});uc(()=>!d.value,()=>{Ge(()=>e[t],h=>{o.value=h})});const c=N({get(){const h=e[t];return s(d.value?h:o.value)},set(h){const g=r(h),p=B(d.value?e[t]:o.value);p===g||s(p)===h||(o.value=g,i==null||i.emit(`update:${t}`,g))}});return Object.defineProperty(c,"externalValue",{get:()=>d.value?e[t]:o.value}),c}const Kc={badge:"Badge",open:"Open",close:"Close",dismiss:"Dismiss",confirmEdit:{ok:"OK",cancel:"Cancel"},dataIterator:{noResultsText:"No matching records found",loadingText:"Loading items..."},dataTable:{itemsPerPageText:"Rows per page:",ariaLabel:{sortDescending:"Sorted descending.",sortAscending:"Sorted ascending.",sortNone:"Not sorted.",activateNone:"Activate to remove sorting.",activateDescending:"Activate to sort descending.",activateAscending:"Activate to sort ascending."},sortBy:"Sort by"},dataFooter:{itemsPerPageText:"Items per page:",itemsPerPageAll:"All",nextPage:"Next page",prevPage:"Previous page",firstPage:"First page",lastPage:"Last page",pageText:"{0}-{1} of {2}"},dateRangeInput:{divider:"to"},datePicker:{itemsSelected:"{0} selected",range:{title:"Select dates",header:"Enter dates"},title:"Select date",header:"Enter date",input:{placeholder:"Enter date"}},noDataText:"No data available",carousel:{prev:"Previous visual",next:"Next visual",ariaLabel:{delimiter:"Carousel slide {0} of {1}"}},calendar:{moreEvents:"{0} more",today:"Today"},input:{clear:"Clear {0}",prependAction:"{0} prepended action",appendAction:"{0} appended action",otp:"Please enter OTP character {0}"},fileInput:{counter:"{0} files",counterSize:"{0} files ({1} in total)"},timePicker:{am:"AM",pm:"PM",title:"Select Time"},pagination:{ariaLabel:{root:"Pagination Navigation",next:"Next page",previous:"Previous page",page:"Go to page {0}",currentPage:"Page {0}, Current page",first:"First page",last:"Last page"}},stepper:{next:"Next",prev:"Previous"},rating:{ariaLabel:{item:"Rating {0} of {1}"}},loading:"Loading...",infiniteScroll:{loadMore:"Load more",empty:"No more"}},Nr="$vuetify.",Hr=(e,t)=>e.replace(/\{(\d+)\}/g,(n,s)=>String(t[+s])),_o=(e,t,n)=>function(s){for(var r=arguments.length,i=new Array(r>1?r-1:0),o=1;o<r;o++)i[o-1]=arguments[o];if(!s.startsWith(Nr))return Hr(s,i);const l=s.replace(Nr,""),a=e.value&&n.value[e.value],d=t.value&&n.value[t.value];let c=Tr(a,l,null);return c||(`${s}${e.value}`,c=Tr(d,l,null)),c||(c=s),typeof c!="string"&&(c=s),Hr(c,i)};function xo(e,t){return(n,s)=>new Intl.NumberFormat([e.value,t.value],s).format(n)}function os(e,t,n){const s=Yc(e,t,e[t]??n.value);return s.value=e[t]??n.value,Ge(n,r=>{e[t]==null&&(s.value=n.value)}),s}function wo(e){return t=>{const n=os(t,"locale",e.current),s=os(t,"fallback",e.fallback),r=os(t,"messages",e.messages);return{name:"vuetify",current:n,fallback:s,messages:r,t:_o(n,s,r),n:xo(n,s),provide:wo({current:n,fallback:s,messages:r})}}}function zc(e){const t=nt((e==null?void 0:e.locale)??"en"),n=nt((e==null?void 0:e.fallback)??"en"),s=Se({en:Kc,...e==null?void 0:e.messages});return{name:"vuetify",current:t,fallback:n,messages:s,t:_o(t,n,s),n:xo(t,n),provide:wo({current:t,fallback:n,messages:s})}}const xs=Symbol.for("vuetify:locale");function Gc(e){return e.name!=null}function Zc(e){const t=e!=null&&e.adapter&&Gc(e==null?void 0:e.adapter)?e==null?void 0:e.adapter:zc(e),n=qc(t,e);return{...t,...n}}function Jc(){return{af:!1,ar:!0,bg:!1,ca:!1,ckb:!1,cs:!1,de:!1,el:!1,en:!1,es:!1,et:!1,fa:!0,fi:!1,fr:!1,hr:!1,hu:!1,he:!0,id:!1,it:!1,ja:!1,km:!1,ko:!1,lv:!1,lt:!1,nl:!1,no:!1,pl:!1,pt:!1,ro:!1,ru:!1,sk:!1,sl:!1,srCyrl:!1,srLatn:!1,sv:!1,th:!1,tr:!1,az:!1,uk:!1,vi:!1,zhHans:!1,zhHant:!1}}function qc(e,t){const n=Se((t==null?void 0:t.rtl)??Jc()),s=N(()=>n.value[e.current.value]??!1);return{isRtl:s,rtl:n,rtlClasses:N(()=>`v-locale--is-${s.value?"rtl":"ltr"}`)}}function Xc(){const e=ze(xs);if(!e)throw new Error("[Vuetify] Could not find injected rtl instance");return{isRtl:e.isRtl,rtlClasses:e.rtlClasses}}const Wn={"001":1,AD:1,AE:6,AF:6,AG:0,AI:1,AL:1,AM:1,AN:1,AR:1,AS:0,AT:1,AU:1,AX:1,AZ:1,BA:1,BD:0,BE:1,BG:1,BH:6,BM:1,BN:1,BR:0,BS:0,BT:0,BW:0,BY:1,BZ:0,CA:0,CH:1,CL:1,CM:1,CN:1,CO:0,CR:1,CY:1,CZ:1,DE:1,DJ:6,DK:1,DM:0,DO:0,DZ:6,EC:1,EE:1,EG:6,ES:1,ET:0,FI:1,FJ:1,FO:1,FR:1,GB:1,"GB-alt-variant":0,GE:1,GF:1,GP:1,GR:1,GT:0,GU:0,HK:0,HN:0,HR:1,HU:1,ID:0,IE:1,IL:0,IN:0,IQ:6,IR:6,IS:1,IT:1,JM:0,JO:6,JP:0,KE:0,KG:1,KH:0,KR:0,KW:6,KZ:1,LA:0,LB:1,LI:1,LK:1,LT:1,LU:1,LV:1,LY:6,MC:1,MD:1,ME:1,MH:0,MK:1,MM:0,MN:1,MO:0,MQ:1,MT:0,MV:5,MX:0,MY:1,MZ:0,NI:0,NL:1,NO:1,NP:0,NZ:1,OM:6,PA:0,PE:0,PH:0,PK:0,PL:1,PR:0,PT:0,PY:0,QA:6,RE:1,RO:1,RS:1,RU:1,SA:0,SD:6,SE:1,SG:0,SI:1,SK:1,SM:1,SV:0,SY:6,TH:0,TJ:1,TM:1,TR:1,TT:0,TW:0,UA:1,UM:0,US:0,UY:1,UZ:1,VA:1,VE:0,VI:0,VN:1,WS:0,XK:1,YE:0,ZA:0,ZW:0};function Qc(e,t,n){const s=[];let r=[];const i=So(e),o=To(e),l=n??Wn[t.slice(-2).toUpperCase()]??0,a=(i.getDay()-l+7)%7,d=(o.getDay()-l+7)%7;for(let c=0;c<a;c++){const h=new Date(i);h.setDate(h.getDate()-(a-c)),r.push(h)}for(let c=1;c<=o.getDate();c++){const h=new Date(e.getFullYear(),e.getMonth(),c);r.push(h),r.length===7&&(s.push(r),r=[])}for(let c=1;c<7-d;c++){const h=new Date(o);h.setDate(h.getDate()+c),r.push(h)}return r.length>0&&s.push(r),s}function eu(e,t,n){const s=n??Wn[t.slice(-2).toUpperCase()]??0,r=new Date(e);for(;r.getDay()!==s;)r.setDate(r.getDate()-1);return r}function tu(e,t){const n=new Date(e),s=((Wn[t.slice(-2).toUpperCase()]??0)+6)%7;for(;n.getDay()!==s;)n.setDate(n.getDate()+1);return n}function So(e){return new Date(e.getFullYear(),e.getMonth(),1)}function To(e){return new Date(e.getFullYear(),e.getMonth()+1,0)}function nu(e){const t=e.split("-").map(Number);return new Date(t[0],t[1]-1,t[2])}const su=/^([12]\d{3}-([1-9]|0[1-9]|1[0-2])-([1-9]|0[1-9]|[12]\d|3[01]))$/;function Do(e){if(e==null)return new Date;if(e instanceof Date)return e;if(typeof e=="string"){let t;if(su.test(e))return nu(e);if(t=Date.parse(e),!isNaN(t))return new Date(t)}return null}const Vr=new Date(2e3,0,2);function ru(e,t){const n=t??Wn[e.slice(-2).toUpperCase()]??0;return ao(7).map(s=>{const r=new Date(Vr);return r.setDate(Vr.getDate()+n+s),new Intl.DateTimeFormat(e,{weekday:"narrow"}).format(r)})}function iu(e,t,n,s){const r=Do(e)??new Date,i=s==null?void 0:s[t];if(typeof i=="function")return i(r,t,n);let o={};switch(t){case"fullDate":o={year:"numeric",month:"long",day:"numeric"};break;case"fullDateWithWeekday":o={weekday:"long",year:"numeric",month:"long",day:"numeric"};break;case"normalDate":const l=r.getDate(),a=new Intl.DateTimeFormat(n,{month:"long"}).format(r);return`${l} ${a}`;case"normalDateWithWeekday":o={weekday:"short",day:"numeric",month:"short"};break;case"shortDate":o={month:"short",day:"numeric"};break;case"year":o={year:"numeric"};break;case"month":o={month:"long"};break;case"monthShort":o={month:"short"};break;case"monthAndYear":o={month:"long",year:"numeric"};break;case"monthAndDate":o={month:"long",day:"numeric"};break;case"weekday":o={weekday:"long"};break;case"weekdayShort":o={weekday:"short"};break;case"dayOfMonth":return new Intl.NumberFormat(n).format(r.getDate());case"hours12h":o={hour:"numeric",hour12:!0};break;case"hours24h":o={hour:"numeric",hour12:!1};break;case"minutes":o={minute:"numeric"};break;case"seconds":o={second:"numeric"};break;case"fullTime":o={hour:"numeric",minute:"numeric",second:"numeric",hour12:!0};break;case"fullTime12h":o={hour:"numeric",minute:"numeric",second:"numeric",hour12:!0};break;case"fullTime24h":o={hour:"numeric",minute:"numeric",second:"numeric",hour12:!1};break;case"fullDateTime":o={year:"numeric",month:"long",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric",hour12:!0};break;case"fullDateTime12h":o={year:"numeric",month:"long",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric",hour12:!0};break;case"fullDateTime24h":o={year:"numeric",month:"long",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric",hour12:!1};break;case"keyboardDate":o={year:"numeric",month:"2-digit",day:"2-digit"};break;case"keyboardDateTime":o={year:"numeric",month:"2-digit",day:"2-digit",hour:"numeric",minute:"numeric",second:"numeric",hour12:!1};break;case"keyboardDateTime12h":o={year:"numeric",month:"2-digit",day:"2-digit",hour:"numeric",minute:"numeric",second:"numeric",hour12:!0};break;case"keyboardDateTime24h":o={year:"numeric",month:"2-digit",day:"2-digit",hour:"numeric",minute:"numeric",second:"numeric",hour12:!1};break;default:o=i??{timeZone:"UTC",timeZoneName:"short"}}return new Intl.DateTimeFormat(n,o).format(r)}function ou(e,t){const n=e.toJsDate(t),s=n.getFullYear(),r=Mr(String(n.getMonth()+1),2,"0"),i=Mr(String(n.getDate()),2,"0");return`${s}-${r}-${i}`}function lu(e){const[t,n,s]=e.split("-").map(Number);return new Date(t,n-1,s)}function au(e,t){const n=new Date(e);return n.setMinutes(n.getMinutes()+t),n}function cu(e,t){const n=new Date(e);return n.setHours(n.getHours()+t),n}function uu(e,t){const n=new Date(e);return n.setDate(n.getDate()+t),n}function fu(e,t){const n=new Date(e);return n.setDate(n.getDate()+t*7),n}function du(e,t){const n=new Date(e);return n.setDate(1),n.setMonth(n.getMonth()+t),n}function hu(e){return e.getFullYear()}function mu(e){return e.getMonth()}function gu(e){return e.getDate()}function pu(e){return new Date(e.getFullYear(),e.getMonth()+1,1)}function vu(e){return new Date(e.getFullYear(),e.getMonth()-1,1)}function yu(e){return e.getHours()}function bu(e){return e.getMinutes()}function _u(e){return new Date(e.getFullYear(),0,1)}function xu(e){return new Date(e.getFullYear(),11,31)}function wu(e,t){return An(e,t[0])&&Du(e,t[1])}function Su(e){const t=new Date(e);return t instanceof Date&&!isNaN(t.getTime())}function An(e,t){return e.getTime()>t.getTime()}function Tu(e,t){return An(ws(e),ws(t))}function Du(e,t){return e.getTime()<t.getTime()}function Br(e,t){return e.getTime()===t.getTime()}function Cu(e,t){return e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}function Mu(e,t){return e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}function Ou(e,t){return e.getFullYear()===t.getFullYear()}function Iu(e,t,n){const s=new Date(e),r=new Date(t);switch(n){case"years":return s.getFullYear()-r.getFullYear();case"quarters":return Math.floor((s.getMonth()-r.getMonth()+(s.getFullYear()-r.getFullYear())*12)/4);case"months":return s.getMonth()-r.getMonth()+(s.getFullYear()-r.getFullYear())*12;case"weeks":return Math.floor((s.getTime()-r.getTime())/(1e3*60*60*24*7));case"days":return Math.floor((s.getTime()-r.getTime())/(1e3*60*60*24));case"hours":return Math.floor((s.getTime()-r.getTime())/(1e3*60*60));case"minutes":return Math.floor((s.getTime()-r.getTime())/(1e3*60));case"seconds":return Math.floor((s.getTime()-r.getTime())/1e3);default:return s.getTime()-r.getTime()}}function Au(e,t){const n=new Date(e);return n.setHours(t),n}function Eu(e,t){const n=new Date(e);return n.setMinutes(t),n}function Fu(e,t){const n=new Date(e);return n.setMonth(t),n}function Ru(e,t){const n=new Date(e);return n.setDate(t),n}function Pu(e,t){const n=new Date(e);return n.setFullYear(t),n}function ws(e){return new Date(e.getFullYear(),e.getMonth(),e.getDate(),0,0,0,0)}function $u(e){return new Date(e.getFullYear(),e.getMonth(),e.getDate(),23,59,59,999)}class ku{constructor(t){this.locale=t.locale,this.formats=t.formats}date(t){return Do(t)}toJsDate(t){return t}toISO(t){return ou(this,t)}parseISO(t){return lu(t)}addMinutes(t,n){return au(t,n)}addHours(t,n){return cu(t,n)}addDays(t,n){return uu(t,n)}addWeeks(t,n){return fu(t,n)}addMonths(t,n){return du(t,n)}getWeekArray(t,n){return Qc(t,this.locale,n?Number(n):void 0)}startOfWeek(t,n){return eu(t,this.locale,n?Number(n):void 0)}endOfWeek(t){return tu(t,this.locale)}startOfMonth(t){return So(t)}endOfMonth(t){return To(t)}format(t,n){return iu(t,n,this.locale,this.formats)}isEqual(t,n){return Br(t,n)}isValid(t){return Su(t)}isWithinRange(t,n){return wu(t,n)}isAfter(t,n){return An(t,n)}isAfterDay(t,n){return Tu(t,n)}isBefore(t,n){return!An(t,n)&&!Br(t,n)}isSameDay(t,n){return Cu(t,n)}isSameMonth(t,n){return Mu(t,n)}isSameYear(t,n){return Ou(t,n)}setMinutes(t,n){return Eu(t,n)}setHours(t,n){return Au(t,n)}setMonth(t,n){return Fu(t,n)}setDate(t,n){return Ru(t,n)}setYear(t,n){return Pu(t,n)}getDiff(t,n,s){return Iu(t,n,s)}getWeekdays(t){return ru(this.locale,t?Number(t):void 0)}getYear(t){return hu(t)}getMonth(t){return mu(t)}getDate(t){return gu(t)}getNextMonth(t){return pu(t)}getPreviousMonth(t){return vu(t)}getHours(t){return yu(t)}getMinutes(t){return bu(t)}startOfDay(t){return ws(t)}endOfDay(t){return $u(t)}startOfYear(t){return _u(t)}endOfYear(t){return xu(t)}}const Lu=Symbol.for("vuetify:date-options"),jr=Symbol.for("vuetify:date-adapter");function Nu(e,t){const n=st({adapter:ku,locale:{af:"af-ZA",bg:"bg-BG",ca:"ca-ES",ckb:"",cs:"cs-CZ",de:"de-DE",el:"el-GR",en:"en-US",et:"et-EE",fa:"fa-IR",fi:"fi-FI",hr:"hr-HR",hu:"hu-HU",he:"he-IL",id:"id-ID",it:"it-IT",ja:"ja-JP",ko:"ko-KR",lv:"lv-LV",lt:"lt-LT",nl:"nl-NL",no:"no-NO",pl:"pl-PL",pt:"pt-PT",ro:"ro-RO",ru:"ru-RU",sk:"sk-SK",sl:"sl-SI",srCyrl:"sr-SP",srLatn:"sr-SP",sv:"sv-SE",th:"th-TH",tr:"tr-TR",az:"az-AZ",uk:"uk-UA",vi:"vi-VN",zhHans:"zh-CN",zhHant:"zh-TW"}},e);return{options:n,instance:Hu(n,t)}}function Hu(e,t){const n=Ae(typeof e.adapter=="function"?new e.adapter({locale:e.locale[t.current.value]??t.current.value,formats:e.formats}):e.adapter);return Ge(t.current,s=>{n.locale=e.locale[s]??s??n.locale}),n}const Ur=Symbol.for("vuetify:display"),Wr={mobileBreakpoint:"lg",thresholds:{xs:0,sm:600,md:960,lg:1280,xl:1920,xxl:2560}},Vu=function(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Wr;return st(Wr,e)};function Yr(e){return Le&&!e?window.innerWidth:typeof e=="object"&&e.clientWidth||0}function Kr(e){return Le&&!e?window.innerHeight:typeof e=="object"&&e.clientHeight||0}function zr(e){const t=Le&&!e?window.navigator.userAgent:"ssr";function n(T){return!!t.match(T)}const s=n(/android/i),r=n(/iphone|ipad|ipod/i),i=n(/cordova/i),o=n(/electron/i),l=n(/chrome/i),a=n(/edge/i),d=n(/firefox/i),c=n(/opera/i),h=n(/win/i),g=n(/mac/i),p=n(/linux/i);return{android:s,ios:r,cordova:i,electron:o,chrome:l,edge:a,firefox:d,opera:c,win:h,mac:g,linux:p,touch:fc,ssr:t==="ssr"}}function Bu(e,t){const{thresholds:n,mobileBreakpoint:s}=Vu(e),r=nt(Kr(t)),i=nt(zr(t)),o=Ae({}),l=nt(Yr(t));function a(){r.value=Kr(),l.value=Yr()}function d(){a(),i.value=zr()}return Bn(()=>{const c=l.value<n.sm,h=l.value<n.md&&!c,g=l.value<n.lg&&!(h||c),p=l.value<n.xl&&!(g||h||c),T=l.value<n.xxl&&!(p||g||h||c),x=l.value>=n.xxl,I=c?"xs":h?"sm":g?"md":p?"lg":T?"xl":"xxl",P=typeof s=="number"?s:n[s],V=l.value<P;o.xs=c,o.sm=h,o.md=g,o.lg=p,o.xl=T,o.xxl=x,o.smAndUp=!c,o.mdAndUp=!(c||h),o.lgAndUp=!(c||h||g),o.xlAndUp=!(c||h||g||p),o.smAndDown=!(g||p||T||x),o.mdAndDown=!(p||T||x),o.lgAndDown=!(T||x),o.xlAndDown=!x,o.name=I,o.height=r.value,o.width=l.value,o.mobile=V,o.mobileBreakpoint=s,o.platform=i.value,o.thresholds=n}),Le&&window.addEventListener("resize",a,{passive:!0}),{...Ci(o),update:d,ssr:!!t}}const ju=Symbol.for("vuetify:goto");function Uu(){return{container:void 0,duration:300,layout:!1,offset:0,easing:"easeInOutCubic",patterns:{linear:e=>e,easeInQuad:e=>e**2,easeOutQuad:e=>e*(2-e),easeInOutQuad:e=>e<.5?2*e**2:-1+(4-2*e)*e,easeInCubic:e=>e**3,easeOutCubic:e=>--e**3+1,easeInOutCubic:e=>e<.5?4*e**3:(e-1)*(2*e-2)*(2*e-2)+1,easeInQuart:e=>e**4,easeOutQuart:e=>1- --e**4,easeInOutQuart:e=>e<.5?8*e**4:1-8*--e**4,easeInQuint:e=>e**5,easeOutQuint:e=>1+--e**5,easeInOutQuint:e=>e<.5?16*e**5:1+16*--e**5}}}function Wu(e,t){return{rtl:t.isRtl,options:st(Uu(),e)}}const Yu={collapse:"mdi-chevron-up",complete:"mdi-check",cancel:"mdi-close-circle",close:"mdi-close",delete:"mdi-close-circle",clear:"mdi-close-circle",success:"mdi-check-circle",info:"mdi-information",warning:"mdi-alert-circle",error:"mdi-close-circle",prev:"mdi-chevron-left",next:"mdi-chevron-right",checkboxOn:"mdi-checkbox-marked",checkboxOff:"mdi-checkbox-blank-outline",checkboxIndeterminate:"mdi-minus-box",delimiter:"mdi-circle",sortAsc:"mdi-arrow-up",sortDesc:"mdi-arrow-down",expand:"mdi-chevron-down",menu:"mdi-menu",subgroup:"mdi-menu-down",dropdown:"mdi-menu-down",radioOn:"mdi-radiobox-marked",radioOff:"mdi-radiobox-blank",edit:"mdi-pencil",ratingEmpty:"mdi-star-outline",ratingFull:"mdi-star",ratingHalf:"mdi-star-half-full",loading:"mdi-cached",first:"mdi-page-first",last:"mdi-page-last",unfold:"mdi-unfold-more-horizontal",file:"mdi-paperclip",plus:"mdi-plus",minus:"mdi-minus",calendar:"mdi-calendar",treeviewCollapse:"mdi-menu-down",treeviewExpand:"mdi-menu-right",eyeDropper:"mdi-eyedropper"},Ku={component:e=>La(Oo,{...e,class:"mdi"})},Co=[String,Function,Object,Array],Ss=Symbol.for("vuetify:icons"),Yn=He({icon:{type:Co},tag:{type:String,required:!0}},"icon"),Gr=Un()({name:"VComponentIcon",props:Yn(),setup(e,t){let{slots:n}=t;return()=>{const s=e.icon;return k(e.tag,null,{default:()=>{var r;return[e.icon?k(s,null,null):(r=n.default)==null?void 0:r.call(n)]}})}}}),Mo=un({name:"VSvgIcon",inheritAttrs:!1,props:Yn(),setup(e,t){let{attrs:n}=t;return()=>k(e.tag,ro(n,{style:null}),{default:()=>[k("svg",{class:"v-icon__svg",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",role:"img","aria-hidden":"true"},[Array.isArray(e.icon)?e.icon.map(s=>Array.isArray(s)?k("path",{d:s[0],"fill-opacity":s[1]},null):k("path",{d:s},null)):k("path",{d:e.icon},null)])]})}});un({name:"VLigatureIcon",props:Yn(),setup(e){return()=>k(e.tag,null,{default:()=>[e.icon]})}});const Oo=un({name:"VClassIcon",props:Yn(),setup(e){return()=>k(e.tag,{class:e.icon},null)}});function zu(){return{svg:{component:Mo},class:{component:Oo}}}function Gu(e){const t=zu(),n=(e==null?void 0:e.defaultSet)??"mdi";return n==="mdi"&&!t.mdi&&(t.mdi=Ku),st({defaultSet:n,sets:t,aliases:{...Yu,vuetify:["M8.2241 14.2009L12 21L22 3H14.4459L8.2241 14.2009Z",["M7.26303 12.4733L7.00113 12L2 3H12.5261C12.5261 3 12.5261 3 12.5261 3L7.26303 12.4733Z",.6]],"vuetify-outline":"svg:M7.26 12.47 12.53 3H2L7.26 12.47ZM14.45 3 8.22 14.2 12 21 22 3H14.45ZM18.6 5 12 16.88 10.51 14.2 15.62 5ZM7.26 8.35 5.4 5H9.13L7.26 8.35Z","vuetify-play":["m6.376 13.184-4.11-7.192C1.505 4.66 2.467 3 4.003 3h8.532l-.953 1.576-.006.01-.396.677c-.429.732-.214 1.507.194 2.015.404.503 1.092.878 1.869.806a3.72 3.72 0 0 1 1.005.022c.276.053.434.143.523.237.138.146.38.635-.25 2.09-.893 1.63-1.553 1.722-1.847 1.677-.213-.033-.468-.158-.756-.406a4.95 4.95 0 0 1-.8-.927c-.39-.564-1.04-.84-1.66-.846-.625-.006-1.316.27-1.693.921l-.478.826-.911 1.506Z",["M9.093 11.552c.046-.079.144-.15.32-.148a.53.53 0 0 1 .43.207c.285.414.636.847 1.046 1.2.405.35.914.662 1.516.754 1.334.205 2.502-.698 3.48-2.495l.014-.028.013-.03c.687-1.574.774-2.852-.005-3.675-.37-.391-.861-.586-1.333-.676a5.243 5.243 0 0 0-1.447-.044c-.173.016-.393-.073-.54-.257-.145-.18-.127-.316-.082-.392l.393-.672L14.287 3h5.71c1.536 0 2.499 1.659 1.737 2.992l-7.997 13.996c-.768 1.344-2.706 1.344-3.473 0l-3.037-5.314 1.377-2.278.004-.006.004-.007.481-.831Z",.6]]}},e)}const Zu=e=>{const t=ze(Ss);if(!t)throw new Error("Missing Vuetify Icons provide!");return{iconData:N(()=>{var a;const s=Ti(e);if(!s)return{component:Gr};let r=s;if(typeof r=="string"&&(r=r.trim(),r.startsWith("$")&&(r=(a=t.aliases)==null?void 0:a[r.slice(1)])),Array.isArray(r))return{component:Mo,icon:r};if(typeof r!="string")return{component:Gr,icon:r};const i=Object.keys(t.sets).find(d=>typeof r=="string"&&r.startsWith(`${d}:`)),o=i?r.slice(i.length+1):r;return{component:t.sets[i??t.defaultSet].component,icon:o}})}},En=Symbol.for("vuetify:theme"),Io=He({theme:String},"theme");function Zr(){return{defaultTheme:"light",variations:{colors:[],lighten:0,darken:0},themes:{light:{dark:!1,colors:{background:"#FFFFFF",surface:"#FFFFFF","surface-bright":"#FFFFFF","surface-light":"#EEEEEE","surface-variant":"#424242","on-surface-variant":"#EEEEEE",primary:"#1867C0","primary-darken-1":"#1F5592",secondary:"#48A9A6","secondary-darken-1":"#018786",error:"#B00020",info:"#2196F3",success:"#4CAF50",warning:"#FB8C00"},variables:{"border-color":"#000000","border-opacity":.12,"high-emphasis-opacity":.87,"medium-emphasis-opacity":.6,"disabled-opacity":.38,"idle-opacity":.04,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.12,"dragged-opacity":.08,"theme-kbd":"#212529","theme-on-kbd":"#FFFFFF","theme-code":"#F5F5F5","theme-on-code":"#000000"}},dark:{dark:!0,colors:{background:"#121212",surface:"#212121","surface-bright":"#ccbfd6","surface-light":"#424242","surface-variant":"#a3a3a3","on-surface-variant":"#424242",primary:"#2196F3","primary-darken-1":"#277CC1",secondary:"#54B6B2","secondary-darken-1":"#48A9A6",error:"#CF6679",info:"#2196F3",success:"#4CAF50",warning:"#FB8C00"},variables:{"border-color":"#FFFFFF","border-opacity":.12,"high-emphasis-opacity":1,"medium-emphasis-opacity":.7,"disabled-opacity":.5,"idle-opacity":.1,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.16,"dragged-opacity":.08,"theme-kbd":"#212529","theme-on-kbd":"#FFFFFF","theme-code":"#343434","theme-on-code":"#CCCCCC"}}}}}function Ju(){var s,r;let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Zr();const t=Zr();if(!e)return{...t,isDisabled:!0};const n={};for(const[i,o]of Object.entries(e.themes??{})){const l=o.dark||i==="dark"?(s=t.themes)==null?void 0:s.dark:(r=t.themes)==null?void 0:r.light;n[i]=st(l,o)}return st(t,{...e,themes:n})}function qu(e){const t=Ju(e),n=Se(t.defaultTheme),s=Se(t.themes),r=N(()=>{const c={};for(const[h,g]of Object.entries(s.value)){const p=c[h]={...g,colors:{...g.colors}};if(t.variations)for(const T of t.variations.colors){const x=p.colors[T];if(x)for(const I of["lighten","darken"]){const P=I==="lighten"?Lc:Nc;for(const V of ao(t.variations[I],1))p.colors[`${T}-${I}-${V}`]=Pc(P(Ye(x),V))}}for(const T of Object.keys(p.colors)){if(/^on-[a-z]/.test(T)||p.colors[`on-${T}`])continue;const x=`on-${T}`,I=Ye(p.colors[T]);p.colors[x]=po(I)}}return c}),i=N(()=>r.value[n.value]),o=N(()=>{var T;const c=[];(T=i.value)!=null&&T.dark&&yt(c,":root",["color-scheme: dark"]),yt(c,":root",Jr(i.value));for(const[x,I]of Object.entries(r.value))yt(c,`.v-theme--${x}`,[`color-scheme: ${I.dark?"dark":"normal"}`,...Jr(I)]);const h=[],g=[],p=new Set(Object.values(r.value).flatMap(x=>Object.keys(x.colors)));for(const x of p)/^on-[a-z]/.test(x)?yt(g,`.${x}`,[`color: rgb(var(--v-theme-${x})) !important`]):(yt(h,`.bg-${x}`,[`--v-theme-overlay-multiplier: var(--v-theme-${x}-overlay-multiplier)`,`background-color: rgb(var(--v-theme-${x})) !important`,`color: rgb(var(--v-theme-on-${x})) !important`]),yt(g,`.text-${x}`,[`color: rgb(var(--v-theme-${x})) !important`]),yt(g,`.border-${x}`,[`--v-border-color: var(--v-theme-${x})`]));return c.push(...h,...g),c.map((x,I)=>I===0?x:`    ${x}`).join("")});function l(){return{style:[{children:o.value,id:"vuetify-theme-stylesheet",nonce:t.cspNonce||!1}]}}function a(c){if(t.isDisabled)return;const h=c._context.provides.usehead;if(h)if(h.push){const g=h.push(l);Le&&Ge(o,()=>{g.patch(l)})}else Le?(h.addHeadObjs(N(l)),Bn(()=>h.updateDOM())):h.addHeadObjs(l());else{let p=function(){if(typeof document<"u"&&!g){const T=document.createElement("style");T.type="text/css",T.id="vuetify-theme-stylesheet",t.cspNonce&&T.setAttribute("nonce",t.cspNonce),g=T,document.head.appendChild(g)}g&&(g.innerHTML=o.value)},g=Le?document.getElementById("vuetify-theme-stylesheet"):null;Le?Ge(o,p,{immediate:!0}):p()}}const d=N(()=>t.isDisabled?void 0:`v-theme--${n.value}`);return{install:a,isDisabled:t.isDisabled,name:n,themes:s,current:i,computedThemes:r,themeClasses:d,styles:o,global:{name:n,current:i}}}function Ao(e){ht("provideTheme");const t=ze(En,null);if(!t)throw new Error("Could not find Vuetify theme injection");const n=N(()=>e.theme??t.name.value),s=N(()=>t.themes.value[n.value]),r=N(()=>t.isDisabled?void 0:`v-theme--${n.value}`),i={...t,name:n,current:s,themeClasses:r};return Vn(En,i),i}function yt(e,t,n){e.push(`${t} {
`,...n.map(s=>`  ${s};
`),`}
`)}function Jr(e){const t=e.dark?2:1,n=e.dark?1:2,s=[];for(const[r,i]of Object.entries(e.colors)){const o=Ye(i);s.push(`--v-theme-${r}: ${o.r},${o.g},${o.b}`),r.startsWith("on-")||s.push(`--v-theme-${r}-overlay-multiplier: ${Hc(i)>.18?t:n}`)}for(const[r,i]of Object.entries(e.variables)){const o=typeof i=="string"&&i.startsWith("#")?Ye(i):void 0,l=o?`${o.r}, ${o.g}, ${o.b}`:void 0;s.push(`--v-${r}: ${l??i}`)}return s}function Xu(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"content";const n=yc(),s=Se();if(Le){const r=new ResizeObserver(i=>{i.length&&(t==="content"?s.value=i[0].contentRect:s.value=i[0].target.getBoundingClientRect())});Li(()=>{r.disconnect()}),Ge(()=>n.el,(i,o)=>{o&&(r.unobserve(o),s.value=void 0),i&&r.observe(i)},{flush:"post"})}return{resizeRef:n,contentRect:Ln(s)}}const Ts=Symbol.for("vuetify:layout"),Qu=Symbol.for("vuetify:layout-item"),qr=1e3,ef=He({overlaps:{type:Array,default:()=>[]},fullHeight:Boolean},"layout");function tf(){const e=ze(Ts);if(!e)throw new Error("[Vuetify] Could not find injected layout");return{getLayoutItem:e.getLayoutItem,mainRect:e.mainRect,mainStyles:e.mainStyles}}const nf=(e,t,n,s)=>{let r={top:0,left:0,right:0,bottom:0};const i=[{id:"",layer:{...r}}];for(const o of e){const l=t.get(o),a=n.get(o),d=s.get(o);if(!l||!a||!d)continue;const c={...r,[l.value]:parseInt(r[l.value],10)+(d.value?parseInt(a.value,10):0)};i.push({id:o,layer:c}),r=c}return i};function sf(e){const t=ze(Ts,null),n=N(()=>t?t.rootZIndex.value-100:qr),s=Se([]),r=Ae(new Map),i=Ae(new Map),o=Ae(new Map),l=Ae(new Map),a=Ae(new Map),{resizeRef:d,contentRect:c}=Xu(),h=N(()=>{const W=new Map,te=e.overlaps??[];for(const j of te.filter(ie=>ie.includes(":"))){const[ie,Q]=j.split(":");if(!s.value.includes(ie)||!s.value.includes(Q))continue;const ue=r.get(ie),Te=r.get(Q),Ve=i.get(ie),qe=i.get(Q);!ue||!Te||!Ve||!qe||(W.set(Q,{position:ue.value,amount:parseInt(Ve.value,10)}),W.set(ie,{position:Te.value,amount:-parseInt(qe.value,10)}))}return W}),g=N(()=>{const W=[...new Set([...o.values()].map(j=>j.value))].sort((j,ie)=>j-ie),te=[];for(const j of W){const ie=s.value.filter(Q=>{var ue;return((ue=o.get(Q))==null?void 0:ue.value)===j});te.push(...ie)}return nf(te,r,i,l)}),p=N(()=>!Array.from(a.values()).some(W=>W.value)),T=N(()=>g.value[g.value.length-1].layer),x=N(()=>({"--v-layout-left":_e(T.value.left),"--v-layout-right":_e(T.value.right),"--v-layout-top":_e(T.value.top),"--v-layout-bottom":_e(T.value.bottom),...p.value?void 0:{transition:"none"}})),I=N(()=>g.value.slice(1).map((W,te)=>{let{id:j}=W;const{layer:ie}=g.value[te],Q=i.get(j),ue=r.get(j);return{id:j,...ie,size:Number(Q.value),position:ue.value}})),P=W=>I.value.find(te=>te.id===W),V=ht("createLayout"),X=nt(!1);Ns(()=>{X.value=!0}),Vn(Ts,{register:(W,te)=>{let{id:j,order:ie,position:Q,layoutSize:ue,elementSize:Te,active:Ve,disableTransitions:qe,absolute:fn}=te;o.set(j,ie),r.set(j,Q),i.set(j,ue),l.set(j,Ve),qe&&a.set(j,qe);const G=Kt(Qu,V==null?void 0:V.vnode).indexOf(W);G>-1?s.value.splice(G,0,j):s.value.push(j);const Y=N(()=>I.value.findIndex(de=>de.id===j)),Pe=N(()=>n.value+g.value.length*2-Y.value*2),Ct=N(()=>{const de=Q.value==="left"||Q.value==="right",Mt=Q.value==="right",Kn=Q.value==="bottom",Vt=Te.value??ue.value,mt=Vt===0?"%":"px",gt={[Q.value]:0,zIndex:Pe.value,transform:`translate${de?"X":"Y"}(${(Ve.value?0:-(Vt===0?100:Vt))*(Mt||Kn?-1:1)}${mt})`,position:fn.value||n.value!==qr?"absolute":"fixed",...p.value?void 0:{transition:"none"}};if(!X.value)return gt;const xe=I.value[Y.value];if(!xe)throw new Error(`[Vuetify] Could not find layout item "${j}"`);const Ot=h.value.get(j);return Ot&&(xe[Ot.position]+=Ot.amount),{...gt,height:de?`calc(100% - ${xe.top}px - ${xe.bottom}px)`:Te.value?`${Te.value}px`:void 0,left:Mt?void 0:`${xe.left}px`,right:Mt?`${xe.right}px`:void 0,top:Q.value!=="bottom"?`${xe.top}px`:void 0,bottom:Q.value!=="top"?`${xe.bottom}px`:void 0,width:de?Te.value?`${Te.value}px`:void 0:`calc(100% - ${xe.left}px - ${xe.right}px)`}}),Xe=N(()=>({zIndex:Pe.value-1}));return{layoutItemStyles:Ct,layoutItemScrimStyles:Xe,zIndex:Pe}},unregister:W=>{o.delete(W),r.delete(W),i.delete(W),l.delete(W),a.delete(W),s.value=s.value.filter(te=>te!==W)},mainRect:T,mainStyles:x,getLayoutItem:P,items:I,layoutRect:c,rootZIndex:n});const E=N(()=>["v-layout",{"v-layout--full-height":e.fullHeight}]),se=N(()=>({zIndex:t?n.value:void 0,position:t?"relative":void 0,overflow:t?"hidden":void 0}));return{layoutClasses:E,layoutStyles:se,getLayoutItem:P,items:I,layoutRect:c,layoutRef:d}}function Eo(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const{blueprint:t,...n}=e,s=st(t,n),{aliases:r={},components:i={},directives:o={}}=s,l=jc(s.defaults),a=Bu(s.display,s.ssr),d=qu(s.theme),c=Gu(s.icons),h=Zc(s.locale),g=Nu(s.date,h),p=Wu(s.goTo,h);return{install:x=>{for(const I in o)x.directive(I,o[I]);for(const I in i)x.component(I,i[I]);for(const I in r)x.component(I,un({...r[I],name:I,aliasName:r[I].name}));if(d.install(x),x.provide(on,l),x.provide(Ur,a),x.provide(En,d),x.provide(Ss,c),x.provide(xs,h),x.provide(Lu,g.options),x.provide(jr,g.instance),x.provide(ju,p),Le&&s.ssr)if(x.$nuxt)x.$nuxt.hook("app:suspense:resolve",()=>{a.update()});else{const{mount:I}=x;x.mount=function(){const P=I(...arguments);return Ii(()=>a.update()),x.mount=I,P}}yo.reset(),x.mixin({computed:{$vuetify(){return Ae({defaults:Et.call(this,on),display:Et.call(this,Ur),theme:Et.call(this,En),icons:Et.call(this,Ss),locale:Et.call(this,xs),date:Et.call(this,jr)})}}})},defaults:l,display:a,theme:d,icons:c,locale:h,date:g,goTo:p}}const rf="3.7.3";Eo.version=rf;function Et(e){var s,r;const t=this.$,n=((s=t.parent)==null?void 0:s.provides)??((r=t.vnode.appContext)==null?void 0:r.provides);if(n&&e in n)return n[e]}const of=Eo();function lf(e){e.use(of)}const Je=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},af={key:0,class:"container"},cf={class:"contact-details-row"},uf={id:"name"},ff={key:0,class:"role"},df={key:1,class:"role"},hf={class:"communication-row"},mf={class:"dialogue-card",id:"speech"},gf={class:"dialogue-responses"},pf=["onClick"],vf={class:"dialogue-response-icon"},yf={__name:"Dialogue",setup(e){const t=Se(""),n=Se(void 0),s=Se(void 0);let r=null;window.addEventListener("message",a=>{if(a.data.dialogue!=null)if(Array.isArray(a.data.dialogue)){const d=Math.floor(Math.random()*a.data.dialogue.length);i.value=a.data.dialogue[d]}else i.value=a.data.dialogue;a.data.characterRole!=null&&(n.value=a.data.characterRole),a.data.characterRep!=null&&(s.value=a.data.characterRep),a.data.characterName!=null&&(t.value=a.data.characterName),a.data.ped!=null&&(r=a.data.ped)});const i=Se(void 0);function o(){i.value=void 0,fetch("http://core_hud/characterDialogueClose",{method:"POST",body:JSON.stringify({})}),r=null}function l(a){if(a.responses){const d=Math.floor(Math.random()*a.responses.length);i.value=a.responses[d]}else a.event&&(fetch("http://core_hud/characterDialogueResponse",{method:"POST",body:JSON.stringify({event:a.event,data:a.data,ped:r})}),o())}return(a,d)=>i.value?(L(),z("div",af,[O("div",cf,[O("span",uf,ne(t.value),1),O("div",null,[s.value!=null?(L(),z("span",ff,ne(s.value)+" REP",1)):le("",!0),n.value!=null?(L(),z("span",df,ne(n.value),1)):le("",!0)])]),O("div",hf,[O("div",mf,ne(i.value.characterText),1)]),O("div",gf,[(L(!0),z(we,null,yn(i.value.responses,(c,h)=>(L(),z("div",{class:"dialogue-card dialogue-response",onClick:g=>l(c)},[O("div",vf,ne(h+1),1),O("span",null,ne(c.responseText),1)],8,pf))),256))])])):le("",!0)}},Fo=Je(yf,[["__scopeId","data-v-ab27e8a5"]]),bf={name:"Notification",props:{item:Object}},_f={key:0,class:"pill code"},xf={key:1,class:"title"},wf={key:2,class:"pill callsign"},Sf={key:0,class:"location"},Tf={key:1,class:"direction"},Df={key:2,class:"direction"},Cf=["innerHTML"];function Mf(e,t,n,s,r,i){var o,l,a,d,c,h,g,p,T,x,I,P;return L(),z("div",{class:Fe(["main-container",[n.item.priority,n.item.job]])},[(o=n.item.data)!=null&&o.code||n.item.title||(l=n.item.data)!=null&&l.callsign?(L(),z("div",{key:0,class:Fe(["header",{animation:!((a=n.item.overrides)!=null&&a.headerBackground)}]),style:Ee({background:(d=n.item.overrides)==null?void 0:d.headerBackground,color:(c=n.item.overrides)==null?void 0:c.headerTextColor})},[(h=n.item.data)!=null&&h.code?(L(),z("span",_f,ne(n.item.data.code),1)):le("",!0),n.item.title?(L(),z("span",xf,ne(n.item.title),1)):le("",!0),(g=n.item.data)!=null&&g.callsign?(L(),z("span",wf,ne(n.item.data.callsign),1)):le("",!0)],6)):le("",!0),O("div",{class:"body",style:Ee({background:(p=n.item.overrides)==null?void 0:p.bodyBackground,color:(T=n.item.overrides)==null?void 0:T.bodyTextColor})},[(x=n.item.data)!=null&&x.location?(L(),z("div",Sf,[t[0]||(t[0]=O("i",{class:"fas fa-map-marker-alt"},null,-1)),O("span",null,ne(n.item.data.location),1)])):le("",!0),(I=n.item.data)!=null&&I.direction?(L(),z("div",Tf,[t[1]||(t[1]=O("i",{class:"fas fa-compass"},null,-1)),O("span",null,ne(n.item.data.direction),1)])):le("",!0),(P=n.item.data)!=null&&P.message?(L(),z("div",Df,[t[2]||(t[2]=O("i",{class:"fas fa-comment-alt"},null,-1)),O("span",{innerHTML:n.item.data.message},null,8,Cf)])):le("",!0)],4)],2)}const Ro=Je(bf,[["render",Mf],["__scopeId","data-v-3a8a6d06"]]),Of={name:"Notifications",components:{Notification:Ro},props:{items:Array}},If={class:"notifications"},Af={class:"top-right"},Ef={class:"bottom-right"},Ff={class:"top-left"};function Rf(e,t,n,s,r,i){const o=Ro;return L(),z("div",If,[O("div",Af,[(L(!0),z(we,null,yn(n.items.filter(l=>{var a,d;return!((a=l.options)!=null&&a.position)||((d=l.options)==null?void 0:d.position)==="top-right"}),l=>(L(),xt(o,{item:l},null,8,["item"]))),256))]),O("div",Ef,[(L(!0),z(we,null,yn(n.items.filter(l=>{var a;return((a=l.options)==null?void 0:a.position)==="bottom-right"}),l=>(L(),xt(o,{item:l},null,8,["item"]))),256))]),O("div",Ff,[(L(!0),z(we,null,yn(n.items.filter(l=>{var a;return((a=l.options)==null?void 0:a.position)==="top-left"}),l=>(L(),xt(o,{item:l},null,8,["item"]))),256))])])}const Po=Je(Of,[["render",Rf],["__scopeId","data-v-92d62b2a"]]);function Pf(e){return uo(()=>{const t=[],n={};if(e.value.background)if(_s(e.value.background)){if(n.backgroundColor=e.value.background,!e.value.text&&Fc(e.value.background)){const s=Ye(e.value.background);if(s.a==null||s.a===1){const r=po(s);n.color=r,n.caretColor=r}}}else t.push(`bg-${e.value.background}`);return e.value.text&&(_s(e.value.text)?(n.color=e.value.text,n.caretColor=e.value.text):t.push(`text-${e.value.text}`)),{colorClasses:t,colorStyles:n}})}function $f(e,t){const n=N(()=>({text:oe(e)?e.value:null})),{colorClasses:s,colorStyles:r}=Pf(n);return{textColorClasses:s,textColorStyles:r}}const kf=["x-small","small","default","large","x-large"],Lf=He({size:{type:[String,Number],default:"default"}},"size");function Nf(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Vc();return uo(()=>{let n,s;return vc(kf,e.size)?n=`${t}--size-${e.size}`:e.size&&(s={width:_e(e.size),height:_e(e.size)}),{sizeClasses:n,sizeStyles:s}})}const $o=He({tag:{type:String,default:"div"}},"tag"),Hf=He({color:String,disabled:Boolean,start:Boolean,end:Boolean,icon:Co,...Ys(),...Lf(),...$o({tag:"i"}),...Io()},"VIcon"),Rt=Un()({name:"VIcon",props:Hf(),setup(e,t){let{attrs:n,slots:s}=t;const r=Se(),{themeClasses:i}=Ao(e),{iconData:o}=Zu(N(()=>r.value||e.icon)),{sizeClasses:l}=Nf(e),{textColorClasses:a,textColorStyles:d}=$f(Il(e,"color"));return Ks(()=>{var g,p;const c=(g=s.default)==null?void 0:g.call(s);c&&(r.value=(p=co(c).filter(T=>T.type===an&&T.children&&typeof T.children=="string")[0])==null?void 0:p.children);const h=!!(n.onClick||n.onClickOnce);return k(o.value.component,{tag:e.tag,icon:o.value.icon,class:["v-icon","notranslate",i.value,l.value,a.value,{"v-icon--clickable":h,"v-icon--disabled":e.disabled,"v-icon--start":e.start,"v-icon--end":e.end},e.class],style:[l.value?void 0:{fontSize:_e(e.size),height:_e(e.size),width:_e(e.size)},d.value,e.style],role:h?"button":void 0,"aria-hidden":!h,tabindex:h?e.disabled?-1:0:void 0},{default:()=>[c]})}),{}}}),Vf={name:"Voice",props:{active:{required:!0},level:Number},computed:{levelIcon(){switch(this.level){case 1:return"mdi-volume-medium";case 2:return"mdi-volume-high";case 3:return"mdi-volume-low";default:return"mdi-volume-medium"}}}},Bf={class:"main-container"};function jf(e,t,n,s,r,i){return L(),z("div",Bf,[k(Rt,{class:Fe({active:n.active}),icon:i.levelIcon},null,8,["class","icon"])])}const ko=Je(Vf,[["render",jf],["__scopeId","data-v-e2c609c7"]]),Uf={name:"Vehicle",props:{vehicleType:String,seatbelt:Boolean,speed:Number,fuel:Number},computed:{speedLabel(){const e=this.speed;switch(this.vehicleType){case"air":return`${e} knots`;default:return`${e} km/h`}}}},Wf={class:"main-container"},Yf={class:"fuel"},Kf={class:"speed"};function zf(e,t,n,s,r,i){return L(),z("div",Wf,[O("span",{class:Fe(["seatbelt",{active:n.seatbelt}])},t[0]||(t[0]=[O("span",null,"BELT",-1)]),2),O("span",Yf,[k(Rt,{icon:"mdi-gas-station"}),O("span",{class:Fe(["value",{empty:n.fuel<=20,critical:n.fuel>20&&n.fuel<=40,full:n.fuel>40}])},ne(n.fuel)+"%",3)]),O("span",Kf,[O("span",{class:Fe(["value",{high:n.speed>=200,medium:n.speed>=100&&n.speed<200,low:n.speed<100}])},ne(i.speedLabel),3)])])}const Lo=Je(Uf,[["render",zf],["__scopeId","data-v-1d3f8279"]]),Gf={name:"Location",props:{heading:String,street:String,suburb:String,postcode:String,time:String},data:()=>({})},Zf={class:"main-container"},Jf={class:"heading-street"},qf={class:"street"},Xf={class:"suburb-postcode-time"};function Qf(e,t,n,s,r,i){return L(),z("div",Zf,[O("div",Jf,[so(ne(n.heading)+" | ",1),O("span",qf,ne(n.street),1)]),O("div",Xf,ne(n.suburb)+" | "+ne(n.postcode)+" | "+ne(n.time),1)])}const No=Je(Gf,[["render",Qf],["__scopeId","data-v-709fe31e"]]),ed=""+new URL("christmas-lights-56LhR7CZ.svg",import.meta.url).href,td=""+new URL("santa-hat-D_Ebbfjw.svg",import.meta.url).href,nd=""+new URL("snow-Dt26Q5ii.webp",import.meta.url).href,sd={name:"Health",data:()=>({theme:null,themes:{halloween:{healthIcon:"mdi-skull",deadIcon:"mdi-coffin",armourIcon:"mdi-shield-cross"},christmas:{healthIcon:"mdi-snowflake",deadIcon:"mdi-ornament"}},defaultTheme:{healthIcon:"mdi-heart-pulse",deadIcon:"mdi-wheelchair-accessibility",overhealthIcon:"mdi-plus-box",armourIcon:"mdi-shield"},christmasLightsSvg:ed,snowWebp:nd,santaHatSvg:td}),props:{health:Number,armour:Number},computed:{currentTheme(){return{...this.defaultTheme,...this.themes[this.theme]||{}}},healthValue(){return Math.min(this.health,100)},overhealth(){return Math.max(this.health-100,0)},healthLabel(){const e=this.healthValue;if(e<=0)return"You are unconscious";if(e<=10)return"Critical";if(e<=25)return"Bleeding";if(e<=50)return"Injured";if(e<=75)return"Hurt";if(e<=100)return"Healthy"}}},rd={key:0,class:"theme-overlay"},id={class:"halloween-icon"},od={key:1,class:"theme-overlay"},ld={class:"christmas-icon"},ad=["src"],cd={class:"snow"},ud=["src"],fd={class:"lights"},dd=["src"],hd=["src"],md=["src"],gd=["src"],pd=["src"],vd={class:"text"},yd={class:"text"},bd={class:"text"};function _d(e,t,n,s,r,i){return L(),z("div",{class:Fe(["main-container",e.theme])},[e.theme==="halloween"?(L(),z("div",rd,[O("div",id,[k(Rt,{icon:"mdi-ghost"})])])):le("",!0),e.theme==="christmas"?(L(),z("div",od,[O("div",ld,[O("img",{src:e.santaHatSvg},null,8,ad)]),O("div",cd,[O("img",{class:"lights-svg",src:e.snowWebp},null,8,ud)]),O("div",fd,[O("img",{class:"lights-svg",src:e.christmasLightsSvg},null,8,dd),O("img",{class:"lights-svg",src:e.christmasLightsSvg},null,8,hd),O("img",{class:"lights-svg",src:e.christmasLightsSvg},null,8,md),O("img",{class:"lights-svg",src:e.christmasLightsSvg},null,8,gd),O("img",{class:"lights-svg",src:e.christmasLightsSvg},null,8,pd)])])):le("",!0),O("div",{class:Fe(["health",{unconscious:n.health<=0}]),style:Ee({flexBasis:`${i.healthValue}%`})},[n.health>0?(L(),z("div",{key:0,class:"flowbar",style:Ee({width:n.armour<=0?`${i.healthValue}%`:"100%"})},null,4)):le("",!0),O("div",vd,[k(Rt,{icon:i.healthValue>0?i.currentTheme.healthIcon:i.currentTheme.deadIcon,size:"x-small"},null,8,["icon"]),O("span",null,ne(i.healthLabel),1)])],6),n.health>0&&i.overhealth>0?(L(),z("div",{key:2,class:"overhealth",style:Ee({flexBasis:`${i.overhealth}%`})},[O("div",yd,[k(Rt,{icon:i.currentTheme.overhealthIcon,size:"x-small"},null,8,["icon"]),O("span",null,ne(i.overhealth)+"%",1)])],4)):le("",!0),n.health>0&&n.armour>0?(L(),z("div",{key:3,class:"armour",style:Ee({flexBasis:`${n.armour}%`})},[O("div",bd,[k(Rt,{icon:i.currentTheme.armourIcon,size:"x-small"},null,8,["icon"]),O("span",null,ne(n.armour)+"%",1)])],4)):le("",!0)],2)}const Ho=Je(sd,[["render",_d],["__scopeId","data-v-647d1fbd"]]),xd={name:"Health",props:{time:Number},data:()=>({})};function wd(e,t,n,s,r,i){return L(),z("div",{class:"main-container",style:Ee({visibility:n.time>=0?"visible":"hidden"})},[O("div",{class:Fe(["underwater",{drowning:n.time<=0}])},[n.time>0?(L(),z("div",{key:0,class:"flowbar",style:Ee({width:`${n.time}%`})},null,4)):le("",!0)],2)],4)}const Vo=Je(xd,[["render",wd],["__scopeId","data-v-93436bd3"]]),Sd={name:"Hud",components:{Health:Ho,Location:No,Voice:ko,Vehicle:Lo,Underwater:Vo},props:{hud:Object},data:()=>({})},Td={class:"hud"},Dd={class:"entity-details"},Cd={class:"underwater"},Md={class:"health"},Od={class:"vehicle-location"},Id={class:"voice"};function Ad(e,t,n,s,r,i){const o=Vo,l=Ho,a=No,d=Lo,c=ko;return L(),z("div",Td,[O("div",Dd,[O("div",Cd,[k(o,{time:n.hud.underwaterTime},null,8,["time"])]),O("div",Md,[k(l,{health:n.hud.health,armour:n.hud.armour},null,8,["health","armour"])])]),O("div",Od,[k(a,{heading:n.hud.location.heading,street:n.hud.location.street,suburb:n.hud.location.suburb,postcode:n.hud.location.postcode,time:n.hud.location.time},null,8,["heading","street","suburb","postcode","time"]),n.hud.vehicle.inVehicle?(L(),xt(d,{key:0,vehicleType:n.hud.vehicle.type,seatbelt:n.hud.vehicle.seatbelt,fuel:n.hud.vehicle.fuel,speed:n.hud.vehicle.speed},null,8,["vehicleType","seatbelt","fuel","speed"])):le("",!0)]),O("div",Id,[k(c,{active:n.hud.voice.active,level:n.hud.voice.level},null,8,["active","level"])])])}const Bo=Je(Sd,[["render",Ad],["__scopeId","data-v-fbac2b94"]]),ce=[];for(let e=0;e<256;++e)ce.push((e+256).toString(16).slice(1));function Ed(e,t=0){return(ce[e[t+0]]+ce[e[t+1]]+ce[e[t+2]]+ce[e[t+3]]+"-"+ce[e[t+4]]+ce[e[t+5]]+"-"+ce[e[t+6]]+ce[e[t+7]]+"-"+ce[e[t+8]]+ce[e[t+9]]+"-"+ce[e[t+10]]+ce[e[t+11]]+ce[e[t+12]]+ce[e[t+13]]+ce[e[t+14]]+ce[e[t+15]]).toLowerCase()}let ls;const Fd=new Uint8Array(16);function Rd(){if(!ls){if(typeof crypto>"u"||!crypto.getRandomValues)throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");ls=crypto.getRandomValues.bind(crypto)}return ls(Fd)}const Pd=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),Xr={randomUUID:Pd};function $d(e,t,n){if(Xr.randomUUID&&!t&&!e)return Xr.randomUUID();e=e||{};const s=e.random||(e.rng||Rd)();return s[6]=s[6]&15|64,s[8]=s[8]&63|128,Ed(s)}const kd=He({...Ys(),...ef({fullHeight:!0}),...Io()},"VApp"),Ld=Un()({name:"VApp",props:kd(),setup(e,t){let{slots:n}=t;const s=Ao(e),{layoutClasses:r,getLayoutItem:i,items:o,layoutRef:l}=sf(e),{rtlClasses:a}=Xc();return Ks(()=>{var d;return k("div",{ref:l,class:["v-application",s.themeClasses.value,r.value,a.value,e.class],style:[e.style]},[k("div",{class:"v-application__wrap"},[(d=n.default)==null?void 0:d.call(n)])])}),{getLayoutItem:i,items:o,theme:s}}}),Nd=He({height:[Number,String],maxHeight:[Number,String],maxWidth:[Number,String],minHeight:[Number,String],minWidth:[Number,String],width:[Number,String]},"dimension");function Hd(e){return{dimensionStyles:N(()=>{const n={},s=_e(e.height),r=_e(e.maxHeight),i=_e(e.maxWidth),o=_e(e.minHeight),l=_e(e.minWidth),a=_e(e.width);return s!=null&&(n.height=s),r!=null&&(n.maxHeight=r),i!=null&&(n.maxWidth=i),o!=null&&(n.minHeight=o),l!=null&&(n.minWidth=l),a!=null&&(n.width=a),n})}}function Vd(){const e=nt(!1);return Ns(()=>{window.requestAnimationFrame(()=>{e.value=!0})}),{ssrBootStyles:N(()=>e.value?void 0:{transition:"none !important"}),isBooted:Ln(e)}}const Bd=He({scrollable:Boolean,...Ys(),...Nd(),...$o({tag:"main"})},"VMain"),jd=Un()({name:"VMain",props:Bd(),setup(e,t){let{slots:n}=t;const{dimensionStyles:s}=Hd(e),{mainStyles:r}=tf(),{ssrBootStyles:i}=Vd();return Ks(()=>k(e.tag,{class:["v-main",{"v-main--scrollable":e.scrollable},e.class],style:[r.value,i.value,s.value,e.style]},{default:()=>{var o,l;return[e.scrollable?k("div",{class:"v-main__scroller"},[(o=n.default)==null?void 0:o.call(n)]):(l=n.default)==null?void 0:l.call(n)]}})),{}}}),Ud={name:"App",components:{Hud:Bo,Notifications:Po,Dialogue:Fo},data:()=>({hud:{hide:!1,health:null,armour:null,underwaterTime:0,location:{heading:"",street:"",suburb:"",postcode:"",time:""},voice:{active:!1,level:3},vehicle:{inVehicle:!1,type:"",seatbelt:!1,fuel:0,speed:0}},notifications:[]}),created(){window.addEventListener("message",this.handleMessageEvent)},destroyed(){window.removeEventListener("message",this.handleMessageEvent)},mounted(){},methods:{handleMessageEvent(e){const t=e.data;t.id==="HUD_REFRESH"?this.hud={...t}:t.id==="NOTIFICATION_ADD"&&this.addNotification(t)},addNotification(e){var t;e._id=$d(),e._timestamp=new Date().getTime(),this.notifications=[...this.notifications,e].sort((n,s)=>s._timestamp-n._timestamp),setTimeout(()=>{this.notifications=this.notifications.filter(n=>n._id!==e._id)},((t=e.options)==null?void 0:t.timeout)||1e3)}}};function Wd(e,t,n,s,r,i){const o=Bo,l=Po,a=Fo;return L(),xt(Ld,{style:{"background-color":"transparent"}},{default:ds(()=>[k(jd,null,{default:ds(()=>[O("div",{class:"main-container",style:Ee({opacity:e.hud.hide?0:1})},[t[0]||(t[0]=O("img",{src:"https://app.fatduckgaming.com/assets/logos/fdg_text.png",alt:"",style:{"margin-top":"2px","margin-left":"4px",opacity:"0.3",height:"auto",width:"8%",position:"absolute"}},null,-1)),k(o,{hud:e.hud},null,8,["hud"]),e.notifications.length>0?(L(),xt(l,{key:0,items:e.notifications},null,8,["items"])):le("",!0)],4),k(a)]),_:1})]),_:1})}const Yd=Je(Ud,[["render",Wd],["__scopeId","data-v-b39b6a3c"]]),jo=lc(Yd);lf(jo);jo.mount("#app");
