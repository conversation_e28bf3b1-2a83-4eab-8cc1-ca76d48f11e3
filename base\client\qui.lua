QUI = {}
QUI.Radial = {}

--[====[
    @type func
    @name QUI.Radial.registerMenuAction(name, cb, options)
    @param name: string - A unique string identifier for the action
    @param cb: function - Callback function when the menu is triggered
    @param options: MenuOptions - An object defining the properties of the menu
    @brief Registers an action which can be triggered through QUI menus
    @description [
        MenuOptions Example
        {
            options.requires: {Player = true, Vehicle = true, Object = true} = Table which specifies the entity a menu action affects
            maxEntityDist: int = The max distance with which a menu action will trigger
        }
    ]
--]====]
QUI.registerMenuAction = function(name, cb, options)
    exports["base_ui"]:registerMenuAction(name, cb, options)
end

-- Radial Menu

--[====[
    @type func
    @name QUI.Radial.setMenuItems(menuObject)
    @param menuObject: MenuObject - An identifier used to refer to a clipset
    @brief Sets the current active menu items for the radial menu
    @description [
        MenuObject Example
        {
            { text = "Surrender", icon = "&#xF023B;", action = "surrender"},
            { text = "Face Accessories", icon = "&#xF0643;", subMenu =  {
                { text = "Hat", icon = "&#xF0BA4;", action = "clothing_hat" },
            }},
        }
    ]
--]====]
QUI.Radial.setMenuItems = function(...)
    exports["base_ui"]:setRadialMenuItems(...)
end

--[====[
    @type func
    @name QUI.Radial.open()
    @brief Toggles the radial menu to be visible and active
    @description [
        Toggles the radial menu, making it visible and enabling the controls.

        Also handles disabling of appropriate controls and screen darkening
    ]
--]====]
QUI.Radial.open = function(...)
    exports["base_ui"]:openRadialMenu(...)
end

--[====[
    @type func
    @name QUI.Radial.close()
    @brief Hides the radial menu
    @description [
        Toggles the radial menu off, hiding it, clearing the items and re-enabling normal controls
    ]
--]====]
QUI.Radial.close = function(...)
    exports["base_ui"]:closeRadialMenu(...)
end

---Triggers an NUI event to the core UI controller
---@type function
---@param eventName string The name of the event
---@param ... any Additional data sent as parameters to the event
function QUI.TriggerEvent(eventName, ...)
    exports["base_ui"]:sendQUIEvent(eventName, ...)
end

---Sets whether QUI is in focus
---@param keyboardFocus boolean
---@param mouseInput boolean
---@param passthrough boolean
function QUI.SetFocus(keyboardFocus, mouseInput, passthrough)
    exports["base_ui"]:toggleQUIState(keyboardFocus, mouseInput, passthrough)
end

---Creates a QUI callback, not currently implemented
---@param eventName string Identifier for the callback
---@param callback function
---@deprecated
function QUI.RegisterCallback(eventName, callback)
    exports["base_ui"]:registerQUICallback(eventName, callback)
end

---Triggers when QUI becomes available
---@param callback function
function QUI.onReady(callback)
    Citizen.CreateThread(function()
        Wait(1000)
        callback()
    end)
end