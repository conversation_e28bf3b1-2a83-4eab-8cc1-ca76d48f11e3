local diamondCasinoStaffDialogue = {
    {
        characterText = "Welcome to Diamond Casino! How can I be of assistance today?",
        responses = {
            {
                responseText = "I would like to blacklist myself from the Casino",
                responses = {
                    {
                        characterText = "Not a problem at all! How long would you like to blacklist yourself for?",
                        responses = {
                            {
                                responseText = "3 Months",
                                responses = {
                                    {
                                        characterText =
                                        "Do you understand that this is irreversible and in real-time time?",
                                        responses = {
                                            {
                                                responseText =
                                                "Yes. I understand that this blacklist cannot be lifted early, and is real-time time period",
                                                event = "casino-self-blacklist-3"
                                            },
                                            {
                                                responseText = "No, I don't want to.",
                                                event = "casino-leave"
                                            },
                                        }
                                    }
                                }
                            },
                            {
                                responseText = "6 Months",
                                responses = {
                                    {
                                        characterText =
                                        "Do you understand that this is irreversible and in real-time time?",
                                        responses = {
                                            {
                                                responseText =
                                                "Yes. I understand that this blacklist cannot be lifted early, and is real-time time period",
                                                event = "casino-self-blacklist-6"
                                            },
                                            {
                                                responseText = "No, I don't want to.",
                                                event = "casino-leave"
                                            },
                                        }
                                    }
                                }
                            },
                            {
                                responseText = "12 Months",
                                responses = {
                                    {
                                        characterText =
                                        "Do you understand that this is irreversible and in real-time time?",
                                        responses = {
                                            {
                                                responseText =
                                                "Yes. I understand that this blacklist cannot be lifted early, and is real-time time period",
                                                event = "casino-self-blacklist-12"
                                            },
                                            {
                                                responseText = "No, I don't want to.",
                                                event = "casino-leave"
                                            },
                                        }
                                    }
                                }
                            },
                            {
                                responseText = "24 Months",
                                responses = {
                                    {
                                        characterText =
                                        "Do you understand that this is irreversible and in real-time time?",
                                        responses = {
                                            {
                                                responseText =
                                                "Yes. I understand that this blacklist cannot be lifted early, and is real-time time period",
                                                event = "casino-self-blacklist-24"
                                            },
                                            {
                                                responseText = "No, I don't want to.",
                                                event = "casino-leave"
                                            },
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            {
                responseText = "I would like to speak with a member of Diamond Casino Staff",
                event = "casino-request-staff"
            },
            {
                responseText = "I would like to deposit cash into my bank account",
                event = "casino-deposit-cash"
            },
            {
                responseText = "I'm okay, thanks.",
                event = "casino-leave"
            }
        }
    }
}

local bankCasinoStaff = {
    {
        characterText = "Welcome to The Hub! How can I be of assistance today?",
        responses = {
            {
                responseText = "I would like to deposit cash into my bank account",
                event = "casino-deposit-cash"
            },
            {
                responseText = "I'm okay, thanks.",
                event = "casino-leave"
            }
        }
    }
}

RegisterDialogueAction("casino-self-blacklist-3", function()
    TriggerServerEvent("main_casino:BlacklistPlayer", 7890000)
end)
RegisterDialogueAction("casino-self-blacklist-6", function()
    TriggerServerEvent("main_casino:BlacklistPlayer", ********)
end)
RegisterDialogueAction("casino-self-blacklist-12", function()
    TriggerServerEvent("main_casino:BlacklistPlayer", ********)
end)
RegisterDialogueAction("casino-self-blacklist-24", function()
    TriggerServerEvent("main_casino:BlacklistPlayer", ********)
end)

RegisterDialogueAction("casino-request-staff", function()
    TriggerServerEvent("main_casino:playerRequestStaff")
end)

RegisterDialogueAction("casino-leave", function()
end)

local function openDiamondCasinoStaff(ped)
    OpenDialogueWithPed("Diamond Casino", ped, diamondCasinoStaffDialogue)
end

local function openHubCasinoStaff(ped)
    OpenDialogueWithPed("The Hub", ped, bankCasinoStaff)
end

CreateThread(function()
    Wait(1000)
    CreateInteractablePed("diamondcasino_frontdesk", "u_f_m_casinoshop_01",
        { coords = vector3(1088.162, 221.109, -50.200), heading = 180.011688 }, openDiamondCasinoStaff, {})
    CreateInteractablePed("thehub_frontdesk", "csb_vincent",
        { coords = vector3(124.7, -3007.02, 9.7), heading = 182.*********** }, openHubCasinoStaff, {})
end)


--Dialogue for Players
local diamondCasinoStaffDialogueHR = {
    {
        characterText = "Welcome to Diamond Casino! How can I be of assistance today?",
        responses = {
            {
                responseText = "I would like to speak with a member of Diamond Casino Staff",
                event = "casino-request-staff-hr"
            },
            {
                responseText = "I'm okay, thanks.",
                event = "casino-leave"
            }
        }
    }
}

-- Dialogue for Staff
local diamondCasinoStaffDialogueStaff = {
    {
        characterText = "Welcome to Diamond Casino! How can I be of assistance today?",
        responses = {
            {
                responseText = "I would like to withdraw some chips for a VIP",
                responses = {
                    {
                        characterText = "Certainly! Have you invoiced the guest and has the invoice been paid?",
                        responses = {
                            {
                                responseText = "Yes, it's all settled.",
                                event = "casino-vip-chips-withdrawal"
                            },
                            {
                                responseText = "No, I haven't completed that yet.",
                                event = "casino-leave"
                            }
                        }
                    }
                }
            },
            {
                responseText = "I'm okay, thanks.",
                event = "casino-leave"
            }
        }
    }
}

RegisterDialogueAction("casino-request-staff-hr", function()
    TriggerServerEvent("main_casino:playerRequestStaffHR")
end)

RegisterDialogueAction("casino-deposit-cash", function(text)
    OpenDialog("Amount to Deposit", function(text)
        if tonumber(text) then
            local amount = tonumber(text)
            if amount > 0 then
                TriggerServerEvent("bank:deposit", amount)
                TriggerServerEvent("bank:balance")
            else
                Base.ShowNotification("Invalid amount.")
            end
        else
            Base.ShowNotification("Invalid amount.")
        end
    end)
end)

local function openDiamondCasinoStaffHR(ped)
    TriggerServerEvent("main_casino:checkStaff", ped)
end

RegisterNetEvent("main_casino:checkStaff")
AddEventHandler("main_casino:checkStaff", function(isStaff, ped)
    print(isStaff)
    if isStaff then
        OpenDialogueWithPed("Diamond Casino Staff", ped, diamondCasinoStaffDialogueStaff)
    else
        OpenDialogueWithPed("Diamond Casino", ped, diamondCasinoStaffDialogueHR)
    end
end)

RegisterDialogueAction("casino-vip-chips-withdrawal", function(text)
    OpenDialog("Amount of Chips", function(text)
        if tonumber(text) then
            local amount = tonumber(text)
            print(amount)
            if amount > 0 then
                TriggerServerEvent("main_casino:StaffExchange", amount)
            else
                Base.ShowNotification("Invalid amount.")
            end
        else
            Base.ShowNotification("Invalid amount.")
        end
    end)
end)

-- Main Casino High Roller Ped
CreateThread(function()
    Wait(1000)
    CreateInteractablePed("diamondcasino_highroller_staff_casino", "u_f_m_casinoshop_01",
        { coords = vector3(1144.0609130859, 253.59840393066, -52.035732269287), heading = 224.0 },
        openDiamondCasinoStaffHR, {})

    -- F1 Track
    -- CreateInteractablePed("diamondcasino_highroller_staff_f1", "u_f_m_casinoshop_01", { coords = vector3(4231.0107421875, 7951.5302734375, 97.04069519043), heading = 15.0 }, openDiamondCasinoStaffHR, {})
    -- CreateInteractablePed("diamondcasino_highroller_staff_f1", "u_f_m_casinoshop_01", { coords = vector3(4216.8212890625, 7965.3681640625, 97.215270996094), heading = 290.0 }, openDiamondCasinoStaffHR, {})

    -- Blackwoods
    CreateInteractablePed("diamondcasino_highroller_staff_blackwood", "u_f_m_casinoshop_01",
        { coords = vector3(-306.52920532227, 6268.4345703125, 33.894748687744), heading = 224.0 },
        openDiamondCasinoStaffHR, {})

    -- Penthouse
    CreateInteractablePed("diamondcasino_highroller_staff_lsia", "u_f_m_casinoshop_01",
        { coords = vector3(945.42980957031, 8.5478620529175, 115.18416168213), heading = 99.0 }, openDiamondCasinoStaffHR,
        {})

    -- VU
    CreateInteractablePed("diamondcasino_highroller_staff_lsia", "u_f_m_casinoshop_01",
        { coords = vector3(121.18, -1296.36, 20.39), heading = 30.9 }, openDiamondCasinoStaffHR,
        {})
    --Yacht
    -- CreateInteractablePed("diamondcasino_highroller_staff_yacht", "u_f_m_casinoshop_01", { coords = vector3(-2022.**********, -1505.**********, 11.************), heading = 231.0 }, openDiamondCasinoStaffHR, {})
    -- CreateInteractablePed("diamondcasino_highroller_staff_yacht", "u_f_m_casinoshop_01", { coords = vector3(-2021.**********, -1499.**********, 7.*************), heading = 147.0 }, openDiamondCasinoStaffHR, {})

    --Maze Bank Arena
    --CreateInteractablePed("diamondcasino_highroller_staff_mba", "u_f_m_casinoshop_01", { coords = vector3(-311.***********, -1924.**********, 41.************), heading = 228.0 }, openDiamondCasinoStaffHR, {})
    -- CreateInteractablePed("diamondcasino_highroller_staff_mba2", "u_f_m_casinoshop_01", { coords = vector3(-300.***********, -1932.**********, 33.************), heading = 149.0 }, openDiamondCasinoStaffHR, {})
end)
