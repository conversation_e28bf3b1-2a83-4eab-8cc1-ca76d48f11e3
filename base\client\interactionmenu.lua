---Adds an option to the radial
---@param condition function A function that adds a menuItem if result is true
---@param menuAction function A function declaring what happens when item is picked (ignore for elements with a sub menu, each submenu element needs to be registered using QUI)
---@param menuItem table A table including the data that will appear in the interaction menu
---@param requires table A table containing required circumstances where an action can performed
function AddPlayerInteractionOption(condition, menuAction, menuItem, requires)
    exports["core_roleplay"]:addInteractionOption("player", condition, menuAction, menuItem, requires)
end

---Adds an option to the radial
---@param condition function A function that adds a menuItem if result is true
---@param menuAction function A function declaring what happens when item is picked (ignore for elements with a sub menu, each submenu element needs to be registered using QUI)
---@param menuItem table A table including the data that will appear in the interaction menu
---@param requires table A table containing required circumstances where an action can performed
function AddVehicleInteractionAction(condition, menuAction, menuItem, requires)
    exports["core_roleplay"]:addInteractionOption("vehicle", condition, menuAction, menuItem, requires)
end
