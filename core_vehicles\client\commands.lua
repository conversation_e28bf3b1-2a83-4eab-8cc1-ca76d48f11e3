-------------------------------------------
--               Car Commands
-------------------------------------------
local driftThread

function HasDriftTyresMod(veh)
    return DecorGetBool(veh, "driftTyres") or false
end

local function CreateDriftThread()
    driftThread = true
    Citizen.CreateThread(function()
        while true do 
            Wait(10000)
            local veh = GetVehiclePedIsIn(PlayerPedId(), false)
            if veh and GetPedInVehicleSeat(veh, -1) == PlayerPedId() and GetEntitySpeed(veh) >= (150 / 3.6) and GetDriftTyresEnabled(veh) then --150kph
                Base.Notification("Drift mode disabled due to high speed")
                SetDriftTyresEnabled(veh, false)
            end
        end
    end)
end

AddVehicleInteractionAction(
    function()
	    return Base.Vehicles.GetClosestVehicle(playerPed, 4.0, true)
    end,

    function() end,

    { text = "Doors", icon = "&#xF0B6B;", subMenu = {
        { text = "Driver", icon = "&#xF0B6B;", action = "door1" },
        { text = "Passenger", icon = "&#xF0B6B;", action = "door2" },
        { text = "Rear Left", icon = "&#xF0B6B;", action = "door3" },
        { text = "Rear Right", icon = "&#xF0B6B;", action = "door4" },
        { text = "Hood", icon = "&#xF0B6B;", action = "hood" },
        { text = "Trunk", icon = "&#xF0B6B;", action = "trunk" },
    }},
    
    {requires = {Vehicle = true}}
)

AddVehicleInteractionAction(
    function() -- Condition
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
	    return HasDriftTyresMod(veh) and GetDriftTyresEnabled(veh)
    end,

    function() -- Action
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
        if (GetPedInVehicleSeat(veh, -1) ~= PlayerPedId()) then
            return
        end
        if GetEntitySpeed(veh) >= (50 / 3.6) and GetDriftTyresEnabled(veh) then 
            Base.Notification("You cannot toggle drift mode at this speed!")
            return
        end

        SetDriftTyresEnabled(veh, not GetDriftTyresEnabled(veh))
    end,

    { text = "Disable Drift Mode", icon = "&#xF0D64;", action = "disable_driftmode"},  -- Menu Item

    {requires = {Vehicle = true}}  -- Requires
)

AddVehicleInteractionAction(
    function() -- Condition
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
	    return HasDriftTyresMod(veh) and not GetDriftTyresEnabled(veh)
    end,

    function() -- Action
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
        if (GetPedInVehicleSeat(veh, -1) ~= PlayerPedId()) then
            return
        end
        if GetEntitySpeed(veh) >= (50 / 3.6) then 
            Base.Notification("You cannot toggle drift mode at this speed!")
            return
        end
        
        if not driftThread then CreateDriftThread() end

        SetDriftTyresEnabled(veh, not GetDriftTyresEnabled(veh))
    end,

    { text = "Enable Drift Mode", icon = "&#xF0D64;", action = "enable_driftmode"},  -- Menu Item

    {requires = {Vehicle = true}}  -- Requires
)


AddVehicleInteractionAction( --Lower Boat Anchor
    function() -- Condition
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
	    return (IsPedInAnyBoat(PlayerPedId()) or GetEntityModel(veh) == `seasparrow` or GetEntityModel(veh) == `dodo`)
        and CanAnchorBoatHere(veh) 
        and not IsBoatAnchoredAndFrozen(veh)
    end,

    function() -- Action
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
        if (GetPedInVehicleSeat(veh, -1) ~= PlayerPedId()) then
            return
        end
        if GetEntitySpeed(veh) >= 5 then 
            Base.Notification("You cannot lower the anchor at this speed!")
            return
        end

        SetBoatFrozenWhenAnchored(veh, true)
        SetBoatAnchor(veh, true)
    end,

    { text = "Lower Anchor", icon = "&#xF0031;", action = "lower_anchor"},  -- Menu Item

    {requires = {Vehicle = true}}  -- Requires
)

AddVehicleInteractionAction( --Raise Boat Anchor
    function() -- Condition
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
	    return (IsPedInAnyBoat(PlayerPedId()) or GetEntityModel(veh) == `seasparrow` or GetEntityModel(veh) == `dodo`) and IsBoatAnchoredAndFrozen(veh)
    end,

    function() -- Action
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
        if (GetPedInVehicleSeat(veh, -1) ~= PlayerPedId()) then
            return
        end

        SetBoatFrozenWhenAnchored(veh, false)
        SetBoatAnchor(veh, false)
    end,

    { text = "Raise Anchor", icon = "&#xF0031;", action = "raise_anchor"},  -- Menu Item

    {requires = {Vehicle = true}}  -- Requires
)


function ToggleDoor(door)
    local playerPed = GetPlayerPed(-1)
    local playerVeh = Base.Vehicles.GetClosestVehicle(playerPed, 4.0, true)
    TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(playerVeh), "toggleDoor", door)
end

ToggleDoor1 = function() ToggleDoor(0) end
ToggleDoor2 = function() ToggleDoor(1) end
ToggleDoor3 = function() ToggleDoor(2) end
ToggleDoor4 = function() ToggleDoor(3) end
ToggleDoor5 = function() ToggleDoor(4) end
ToggleDoor6 = function() ToggleDoor(5) end

onBaseReady(function()
    QUI.registerMenuAction("door1", ToggleDoor1)
    QUI.registerMenuAction("door2", ToggleDoor2)
    QUI.registerMenuAction("door3", ToggleDoor3)
    QUI.registerMenuAction("door4", ToggleDoor4)
    QUI.registerMenuAction("hood", ToggleDoor5)
    QUI.registerMenuAction("trunk", ToggleDoor6)
end)


RegisterCommand('d1', function(source, args, raw)
    local playerPed = GetPlayerPed(-1)
    local playerVeh = Base.Vehicles.GetClosestVehicle(playerPed, 4.0, true)
    TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(playerVeh), "toggleDoor", 0)
end)

RegisterCommand('d2', function(source, args, raw)
    local playerPed = GetPlayerPed(-1)
    local playerVeh = Base.Vehicles.GetClosestVehicle(playerPed, 4.0, true)
    TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(playerVeh), "toggleDoor", 1)
end)

RegisterCommand('d3', function(source, args, raw)
    local playerPed = GetPlayerPed(-1)
    local playerVeh = Base.Vehicles.GetClosestVehicle(playerPed, 4.0, true)
    TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(playerVeh), "toggleDoor", 2)
end)

RegisterCommand('d4', function(source, args, raw)
    local playerPed = GetPlayerPed(-1)
    local playerVeh = Base.Vehicles.GetClosestVehicle(playerPed, 4.0, true)
    TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(playerVeh), "toggleDoor", 3)
end)

RegisterCommand('h', function(source, args, raw)
    local playerPed = GetPlayerPed(-1)
    local playerVeh = Base.Vehicles.GetClosestVehicle(playerPed, 4.0, true)
    TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(playerVeh), "toggleDoor", 4)
end)

RegisterCommand('t', function(source, args, raw)
    local playerPed = GetPlayerPed(-1)
    local playerVeh = Base.Vehicles.GetClosestVehicle(playerPed, 4.0, true)
    TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(playerVeh), "toggleDoor", 5)
end)
