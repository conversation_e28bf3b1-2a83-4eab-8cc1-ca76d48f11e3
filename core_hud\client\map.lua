local cayoFixBlip = AddBlipForCoord(5943.0, -6272.0, 0)
SetBlipSprite(cayoFixBlip, 575)
SetBlipDisplay(cayoFixBlip, 4)
SetBlipScale(cayoFixBlip, 0.0)
SetBlipColour(cayoFixBlip, 0)
SetBlipAsShortRange(cayoFixBlip, true)
BeginTextCommandSetBlipName("STRING")
AddTextComponentString("Cayo Perico")
EndTextCommandSetBlipName(cayoFixBlip)

local isCloseToCayo = false
local isCayoMinimapLoaded = false

---Checks coords to know if the player is close to cayo
Citizen.CreateThread(function()
    while true do
        isCloseToCayo = #(GetEntityCoords(PlayerPedId()) - vector3(4858.0, -5171.0, 2.0)) < 2200.0
        isCayoMinimapLoaded = isCloseToCayo
        SetToggleMinimapHeistIsland(isCloseToCayo)
        Wait(5000)
    end
end)

---Handle the minimap loading and unloading
CreateThread(function()
    while true do
        local wait = 500

        if IsPauseMenuActive() and not IsMinimapInInterior() and isCloseToCayo then
            if isCayoMinimapLoaded then
                isCayoMinimapLoaded = false
                SetToggleMinimapHeistIsland(false)
            end
            SetRadarAsExteriorThisFrame()
            SetRadarAsInteriorThisFrame(GetHashKey("h4_fake_islandx"), 4700.0, -5145.0, 0, 0)
            wait = 0

        elseif not isCayoMinimapLoaded and isCloseToCayo then
            isCayoMinimapLoaded = true
            SetToggleMinimapHeistIsland(true)
        end
        Wait(wait)
    end
end)