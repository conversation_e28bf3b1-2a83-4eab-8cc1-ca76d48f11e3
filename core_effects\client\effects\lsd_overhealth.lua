local overhealthEffect = TimedStatusEffect(StatusEffect("overhealth", "Overhealth",
    {
        onStart = function(effect)
            TriggerServerEvent("main_crime_drugs:addHigh")
        end,

        onTick = function(effect)
        end,

        onStop = function(effect)
            TriggerServerEvent("main_crime_drugs:removeHigh")
        end,
    },
    {
        alert = false,
        desc = "You're feeling invincible!",
        subdesc = "+25% Health"
    }
), { maxTime = 24 * 60 })

RegisterNetEvent("core_effects:overhealthEffect")
AddEventHandler("core_effects:overhealthEffect", function(overhealthDuration)
    ClearPedTasks(PlayerPedId())
    Wait(100)

    overhealthEffect.addTime(overhealthDuration)
end)

AddEventHandler("overhealth:off", function(source)
    SetPlayerMaxHealthBuff("salvia", 0)
    overhealthEffect.stop()
end)
