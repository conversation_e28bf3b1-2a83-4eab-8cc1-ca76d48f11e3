--[[
Los Santos Customs V1.1
Credits - MythicalBro
/////License/////
Do not reupload/re release any part of this script without my permission
]]
--### Colors Below are as they are listed in Mechanic Menu ###--
local colors = {
    { name = "Black",            colorindex = 0 },
    { name = "Black & Blue",     colorindex = 141 },
    { name = "Black & Purple",   colorindex = 142 },
    { name = "Black & Red",      colorindex = 143 },
    { name = "Carbon Black",     colorindex = 147 },
    { name = "Graphit<PERSON>",         colorindex = 1 },
    { name = "Anhracite Black",  colorindex = 11 },
    { name = "Black Steel",      colorindex = 2 },
    { name = "Dark Steel",       colorindex = 3 },
    { name = "Alloy",            colorindex = 156 },
    { name = "Silver",           colorindex = 4 },
    { name = "Bluish Silver",    colorindex = 5 },
    { name = "Rolled Steel",     colorindex = 6 },
    { name = "Shadow Silver",    colorindex = 7 },
    { name = "Stone Silver",     colorindex = 8 },
    { name = "Midnight Silver",  colorindex = 9 },
    { name = "Cast Iron Silver", colorindex = 10 },
    { name = "Red",              colorindex = 27 },
    { name = "Torino Red",       colorindex = 28 },
    { name = "Formula Red",      colorindex = 29 },
    { name = "Lava Red",         colorindex = 150 },
    { name = "Blaze Red",        colorindex = 30 },
    { name = "Grace Red",        colorindex = 31 },
    { name = "Garnet Red",       colorindex = 32 },
    { name = "Sunset Red",       colorindex = 33 },
    { name = "Cabernet Red",     colorindex = 34 },
    { name = "Wine Red",         colorindex = 143 },
    { name = "Candy Red",        colorindex = 35 },
    { name = "Hot Pink",         colorindex = 135 },
    { name = "Pfsiter Pink",     colorindex = 137 },
    { name = "Salmon Pink",      colorindex = 136 },
    { name = "Sunrise Orange",   colorindex = 36 },
    { name = "Orange",           colorindex = 38 },
    { name = "Bright Orange",    colorindex = 138 },
    { name = "Classic Gold",     colorindex = 37 },
    { name = "Gold",             colorindex = 99 },
    { name = "Bronze",           colorindex = 90 },
    { name = "Yellow",           colorindex = 88 },
    { name = "Race Yellow",      colorindex = 89 },
    { name = "Dew Yellow",       colorindex = 91 },
    { name = "Dark Green",       colorindex = 49 },
    { name = "Racing Green",     colorindex = 50 },
    { name = "Sea Green",        colorindex = 51 },
    { name = "Olive Green",      colorindex = 52 },
    { name = "Bright Green",     colorindex = 53 },
    { name = "Gasoline Green",   colorindex = 54 },
    { name = "Lime Green",       colorindex = 92 },
    { name = "Securicor Green",  colorindex = 125 },
    { name = "Midnight Blue",    colorindex = 141 },
    { name = "Galaxy Blue",      colorindex = 61 },
    { name = "Dark Blue",        colorindex = 62 },
    { name = "Pacific Blue",     colorindex = 146 },
    { name = "Saxon Blue",       colorindex = 63 },
    { name = "Blue",             colorindex = 64 },
    { name = "Mariner Blue",     colorindex = 65 },
    { name = "Harbor Blue",      colorindex = 66 },
    { name = "Diamond Blue",     colorindex = 67 },
    { name = "Epsilon Blue",     colorindex = 157 },
    { name = "Surf Blue",        colorindex = 68 },
    { name = "Nautical Blue",    colorindex = 69 },
    { name = "Racing Blue",      colorindex = 73 },
    { name = "Ultra Blue",       colorindex = 70 },
    { name = "Light Blue",       colorindex = 74 },
    { name = "Chocolate Brown",  colorindex = 96 },
    { name = "Bison Brown",      colorindex = 101 },
    { name = "Creeen Brown",     colorindex = 95 },
    { name = "Feltzer Brown",    colorindex = 94 },
    { name = "Maple Brown",      colorindex = 97 },
    { name = "Beechwood Brown",  colorindex = 103 },
    { name = "Sienna Brown",     colorindex = 104 },
    { name = "Saddle Brown",     colorindex = 98 },
    { name = "Moss Brown",       colorindex = 100 },
    { name = "Woodbeech Brown",  colorindex = 102 },
    { name = "Straw Brown",      colorindex = 99 },
    { name = "Sandy Brown",      colorindex = 105 },
    { name = "Bleached Brown",   colorindex = 106 },
    { name = "Schafter Purple",  colorindex = 71 },
    { name = "Spinnaker Purple", colorindex = 72 },
    { name = "Midnight Purple",  colorindex = 142 },
    { name = "Bright Purple",    colorindex = 145 },
    { name = "Cream",            colorindex = 107 },
    { name = "Ice White",        colorindex = 111 },
    { name = "Frost White",      colorindex = 112 }
}
local metalcolors = {
    { name = "Brushed Steel",       colorindex = 117 },
    { name = "Brushed Black Steel", colorindex = 118 },
    { name = "Brushed Aluminum",    colorindex = 119 },
    { name = "Pure Gold",           colorindex = 158 },
    { name = "Brushed Gold",        colorindex = 159 }
}
local mattecolors = {
    { name = "Black",           colorindex = 12 },
    { name = "Gray",            colorindex = 13 },
    { name = "Light Gray",      colorindex = 14 },
    { name = "Ice White",       colorindex = 131 },
    { name = "Blue",            colorindex = 83 },
    { name = "Dark Blue",       colorindex = 82 },
    { name = "Midnight Blue",   colorindex = 84 },
    { name = "Midnight Purple", colorindex = 149 },
    { name = "Schafter Purple", colorindex = 148 },
    { name = "Red",             colorindex = 39 },
    { name = "Dark Red",        colorindex = 40 },
    { name = "Orange",          colorindex = 41 },
    { name = "Yellow",          colorindex = 42 },
    { name = "Lime Green",      colorindex = 55 },
    { name = "Green",           colorindex = 128 },
    { name = "Forest Green",    colorindex = 151 },
    { name = "Foliage Green",   colorindex = 155 },
    { name = "Olive Darb",      colorindex = 152 },
    { name = "Dark Earth",      colorindex = 153 },
    { name = "Desert Tan",      colorindex = 154 }
}
local utilcolors = {
    { name = "Black",          colorindex = 15 },
    { name = "Black Poly",     colorindex = 16 },
    { name = "Dark Silver",    colorindex = 17 },
    { name = "Silver",         colorindex = 18 },
    { name = "Gun Metal",      colorindex = 19 },
    { name = "Shadow Silver",  colorindex = 20 },
    { name = "Red",            colorindex = 43 },
    { name = "Garnet Red",     colorindex = 45 },
    { name = "Bright Red",     colorindex = 44 },
    { name = "Green",          colorindex = 57 },
    { name = "Dark Green",     colorindex = 56 },
    { name = "Blue",           colorindex = 77 },
    { name = "Lightning Blue", colorindex = 79 },
    { name = "Maui Blue",      colorindex = 80 },
    { name = "Bright Blue",    colorindex = 81 },
    { name = "Seafoam Blue",   colorindex = 78 },
    { name = "Midnight Blue",  colorindex = 76 },
    { name = "Dark Blue",      colorindex = 75 },
    { name = "Frost White",    colorindex = 122 }

}
local worncolors = {
    { name = "Black",         colorindex = 21 },
    { name = "Graphite",      colorindex = 22 },
    { name = "Silver Gray",   colorindex = 23 },
    { name = "Silver",        colorindex = 24 },
    { name = "Blue Silver",   colorindex = 25 },
    { name = "Shadow Silver", colorindex = 26 },
    { name = "Red",           colorindex = 46 },
    { name = "Golden Red",    colorindex = 47 },
    { name = "Dark Red",      colorindex = 48 },
    { name = "Orange",        colorindex = 123 },
    { name = "Sunset Orange", colorindex = 124 },
    { name = "Light Orange",  colorindex = 130 },
    { name = "Taxi Yellow",   colorindex = 126 },
    { name = "Green",         colorindex = 58 },
    { name = "Olive Green",   colorindex = 133 },
    { name = "Dark Green",    colorindex = 59 },
    { name = "Sea Wash",      colorindex = 60 },
    { name = "Blue",          colorindex = 86 },
    { name = "Light Blue",    colorindex = 87 },
    { name = "Honey Beige",   colorindex = 113 },
    { name = "Straw Beige",   colorindex = 116 },
    { name = "Brown",         colorindex = 114 },
    { name = "Dark Brown",    colorindex = 115 },
    { name = "Frost White",   colorindex = 121 },
    { name = "Ice White",     colorindex = 132 }
}

LSC_Config = {}
LSC_Config.prices = {}

--------Prices---------
LSC_Config.prices = {

    ------Window tint------
    windowtint = {
        { name = "Pure Black", tint = 1, price = 1000 },
        { name = "Darksmoke",  tint = 2, price = 1000 },
        { name = "Lightsmoke", tint = 3, price = 1000 },
        { name = "Limo",       tint = 4, price = 1000 },
        { name = "Green",      tint = 5, price = 1000 },
    },

    -------Respray--------
    ----Primary color---
    --Chrome
    chrome = {
        colors = {
            { name = "Chrome", colorindex = 120 }
        },
        price = 10000
    },
    --Classic
    classic = {
        colors = colors,
        price = 5000
    },
    --Matte
    matte = {
        colors = mattecolors,
        price = 5000
    },
    --Metallic
    metallic = {
        colors = colors,
        price = 5000
    },
    --Metals
    metal = {
        colors = metalcolors,
        price = 5000
    },
    --Utils
    util = {
        colors = utilcolors,
        price = 5000
    },
    --Worn
    worn = {
        colors = worncolors,
        price = 5000
    },
    ----Secondary color---
    --Chrome
    chrome2 = {
        colors = {
            { name = "Chrome", colorindex = 120 }
        },
        price = 1000
    },
    --Classic
    classic2 = {
        colors = colors,
        price = 200
    },
    --Matte
    matte2 = {
        colors = mattecolors,
        price = 500
    },
    --Metallic
    metallic2 = {
        colors = colors,
        price = 300
    },
    --Metals
    metal2 = {
        colors = metalcolors,
        price = 300
    },
    --Utils
    util2 = {
        colors = utilcolors,
        price = 300
    },
    --Worn
    worn2 = {
        colors = worncolors,
        price = 500
    },
    ------Neon layout------
    neonlayout = {
        { name = "Front", price = 1000 },
        { name = "Back",  price = 1000 },
        { name = "Left",  price = 1000 },
        { name = "Right", price = 1000 },
    },
    --Neon color
    neoncolor = {
        { name = "White",         neon = { 255, 255, 255 }, price = 1000 },
        { name = "Blue",          neon = { 0, 15, 255 },    price = 1000 },
        { name = "Electric Blue", neon = { 3, 83, 255 },    price = 1000 },
        { name = "Dark Blue",     neon = { 0, 0, 255 },     price = 1000 },
        { name = "Mint Green",    neon = { 0, 255, 140 },   price = 1000 },
        { name = "Bright Green",  neon = { 0, 255, 0 },     price = 1000 },
        { name = "Lime Green",    neon = { 94, 255, 1 },    price = 1000 },
        { name = "Yellow",        neon = { 255, 255, 0 },   price = 1000 },
        { name = "Golden Shower", neon = { 255, 130, 5 },   price = 1000 },
        { name = "Orange",        neon = { 255, 62, 0 },    price = 1000 },
        { name = "Red",           neon = { 255, 0, 0 },     price = 1000 },
        { name = "Pony Pink",     neon = { 255, 50, 100 },  price = 1000 },
        { name = "Hot Pink",      neon = { 255, 5, 190 },   price = 1000 },
        { name = "Salmon Pink",   neon = { 143, 47, 85 },   price = 1000 },
        { name = "Pale Purple",   neon = { 55, 17, 82 },    price = 1000 },
        { name = "Purple",        neon = { 35, 5, 255 },    price = 1000 },
        { name = "Brown",         neon = { 139, 69, 19 },   price = 1000 },
        { name = "Blacklight",    neon = { 15, 3, 255 },    price = 1000 },
    },

    --------Plates---------
    plates = {
        { name = "Blue on White 1", plateindex = 0, price = 200 },
        { name = "Blue On White 2", plateindex = 3, price = 200 },
        { name = "Yellow on Blue",  plateindex = 2, price = 300 },
        { name = "Yellow on Black", plateindex = 1, price = 600 },
    },

    --------Wheels--------
    ----Wheel accessories----
    wheelaccessories = {
        { name = "Stock Tires",       price = 1000 },
        { name = "Custom Tires",      price = 1250 },
        { name = "White Tire Smoke",  smokecolor = { 254, 254, 254 }, price = 3000 },
        { name = "Black Tire Smoke",  smokecolor = { 1, 1, 1 },       price = 3000 },
        { name = "Blue Tire Smoke",   smokecolor = { 0, 150, 255 },   price = 3000 },
        { name = "Yellow Tire Smoke", smokecolor = { 255, 255, 50 },  price = 3000 },
        { name = "Orange Tire Smoke", smokecolor = { 255, 153, 51 },  price = 3000 },
        { name = "Red Tire Smoke",    smokecolor = { 255, 10, 10 },   price = 3000 },
        { name = "Green Tire Smoke",  smokecolor = { 10, 255, 10 },   price = 3000 },
        { name = "Purple Tire Smoke", smokecolor = { 140, 44, 209 },  price = 3000 },
        { name = "Pink Tire Smoke",   smokecolor = { 255, 102, 178 }, price = 3000 },
        { name = "Gray Tire Smoke",   smokecolor = { 128, 128, 128 }, price = 3000 },
    },

    ----Wheel color----
    wheelcolor = {
        colors = colors,
        price = 1000,
    },

    ----Front wheel (Bikes)----
    frontwheel = {
        { name = "Stock",             wtype = 6, mod = -1, price = 1000 },
        { name = "Speedway",          wtype = 6, mod = 0,  price = 1000 },
        { name = "Streetspecial",     wtype = 6, mod = 1,  price = 1000 },
        { name = "Racer",             wtype = 6, mod = 2,  price = 1000 },
        { name = "Trackstar",         wtype = 6, mod = 3,  price = 1000 },
        { name = "Overlord",          wtype = 6, mod = 4,  price = 1000 },
        { name = "Trident",           wtype = 6, mod = 5,  price = 1000 },
        { name = "Triplethreat",      wtype = 6, mod = 6,  price = 1000 },
        { name = "Stilleto",          wtype = 6, mod = 7,  price = 1000 },
        { name = "Wires",             wtype = 6, mod = 8,  price = 1000 },
        { name = "Bobber",            wtype = 6, mod = 9,  price = 1000 },
        { name = "Solidus",           wtype = 6, mod = 10, price = 1000 },
        { name = "Iceshield",         wtype = 6, mod = 11, price = 1000 },
        { name = "Loops",             wtype = 6, mod = 12, price = 1000 },
        { name = "Romper Racing",     wtype = 6, mod = 13, price = 1000 },
        { name = "Warp Drive",        wtype = 6, mod = 14, price = 1000 },
        { name = "Snowflake",         wtype = 6, mod = 15, price = 1000 },
        { name = "Holy Spoke",        wtype = 6, mod = 16, price = 1000 },
        { name = "Old Skool Triple",  wtype = 6, mod = 17, price = 1000 },
        { name = "Futura",            wtype = 6, mod = 18, price = 1000 },
        { name = "Quarter Mile King", wtype = 6, mod = 19, price = 1000 },
        { name = "Cartwheel",         wtype = 6, mod = 20, price = 1000 },
        { name = "Double Five",       wtype = 6, mod = 21, price = 1000 },
        { name = "Shuriken",          wtype = 6, mod = 22, price = 1000 },
        { name = "Simple Six",        wtype = 6, mod = 23, price = 1000 },
        { name = "Celtic",            wtype = 6, mod = 24, price = 1000 },
        { name = "Razer",             wtype = 6, mod = 25, price = 1000 },
        { name = "Twisted",           wtype = 6, mod = 26, price = 1000 },
        { name = "Morning Star",      wtype = 6, mod = 27, price = 1000 },
        { name = "Jagged Spokes",     wtype = 6, mod = 28, price = 1000 },
        { name = "Eidolon",           wtype = 6, mod = 29, price = 1000 },
        { name = "Enigma",            wtype = 6, mod = 30, price = 1000 },
        { name = "Big Spokes",        wtype = 6, mod = 31, price = 1000 },
        { name = "Webs",              wtype = 6, mod = 32, price = 1000 },
        { name = "Hotplate",          wtype = 6, mod = 33, price = 1000 },
        { name = "Bobsta",            wtype = 6, mod = 34, price = 1000 },
        { name = "Grouch",            wtype = 6, mod = 35, price = 1000 },
    },

    ----Back wheel (Bikes)-----
    backwheel = {
        { name = "Stock",             wtype = 6, mod = -1, price = 1000 },
        { name = "Speedway",          wtype = 6, mod = 0,  price = 1000 },
        { name = "Streetspecial",     wtype = 6, mod = 1,  price = 1000 },
        { name = "Racer",             wtype = 6, mod = 2,  price = 1000 },
        { name = "Trackstar",         wtype = 6, mod = 3,  price = 1000 },
        { name = "Overlord",          wtype = 6, mod = 4,  price = 1000 },
        { name = "Trident",           wtype = 6, mod = 5,  price = 1000 },
        { name = "Triplethreat",      wtype = 6, mod = 6,  price = 1000 },
        { name = "Stilleto",          wtype = 6, mod = 7,  price = 1000 },
        { name = "Wires",             wtype = 6, mod = 8,  price = 1000 },
        { name = "Bobber",            wtype = 6, mod = 9,  price = 1000 },
        { name = "Solidus",           wtype = 6, mod = 10, price = 1000 },
        { name = "Iceshield",         wtype = 6, mod = 11, price = 1000 },
        { name = "Loops",             wtype = 6, mod = 12, price = 1000 },
        { name = "Romper Racing",     wtype = 6, mod = 13, price = 1000 },
        { name = "Warp Drive",        wtype = 6, mod = 14, price = 1000 },
        { name = "Snowflake",         wtype = 6, mod = 15, price = 1000 },
        { name = "Holy Spoke",        wtype = 6, mod = 16, price = 1000 },
        { name = "Old Skool Triple",  wtype = 6, mod = 17, price = 1000 },
        { name = "Futura",            wtype = 6, mod = 18, price = 1000 },
        { name = "Quarter Mile King", wtype = 6, mod = 19, price = 1000 },
        { name = "Cartwheel",         wtype = 6, mod = 20, price = 1000 },
        { name = "Double Five",       wtype = 6, mod = 21, price = 1000 },
        { name = "Shuriken",          wtype = 6, mod = 22, price = 1000 },
        { name = "Simple Six",        wtype = 6, mod = 23, price = 1000 },
        { name = "Celtic",            wtype = 6, mod = 24, price = 1000 },
        { name = "Razer",             wtype = 6, mod = 25, price = 1000 },
        { name = "Twisted",           wtype = 6, mod = 26, price = 1000 },
        { name = "Morning Star",      wtype = 6, mod = 27, price = 1000 },
        { name = "Jagged Spokes",     wtype = 6, mod = 28, price = 1000 },
        { name = "Eidolon",           wtype = 6, mod = 29, price = 1000 },
        { name = "Enigma",            wtype = 6, mod = 30, price = 1000 },
        { name = "Big Spokes",        wtype = 6, mod = 31, price = 1000 },
        { name = "Webs",              wtype = 6, mod = 32, price = 1000 },
        { name = "Hotplate",          wtype = 6, mod = 33, price = 1000 },
        { name = "Bobsta",            wtype = 6, mod = 34, price = 1000 },
        { name = "Grouch",            wtype = 6, mod = 35, price = 1000 },
    },

    ----Sport wheels-----
    sportwheels = {
        { name = "Stock",        wtype = 0, mod = -1, price = 1000 },
        { name = "Inferno",      wtype = 0, mod = 0,  price = 1000 },
        { name = "Deepfive",     wtype = 0, mod = 1,  price = 1000 },
        { name = "Lozspeed",     wtype = 0, mod = 2,  price = 1000 },
        { name = "Diamondcut",   wtype = 0, mod = 3,  price = 1000 },
        { name = "Chrono",       wtype = 0, mod = 4,  price = 1000 },
        { name = "Feroccirr",    wtype = 0, mod = 5,  price = 1000 },
        { name = "Fiftynine",    wtype = 0, mod = 6,  price = 1000 },
        { name = "Mercie",       wtype = 0, mod = 7,  price = 1000 },
        { name = "Syntheticz",   wtype = 0, mod = 8,  price = 1000 },
        { name = "Organictyped", wtype = 0, mod = 9,  price = 1000 },
        { name = "Endov1",       wtype = 0, mod = 10, price = 1000 },
        { name = "Duper7",       wtype = 0, mod = 11, price = 1000 },
        { name = "Uzer",         wtype = 0, mod = 12, price = 1000 },
        { name = "Groundride",   wtype = 0, mod = 13, price = 1000 },
        { name = "Spacer",       wtype = 0, mod = 14, price = 1000 },
        { name = "Venum",        wtype = 0, mod = 15, price = 1000 },
        { name = "Cosmo",        wtype = 0, mod = 16, price = 1000 },
        { name = "Dashvip",      wtype = 0, mod = 17, price = 1000 },
        { name = "Icekid",       wtype = 0, mod = 18, price = 1000 },
        { name = "Ruffeld",      wtype = 0, mod = 19, price = 1000 },
        { name = "Wangenmaster", wtype = 0, mod = 20, price = 1000 },
        { name = "Superfive",    wtype = 0, mod = 21, price = 1000 },
        { name = "Endov2",       wtype = 0, mod = 22, price = 1000 },
        { name = "Slitsix",      wtype = 0, mod = 23, price = 1000 },
    },
    -----Suv wheels------
    suvwheels = {
        { name = "Stock",          wtype = 3, mod = -1, price = 1000 },
        { name = "Vip",            wtype = 3, mod = 0,  price = 1000 },
        { name = "Benefactor",     wtype = 3, mod = 1,  price = 1000 },
        { name = "Cosmo",          wtype = 3, mod = 2,  price = 1000 },
        { name = "Bippu",          wtype = 3, mod = 3,  price = 1000 },
        { name = "Royalsix",       wtype = 3, mod = 4,  price = 1000 },
        { name = "Fagorme",        wtype = 3, mod = 5,  price = 1000 },
        { name = "Deluxe",         wtype = 3, mod = 6,  price = 1000 },
        { name = "Icedout",        wtype = 3, mod = 7,  price = 1000 },
        { name = "Cognscenti",     wtype = 3, mod = 8,  price = 1000 },
        { name = "Lozspeedten",    wtype = 3, mod = 9,  price = 1000 },
        { name = "Supernova",      wtype = 3, mod = 10, price = 1000 },
        { name = "Obeyrs",         wtype = 3, mod = 11, price = 1000 },
        { name = "Lozspeedballer", wtype = 3, mod = 12, price = 1000 },
        { name = "Extra vaganzo",  wtype = 3, mod = 13, price = 1000 },
        { name = "Splitsix",       wtype = 3, mod = 14, price = 1000 },
        { name = "Empowered",      wtype = 3, mod = 15, price = 1000 },
        { name = "Sunrise",        wtype = 3, mod = 16, price = 1000 },
        { name = "Dashvip",        wtype = 3, mod = 17, price = 1000 },
        { name = "Cutter",         wtype = 3, mod = 18, price = 1000 },
    },
    -----Offroad wheels-----
    offroadwheels = {
        { name = "Stock",                wtype = 4, mod = -1,     price = 1000 },
        { name = "Raider",               wtype = 4, mod = 0,      price = 1000 },
        { name = "Mudslinger",           wtype = 4, modtype = 23, mod = 1,     price = 1000 },
        { name = "Nevis",                wtype = 4, mod = 2,      price = 1000 },
        { name = "Cairngorm",            wtype = 4, mod = 3,      price = 1000 },
        { name = "Amazon",               wtype = 4, mod = 4,      price = 1000 },
        { name = "Challenger",           wtype = 4, mod = 5,      price = 1000 },
        { name = "Dunebasher",           wtype = 4, mod = 6,      price = 1000 },
        { name = "Fivestar",             wtype = 4, mod = 7,      price = 1000 },
        { name = "Rockcrawler",          wtype = 4, mod = 8,      price = 1000 },
        { name = "Milspecsteelie",       wtype = 4, mod = 9,      price = 1000 },
        { name = "Retro Steelie",        wtype = 4, mod = 10,     price = 1000 },
        { name = "Heavy Duty Steelie",   wtype = 4, mod = 11,     price = 1000 },
        { name = "Concave Steelie",      wtype = 4, mod = 12,     price = 1000 },
        { name = "Police Issue Steelie", wtype = 4, mod = 13,     price = 1000 },
        { name = "Lightweight Steelie",  wtype = 4, mod = 14,     price = 1000 },
        { name = "Dukes",                wtype = 4, mod = 15,     price = 1000 },
        { name = "Avalanche",            wtype = 4, mod = 16,     price = 1000 },
        { name = "Mountain Man",         wtype = 4, mod = 17,     price = 1000 },
        { name = "Ridge Climber",        wtype = 4, mod = 18,     price = 1000 },
        { name = "Concave 5",            wtype = 4, mod = 19,     price = 1000 },
        { name = "Flat Six",             wtype = 4, mod = 20,     price = 1000 },
        { name = "All Terrain Monster",  wtype = 4, mod = 21,     price = 1000 },
        { name = "Drag SPL",             wtype = 4, mod = 22,     price = 1000 },
        { name = "Concave Rally Master", wtype = 4, mod = 23,     price = 1000 },
        { name = "Rugged Snowflake",     wtype = 4, mod = 24,     price = 1000 },
    },
    -----Tuner wheels------
    tunerwheels = {
        { name = "Stock",        wtype = 5, mod = -1, price = 1000 },
        { name = "Cosmo",        wtype = 5, mod = 0,  price = 1000 },
        { name = "Supermesh",    wtype = 5, mod = 1,  price = 1000 },
        { name = "Outsider",     wtype = 5, mod = 2,  price = 1000 },
        { name = "Rollas",       wtype = 5, mod = 3,  price = 1000 },
        { name = "Driffmeister", wtype = 5, mod = 4,  price = 1000 },
        { name = "Slicer",       wtype = 5, mod = 5,  price = 1000 },
        { name = "Elquatro",     wtype = 5, mod = 6,  price = 1000 },
        { name = "Dubbed",       wtype = 5, mod = 7,  price = 1000 },
        { name = "Fivestar",     wtype = 5, mod = 8,  price = 1000 },
        { name = "Slideways",    wtype = 5, mod = 9,  price = 1000 },
        { name = "Apex",         wtype = 5, mod = 10, price = 1000 },
        { name = "Stancedeg",    wtype = 5, mod = 11, price = 1000 },
        { name = "Countersteer", wtype = 5, mod = 12, price = 1000 },
        { name = "Endov1",       wtype = 5, mod = 13, price = 1000 },
        { name = "Endov2dish",   wtype = 5, mod = 14, price = 1000 },
        { name = "Guppez",       wtype = 5, mod = 15, price = 1000 },
        { name = "Chokadori",    wtype = 5, mod = 16, price = 1000 },
        { name = "Chicane",      wtype = 5, mod = 17, price = 1000 },
        { name = "Saisoku",      wtype = 5, mod = 18, price = 1000 },
        { name = "Dishedeight",  wtype = 5, mod = 19, price = 1000 },
        { name = "Fujiwara",     wtype = 5, mod = 20, price = 1000 },
        { name = "Zokusha",      wtype = 5, mod = 21, price = 1000 },
        { name = "Battlevill",   wtype = 5, mod = 22, price = 1000 },
        { name = "Rallymaster",  wtype = 5, mod = 23, price = 1000 },
    },
    -----Highend wheels------
    highendwheels = {
        { name = "Stock",          wtype = 7, mod = -1, price = 1000 },
        { name = "Shadow",         wtype = 7, mod = 0,  price = 1000 },
        { name = "Hyper",          wtype = 7, mod = 1,  price = 1000 },
        { name = "Blade",          wtype = 7, mod = 2,  price = 1000 },
        { name = "Diamond",        wtype = 7, mod = 3,  price = 1000 },
        { name = "Supagee",        wtype = 7, mod = 4,  price = 1000 },
        { name = "Chromaticz",     wtype = 7, mod = 5,  price = 1000 },
        { name = "Merciechlip",    wtype = 7, mod = 6,  price = 1000 },
        { name = "Obeyrs",         wtype = 7, mod = 7,  price = 1000 },
        { name = "Gtchrome",       wtype = 7, mod = 8,  price = 1000 },
        { name = "Cheetahr",       wtype = 7, mod = 9,  price = 1000 },
        { name = "Solar",          wtype = 7, mod = 10, price = 1000 },
        { name = "Splitten",       wtype = 7, mod = 11, price = 1000 },
        { name = "Dashvip",        wtype = 7, mod = 12, price = 1000 },
        { name = "Lozspeedten",    wtype = 7, mod = 13, price = 1000 },
        { name = "Carboninferno",  wtype = 7, mod = 14, price = 1000 },
        { name = "Carbonshadow",   wtype = 7, mod = 15, price = 1000 },
        { name = "Carbonz",        wtype = 7, mod = 16, price = 1000 },
        { name = "Carbonsolar",    wtype = 7, mod = 17, price = 1000 },
        { name = "Carboncheetahr", wtype = 7, mod = 18, price = 1000 },
        { name = "Carbonsracer",   wtype = 7, mod = 19, price = 1000 },
    },
    -----Lowrider wheels------
    lowriderwheels = {
        { name = "Stock",       wtype = 2, mod = -1, price = 1000 },
        { name = "Flare",       wtype = 2, mod = 0,  price = 1000 },
        { name = "Wired",       wtype = 2, mod = 1,  price = 1000 },
        { name = "Triplegolds", wtype = 2, mod = 2,  price = 1000 },
        { name = "Bigworm",     wtype = 2, mod = 3,  price = 1000 },
        { name = "Sevenfives",  wtype = 2, mod = 4,  price = 1000 },
        { name = "Splitsix",    wtype = 2, mod = 5,  price = 1000 },
        { name = "Freshmesh",   wtype = 2, mod = 6,  price = 1000 },
        { name = "Leadsled",    wtype = 2, mod = 7,  price = 1000 },
        { name = "Turbine",     wtype = 2, mod = 8,  price = 1000 },
        { name = "Superfin",    wtype = 2, mod = 9,  price = 1000 },
        { name = "Classicrod",  wtype = 2, mod = 10, price = 1000 },
        { name = "Dollar",      wtype = 2, mod = 11, price = 1000 },
        { name = "Dukes",       wtype = 2, mod = 12, price = 1000 },
        { name = "Lowfive",     wtype = 2, mod = 13, price = 1000 },
        { name = "Gooch",       wtype = 2, mod = 14, price = 1000 },
    },
    -----Muscle wheels-----
    musclewheels = {
        { name = "Stock",       wtype = 1, mod = -1, price = 1000 },
        { name = "Classicfive", wtype = 1, mod = 0,  price = 1000 },
        { name = "Dukes",       wtype = 1, mod = 1,  price = 1000 },
        { name = "Musclefreak", wtype = 1, mod = 2,  price = 1000 },
        { name = "Kracka",      wtype = 1, mod = 3,  price = 1000 },
        { name = "Azrea",       wtype = 1, mod = 4,  price = 1000 },
        { name = "Mecha",       wtype = 1, mod = 5,  price = 1000 },
        { name = "Blacktop",    wtype = 1, mod = 6,  price = 1000 },
        { name = "Dragspl",     wtype = 1, mod = 7,  price = 1000 },
        { name = "Revolver",    wtype = 1, mod = 8,  price = 1000 },
        { name = "Classicrod",  wtype = 1, mod = 9,  price = 1000 },
        { name = "Spooner",     wtype = 1, mod = 10, price = 1000 },
        { name = "Fivestar",    wtype = 1, mod = 11, price = 1000 },
        { name = "Oldschool",   wtype = 1, mod = 12, price = 1000 },
        { name = "Eljefe",      wtype = 1, mod = 13, price = 1000 },
        { name = "Dodman",      wtype = 1, mod = 14, price = 1000 },
        { name = "Sixgun",      wtype = 1, mod = 15, price = 1000 },
        { name = "Mercenary",   wtype = 1, mod = 16, price = 1000 },
    },
    -----new wheels-----
    bennysoriginalwheels = {
        { name = "Stock",                     wtype = 8, mod = -1, price = 1000 },
        { name = "OG Hunnets",                wtype = 8, mod = 0,  price = 1000 },
        { name = "OG Hunnets (Chrome Lip)",   wtype = 8, mod = 1,  price = 1000 },
        { name = "Knock-Offs",                wtype = 8, mod = 2,  price = 1000 },
        { name = "Knock-Offs (Chrome Lip)",   wtype = 8, mod = 3,  price = 1000 },
        { name = "Spoked Out",                wtype = 8, mod = 4,  price = 1000 },
        { name = "Spoked Out (Chrome Lip)",   wtype = 8, mod = 5,  price = 1000 },
        { name = "Vintage Wire",              wtype = 8, mod = 6,  price = 1000 },
        { name = "Vintage Wire (Chrome Lip)", wtype = 8, mod = 7,  price = 1000 },
        { name = "Smoothie",                  wtype = 8, mod = 8,  price = 1000 },
        { name = "Smoothie (Chrome Lip)",     wtype = 8, mod = 9,  price = 1000 },
        { name = "Smoothie (Solid Color)",    wtype = 8, mod = 10, price = 1000 },
        { name = "Rod Me Up",                 wtype = 8, mod = 11, price = 1000 },
        { name = "Rod Me Up (Chrome Lip)",    wtype = 8, mod = 12, price = 1000 },
        { name = "Rod Me Up (Solid Color)",   wtype = 8, mod = 13, price = 1000 },
        { name = "Clean",                     wtype = 8, mod = 14, price = 1000 },
        { name = "Lotta Chrome",              wtype = 8, mod = 15, price = 1000 },
        { name = "Spindles",                  wtype = 8, mod = 16, price = 1000 },
        { name = "Viking",                    wtype = 8, mod = 17, price = 1000 },
        { name = "Triple Spoke",              wtype = 8, mod = 18, price = 1000 },
        { name = "Pharohe",                   wtype = 8, mod = 19, price = 1000 },
        { name = "Tiger Style",               wtype = 8, mod = 20, price = 1000 },
        { name = "Three Wheelin",             wtype = 8, mod = 21, price = 1000 },
        { name = "Big Bar",                   wtype = 8, mod = 22, price = 1000 },
        { name = "Biohazard",                 wtype = 8, mod = 23, price = 1000 },
        { name = "Waves",                     wtype = 8, mod = 24, price = 1000 },
        { name = "Lick Lick",                 wtype = 8, mod = 25, price = 1000 },
        { name = "Spiralizer",                wtype = 8, mod = 26, price = 1000 },
        { name = "Hypnotics",                 wtype = 8, mod = 27, price = 1000 },
        { name = "Psycho-Delic",              wtype = 8, mod = 28, price = 1000 },
        { name = "Half Cut",                  wtype = 8, mod = 29, price = 1000 },
        { name = "Super Electric",            wtype = 8, mod = 30, price = 1000 },
    },

    bennysbespokewheels = {
        { name = "Stock",                wtype = 9, mod = -1, price = 1000 },
        { name = "Chrome OG Hunnets",    wtype = 9, mod = 0,  price = 1000 },
        { name = "Gold OG Hunnets",      wtype = 9, mod = 1,  price = 1000 },
        { name = "Chrome Wires",         wtype = 9, mod = 2,  price = 1000 },
        { name = "Gold Wires",           wtype = 9, mod = 3,  price = 1000 },
        { name = "Chrome Spoked Out",    wtype = 9, mod = 4,  price = 1000 },
        { name = "Gold Spoked Out",      wtype = 9, mod = 5,  price = 1000 },
        { name = "Chrome Knock-Offs",    wtype = 9, mod = 6,  price = 1000 },
        { name = "Gold Knock-Offs",      wtype = 9, mod = 7,  price = 1000 },
        { name = "Chrome Bigger Worm",   wtype = 9, mod = 8,  price = 1000 },
        { name = "Gold Bigger Worm",     wtype = 9, mod = 9,  price = 1000 },
        { name = "Chrome Vintage Wire",  wtype = 9, mod = 10, price = 1000 },
        { name = "Gold Vintage Wire",    wtype = 9, mod = 11, price = 1000 },
        { name = "Chrome Classic Wire",  wtype = 9, mod = 12, price = 1000 },
        { name = "Gold Classic Wire",    wtype = 9, mod = 13, price = 1000 },
        { name = "Chrome Smoothie",      wtype = 9, mod = 14, price = 1000 },
        { name = "Gold Smoothie",        wtype = 9, mod = 15, price = 1000 },
        { name = "Chrome Classic Rod",   wtype = 9, mod = 16, price = 1000 },
        { name = "Gold Classic Rod",     wtype = 9, mod = 17, price = 1000 },
        { name = "Chrome Dollar",        wtype = 9, mod = 18, price = 1000 },
        { name = "Gold Dollar",          wtype = 9, mod = 19, price = 1000 },
        { name = "Chrome Mighty Star",   wtype = 9, mod = 20, price = 1000 },
        { name = "Gold Mighty Star",     wtype = 9, mod = 21, price = 1000 },
        { name = "Chrome Decadent Dish", wtype = 9, mod = 22, price = 1000 },
        { name = "Gold Decadent Dish",   wtype = 9, mod = 23, price = 1000 },
        { name = "Chrome Razor Style",   wtype = 9, mod = 24, price = 1000 },
        { name = "Gold Razor Style",     wtype = 9, mod = 25, price = 1000 },
        { name = "Chrome Celtic Knot",   wtype = 9, mod = 26, price = 1000 },
        { name = "Gold Celtic Knot",     wtype = 9, mod = 27, price = 1000 },
        { name = "Chrome Warrior Dish",  wtype = 9, mod = 28, price = 1000 },
        { name = "Gold Warrior Dish",    wtype = 9, mod = 29, price = 1000 },
        { name = "Gold Big Dog Spokes",  wtype = 9, mod = 30, price = 1000 },
    },

    openwheels = {
        { name = "Stock", wtype = 10, mod = -1, price = 1000 },
        { name = "0",     wtype = 10, mod = 0,  price = 1000 },
        { name = "1",     wtype = 10, mod = 1,  price = 1000 },
        { name = "2",     wtype = 10, mod = 2,  price = 1000 },
        { name = "3",     wtype = 10, mod = 3,  price = 1000 },
        { name = "4",     wtype = 10, mod = 4,  price = 1000 },
        { name = "5",     wtype = 10, mod = 5,  price = 1000 },
        { name = "6",     wtype = 10, mod = 6,  price = 1000 },
        { name = "7",     wtype = 10, mod = 7,  price = 1000 },
        { name = "8",     wtype = 10, mod = 8,  price = 1000 },
        { name = "9",     wtype = 10, mod = 9,  price = 1000 },
        { name = "10",    wtype = 10, mod = 10, price = 1000 },
        { name = "11",    wtype = 10, mod = 11, price = 1000 },
        { name = "12",    wtype = 10, mod = 12, price = 1000 },
        { name = "13",    wtype = 10, mod = 13, price = 1000 },
        { name = "14",    wtype = 10, mod = 14, price = 1000 },
        { name = "15",    wtype = 10, mod = 15, price = 1000 },
        { name = "16",    wtype = 10, mod = 16, price = 1000 },
        { name = "17",    wtype = 10, mod = 17, price = 1000 },
        { name = "18",    wtype = 10, mod = 18, price = 1000 },
        { name = "19",    wtype = 10, mod = 19, price = 1000 },
    },

    streetwheels = {
        { name = "Stock",                wtype = 11, mod = -1, price = 1000 },
        { name = "Retro Steelie",        wtype = 11, mod = 0,  price = 1000 },
        { name = "Poverty Spec Steelie", wtype = 11, mod = 1,  price = 1000 },
        { name = "Concave Steelie",      wtype = 11, mod = 2,  price = 1000 },
        { name = "Nebula",               wtype = 11, mod = 3,  price = 1000 },
        { name = "Hotring Steelie",      wtype = 11, mod = 4,  price = 1000 },
        { name = "Cup Champion",         wtype = 11, mod = 5,  price = 1000 },
        { name = "Stanced EG Custom",    wtype = 11, mod = 6,  price = 1000 },
        { name = "Kracka Custom",        wtype = 11, mod = 7,  price = 1000 },
        { name = "Dukes Custom",         wtype = 11, mod = 8,  price = 1000 },
        { name = "Endo v.3 Custom",      wtype = 11, mod = 9,  price = 1000 },
        { name = "V8 Killer",            wtype = 11, mod = 10, price = 1000 },
        { name = "Fujiwara Custom",      wtype = 11, mod = 11, price = 1000 },
        { name = "Cosmo MKII",           wtype = 11, mod = 12, price = 1000 },
        { name = "Aero Star",            wtype = 11, mod = 13, price = 1000 },
        { name = "Hype Five",            wtype = 11, mod = 14, price = 1000 },
        { name = "Ruff Weld Mega Deep",  wtype = 11, mod = 15, price = 1000 },
        { name = "Mercie Concave",       wtype = 11, mod = 16, price = 1000 },
        { name = "Sugoi Concave",        wtype = 11, mod = 17, price = 1000 },
        { name = "Synthetic Z Concave",  wtype = 11, mod = 18, price = 1000 },
        { name = "Endo v.4 Dished",      wtype = 11, mod = 19, price = 1000 },
        { name = "Hyperfresh",           wtype = 11, mod = 20, price = 1000 },
        { name = "Truffade Concave",     wtype = 11, mod = 21, price = 1000 },
        { name = "Organic Type II",      wtype = 11, mod = 22, price = 1000 },
        { name = "Big Mamba",            wtype = 11, mod = 23, price = 1000 },
        { name = "Deep Flake",           wtype = 11, mod = 24, price = 1000 },
        { name = "Cosmo MKIII",          wtype = 11, mod = 25, price = 1000 },
        { name = "Concave Racer",        wtype = 11, mod = 26, price = 1000 },
        { name = "Deep Flake Reverse",   wtype = 11, mod = 27, price = 1000 },
        { name = "Wild Wagon",           wtype = 11, mod = 28, price = 1000 },
        { name = "Concave Mega Mesh",    wtype = 11, mod = 29, price = 1000 },
    },

    trackwheels = {
        { name = "Stock",            wtype = 12, mod = -1, price = 1000 },
        { name = "Rally Throwback",  wtype = 12, mod = 0,  price = 1000 },
        { name = "Gravel Trap",      wtype = 12, mod = 1,  price = 1000 },
        { name = "Stove Top",        wtype = 12, mod = 2,  price = 1000 },
        { name = "Stove Top Mesh",   wtype = 12, mod = 3,  price = 1000 },
        { name = "Retro 3 Piece",    wtype = 12, mod = 4,  price = 1000 },
        { name = "Rally Monoblock",  wtype = 12, mod = 5,  price = 1000 },
        { name = "Forged 5",         wtype = 12, mod = 6,  price = 1000 },
        { name = "Split Star",       wtype = 12, mod = 7,  price = 1000 },
        { name = "Speed Boy",        wtype = 12, mod = 8,  price = 1000 },
        { name = "90s Running",      wtype = 12, mod = 9,  price = 1000 },
        { name = "Tropos",           wtype = 12, mod = 10, price = 1000 },
        { name = "Exos",             wtype = 12, mod = 11, price = 1000 },
        { name = "High Five",        wtype = 12, mod = 12, price = 1000 },
        { name = "Super Luxe",       wtype = 12, mod = 13, price = 1000 },
        { name = "Pure Business",    wtype = 12, mod = 14, price = 1000 },
        { name = "Pepper Pot",       wtype = 12, mod = 15, price = 1000 },
        { name = "Blacktop Blender", wtype = 12, mod = 16, price = 1000 },
        { name = "Throwback",        wtype = 12, mod = 17, price = 1000 },
        { name = "Expressway",       wtype = 12, mod = 18, price = 1000 },
        { name = "Hidden Six",       wtype = 12, mod = 19, price = 1000 },
        { name = "Dinka SPL",        wtype = 12, mod = 20, price = 1000 },
        { name = "Retro Turbofan",   wtype = 12, mod = 21, price = 1000 },
        { name = "Conical Turbofan", wtype = 12, mod = 22, price = 1000 },
        { name = "Ice Storm",        wtype = 12, mod = 23, price = 1000 },
        { name = "Super Turbine",    wtype = 12, mod = 24, price = 1000 },
        { name = "Modern Mesh",      wtype = 12, mod = 25, price = 1000 },
        { name = "Forged Star",      wtype = 12, mod = 26, price = 1000 },
        { name = "Snowflake",        wtype = 12, mod = 27, price = 1000 },
        { name = "Giga Mesh",        wtype = 12, mod = 28, price = 1000 },
        { name = "Mesh Meister",     wtype = 12, mod = 29, price = 1000 },
    },

    ---------Trim color--------
    trim = {
        colors = colors,
        price = 1000
    },

    ----------Mods-----------
    mods = {

        ----------Liveries--------
        [48] = {
            startprice = 15000,
            increaseby = 2500
        },

        ----------Windows--------
        [46] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Tank--------
        [45] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Trim--------
        [44] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Aerials--------
        [43] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Arch cover--------
        [42] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Struts--------
        [41] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Air filter--------
        [40] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Engine block--------
        [39] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Hydraulics--------
        [38] = {
            startprice = 15000,
            increaseby = 2500
        },

        ----------Trunk--------
        [37] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Speakers--------
        [36] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Plaques--------
        [35] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Shift leavers--------
        [34] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Steeringwheel--------
        [33] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Seats--------
        [32] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Door speaker--------
        [31] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Dial--------
        [30] = {
            startprice = 5000,
            increaseby = 1250
        },
        ----------Dashboard--------
        [29] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Ornaments--------
        [28] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Trim--------
        [27] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Vanity plates--------
        [26] = {
            startprice = 5000,
            increaseby = 1250
        },

        ----------Plate holder--------
        [25] = {
            startprice = 5000,
            increaseby = 1250
        },

        ---------Headlights---------
        [22] = {
            { name = "Stock Lights", mod = 0, price = 0 },
            { name = "Xenon Lights", mod = 1, price = 1625 },
        },

        ----------Turbo---------
        [18] = {
            { name = "None",         mod = 0, price = 0 },
            { name = "Turbo Tuning", mod = 1, price = 50000 },
        },

        -----------Armor-------------
        [16] = {
            { name = "Armor Upgrade 20%",  modtype = 16, mod = 0, price = 10000 },
            { name = "Armor Upgrade 40%",  modtype = 16, mod = 1, price = 250000 },
            { name = "Armor Upgrade 60%",  modtype = 16, mod = 2, price = 500000 },
            { name = "Armor Upgrade 80%",  modtype = 16, mod = 3, price = 750000 },
            { name = "Armor Upgrade 100%", modtype = 16, mod = 4, price = 1000000 },
        },

        ---------Suspension-----------
        [15] = {
            { name = "Lowered Suspension",     mod = 0, price = 1000 },
            { name = "Street Suspension",      mod = 1, price = 2000 },
            { name = "Sport Suspension",       mod = 2, price = 3500 },
            { name = "Competition Suspension", mod = 3, price = 4000 },
        },

        -----------Horn----------
        [14] = {
            { name = "Truck Horn",           mod = 0,  price = 1625 },
            { name = "Police Horn",          mod = 1,  price = 4062 },
            { name = "Clown Horn",           mod = 2,  price = 6500 },
            { name = "Musical Horn 1",       mod = 3,  price = 11375 },
            { name = "Musical Horn 2",       mod = 4,  price = 11375 },
            { name = "Musical Horn 3",       mod = 5,  price = 11375 },
            { name = "Musical Horn 4",       mod = 6,  price = 11375 },
            { name = "Musical Horn 5",       mod = 7,  price = 11375 },
            { name = "Sadtrombone Horn",     mod = 8,  price = 11375 },
            { name = "Calssical Horn 1",     mod = 9,  price = 11375 },
            { name = "Calssical Horn 2",     mod = 10, price = 11375 },
            { name = "Calssical Horn 3",     mod = 11, price = 11375 },
            { name = "Calssical Horn 4",     mod = 12, price = 11375 },
            { name = "Calssical Horn 5",     mod = 13, price = 11375 },
            { name = "Calssical Horn 6",     mod = 14, price = 11375 },
            { name = "Calssical Horn 7",     mod = 15, price = 11375 },
            { name = "Scaledo Horn",         mod = 16, price = 11375 },
            { name = "Scalere Horn",         mod = 17, price = 11375 },
            { name = "Scalemi Horn",         mod = 18, price = 11375 },
            { name = "Scalefa Horn",         mod = 19, price = 11375 },
            { name = "Scalesol Horn",        mod = 20, price = 11375 },
            { name = "Scalela Horn",         mod = 21, price = 11375 },
            { name = "Scaleti Horn",         mod = 22, price = 11375 },
            { name = "Scaledo Horn High",    mod = 23, price = 11375 },
            { name = "Jazz Horn 1",          mod = 25, price = 11375 },
            { name = "Jazz Horn 2",          mod = 26, price = 11375 },
            { name = "Jazz Horn 3",          mod = 27, price = 11375 },
            { name = "Jazzloop Horn",        mod = 28, price = 11375 },
            { name = "Starspangban Horn 1",  mod = 29, price = 11375 },
            { name = "Starspangban Horn 2",  mod = 30, price = 11375 },
            { name = "Starspangban Horn 3",  mod = 31, price = 11375 },
            { name = "Starspangban Horn 4",  mod = 32, price = 11375 },
            { name = "Classicalloop Horn 1", mod = 33, price = 11375 },
            { name = "Classicalloop Horn 2", mod = 34, price = 11375 },
            { name = "Classicalloop Horn 3", mod = 35, price = 11375 },
        },

        ----------Transmission---------
        [13] = {
            { name = "Street Transmission", mod = 0, price = 10000 },
            { name = "Sports Transmission", mod = 1, price = 25000 },
            { name = "Race Transmission",   mod = 2, price = 50000 },
        },

        -----------Brakes-------------
        [12] = {
            { name = "Street Brakes", mod = 0, price = 10000 },
            { name = "Sport Brakes",  mod = 1, price = 25000 },
            { name = "Race Brakes",   mod = 2, price = 50000 },
        },

        ------------Engine----------
        [11] = {
            { name = "EMS Upgrade, Level 2", mod = 0, price = 10000 },
            { name = "EMS Upgrade, Level 3", mod = 1, price = 25000 },
            { name = "EMS Upgrade, Level 4", mod = 2, price = 50000 },
        },

        -------------Roof----------
        [10] = {
            startprice = 1250,
            increaseby = 400
        },

        ------------Rear Fenders---------
        [9] = {
            startprice = 1500,
            increaseby = 400
        },
        ------------Fenders---------
        [8] = {
            startprice = 1500,
            increaseby = 400
        },

        ------------Hood----------
        [7] = {
            startprice = 1500,
            increaseby = 400
        },

        ----------Grille----------
        [6] = {
            startprice = 1250,
            increaseby = 400
        },

        ----------Roll cage----------
        [5] = {
            startprice = 1250,
            increaseby = 400
        },

        ----------Exhaust----------
        [4] = {
            startprice = 1000,
            increaseby = 400
        },

        ----------Skirts----------
        [3] = {
            startprice = 1250,
            increaseby = 400
        },

        -----------Rear bumpers----------
        [2] = {
            startprice = 2500,
            increaseby = 500
        },

        ----------Front bumpers----------
        [1] = {
            startprice = 2500,
            increaseby = 500
        },

        ----------Spoiler----------
        [0] = {
            startprice = 2500,
            increaseby = 400
        },
    }

}

MODS_HORNS = {
    { index = 0,  name = "Truck Horn",             price = 1000 },
    { index = 1,  name = "Police Horn",            price = 1000 },
    { index = 2,  name = "Clown Horn",             price = 1000 },
    { index = 3,  name = "Musical Horn 1",         price = 1000 },
    { index = 4,  name = "Musical Horn 2",         price = 10000 },
    { index = 5,  name = "Musical Horn 3",         price = 10000 },
    { index = 6,  name = "Musical Horn 4",         price = 10000 },
    { index = 7,  name = "Musical Horn 5",         price = 10000 },
    { index = 8,  name = "Sad Trombone",           price = 10000 },
    { index = 9,  name = "Classical Horn 1",       price = 10000 },
    { index = 10, name = "Classical Horn 2",       price = 10000 },
    { index = 11, name = "Classical Horn 3",       price = 10000 },
    { index = 12, name = "Classical Horn 4",       price = 10000 },
    { index = 13, name = "Classical Horn 5",       price = 10000 },
    { index = 14, name = "Classical Horn 6",       price = 10000 },
    { index = 15, name = "Classical Horn 7",       price = 10000 },
    { index = 16, name = "Scale - Do",             price = 10000 },
    { index = 17, name = "Scale - Re",             price = 10000 },
    { index = 18, name = "Scale - Mi",             price = 10000 },
    { index = 19, name = "Scale - Fa",             price = 10000 },
    { index = 20, name = "Scale - Sol",            price = 10000 },
    { index = 21, name = "Scale - La",             price = 10000 },
    { index = 22, name = "Scale - Ti",             price = 10000 },
    { index = 23, name = "Scale - Do",             price = 10000 },
    { index = 24, name = "Jazz Horn 1",            price = 10000 },
    { index = 25, name = "Jazz Horn 2",            price = 10000 },
    { index = 26, name = "Jazz Horn 3",            price = 10000 },
    { index = 27, name = "Jazz Horn Loop",         price = 10000 },
    { index = 28, name = "Star Spangled Banner 1", price = 10000 },
    { index = 29, name = "Star Spangled Banner 2", price = 10000 },
    { index = 30, name = "Star Spangled Banner 3", price = 10000 },
    { index = 31, name = "Star Spangled Banner 4", price = 10000 },
    { index = 32, name = "Classical Horn 8 Loop",  price = 10000 },
    { index = 33, name = "Classical Horn 9 Loop",  price = 10000 },
    { index = 34, name = "Classical Horn 10 Loop", price = 10000 },
    { index = 35, name = "Classical Horn 8",       price = 10000 },
    { index = 36, name = "Classical Horn 9",       price = 10000 },
    { index = 37, name = "Classical Horn 10",      price = 10000 },
    { index = 38, name = "Funeral Loop",           price = 10000 },
    { index = 39, name = "Funeral",                price = 35000 },
    { index = 40, name = "Spooky Loop",            price = 35000 },
    { index = 41, name = "Spooky",                 price = 35000 },
    { index = 42, name = "San Andreas Loop",       price = 35000 },
    { index = 43, name = "San Andreas",            price = 35000 },
    { index = 44, name = "Liberty City Loop",      price = 35000 },
    { index = 45, name = "Liberty City",           price = 35000 },
    { index = 46, name = "Festive 1 Loop",         price = 35000 },
    { index = 47, name = "Festive 1",              price = 35000 },
    { index = 48, name = "Festive 2 Loop",         price = 35000 },
    { index = 49, name = "Festive 2",              price = 35000 },
    { index = 50, name = "Festive 3 Loop",         price = 35000 },
    { index = 51, name = "Festive 3",              price = 35000 },
    { index = 52, name = "Airhorn 1 Loop",         price = 35000 },
    { index = 53, name = "Airhorn 1",              price = 35000 },
    { index = 54, name = "Airhorn 2 Loop",         price = 35000 },
    { index = 55, name = "Airhorn 2",              price = 35000 },
    { index = 56, name = "Airhorn 3 Loop",         price = 35000 },
    { index = 57, name = "Airhorn 3",              price = 35000 },
}

function GetHornFromIndex(index)
    return MODS_HORNS[index - 1]
end
