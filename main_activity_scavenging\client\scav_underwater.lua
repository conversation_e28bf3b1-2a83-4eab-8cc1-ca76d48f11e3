local SCAV_TOOLS = {
    "weapon_knife",
    "weapon_screwdriver",
}

local openWater = 0.0
local lastScavCoords = false
local lastScavTime = 0
local scavCooldown = 15 * 1000

local function doesPlayerHaveTool(ped)
    local weapon = GetSelectedPedWeapon(PlayerPedId())
    for _,v in pairs(SCAV_TOOLS) do
        if (GetHashKey(v) == weapon) then
            return true             
        end
    end
    return false
end

local function isPedUnderWater(ped)
    if IsPedSwimmingUnderWater(ped) then
        return true        
    end
    return false
end

-- I dont think there are any backyard or open pools that are less than 0.0? Maybe Canals.. Alamo Sea is 30.0 but parts go below 0.0
local function isPedinOpenWater(ped)
    local coords = GetEntityCoords(ped)
    local pedZ = coords.z
   
    if pedZ <= openWater then
        return true        
    end
    return false
end

local function closeToGround(ped)
    if GetEntityHeightAboveGround(ped) <= 1 then
        return true        
    end
    return false
end

local function canScav(ped)
    if doesPlayerHaveTool(ped) and isPedUnderWater(ped) and closeToGround(ped) and isPedinOpenWater(ped) then
        return true        
    end
    return false
end

local function hasPlayerMoved(ped)
    if lastScavCoords then
        if GetDistanceBetweenCoords(lastScavCoords, GetEntityCoords(ped)) > 3 then
            lastScavCoords = GetEntityCoords(ped)
            return true
        end
    else
        lastScavCoords = GetEntityCoords(ped)
        return true
    end
    return false
end

onBaseReady(function()
    Citizen.CreateThread(function()
        while true do   
            local ped = PlayerPedId()     
            if IsControlJustPressed(1, 47) then 
                if canScav(ped) then
                    if hasPlayerMoved(ped) then
                        local currentTime = GetGameTimer()
                        if currentTime - lastScavTime > scavCooldown then
                            FreezeEntityPosition(ped, true)  
                            lastScavTime = GetGameTimer()         
                            TriggerServerEvent("main_scav:underwater_scav")
                            Wait(scavCooldown)
                            FreezeEntityPosition(ped, false)
                        end
                    end
                end
            end
            Wait(IsPedSwimmingUnderWater(ped) and 0 or 1000)
        end    
    end)
end)


RegisterNetEvent("inventory:clientSync")
AddEventHandler('inventory:clientSync', function(name, inv, money, dirty, capacity)
    if (string.sub(name, 1, string.len("scav_underwater_")) == "scav_underwater_") then

        local phone = exports["main_phone_2"]
        if phone:isPhoneOpen() then phone:togglePhone() end

        exports["core_inventory"]:loadExternalInventory("Scavenged Items", inv, false, false, 20000,
            function(itemIndex, count)
                TriggerServerEvent("main_scav:serverAddScavItem", itemIndex, count)
            end,

            function(itemIndex, count)
                TriggerServerEvent("main_scav:playerAddScavItem", itemIndex, count)
            end,

            function()
                TriggerServerEvent("main_scav:closeScavMenu")
            end
        )
    end
end)