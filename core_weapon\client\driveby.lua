-- Passenger drive by
local isDisabled = true
local currentWeapon = false
local oldCamera = nil

local function IsPlayerFirstPerson()
	return GetFollowVehicleCamViewMode() == 4
end

AddEventHandler("weaponEquiped", function(weapon)
	currentWeapon = weapon
end)

AddEventHandler("weaponUnequiped", function()
	currentWeapon = false
end)

local function SetCameraMode(mode)
	SetCamViewModeForContext(1, mode)
	SetCamViewModeForContext(2, mode)
	SetCamViewModeForContext(3, mode)
	SetCamViewModeForContext(4, mode)
	SetCamViewModeForContext(5, mode)
	SetCamViewModeForContext(6, mode)
	SetCamViewModeForContext(7, mode)
	SetFollowVehicleCamViewMode(mode)
end

local blockRearShootingVehicles = {}
RegisterDataSyncHandler("blockRearShootingVehicles", function(vehs)
	blockRearShootingVehicles = vehs
end)

local blockShootingFromVehicles = {}
RegisterDataSyncHandler("blockShootingFromVehicles", function(vehs)
	blockShootingFromVehicles = vehs
end)

local damageModifierActive = false

function lookingBehind()
	local coord = GetOffsetFromEntityInWorldCoords(GetPlayerPed(-1), 0.0, -8.0, 0.0)
	local onScreen, _x, _y = World3dToScreen2d(coord.x, coord.y, coord.z)
	return onScreen
end

Citizen.CreateThread(function()
	while true do
		Wait(0)
		local ped = PlayerPedId()
		local veh = GetVehiclePedIsUsing(ped)

		if IsPedInAnyVehicle(ped, true) then
			damageModifierActive = true
			SetPlayerWeaponDamageModifier(PlayerId(), 0.5)

			-- Check if player is 
			if blockShootingFromVehicles[tostring(GetEntityModel(veh))] and GetPedInVehicleSeat(veh, -1) == ped then
				--Block driver from shooting
				SetPlayerCanDoDriveBy(PlayerId(), false)
			elseif blockRearShootingVehicles[tostring(GetEntityModel(veh))] == 1 and GetPedInVehicleSeat(veh, -1) == ped and lookingBehind() then
				--Block driver from shooting backwards
				SetPlayerCanDoDriveBy(PlayerId(), false)
			elseif blockRearShootingVehicles[tostring(GetEntityModel(veh))] == 2 and lookingBehind() then
				--Block everyone from shooting backwards
				SetPlayerCanDoDriveBy(PlayerId(), false)
			elseif GetSelectedPedWeapon(ped) == `WEAPON_STUNGUN` or GetSelectedPedWeapon(ped) == `WEAPON_STUNGUN2` then
				--Block tasers from being shot from vehicles
				SetPlayerCanDoDriveBy(PlayerId(), false)	
			else
				SetPlayerCanDoDriveBy(PlayerId(), true)
			end
		elseif damageModifierActive then
			SetPlayerWeaponDamageModifier(PlayerId(), 1.0)
			damageModifierActive = false
		end
	end
end)
