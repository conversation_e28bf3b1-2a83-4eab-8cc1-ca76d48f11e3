local MAX_KEYS = 5
local currentKeys = {}

function CheckKeyCount()
    if (#currentKeys >= MAX_KEYS) then
        TriggerEvent("core_hud:sendNotification", "key", nil, "You have misplaced the keys to " .. currentKeys[1].plate, {})
        table.remove(currentKeys, 1)
    end
end

function AddKey(veh)
    CheckKeyCount()
    if (not Has<PERSON>ey(veh)) then
        table.insert(currentKeys, { plate = string.upper(GetVehicleNumberPlateText(veh)), model = GetEntityModel(veh) })
        TriggerEvent("core_hud:sendNotification", "key", nil, "You have recieved the keys for " .. string.upper(GetVehicleNumberPlateText(veh)), {})
    else
        TriggerEvent("core_hud:sendNotification", "key", nil, "You already have the keys to this car.", {})
    end
end

function HasKey(veh)
    for k,v in pairs(currentKeys) do
        if (v.plate == string.upper(GetVehicleNumberPlateText(veh)) and v.model == GetEntityModel(veh)) then
            return true
        end
    end
    return false
end

function ClearKeys()
    currentKeys = {}
end

RegisterNetEvent("core_vehicles:clearKeys")
AddEventHandler("core_vehicles:clearKeys", function()
    ClearKeys()
end)

RegisterNetEvent("core_vehicles:addKey")
AddEventHandler("core_vehicles:addKey", function(netId)
    AddKey(NetworkGetEntityFromNetworkId(netId))
end)

RegisterCommand("givekey", function()
    local veh = Base.Vehicles.GetClosestVehicle(GetPlayerPed(-1), 3.0, true)
    local player, dist = Base.Player.getClosestPlayer(GetEntityCoords(PlayerPedId()), 3.0)

    if isDead then return end

    if (player == -1 or player == PlayerId()) then
        TriggerEvent("core_hud:sendNotification", "key", nil, "No Player Found.")
        return
    end

    if (not DoesEntityExist(veh)) then
        TriggerEvent("core_hud:sendNotification", "key", nil, "No Vehicle Found.")
        return
    end

    canUnlockVehicle(veh, function(allowed)
        if (allowed) then
            local vehNetId = NetworkGetNetworkIdFromEntity(veh)
            local vehId = Entity(veh).state.id -- Ensure you're extracting the correct ID from the vehicle entity
            print(vehId) -- Debugging: Print the vehicle ID to ensure it's correct
            TriggerServerEvent("core_vehicles:addKey", GetPlayerServerId(player), vehNetId)
            TriggerServerEvent("core_vehicles:log_givekeys", GetPlayerServerId(PlayerId()), GetPlayerServerId(player), vehNetId, vehId) -- Pass vehId to the server
        else
            TriggerEvent("core_hud:sendNotification", "key", nil, "You do not have the keys.")
        end
    end)
end)