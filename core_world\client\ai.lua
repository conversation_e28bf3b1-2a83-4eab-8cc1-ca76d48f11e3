CreateThread(function()
    --while true do
    -- Force Set Relationships between <PERSON><PERSON> and Players
    SetRelationshipBetweenGroups(3, <PERSON><PERSON><PERSON><PERSON><PERSON>("AMBIENT_GANG_HILLBILLY"), <PERSON><PERSON><PERSON><PERSON><PERSON>('PLAYER'))
    SetRelationshipBetweenGroups(3, <PERSON><PERSON><PERSON><PERSON><PERSON>("AMBIENT_GANG_CULT"), <PERSON><PERSON><PERSON><PERSON><PERSON>('PLAYER'))
    SetRelationshipBetweenGroups(3, <PERSON><PERSON><PERSON><PERSON><PERSON>("AMBIENT_GANG_LOST"), <PERSON><PERSON><PERSON><PERSON><PERSON>('PLAYER'))
    SetRelationshipBetweenGroups(3, <PERSON><PERSON><PERSON><PERSON><PERSON>("AMBIENT_GANG_BALLAS"), <PERSON><PERSON><PERSON><PERSON><PERSON>('PLAYER'))
    SetRelationshipBetweenGroups(3, <PERSON><PERSON><PERSON><PERSON><PERSON>("AMBIENT_GANG_MEXICAN"), <PERSON><PERSON><PERSON><PERSON><PERSON>('PLAYER'))
    SetRelationshipBetweenGroups(3, <PERSON><PERSON><PERSON><PERSON><PERSON>("AMBIENT_GANG_FAMILY"), <PERSON><PERSON><PERSON><PERSON><PERSON>('<PERSON><PERSON>Y<PERSON>'))
    SetRelationshipBetweenGroups(3, <PERSON><PERSON><PERSON><PERSON><PERSON>("AMBIENT_GANG_WEICHENG"), <PERSON><PERSON><PERSON><PERSON><PERSON>('PLAYER'))
    <PERSON>RelationshipBetweenGroups(3, GetHashKey("AMBIENT_GANG_MARABUNTE"), GetHashKey('PLAYER'))
    SetRelationshipBetweenGroups(3, GetHashKey("AMBIENT_GANG_SALVA"), GetHashKey('PLAYER'))
    SetRelationshipBetweenGroups(3, GetHashKey("GANG_1"), GetHashKey('PLAYER'))
    SetRelationshipBetweenGroups(3, GetHashKey("GANG_2"), GetHashKey('PLAYER'))
    SetRelationshipBetweenGroups(3, GetHashKey("GANG_9"), GetHashKey('PLAYER'))
    SetRelationshipBetweenGroups(3, GetHashKey("GANG_10"), GetHashKey('PLAYER'))
    SetRelationshipBetweenGroups(3, GetHashKey("FIREMAN"), GetHashKey('PLAYER'))
    SetRelationshipBetweenGroups(3, GetHashKey("MEDIC"), GetHashKey('PLAYER'))
    SetRelationshipBetweenGroups(3, GetHashKey("COP"), GetHashKey('PLAYER'))
    SetRelationshipBetweenGroups(3, GetHashKey("CIVMALE"), GetHashKey('PLAYER'))
    SetRelationshipBetweenGroups(3, GetHashKey("CIVFEMALE"), GetHashKey('PLAYER'))
    SetRelationshipBetweenGroups(3, GetHashKey("NO_RELATIONSHIP"), GetHashKey('PLAYER'))
    SetRelationshipBetweenGroups(3, GetHashKey("SHARK"), GetHashKey('PLAYER'))
    SetRelationshipBetweenGroups(3, GetHashKey("AGGRESSIVE_INVESTIGATE"), GetHashKey('PLAYER'))     --  Merryweather security for TAC HQ

    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("AMBIENT_GANG_CULT"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("AMBIENT_GANG_HILLBILLY"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("AMBIENT_GANG_LOST"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("AMBIENT_GANG_BALLAS"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("AMBIENT_GANG_MEXICAN"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("AMBIENT_GANG_FAMILY"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("AMBIENT_GANG_WEICHENG"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("AMBIENT_GANG_MARABUNTE"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("AMBIENT_GANG_SALVA"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("GANG_1"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("GANG_2"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("GANG_9"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("GANG_10"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("FIREMAN"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("MEDIC"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("COP"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("CIVMALE"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("CIVFEMALE"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("NO_RELATIONSHIP"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("SHARK"))
    SetRelationshipBetweenGroups(3, GetHashKey('PLAYER'), GetHashKey("AGGRESSIVE_INVESTIGATE"))     --  Merryweather security for TAC HQ

    --Wait(0)

    --end
end)

-- Control peds not to run away unless armed
Citizen.CreateThread(function()
    while true do
        Wait(250)

        -- clear the players wanted level
        --SetPlayerWantedLevel(PlayerId(), 0, false)

        local find, ped = FindFirstPed()
        local success
        repeat
            if not IsEntityAMissionEntity(ped) then
                if (IsPedArmed(PlayerPedId(), 4)) and (not IsPedAPlayer(ped)) and (NetworkGetEntityIsNetworked(ped)) and (GetPedType(ped) ~= 28) then
                    SetBlockingOfNonTemporaryEvents(ped, false)
                elseif (not IsPedDeadOrDying(ped)) and (not IsPedAPlayer(ped)) and NetworkGetEntityIsNetworked(ped) and (GetPedType(ped) ~= 28) then
                    SetBlockingOfNonTemporaryEvents(ped, true)
                end
            end
            success, ped = FindNextPed(find)
        until not success
        EndFindPed(find)
    end
end)
