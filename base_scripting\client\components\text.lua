Base.Text = {}

Base.Text.queue = {}

Base.Text.networkAdd3dText = function(coords, message, dist, time, centre, size)
    TriggerServerEvent("base:networkAdd3dText", coords, message, dist, time, centre, size)
end

RegisterNetEvent("base:networkAdd3dText")
AddEventHandler("base:networkAdd3dText", function(target, coords, message, dist, time, centre, size)
    table.insert(Base.Text.queue, { source = target, coords = coords, message = message, dist = dist or 8.0, time = time or 5000, centre = centre or false, size = size, start = GetGameTimer() })
end)

local function getCoordsFor3ds(coords, centre)
    if (type(coords) == "number") then
        local player = GetPlayerFromServerId(coords)
        if (player ~= -1) then
            return GetEntityCoords(GetPlayerPed(player)) + ( centre and vector3(0, 0, 0) or vector3(0, 0, 0.8) ), HasEntityClearLosToEntity(PlayerPedId(), GetPlayerPed(player), 17)
        end
            -- elseif (DoesEntityExist(NetworkGetEntityFromNetworkId(coords))) then
        --     return GetEntityCoords(NetworkGetEntityFromNetworkId(coords)) + ( centre and vector3(0, 0, 0) or vector3(0, 0, 0.8) ), HasEntityClearLosToEntity(PlayerPedId(), NetworkGetEntityFromNetworkId(coords), 17)
        -- end
    elseif (coords.x) then
        return vector3(coords.x, coords.y, coords.z) + ( centre and vector3(0, 0, 0) or vector3(0, 0, 0.8) ), true
    end
    return false
end

Citizen.CreateThread(function()
    Wait(1000)
    while true do
        local playerCoords = GetEntityCoords(PlayerPedId())
        local currentTime = GetGameTimer()
        local adjust = {}
        for i = #Base.Text.queue, 1, - 1 do
            local text = Base.Text.queue[i]
            if (currentTime - text.start < text.time) then
                local coords, visible = getCoordsFor3ds(text.coords, text.centre)
                if (coords and visible) then
                    local dist = #( coords - playerCoords )
                    if (dist < text.dist) then
                        adjust["-"..text.source] = (adjust["-"..text.source] == nil and 1.5 or adjust["-"..text.source] + 1)
                        Base.DrawText(coords.x, coords.y, coords.z + adjust["-"..text.source]/8, text.message, text.size)
                    end
                end
            else
                table.remove(Base.Text.queue, i)
            end
        end
        Wait(0)
    end
end)