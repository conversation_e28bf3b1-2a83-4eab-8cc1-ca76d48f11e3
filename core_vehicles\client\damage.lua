
-- get/set damage
function getVehicleDamage(veh)
    return Base.Vehicles.Custom.Get(veh).damage 
    -- {
    --     fuel = DecorGetFloat(veh, "tank"),
    --     axle = DecorGetFloat(veh, "axle"),
    --     brakes = DecorGetFloat(veh, "brakes"),
    --     clutch = DecorGetFloat(veh, "clutch"),
    --     radiator = DecorGetFloat(veh, "radiator"),
    --     transmission = DecorGetFloat(veh, "transmission"),
    -- }
end

function setRandomDamage(veh)
    local class = GetVehicleClass(veh)
    if (class == 13 or class == 14 or class == 15 or class == 16 or class == 21) then
        return
    end

    local nosDamage = WasOrIsNosActive()
    local offDamage = isOffroad(veh)

    local damageFuel, damageAxle, damageBrakes, damageClutch, damageRadiator, damageTransmission

    if (nosDamage) then
        
    else

    end

    
    
    
    local current = Base.Vehicles.Custom.Get(veh)


    -- set new damage here


    Base.Vehicles.Custom.Set(veh, current)
end

CreateThread(function()
    while true do
      Wait(1000)
  
      local veh = GetVehiclePedIsUsing(PlayerPedId())

      -- Check if player is driving a vehicle
      if DoesEntityExist(veh) and GetPedInVehicleSeat(veh, -1) == PlayerPedId() then
        local speed = GetEntitySpeed(veh)
        local angle = GetVehicleDoorAngleRatio(veh, 5)
        if speed > 20.0 and angle > 0.5 then
          SetVehicleDoorBroken(veh, 5, false)
        end
      end
  
    end
  end)

function setDamageEffects(veh, tick)
    local damage = getVehicleDamage(veh)
    local class = GetVehicleClass(veh)

    if (class == 13 or class == 14 or class == 15 or class == 16 or class == 21) then
        return
    end

    -- fuel injectors damage
    if (damage.fuel <= 45) then
        local chance = math.random(10, 100)
        if (damage.fuel <= 45 and damage.fuel >= 25) then 
            if (chance > 99) then 
                fuelFailure(veh, 50) 
            end
        elseif (damage.fuel <= 24 and damage.fuel >= 15) then
            if (chance > 99) then 
                fuelFailure(veh, 400) 
            end
        elseif (damage.fuel <= 14 and damage.fuel >= 9) then
            if (chance > 99) then 
                fuelFailure(veh, 600) 
            end
        elseif (damage.fuel <= 8 and damage.fuel >= 0) then
            if (chance > 99) then 
                fuelFailure(veh, 1000) 
            end
        end
    end

    -- radiator damage
    if (damage.radiator <= 35 and tick >= 15) then
        local health = GetVehicleEngineHealth(veh)
        if (damage.radiator <= 35 and damage.radiator >= 15) then
            if (health <= 1000 and health >= 700) then
                SetVehicleEngineHealth(veh, health - 10)
            end
        elseif (damage.radiator <= 19 and damage.radiator >= 10 ) then
            if (health <= 1000 and health >= 500) then
                SetVehicleEngineHealth(veh, health - 20)
            end
        elseif (damage.radiator <= 9 and damage.radiator >= 0) then
            if (health <= 1000 and health >= 200) then
                SetVehicleEngineHealth(veh, health - 30)
            end
        end
    end

    -- axle damage
    if (damage.axle <= 35 and tick >= 15) then 
        local chance = math.random(1,100)
        if (damage.axle <= 35 and damage.axle >= 20 and chance > 90) then
            for i = 0, 360 do
                Wait(5)
                SetVehicleSteeringScale(veh, i)
            end
        elseif (damage.axle <= 19 and damage.axle >= 10 and chance > 70) then
            for i = 0, 360 do
                Wait(10)
                SetVehicleSteeringScale(veh, i)
            end
        elseif (damage.axle <= 9 and damage.axle >= 0 and chance > 50) then
            for i = 0, 360 do
                Wait(15)
                SetVehicleSteeringScale(veh, i)
            end
        end
    end

    -- transmission damage
    if (damage.transmission <= 35 and tick >= 15) then
        local speed = GetEntitySpeed(veh)
        local chance = math.random(100)
        if (damage.transmission <= 35 and damage.transmission >= 20 and chance > 90) then
            for i = 0, 3 do
                if (not IsPedInVehicle(PlayerPedId(), veh, false)) then return end
                Wait(5)
                SetVehicleHandbrake(veh, true)
                Wait(math.random(1000))
                SetVehicleHandbrake(veh, false)
            end
        elseif (damage.transmission <= 19 and damage.transmission >= 10 and chance > 70) then
            for i = 0, 5 do
                if (not IsPedInVehicle(PlayerPedId(), veh, false)) then return end
                Wait(10)
                SetVehicleHandbrake(veh, true)
                Wait(math.random(1000))
                SetVehicleHandbrake(veh, false)
            end
        elseif (damage.transmission <= 9 and damage.transmission >= 0 and chance > 50) then
            for i = 0, 11 do
                if (not IsPedInVehicle(PlayerPedId(), veh, false)) then return end
                Wait(20)
                SetVehicleHandbrake(veh, true)
                Wait(math.random(1000))
                SetVehicleHandbrake(veh, false)
            end
        end
    end

    -- brakes damage
    if (damage.brakes <= 35 and tick >= 15) then
        local chance = math.random(100)
        if (damage.brakes <= 35 and damage.brakes >= 20 and chance > 90) then
            SetVehicleHandbrake(veh, true)
            Wait(1000)
            SetVehicleHandbrake(veh, false)
        elseif (damage.brakes <= 19 and damage.brakes >= 10 and chance > 70) then
            SetVehicleHandbrake(veh, true)
            Wait(4500)
            SetVehicleHandbrake(veh, false)
        elseif (damage.brakes <= 9 and damage.brakes >= 0 and chance > 50) then
            SetVehicleHandbrake(veh, true)
            Wait(7000)
            SetVehicleHandbrake(veh, false)
        end
    else
        SetVehicleHandbrake(veh, false)
    end

    if (damage.clutch <= 35 and tick >= 15) then
        local chance = math.random(100)
        if (damage.clutch <= 35 and damage.clutch >= 20 and chance > 90) then
            SetVehicleHandbrake(veh, true)
            fuelFailure(veh, 50)
            for i = 1, 360 do
                SetVehicleSteeringScale(veh, i)
                Wait(5)
            end
            Wait(2000)
            SetVehicleHandbrake(veh, false)
        elseif (damage.clutch <= 19 and damage.clutch >= 10 and chance > 70) then
            SetVehicleHandbrake(veh, true)
            fuelFailure(veh, 50)
            for i = 1, 360 do
                SetVehicleSteeringScale(veh, i)
                Wait(5)
            end
            Wait(5000)
            SetVehicleHandbrake(veh, false)
        elseif (damage.clutch <= 9 and damage.clutch >= 0 and chance > 50) then
            SetVehicleHandbrake(veh, true)
            fuelFailure(veh, 50)
            for i = 1, 360 do
                SetVehicleSteeringScale(veh, i)
                Wait(5)
            end
            Wait(7000)
            SetVehicleHandbrake(veh, false)
        end
    end

end

-- failure effects
function fuelFailure(veh, wait)

    local start = GetGameTimer()
    while (GetGameTimer() - start < wait) do
        DisableControlAction(0, 71, true)
        Wait(0)
    end


    --SetVehicleEngineOn(veh, false, false, true)
    --ToggleEngineForcedOff(veh, true)
	-- SetVehicleUndriveable(veh, true)
    --Wait(wait)




    --ToggleEngineForcedOff(veh, false)
	--SetVehicleEngineOn(veh, true, false, true)
    -- SetVehicleUndriveable(veh, false)
end

-- Citizen.CreateThread(function()
--     local effectTick = 0
--     local damageTick = 0
--     local lastVeh = 0

--     while (true) do
--         local ped = PlayerPedId()
--         local veh = GetVehiclePedIsUsing(ped)
--         local inVeh = (veh ~= 0)

--         if (lastVeh ~= veh and veh ~= 0) then
--             effectTick = 0
--             damageTick = 0
--         end

--         if (inVeh) then
--             lastVeh = veh
--             effectTick = effectTick + 1
--             damageTick = damageTick + 1

--             if (effectTick >= 15) then
--                 setDamageEffects(veh, effectTick)
--                 effectTick = 0
--             end

--             if (damageTick >= 60) then
--                 -- setRandomDamage(veh)
--             end
--         end

--         Wait(1000)
--     end
-- end)