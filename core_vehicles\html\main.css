
body {
    display: none;
    font-family: <PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>s,Helvetica Neue,Arial,sans-serif !important; 
    outline: none !important;
    -moz-user-select: none;
    -webkit-user-select: none;
}

/* Table CSS */
table {
    font-family: arial, sans-serif;
    border-collapse: collapse;
    width: 100%;
}

td, th {
    border: 1px solid #dddddd;
    text-align: left;
    padding: 8px;
}

tr:nth-child(even) {
    background-color: #dddddd;
}


/* Main CSS */

#chopshop {
    background-image: url(../img/xp.jpg);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}

.screen {
    display: block;
    width: 110vh;
    height: 65vh;
    margin: auto;
    margin-top: 15vh;
    background-color: black;
    border: 10px solid black;
}

/* Content */
.screen #content {
    display: block;
    width: 100%;
    height: 93%;
}

.screen #content #top {
    display: block;
    width: 100%;
    height: 5%;
    background-color: rgba(0,0,0,0.8);
}

.screen #content #top #title {
    float: left;
    width: 30%;
    height: 100%;
}

.screen #content #top #title p {
    float: left;
    color: rgba(255,255,255,0.7);
    padding: 0px;
    margin: 0px;
    padding-top: 1%;
    padding-left: 3%;
    font-size: 1.6vh;
}

.screen #content #top .button {
    float: right;
    width: 3%;
    height: 100%;
}

.screen #content #top .button:hover {
    background-color: rgba(255,255,255,0.1);
}

.screen #content #top .button img {
    display: block;
    width: 50%;
    height: 50%;
    margin: auto;
    margin-top: 20%;
}

/* Screen */
.screen #content #screen {
    display: block;
    width: 100%;
    height: 95%;
    overflow: hidden;

    background-color: rgb(233, 235, 238);
}

/* This is the main css for the chopshop site */

#screen #site-bar {
    display: block;
    width: 100%;
    height: 25%;
    background-color: black;
}

#screen #site-bar #image-hold {
    display: block;
    width: 100%;
    height: 70%;
    margin: auto;
    background-color: yellow;
}

#screen #site-bar #image-hold img {
    display: block;
    max-height: 100%;
    margin: auto
}

#screen #site-bar #button-hold {
    display: block;
    width: 40%;
    height: 30%;
    margin: auto;
}

#screen #site-bar #button-hold button {
    float: left;
    width: 50%;
    height: 90%;
    margin: auto;
    margin-top: .5%;

    border-radius: .5vh;
    border: .1vh solid black;
    background: rgb(209, 0, 0);
    font-size: 2.5vh;
    font-weight: bold;
}

#screen #site-bar #button-hold button:hover {
    background-color: red;
}

#screen #site-bar #button-hold button[selected] {
    background-color: white;
}

#screen .site-content {
    display: block;
    width: 100%;
    height: 75%;
    margin: auto;

    overflow-y: scroll;
}

#screen .site-content .car {
    display: block;
    width: 65%;
    height: 25%;
    margin: auto;
    margin-top: .5%;
    margin-bottom: .5%;
    border: .2vh solid black;
    border-radius: 1vh;
    background-color: rgba(0,0,0,0.1);
}

#screen .site-content .car img {
    float: left;
    max-height: 95%;
    margin-top: .3%;
    margin-left: .5%;
    border: .1vh solid black;
    border-radius: .5vh;
}

#screen .site-content .car .reward {
    display: inline-block;
    width: 50%;
    height: 100%;
    margin-left: 1%;
}

#screen .site-content .car .reward h1 {
    padding: 0px;
    margin: 0px;
    font-size: 2.2vh;
}

#screen .site-content .car .reward p {
    margin: 0px;
    padding: 0px;
    margin-left: 5%;
    font-weight: bold;
    font-size: 1.5vh;
}

#screen .site-content .car .reward p span {
    font-weight: normal;
}

#screen .site-content .car .options {
    float: right;
    width: 21%;
    height: 100%;
    margin-left: 1%;
}

#screen .site-content .car .options button {
    display: block;
    width: 90%;
    height: 50%;
    border: none;
    margin: auto;
    margin-top: 17%;
    border-radius: .5vh;
    border: .1vh solid black;
    background: none;
    font-size: 2.5vh;
    font-weight: bold;
}

#screen .site-content .car .options button:hover {
    background-color: rgb(0, 179, 0);
}

#screen .site-content #part-center {
    display: block;
    width: 80%;
    margin: auto;
}

#screen .site-content #part-center h1 {
    text-align: center;
    font-size: 4vh;
    margin-bottom: 2%;
}

#screen .site-content #part-center img {
    display: block;
    margin: auto;
    max-width: 50%;
}

#screen .site-content #part-center p {
    display: block;
    width: 90%;
    margin: auto;
    margin-top: 2%;
    margin-bottom: 2%;
    font-size: 1.6vh;
    font-weight: bold;
}

#screen .site-content #part-center p span {
    font-weight: normal;
    font-style: italic;
    font-size: 1.3vh;
}

#screen .site-content #part-center button {
    display: block;
    width: 40%;
    height: 10%;
    margin: auto;
    margin-top: 5%;
    margin-bottom: 5%;
    border-radius: .5vh;
    border: .1vh solid black;
    background: rgb(209, 0, 0);
    font-size: 2.5vh;
    font-weight: bold;
} 

#screen .site-content #part-center button:hover {
    background-color: red;
}

/* End of main css for the chopshop site */

/* Task bar */
.screen #task-bar {
    display: block;
    width: 100%;
    height: 7%;
    background-color: rgba(0,0,0,0.6);
}

.screen #task-bar #home {
    float: left;
    width: 5%;
    height: 100%;
    background-color: rgba(0,0,0,0.1);
}

.screen #task-bar #home:hover {
    background-color: rgba(255,255,255,0.1);
}

.screen #task-bar #home img {
    display: block;
    width: 60%;
    height: 70%;
    margin: auto;
    margin-top: 13%;
}

.screen #task-bar #search {
    float: left;
    width: 25%;
    height: 95%;
    background-color: rgba(51,51,51,0.9);
    border: 0.1vh solid rgba(255,255,255,0.2);
}

.screen #task-bar #search p {
    float: left;
    color: rgba(255,255,255,0.4);
    padding: 0px;
    margin: 0px;
    padding-top: 4%;
    padding-left: 3%;
    font-size: 1.6vh;
}

.screen #task-bar .app {
    float: left;
    width: 4.5%;
    height: 100%;
    margin-left: .5%;
}

.screen #task-bar .app:hover {
    background-color: rgba(255,255,255,0.1);
}

.screen #task-bar .app[selected] {
    height: 95%;
    background-color: rgba(255,255,255,0.1);
    border-bottom: 0.2vh solid rgba(255,255,255,0.2);
}

.screen #task-bar .app img {
    display: block;
    width: 60%;
    height: 70%;
    margin: auto;
    margin-top: 13%;
}

.screen #task-bar #date-time {
    float: right;
    width: 7%;
    height: 100%;
    margin-right: 0.5%;
    border-right: 0.1vh solid rgba(255,255,255,0.2);
}

.screen #task-bar #date-time:hover {
    background-color: rgba(255,255,255,0.1);
}

.screen #task-bar #date-time p {
    float: left;
    color: rgba(255,255,255,0.7);
    padding: 0px;
    margin: 0px;
    padding-top: 15%;
    padding-left: 10%;
    font-size: 1.4vh;
}