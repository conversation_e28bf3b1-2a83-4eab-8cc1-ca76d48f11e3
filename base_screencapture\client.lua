local requestID = 0
local sessionid = nil

local responses = {}
RegisterNUICallback("capture", function(data, cb)
    responses[data.requestID] = data.link
end)

local function requestScreenshot(timeout)
    if not sessionid then
        print("Cannot request screenshot without sessionid")
        return nil
    end

    requestID = requestID + 1
    SendNUIMessage({
        requestID = requestID,
        crop = false,
        token = sessionid
    })

    local timeoutAt = GetGameTimer() + (timeout or 15000)
    while GetGameTimer() < timeoutAt do
        if responses[requestID] then
            return responses[requestID]
        end
        Wait(0)
    end

    return nil
end
exports("requestScreenshot", requestScreenshot)

RegisterNetEvent("screencapture:token", function(token)
    sessionid = token
end)
