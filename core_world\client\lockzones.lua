local LOCKEDZONES = {
    {
        name = "CAS_HIGH_ROLLER",
        minHeight = -60.0,
        maxHeight = -20.0,
        coords = {
            vector2(1149.091796875, 243.87246704102),
            vector2(1144.0327148438, 243.86186218262),
            vector2(1141.7669677734, 249.55912780762),
            vector2(1144.5162353516, 255.10382080078),
            vector2(1148.6632080078, 255.21875),
            vector2(1151.3588867188, 249.48742675781)
        },
        walkToPoints = {
            { coords = vector3(1146.936, 257.2968, -51.4408),  heading = 358.48770141602 },
            { coords = vector3(1146.588, 242.8656, -51.04079), heading = 184.85751342773 }
        },
        restrictions = {
            items = { "casino_vipcard" },
            jobs = { "casino", "police", "afp", "lses", "umbrella" },
            noWeapon = true
        },
        defaultAllow = false,
        denyText = "You need a VIP Card to enter this area",
    },
    {
        name = "CAS_ENTRY",
        minHeight = 70.0,
        maxHeight = 90.0,
        coords = {
            vector2(922.51525878906, 42.141223907471),
            vector2(928.02911376953, 50.657917022705),
            vector2(937.99005126953, 45.856739044189),
            vector2(930.71203613281, 33.758678436279)
        },
        walkToPoints = {
            { coords = vector3(922.5656, 44.60301, 81.10632), heading = 56.250587463379 },
            { coords = vector3(924.0479, 46.95021, 81.10632), heading = 58.264556884766 },
            { coords = vector3(925.5942, 49.29324, 81.10632), heading = 60.961750030518 }
        },
        restrictions = {
            jobs = { "casino", "police", "afp", "lses", "umbrella" },
            noWeapon = true
        },
        defaultAllow = true,
        denyText = "You cannot enter the casino with a weapon",
    },
    {
        name = "CAS_HELI_ENTRY",
        minHeight = 122.0,
        maxHeight = 125.0,
        coords = {
            vector2(975.0976, 48.26982),
            vector2(973.6071, 49.5),
            vector2(980.8921, 61.8),
            vector2(982.8, 60.38455)
        },
        walkToPoints = {
            { coords = vector3(974.0211, 48.42621, 123.12),   heading = 150.15385437012 },
            { coords = vector3(985.9724, 60.34062, 123.1202), heading = 83.467765808105 }
        },
        restrictions = {
            jobs = { "casino", "police", "afp", "lses", "umbrella" },
            noWeapon = true
        },
        defaultAllow = true,
        denyText = "You cannot enter the casino with a weapon",
    },
    {
        name = "VU_Entry",
        minHeight = 29.0,
        maxHeight = 33.0,
        coords = {
            vector2(-1392.36, -590.94),
            vector2(-1389.25, -595.68),
            vector2(-1382.35, -590.63),
            vector2(-1385, -586.06)
        },
        walkToPoints = {
            { coords = vector3(-1388.4271240234, -586.82159423828, 30.218473434448), heading = 31.1 }
        },
        restrictions = {
            jobs = { "unicorn", "police", "afp", "lses", "umbrella" },
            noWeapon = true
        },
        defaultAllow = true,
        denyText = "You cannot enter Vanilla Unicorn with a weapon",
    },
    {
        name = "CYBERBAR_ENTRY",
        minHeight = 3.0,
        maxHeight = 12.0,
        coords = {
            vector2(-866.2025, -2136.985),
            vector2(-833.063, -2106.305),
            vector2(-816.8133, -2132.022),
            vector2(-838.9183, -2160.184)
        },
        walkToPoints = {
            { coords = vector3(-863.7512, -2143.836, 8.936057), heading = 135.0 }
        },
        restrictions = {
            jobs = { "cyberbar", "police", "afp", "lses", "umbrella" },
            noWeapon = true
        },
        defaultAllow = true,
        denyText = "You cannot enter the Cyber Bar with a weapon",
    }
}

local blacklistedZones = {}

local function getClosestWalkoutPoint(zone)
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local closestDist = 1000.0
    local closestPoint = nil

    for i, point in ipairs(zone.walkToPoints) do
        local dist = #(coords - point.coords)
        if dist < closestDist then
            closestDist = dist
            closestPoint = point
        end
    end

    return closestPoint
end


local function canPlayerAccessZone(zone)
    local pData = Base.GetPlayerData()

    if blacklistedZones[zone.name] then
        print("Player is blacklisted from ", zone.name)
        return false, "You are blacklisted from this area until " .. blacklistedZones[zone.name]
    end

    if zone.restrictions.items then
        local items = {}

        for i, item in ipairs(zone.restrictions.items) do
            items[item] = true
        end

        for _, item in ipairs(pData.inventory) do
            if items[item.name] then
                print("Player has ", item.name)
                return true
            end
        end
    end

    if zone.restrictions.jobs then
        local jobs = {}

        for i, job in ipairs(zone.restrictions.jobs) do
            if DoesPlayerHaveOrg(job) then
                return true
            end
        end
    end

    if zone.restrictions.noWeapon then
        for _, item in ipairs(pData.inventory) do
            if item.type == "weapon" then
                print("Player has weapon ", item.name)
                return false, 'You cannot have a weapon in this area'
            end
        end
    end

    return zone.defaultAllow or false
end

CreateThread(function()
    Wait(500)
    print("Loading LOCKEDZONES...")
    for i, zone in ipairs(LOCKEDZONES) do
        zone.polyzone = PolyZone:Create(zone.coords, {
            name = zone.name,
            minZ = zone.minHeight,
            maxZ = zone.maxHeight,
            gridDivisions = 25,
            debugGrid = false
        })

        zone.polyzone:onPlayerInOut(function(isPointInside, b)
            if isPointInside then
                local ped = PlayerPedId()

                local canAccess, reason = canPlayerAccessZone(zone)
                if not canAccess then
                    LocalPlayer.state.beingBooted = true
                    local walkToPoint = getClosestWalkoutPoint(zone)

                    if walkToPoint then
                        Base.Notification(reason or zone.denyText or "You cannot access this area")
                        ClearPedTasksImmediately(ped)

                        CreateThread(function()
                            while #(GetEntityCoords(ped) - walkToPoint.coords) > 1.0 do
                                TaskGoStraightToCoord(ped, walkToPoint.coords, 1.0, -1, walkToPoint.heading, 0.0)
                                Wait(0)
                            end
                            LocalPlayer.state.beingBooted = false
                        end)
                    end
                end
            end
        end)
    end

    Wait(1000)
    TriggerServerEvent("lockedzones:getBlacklist")
end)

RegisterNetEvent("lockedzones:getBlacklist", function(blacklist)
    blacklistedZones = blacklist
end)
