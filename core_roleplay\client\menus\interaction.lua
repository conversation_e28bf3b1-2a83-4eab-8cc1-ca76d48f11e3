local registeredPlayerOptions = {}
local registeredVehicleOptions = {}

function addInteractionOption(type, condition, menuAction, menuItem, requires)
    local resource = GetInvokingResource()
    if type == "player" then
        table.insert(registeredPlayerOptions, { condition = condition, menuAction = menuAction, menuItem = menuItem, requires = requires, resource = resource})
    elseif type == "vehicle" then
        table.insert(registeredVehicleOptions, { condition = condition, menuAction = menuAction, menuItem = menuItem, requires = requires, resource = resource})
    end
end
exports("addInteractionOption", addInteractionOption)

local function createPlayerElements()
    local elements = {
        { text = "Surrender", icon = "&#xF023B;", action = "surrender"},
        { text = "Face Accessories", icon = "&#xF0643;", subMenu =  {
            { text = "Hat", icon = "&#xF0BA4;", action = "clothing_hat" },
            { text = "Glasses", icon = "&#xF02AA;", action = "clothing_glasses" },
            { text = "Mask", icon = "&#xF0825;", action = "clothing_mask" },
        }},
        { text = "Clothing", icon = "&#xF0A7B;", subMenu =  {
            { text = "Shirt", icon = "&#xF0A7B;", action = "clothing_shirt" },
            { text = "Pants", icon = "&#xF047E;", action = "clothing_pants" },
            { text = "Shoes", icon = "&#xF0B47;", action = "clothing_shoes" },
            { text = "Chain", icon = "&#xF0F0B;", action = "clothing_chain" },
            { text = "Vest", icon = "&#xF0498;", action = "clothing_vest" },
            { text = "Bag", icon = "&#xF0E10;", action = "clothing_bag" },
            { text = "Gloves", icon = "&#xF0A4F;", action = "clothing_gloves" },
            { text = "Decals", icon = "&#xF0E0D;", action = "clothing_decals" },
        }},
        { text = "Options", icon = "&#xF0493;", subMenu = {
            { text = "Switch Character", icon = "&#xF000A;", action = "switch_character" },
            { text = "Voice Circle", icon = "&#xF001A;", action = "toggle_voice_circles" },
            { text = "Aircraft UI", icon = "&#xF001D;", action = "aircraft_ui" },
        }},
    }

    for _, element in pairs(registeredPlayerOptions) do
        if element then
            if element.menuItem.subMenu and element.condition() then
                table.insert(elements, element.menuItem)
            elseif element.condition() then
                table.insert(elements, element.menuItem)
                QUI.registerMenuAction(element.menuItem.action, element.menuAction, element.requires)
            end
        end
    end

    return elements
end

local function createVehicleElements()
    local elements = {}

    for _, element in pairs(registeredVehicleOptions) do
        if element then
            if element.menuItem.subMenu and element.condition() then
                table.insert(elements, element.menuItem)
            elseif element.condition() then
                table.insert(elements, element.menuItem)
                QUI.registerMenuAction(element.menuItem.action, element.menuAction, element.requires)
            end
        end
    end

    return elements
end


-- Define the menu structure (this could be dynamic too!)
local function createMenuElements()

    if IsPedInAnyVehicle(PlayerPedId(), false) then
        return createVehicleElements()
    else
        return createPlayerElements()
    end

end

-- QUI.registerMenuAction("voice_circle", function()
--     exports["main_roleplay"]:surrender()
-- end)

-- QUI.registerMenuAction("aircraft_ui", function()
--     exports["main_roleplay"]:surrender()
-- end)

-- Define what opens the menu
Controls.register("lmenu", "Interaction Wheel",
    function()
        QUI.Radial.setMenuItems(createMenuElements())
        QUI.Radial.open()
        QUI.TriggerEvent("toggleEffectMini", false)
    end,
    function()
        QUI.Radial.close()
        QUI.TriggerEvent("toggleEffectMini", true)
    end
)

AddEventHandler("onClientResourceStop", function(resourcename)
    for i, option in ipairs(registeredPlayerOptions) do
        if option.resource == resourcename then
            table.remove(registeredPlayerOptions, i)
        end
    end

    for i, option in ipairs(registeredVehicleOptions) do
        if option.resource == resourcename then
            table.remove(registeredVehicleOptions, i)
        end
    end
end)