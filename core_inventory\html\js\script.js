var shiftPressed = false;
var transferTimeout = false;
var transfer;
var inventoryItems = {};

var attachments = {
  ["attachment_extendedmag"]: "Extended Mag",
  ["attachment_extendedmag2"]: "Drum Mag",
  ["attachment_grip"]: "Grip",
  ["attachment_suppressor"]: "Suppressor",
  ["attachment_flashlight"]: "Flashlight",
  ["attachment_scope"]: "Scope Small",
  ["attachment_scope2"]: "Scope Large",
  ["attachment_reddot"]: "Red Dot",
  ["attachment_heavybarrel"]: "Heavy Barrel",
  ["attachment_muzzleboost"]: "Muzzle Boost",
};

function formatNumber(num) {
  return num ? num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,") : "0";
}

function createContextMenu(e, attachments, licenses, options) {
  var menu = $("#itemMenu");
  menu.empty();

  // default item
  menu.append('<li id="useItem">Use Item</li>');
  $("#useItem").on("mousedown", function (e) {
    $.post("http://core_inventory/useItem", contextItem);
    $("#itemMenu").hide();
  });

  //

  // populate with custom items
  if (attachments && attachments[0]) {
    var subMenu = $("<li id='attachments' >Attachments</li>");
    var subMenuList = $("<ul></ul>");

    for (var i = 0; i < attachments.length; i++) {
      subMenuList.append(`<li data-attachment=${attachments[i].key} style='color: red;' >${attachments[i].label}</li>`);
    }

    subMenuList
      .children()
      .sort((a, b) => {
        return $(b).html() < $(a).html() ? 1 : -1;
      })
      .appendTo(subMenuList);
    subMenu.append(subMenuList);
    menu.append(subMenu);

    $("#attachments li").on("mousedown", function (e) {
      $.post(
        "http://core_inventory/removeAttachment",
        JSON.stringify({
          item: contextItem,
          attachment: $(this).data("attachment"),
        })
      );
      $("#itemMenu").hide();
    });
  }

  if (licenses) {
    for (var i = 0; i < licenses.length; i++) {
      menu.append(`<li class="contextMenuOption" id="${licenses[i].key}">${licenses[i].label}</li>`);
    }

    $(".contextMenuOption").on("mousedown", function (e) {
      $.post("http://core_inventory/showLicense", contextItem);
      $("#itemMenu").hide();
    });
  }

  if (options) {
    for (var i = 0; i < options.length; i++) {
      menu.append(`<li class="contextMenuOption" id="${options[i].key}">${options[i].label}</li>`);
    }
  }

  menu.css("left", e.pageX);
  menu.css("top", e.pageY);
  menu.css("display", "block");
}

function createAttachmentLabel(data) {
  var attachments_active = [];
  for (var key in data) {
    if (attachments[key]) {
      if (data[key]) {
        attachments_active.push(attachments[key]);
      }
    }
  }

  attachments_active.sort((a, b) => (a.toLowerCase() > b.toLowerCase() ? 1 : -1));

  var attachment_string = "";
  for (var i = 0; i < attachments_active.length; i++) {
    attachment_string += `<br>- <font color='red'>${attachments_active[i]}</font>`;
  }

  return attachment_string;
}

var labelTask = false;

function startTagItem(item) {
  labelTask = item;
  $("#itemTagDialog").show();
  $("#tagDialogInput").val("");
}

function tagItem() {
  if (labelTask) {
    $.post(
      "http://core_inventory/labelItem",
      JSON.stringify({ itemIndex: labelTask, text: $("#tagDialogInput").val() })
    );
    $("#itemTagDialog").hide();
    labelTask = false;
  }
}

$(function () {
  $("#tagDialogInput").keypress(function (e) {
    if (e.which == 13) {
      tagItem();
    }
  });

  $("#tagDialogCancel").on("click", function (e) {
    $("#itemTagDialog").hide();
    labelTask = false;
  });

  $("#tagDialogConfirm").on("click", function (e) {
    tagItem();
  });
});

function invInterface(element) {
  var self = {};

  self.element = element;
  self.showIndividualWeight = false;

  self.setLabel = function (label) {
    self.element.find(".inventory-label").first().html(label);
  };

  self.updateWeight = function (weight) {
    self.element
      .find(".inventory-weight")
      .first()
      .html(weight / 1000);
  };

  self.updateCapacity = function (capacity) {
    self.element
      .find(".inventory-capacity")
      .first()
      .html(" / " + (capacity / 1000 || " - ") + "KG");
  };

  self.setIndividualWeightDisplay = function (setting) {
    self.showIndividualWeight = setting;
  };

  self.setCash = function (amount) {
    self.element.find(".cashDisplay").html("$" + formatNumber(amount));
    self.element.find(".cashDisplay").parent().attr("count", amount);
  };

  self.setDirty = function (amount) {
    self.element.find(".dirtyDisplay").html("" + formatNumber(amount));
    self.element.find(".dirtyDisplay").parent().attr("count", amount);
  };

  self.updateItems = function (items, isProximity) {
    var weight = 0;
    var currentItem = 4; // Item starts at 4 as the first 4 items are done manually (because of the static slots)
    var itemCount = items.length;

    if (isProximity) {
      currentItem = 0;
    }

    var cashItem = $(`
            <td class="inventoryslot staticslot">
                <div class="inventoryitem staticitem" label="Cash" account="money" count="0">
                    <img src="nui://stream_graphics/data/inventory/cash.png">
                    <label class="countLabel cashDisplay">$0</label>
                </div>
            </td>`);

    var dirtyItem = $(`
            <td class="inventoryslot staticslot">
                <div class="inventoryitem staticitem" label="Casino Chips" account="dirty" count="0">
                    <img src="nui://stream_graphics/data/inventory/dirty.png">
                    <label class="countLabel dirtyDisplay">0</label>
                </div>
            </td>`);

    var requiredRows = Math.ceil((itemCount + 3) / 6);
    var rowCount = Math.max(requiredRows, 5);

    var tbody = $("<tbody></tbody>");

    for (let rowNumber = 0; rowNumber < rowCount; rowNumber++) {
      var row = $('<tr class="inventory-itembar"></tr>');

      if (rowNumber == 0 && !isProximity) {
        row.append(cashItem);
        row.append(dirtyItem);
        row.append(createItemDiv(items[0], 0, self.showIndividualWeight));
        row.append(createItemDiv(items[1], 1, self.showIndividualWeight));
        row.append(createItemDiv(items[2], 2, self.showIndividualWeight));
        row.append(createItemDiv(items[3], 3, self.showIndividualWeight));

        if (items[0]) {
          weight += items[0].count * items[0].weight;
        }
        if (items[1]) {
          weight += items[1].count * items[1].weight;
        }
        if (items[2]) {
          weight += items[2].count * items[2].weight;
        }
        if (items[3]) {
          weight += items[3].count * items[3].weight;
        }
      } else {
        for (let i = 0; i < 6; i++) {
          row.append(createItemDiv(items[currentItem], currentItem, self.showIndividualWeight));

          if (items[currentItem]) {
            weight += items[currentItem].count * items[currentItem].weight;

            currentItem++;
          }
        }
      }

      tbody.append(row);
    }

    self.element.find("tbody").replaceWith(tbody);
    self.updateWeight(weight);

    $(".inventoryitem").draggable({
      revert: true,
      revertDuration: 0,
      scroll: false,
      helper: "clone",
      appendTo: "body",
    });

    $(".inventoryitem").on("click", function (e) {
      if (shiftPressed && !transferTimeout) {
        transferTimeout = true;
        var oldInventory = getInventoryFromSlot($(this));
        var newInventory = "external-inventory";

        if (oldInventory == "external-inventory") {
          newInventory = "player-inventory";
        }

        if ($(this).attr("account")) {
          transfer = {
            oldInventory: oldInventory,
            newInventory: newInventory,
            index: $(this).attr("account"),
            count: $(this).attr("count"),
            proxIndex: $(this).attr("proxIndex"),
          };
        } else {
          transfer = {
            oldInventory: oldInventory,
            newInventory: newInventory,
            index: $(this).attr("index"),
            count: $(this).attr("count"),
            proxIndex: $(this).attr("proxIndex"),
          };
        }

        transferItem();

        setTimeout(function () {
          transferTimeout = false;
        }, 300);
      }
    });

    $(".inventoryitem").hover(
      function () {
        var item = getInvItemFromSlot($(this));

        var labelText = $(this).attr("label");

        if (item) {
          if (item.weight) {
            labelText += ` | ${item.weight}g`;
          }

          if (item.data && item.data.tag) {
            labelText += `<br><i class="subtitle">${item.data.tag}</i>`;
          }

          if (item.data && item.data.blood) {
            labelText += `<br><i class="subtitle"><font color="#c2443d">Bloody</font></i>`;
          }

          labelText += createAttachmentLabel(item.data);
        }

        $("#itemTooltip").html(labelText);
        $("#itemTooltip").show();
      },
      function () {
        $("#itemTooltip").hide();
      }
    );

    $(".inventoryslot")
      .not(".staticslot")
      .droppable({
        drop: function (event, ui) {
          var inventoryItem = ui.draggable;

          if (!inventoryItem.hasClass("inventoryitem")) {
            return false;
          }

          var oldInventory = getInventoryFromSlot(inventoryItem.parent());
          var newInventory = getInventoryFromSlot($(this));

          if (inventoryItem.attr("account")) {
            if (oldInventory != newInventory) {
              transfer = {
                oldInventory: oldInventory,
                newInventory: newInventory,
                index: inventoryItem.attr("account"),
                count: inventoryItem.attr("count"),
              };

              openDialogWindow(inventoryItem.attr("count"));
            }
          } else {
            if (oldInventory != newInventory) {
              transfer = {
                oldInventory: oldInventory,
                newInventory: newInventory,
                index: inventoryItem.attr("index"),
                count: inventoryItem.attr("count"),
                proxIndex: inventoryItem.attr("proxIndex"),
              };

              if (parseInt(inventoryItem.attr("count")) > 1) {
                openDialogWindow(inventoryItem.attr("count"));
              } else {
                transferItem();
              }
            } else {
              var index = $(this).find(".inventoryItem").attr("index");

              if (oldInventory == "player-inventory") {
                var item = inventoryItems[oldInventory][inventoryItem.attr("index")];

                if (item.name == "tag") {
                  startTagItem(index);
                  return;
                }
              }

              if (index) {
                transfer = {
                  oldInventory: oldInventory,
                  newInventory: newInventory,
                  index: inventoryItem.attr("index"),
                  count: inventoryItem.attr("count"),
                  newIndex: index,
                };
                transferItem();
              }
            }
          }
        },
      });

    $(".inventoryitem").on("contextmenu", function (e) {
      e.preventDefault();
      contextItem = $(this).attr("index");

      // look, this system CAN be modified to use lots of different options, but I have yet to figure out a good way of doing it. so for the meantime I am just being cheap asf and adding only support for attachments.
      var options = [];
      var licenses = [];
      var item = getInvItemFromSlot($(this));
      var data = item ? item.data : false;
      if (data) {
        for (var key in data) {
          if (attachments[key]) {
            if (data[key]) {
              options.push({ key: key, label: "Remove " + attachments[key] });
            }
          }
        }
      }

      if (item.idType) {
        if (item.idType == "id") {
          licenses.push({ label: "Show ID" });
        } else if (item.idType == "badge") {
          licenses.push({ label: "Show Badge" });
        } else if (item.idType == "polaroid") {
          licenses.push({ label: "Show Photo" });
        }
      }

      createContextMenu(e, options, licenses);
    });
  };

  return self;
}

function createItemDiv(item, index, showIndividualWeight) {
  var slot = $('<td class="inventoryslot"></td>');

  if (item) {
    var weight = item.count * item.weight;
    if (showIndividualWeight) {
      weight = item.weight;
    }

    var element = $(
      `<div class="inventoryitem" label="${item.label}" item="${
        item.name
      }" weight="${weight}" index="${index}" count="${item.count}" proxIndex="${item.proxIndex}" data=${JSON.stringify(
        item.data
      )}></div>`
    );
    element.append('<img src="nui://stream_graphics/data/inventory/' + item.name + '.png" onerror="noImage(this)">');
    element.append(
      '<label class="countLabel">' +
        (item.name == "cash" || item.name == "dirty" ? "" : "") +
        formatNumber(item.count) +
        "</label>"
    );
    slot.append(element);
  }

  return slot;
}

function noImage(image) {
  var item = $(image).parent();
  $(image).hide();
  item.append($('<label class="itemLabel">' + item.attr("label") + "</label>"));
}

function loadAnimationMenuData(animData) {
  animations = animData;
  var menu = $("#hotkeyBindMenu");

  menu.empty();

  for (const category in animations) {
    if (animations.hasOwnProperty(category)) {
      const catAnims = animations[category];

      var subMenu = $("<li>" + category + "</li>");
      var subMenuList = $("<ul></ul>");

      for (const label in catAnims) {
        subMenuList.append(`<li class="animItem" animCat="${category}">${label}</li>`);
      }

      subMenuList.children().sort(sort_li).appendTo(subMenuList);
      subMenu.append(subMenuList);
      menu.append(subMenu);
    }
  }

  $("#hotkeyBindMenu li").not(".animItem").sort(sort_li).appendTo("#hotkeyBindMenu");

  function sort_li(a, b) {
    return $(b).html() < $(a).html() ? 1 : -1;
  }
}

var hotbarFadeTimeout;

window.onmousemove = function (e) {
  var x = e.clientX,
    y = e.clientY;
  $("#itemTooltip").css("top", y + 20 + "px");
  $("#itemTooltip").css("left", x + 20 + "px");
};

window.addEventListener("message", function (event) {
  var item = event.data;

  // Animations
  if (item.animations) {
    loadAnimationMenuData(item.animations);
  }

  // Hotbar Events
  if (item.selectSlot) {
    $("#slot_" + item.selectSlot).addClass("selected");

    // var image = $(`#slot_${item.selectSlot} .hotbarItem`).attr("src");
    // console.log(image);
    // $("#notification").css("background-image", `url(${image})`);
    // $("#notification").fadeIn(250);
    // setTimeout(function() { $("#notification").fadeOut(250); }, 2000);

    if (hotbarVisibility == 1) {
      $("#hotbar").fadeIn(200);

      if (hotbarFadeTimeout) {
        clearTimeout(hotbarFadeTimeout);
      }

      hotbarFadeTimeout = setTimeout(function () {
        $("#hotbar").fadeOut(200);
        hotbarFadeTimeout = null;
        $("#hotkeyBindMenu").hide();
      }, 2000);
    }
  }

  if (item.deselectSlot) {
    $("#slot_" + item.deselectSlot).removeClass("selected");
  }

  if (item.hotbar) {
    for (const key in item.hotbar) {
      if (item.hotbar[key] != "") {
        $("#slot_" + key).attr("label", item.hotbar[key]);
        $("#slot_" + key)
          .find("img")
          .attr("src", "nui://stream_graphics/data/inventory/" + item.hotbar[key] + ".png");
        $("#slot_" + key)
          .find("img")
          .show();
      } else {
        $("#slot_" + key).attr("label", "");
        $("#slot_" + key)
          .find("img")
          .hide();
      }
    }
  }

  // Inventory Events
  if (item.showInventory === true) {
    $("#inventory-window").fadeIn(100);
    $("#hotbar").fadeIn(100);
  }

  if (item.showInventory === false) {
    $("#inventory-window").fadeOut(100);
    $("#countDialog").fadeOut(100);
    $("#itemTagDialog").fadeOut(100);
    $("#hotkeyBindMenu").fadeOut(100);
    $("#itemTooltip").fadeOut(100);
    $("#itemMenu").fadeOut(100);

    if (hotbarVisibility == 1 || hotbarVisibility == 2) {
      $("#hotbar").fadeOut(100);
    }
  }

  if (item.inventory) {
    var display = invInterface($("#" + item.inventory));

    if (item.showIndividualWeight != undefined) {
      display.setIndividualWeightDisplay(item.showIndividualWeight);
    }

    if (item.items) {
      display.updateItems(item.items, item.isProximity || false);
      inventoryItems[item.inventory] = item.items;
    }

    if (item.label) {
      display.setLabel(item.label);
    }

    if (item.money != null) {
      display.setCash(item.money);
    }

    if (item.dirty != undefined) {
      display.setDirty(item.dirty);
    }

    if (item.capacity) {
      display.updateCapacity(item.capacity);
    }
  }

  if (item.charInfo) {
    if (item.charInfo.name) {
      $("#nameDisplay").html(item.charInfo.name);
    }
    if (item.charInfo.job) {
      $("#jobIcon").attr("src", "nui://stream_graphics/data/jobs/" + item.charInfo.job + ".png");
    }
    if (item.charInfo.jobName) {
      $("#jobDisplay").html(item.charInfo.jobName);
      $("#jobGradeDisplay").html("");
    }
    if (item.charInfo.jobGradeName) {
      $("#jobGradeDisplay").html(item.charInfo.jobGradeName);
    }
    if (item.charInfo.subjob) {
      $("#subjobIcon").attr("src", "nui://stream_graphics/data/jobs/" + item.charInfo.subjob + ".png");
    }
    if (item.charInfo.subjobName) {
      $("#subjobDisplay").html(item.charInfo.subjobName);
      $("#subjobGradeDisplay").html("");
    }
    if (item.charInfo.subjobGradeName) {
      $("#subjobGradeDisplay").html(item.charInfo.subjobGradeName);
    }
    if ('bank' in item.charInfo) {
      $("#bankDisplay").html("Bank: $" + formatNumber(Math.floor(item.charInfo.bank)));
  }
  
  }

  if (item.time) {
    $("#timeDisplay").html(item.time);
  }
  if (item.date) {
    $("#dateDisplay").html(item.date);
  }
});

var control = undefined;
var hotbarVisibility = 0;
// Hotbar onload functions
$(function () {
  hotbarVisibility = localStorage.getItem("hotbarVisibility") || 0;

  if (hotbarVisibility == 1 || hotbarVisibility == 2) {
    $("#hotbar").hide();
  }

  refreshHotbarVisibilityIcon();

  $(".hotbarslot").on("contextmenu", function (e) {
    e.preventDefault();

    var menu = $("#hotkeyBindMenu");

    menu.css("left", e.pageX);
    menu.css("top", e.pageY);
    menu.css("display", "block");

    control = $(this).attr("control");
  });

  $("#hotkeyBindMenu").on("mousedown", ".animItem", function (e) {
    $.post(
      "http://core_inventory/setHotbarAnim",
      JSON.stringify({
        control: control,
        cat: $(this).attr("animCat"),
        anim: $(this).html(),
      })
    );
  });

  $(".hotbarslot").droppable({
    drop: function (event, ui) {
      var inventoryItem = ui.draggable;

      if (inventoryItem.hasClass("hotbarItem")) {
        var oldSlot = inventoryItem.parent();

        $.post(
          "http://core_inventory/swapHotbar",
          JSON.stringify({
            control1: $(this).attr("control"),
            control2: oldSlot.attr("control"),
          })
        );
      } else {
        $.post(
          "http://core_inventory/setHotbarItem",
          JSON.stringify({
            control: $(this).attr("control"),
            index: inventoryItem.attr("index"),
          })
        );
      }
    },
  });

  $("#hotbarClearZone").droppable({
    drop: function (event, ui) {
      var hotbarItem = ui.draggable;

      if (hotbarItem.hasClass("hotbarItem")) {
        $.post(
          "http://core_inventory/clearHotbarSlot",
          JSON.stringify({
            control: hotbarItem.parent().attr("control"),
          })
        );
      }
    },
  });

  $(".hotbarItem").draggable({
    revert: true,
    revertDuration: 0,
    containment: "body",
    start: function (event, ui) {
      $("#hotbarClearZone").show();
    },

    stop: function (event, ui) {
      $(this).css("left", "");
      $(this).css("top", "");
      $("#hotbarClearZone").hide();
    },
  });

  $(".hotbarslot img").on("error", function (e) {
    $(this).hide();
    $(this).parent().find(".hotbarLabel").remove();
    $(this)
      .parent()
      .append($('<label class="hotbarLabel">' + $(this).parent().attr("label") + "</label>"));
  });

  $(".hotbarslot img").on("load", function (e) {
    $(this).show();
    $(this).parent().find(".hotbarLabel").remove();
  });

  $("#hotbar-visibility").on("click", function (e) {
    if (hotbarVisibility == 0) {
      hotbarVisibility = 1;
    } else if (hotbarVisibility == 1) {
      hotbarVisibility = 2;
    } else if (hotbarVisibility == 2) {
      hotbarVisibility = 0;
    }

    refreshHotbarVisibilityIcon();
    localStorage.setItem("hotbarVisibility", hotbarVisibility);
    // console.log("Hotbar Visibility: " + hotbarVisibility)
  });

  $(document).on("mouseup", function (e) {
    $("#hotkeyBindMenu").hide();
  });

  $(document).keydown(function (event) {
    shiftPressed = event.keyCode == 16;
  });
  $(document).keyup(function (event) {
    if (event.keyCode == 16) {
      shiftPressed = false;
    }
  });
});

function getInventoryFromSlot(slot) {
  return slot.closest(".inventory").attr("id");
}

function getInvItemFromSlot(slot) {
  var inventoryName = getInventoryFromSlot(slot);
  var inventory = inventoryItems[inventoryName];
  var item;

  if (inventory) {
    item = inventory[parseInt(slot.attr("index"))];
  }

  return item;
}

function refreshHotbarVisibilityIcon() {
  if (hotbarVisibility == 0) {
    $("#hotbar-visibility").attr("src", "nui://stream_graphics/data/inventory/icons/hotbar_visible.png");
  } else if (hotbarVisibility == 1) {
    $("#hotbar-visibility").attr("src", "nui://stream_graphics/data/inventory/icons/hotbar_semivisible.png");
  } else if (hotbarVisibility == 2) {
    $("#hotbar-visibility").attr("src", "nui://stream_graphics/data/inventory/icons/hotbar_invisible.png");
  }
}

// Inventory onload functions
$(function () {
  $(".jobDisplayIcon").on("error", function (e) {
    $(this).attr("src", "nui://stream_graphics/data/jobs/default.png");
  });

  $(".inventoryitem").draggable({
    revert: true,
    revertDuration: 0,
  });

  $("#dialogConfirm").on("click", function (e) {
    transferItem();
  });

  $("#dialogInput").on("change paste keyup", function (e) {
    var countNumber = parseInt($(this).val());
    if (countNumber || $(this).val() == "") {
      $("#dialogInput").css("border", "0px");
      $("#dialogExc").hide();
    } else {
      $("#dialogInput").css("border", "1px solid red");
      $("#dialogExc").show();
    }
  });

  $("#dialogInput").keypress(function (e) {
    if (e.which == 13) {
      transferItem();
    }
  });

  $("#dialogCancel").on("click", function (e) {
    $("#countDialog").hide();
  });

  $(".copy-inventory").on("click", function (e) {
    var inventory = $(this).closest(".inventory").attr("id");
    var inventContainer = "#inventory-container-left";

    if (inventory == "external-inventory") {
      inventContainer = "#inventory-container-right";
    }

    var inventArray = $(inventContainer + " .inventoryitem");
    var inventString = "";

    inventArray.each(function (key, val) {
      if ($(this).attr("count") > 0) {
        inventString += `${$(val).attr("label")}\t${$(this).attr("count")}\n`;
      }
    });

    console.log(inventString);
    copyToClipboard(inventString);
  });
});

function copyToClipboard(text) {
  var dummy = document.createElement("textarea");
  document.body.appendChild(dummy);
  dummy.value = text;
  dummy.select();
  document.execCommand("copy");
  document.body.removeChild(dummy);
}

function openDialogWindow(maxAmount) {
  $("#countDialog").show();
  $("#dialogInput").focus();
  $("#dialogInput").val("");
  // $("#dialogInput").attr("placeholder", maxAmount)
}

function transferItem() {
  // console.log(JSON.stringify(transfer))

  if (transfer.count > 1) {
    var count = $("#dialogInput").val();

    // console.log("Read dialog entry!")

    if (count != "") {
      var countNumber = parseInt(count);
      if (countNumber) {
        if (countNumber > 0) {
          transfer.count = countNumber;
        } else {
          // console.log("idiot tried to exploit")
          return;
        }
      } else {
        // console.log("idiot tried transferring an invalid number")
        return;
      }
    }
  } else {
    // console.log("Disregard dialog entry!")
  }

  // console.log(JSON.stringify(transfer))

  $.post("http://core_inventory/transferInvItem", JSON.stringify(transfer));
  $("#countDialog").hide();

  transfer = {};
}

// Item Menu onload functions
$(function () {
  $(document).on("mouseup", function (e) {
    $("#itemMenu").hide();
  });
});
