local deathSource = false

AddEventHandler("onPlayerKilled", function(killerSource)
	deathSource = killerSource
end)

AddEventHandler("onPlayerDied", function()
	deathSource = false
end)

function HandleSusDeath()
	-- %%% NOT WORKING, NEED TO ADJUST SO COP KILLS DONT TRIGGER SUS DEATHS %%%
	-- if deathSource and Player(deathSource).state.jobName == "police" then
	-- 	print("Skipped as cops killed the dog")
	-- 	return
	-- end
	SendDistressSignal()
end

local susReportingZones = {
	{
		coords = {
			vector2(-2262.79, -3247.41), -- airport
			vector2(-1575.77, -2047.41), -- v beach
			vector2(-3226.77, 105.41), -- chumash
			vector2(-3392.77, 1504.41), -- north chumash
			vector2(-2708.77, 447.41), -- near kortz
			vector2(-391.77, 1278.41), -- observatory
			vector2(1375.77, 323.41), -- casino
			vector2(1480.77, -628.41), -- mirror park
			vector2(1310.77, -3361.41), -- docks
			vector2(-997.79, -3690.41), -- airport
		},
		details = {
			label = "City",
			name = "susdeath_city",
			minZ = 0.0,
			maxZ = 500.0,
			debugGrid = false,
			gridDivisions = 25
		}
	},
	{
		coords = {
			vector2(1705.79, 3201.41), -- flywheels
			vector2(1191.79, 3672.41), -- near marina
			vector2(1903.79, 4063.41), -- off from the slow 90 degree turn in ssandy
			vector2(2169.79, 3716.41), -- railway near alien stones
			vector2(1811.79, 3511.41), -- railway near joshua and panorama intersection
			vector2(1870.79, 3243.41), -- flywheels near pub
		},
		details = {
			label = "Sandy",
			name = "susdeath_sandy",
			minZ = 0.0,
			maxZ = 500.0,
			debugGrid = false,
			gridDivisions = 25
		}
	},
	{
		coords = {
			vector2(577.79, 6611.41), -- north highway
			vector2(158.79, 6856.41), -- north beach
			vector2(-870.79, 6185.41), -- south beach
			vector2(-397.79, 5813.41), -- south railway
		},
		details = {
			label = "Paleto",
			name = "susdeath_paleto",
			minZ = 0.0,
			maxZ = 500.0,
			debugGrid = false,
			gridDivisions = 25
		}
	},
}

CreateThread(function()
	for i, zone in ipairs(susReportingZones) do
		local polyzone = PolyZone:Create(zone.coords, zone.details)

		if polyzone then
			polyzone:onPlayerInOut(function(inside)
				inReportingZone = inside
			end)
		end
	end
end)

function SendDistressSignal()
    local skip = true
	local coords = GetEntityCoords(PlayerPedId())
    if not LocalPlayer.state.disarmed then
        skip = false
    end


    if not inReportingZone then
        skip = true
    end

    if not skip then
        TriggerServerEvent("main_dispatch:StartCall", 'susdeath_' .. GetPlayerServerId(PlayerId()), 'police', "Homicide", 'Suspected homicide; victim found unresponsive with signs of foul play.',
            {
                location = exports["core_hud"]:getPlayerLocation(),
                code = "10-35"
            },
            {
                caller = PlayerId(),
                theme = "error",
                gps = { x = coords.x, y = coords.y, z = coords.z },
                blip = {
                    label = "Homicide",
                    pos = { x = coords.x, y = coords.y, z = coords.z },
					sprite = 310,
                },
                inactivityTimeout = 8 * 60,
            }
        )
    end
end