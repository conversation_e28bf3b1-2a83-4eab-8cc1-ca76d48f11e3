local coffeeEffect = TimedStatusEffect(StatusEffect("caffeinated", "Caffeinated",
    {
        onStart = function(effect)
            AddPlayerSpeedBuff("caffeinated", 0.1)
        end,

        onTick = function(effect)
        end,

        onStop = function(effect)
            RemoveSpeedBuff("caffeinated")
        end,
    },
    {
        alert = false,
        desc = "Your feeling quick after your coffee!",
        subdesc = "+10% Movement Speed"
    }
), { maxTime = 30 * 60 })

RegisterNetEvent("drinkCoffee", function(itemName)
    ClearPedTasks(PlayerPedId())
    Wait(100)
    exports.main_roleplay:startDatabaseAnim("coffee")
    exports.main_progressbar:start(15000, "Drinking Coffee")
    Wait(15000)
    if not IsEntityPlayingAnim(PlayerPedId(), "amb@world_human_drinking@coffee@male@idle_a", "idle_c", 3) then
        Base.Notification("You didn't finish your coffee")
        return
    end

    Base.Notification("You finished your coffee")
    coffeeEffect.addTime(3600) -- 30 minutes
    TriggerServerEvent("consumeCoffee", itemName)
end)
