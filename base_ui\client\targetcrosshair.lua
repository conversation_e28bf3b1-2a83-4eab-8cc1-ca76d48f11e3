local hit, crosshairActive = false, false
local entity, entityType = false, 0
local inventoryOpen = false
focussedEntity = 0

AddEventHandler("onInventoryOpen", function()
	inventoryOpen = true
end)

AddEventHandler("onInventoryClose", function()
	inventoryOpen = false
end)

-- Registered Interactions
local registeredOptions = { player = {}, vehicle = {}, object = {} }

function registerInteraction(typeId, object, onInteract, condition)
	if (type(object) == "table") then
		registeredOptions[typeId][object] = { onInteract = onInteract, condition = condition }
	else
		registeredOptions[typeId][tostring(object)] = { onInteract = onInteract, condition = condition }
	end
end

function unregisterInteraction(type, object)
	registeredOptions[type][tostring(object)] = nil
end

-- Checks to see if the selected entity is a valid target
function isPedInteractable(entity)
	return IsPedAPlayer(entity)
end

function isVehInteractable(entity)
	return true
end

function isObjInteractable(entity)
	local model = tostring(GetEntityModel(entity))
	local interaction = registeredOptions.object[model]

	if interaction then
		if interaction.condition and interaction.condition() then
			return interaction
		else
			return interaction
		end
	else
		return false
	end
end

function isPlayerAlive(entity)
	return (GetEntityHealth(entity) > 0.0) and (GetEntityHealth(entity) < 500.0)
end

-- Draw Crosshair when applicable, and remember the Entity
-- Citizen.CreateThread(function()
-- 	while true do
-- 		focussedEntity = 0
-- 		-- if not radialMenuOpen and not inventoryOpen and not isCuffed and not isDead and not IsPlayerFreeAiming( PlayerId() ) then
-- 		if not inventoryOpen and not isCuffed and not isDead and not IsPlayerFreeAiming(PlayerId()) then
-- 			hit, entity = GetEntityFromCamera()

-- 			if (hit and DoesEntityExist(entity)) then
-- 				entityType = GetEntityType(entity)
-- 				if (entityType == 2) and isVehInteractable(entity) then
-- 					sendQUIEvent("toggleCrosshair", true)
-- 					crosshairActive = true
-- 					focussedEntity = entity
-- 				elseif (entityType == 1) and (isPedInteractable(entity)) then
-- 					sendQUIEvent("toggleCrosshair", true)
-- 					crosshairActive = true
-- 					focussedEntity = entity
-- 				elseif (entityType == 3) and isObjInteractable(entity) then
-- 					sendQUIEvent("toggleCrosshair", true)
-- 					crosshairActive = true
-- 					focussedEntity = entity
-- 				elseif crosshairActive then
-- 					sendQUIEvent("toggleCrosshair", false)
-- 					entity = false
-- 					crosshairActive = false
-- 				end
-- 			else
-- 				sendQUIEvent("toggleCrosshair", false)
-- 				entity = false
-- 				Wait(150)
-- 			end
-- 		elseif crosshairActive then
-- 			sendQUIEvent("toggleCrosshair", false)
-- 			entity = false
-- 			crosshairActive = false
-- 		end

-- 		Wait(100)
-- 	end
-- end)

------------------------------------------------------------------
--                     Target System
------------------------------------------------------------------

-- Get entity in front of player
local function RotationToDirection(rotation)
	local adjustedRotation =
	{
		x = (math.pi / 180) * rotation.x,
		y = (math.pi / 180) * rotation.y,
		z = (math.pi / 180) * rotation.z
	}
	local direction =
	{
		x = -math.sin(adjustedRotation.z) * math.abs(math.cos(adjustedRotation.x)),
		y = math.cos(adjustedRotation.z) * math.abs(math.cos(adjustedRotation.x)),
		z = math.sin(adjustedRotation.x)
	}
	return direction
end

-- Camera's coords
local function GetCoordsFromCam(distance)
	local cameraRotation = GetGameplayCamRot()
	local cameraCoord = GetGameplayCamCoord()
	local direction = RotationToDirection(cameraRotation)
	local destination = {
		x = cameraCoord.x + direction.x * distance,
		y = cameraCoord.y + direction.y * distance,
		z = cameraCoord.z + direction.z * distance
	}
	return vector3(destination.x, destination.y, destination.z)
end

local function GetAddedCamDistance()
	local camZoom = GetFollowPedCamViewMode()
	if camZoom == 0 then
		return 4.0
	elseif camZoom == 1 then
		return 6.5
	elseif camZoom == 2 then
		return 7.5
	elseif camZoom == 4 then
		return 2.0
	end
	return 0.0
end

-- Get entity's ID and coords from where player sis targeting
function GetEntityFromCamera()
	local camCoords = GetFinalRenderedCamCoord()
	local farCoords = GetCoordsFromCam(GetAddedCamDistance())
	local handle = StartShapeTestLosProbe(camCoords.x, camCoords.y, camCoords.z, farCoords.x, farCoords.y, farCoords.z, 30, PlayerPedId(), 0)
	while (true) do
		Wait(0)
		local result, hit, endCoords, surfaceNormal, entityHit = GetShapeTestResult(handle)
		if (result ~= 1) then
			return hit, entityHit
		end
	end
end
