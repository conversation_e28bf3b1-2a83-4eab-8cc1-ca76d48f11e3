local lastVehicle = nil
local lastSpeed = 0.0
local CRASH_SPEED = 150.0 / 3.6
local nextTickTime = GetGameTimer()

function startVehicleFire(veh)
    --add a statebag so the car could not be looted
    Entity(veh).state.MVA = 'true'

    local coords = GetEntityCoords(veh)
    local netId = NetworkGetNetworkIdFromEntity(veh)

    TriggerServerEvent(
        "main_dispatch:StartCall",
        "vehfire_" .. netId,
        "lses",
        "VEHICLE FIRE",
        "Fire reported following MVA",
        {
            direction = exports["core_hud"]:getPlayerDirection(),
            location = exports["core_hud"]:getPlayerLocation(),
            code = "10-57"
        },
        {
            caller = PlayerId(),
            theme = "error",
            gps = { x = coords.x, y = coords.y, z = coords.z },
            blip = {
                label = "10-57 : Vehicle Fire",
                pos = { x = coords.x, y = coords.y, z = coords.z },
                color = 1
            }
        }
    )

    TriggerServerEvent(
        "main_dispatch:StartCall",
        "vehfire_" .. netId,
        "police",
        "VEHICLE FIRE",
        "Fire reported following MVA",
        {
            direction = exports["core_hud"]:getPlayerDirection(),
            location = exports["core_hud"]:getPlayerLocation(),
            code = "10-57"
        },
        {
            caller = PlayerId(),
            theme = "error",
            gps = { x = coords.x, y = coords.y, z = coords.z },
            blip = {
                label = "10-57 : Vehicle Fire",
                pos = { x = coords.x, y = coords.y, z = coords.z },
                color = 1,
                sprite = 380
            }
        }
    )

    TriggerEvent("fire:startVehicleFire", veh, true)
end

AddEventHandler("vehicleDriverTick", function(veh)
    local veh = GetVehiclePedIsIn(PlayerPedId(), false)

    -- Delay ticks
    if (nextTickTime > GetGameTimer()) then
        return
    end
    nextTickTime = GetGameTimer() + 200

    -- Ensure vehicle exists
    if not DoesEntityExist(veh) then
        lastVehicle = nil
        lastSpeed = 0.0
        return
    end

    -- Reset the last speed if the vehicle has changed
    if lastVehicle ~= veh then
        lastVehicle = veh
        lastSpeed = 0.0
        return
    end

    -- Detect players under the map
    local coords = GetEntityCoords(veh)
    if coords and coords.z < 0.0 then
        lastSpeed = 0.0
        return
    end

    -- Get the vehicles speed
    local speed = GetEntitySpeed(veh)

    --
    if lastSpeed then
        local speedChange = lastSpeed - speed
        if (speedChange > CRASH_SPEED) then
            -- Random chance of starting a vehicle fire
            local chance = math.random(1, 4)
            if chance == 1 then
                startVehicleFire(veh)
            end
        end
    end

    -- Save the last reported speed
    lastSpeed = speed
end
)

AddEventHandler("vehicleExit", function()
    lastSpeed = 0.0
    nextTickTime = GetGameTimer() + 500
end)
