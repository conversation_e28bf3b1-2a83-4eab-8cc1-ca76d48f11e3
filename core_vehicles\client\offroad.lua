cfg = {
	deformationMultiplier = -1,      -- How much should the vehicle visually deform from a collision. Range 0.0 to 10.0 Where 0.0 is no deformation and 10.0 is 10x deformation. -1 = Don't touch. Visual damage does not sync well to other players.
	deformationExponent = 1.0,       -- How much should the handling file deformation setting be compressed toward 1.0. (Make cars more similar). A value of 1=no change. Lower values will compress more, values above 1 it will expand. Dont set to zero or negative.
	collisionDamageExponent = 1.0,   -- How much should the handling file deformation setting be compressed toward 1.0. (Make cars more similar). A value of 1=no change. Lower values will compress more, values above 1 it will expand. Dont set to zero or negative.
	damageFactorEngine = 1.5,        -- Sane values are 1 to 100. Higher values means more damage to vehicle. A good starting point is 10
	damageFactorBody = 5.0,          -- Sane values are 1 to 100. Higher values means more damage to vehicle. A good starting point is 10
	damageFactorPetrolTank = 5.0,    -- Sane values are 1 to 200. Higher values means more damage to vehicle. A good starting point is 64
	engineDamageExponent = 1.0,      -- How much should the handling file engine damage setting be compressed toward 1.0. (Make cars more similar). A value of 1=no change. Lower values will compress more, values above 1 it will expand. Dont set to zero or negative.
	weaponsDamageMultiplier = -1,    -- How much damage should the vehicle get from weapons fire. Range 0.0 to 10.0, where 0.0 is no damage and 10.0 is 10x damage. -1 = don't touch
	degradingHealthSpeedFactor = 0.0, -- Speed of slowly degrading health, but not failure. Value of 10 means that it will take about 0.25 second per health point, so degradation from 800 to 305 will take about 2 minutes of clean driving. Higher values means faster degradation
	cascadingFailureSpeedFactor = 8.0, -- Sane values are 1 to 100. When vehicle health drops below a certain point, cascading failure sets in, and the health drops rapidly until the vehicle dies. Higher values means faster failure. A good starting point is 8
	degradingFailureThreshold = 350.0, -- Below this value, slow health degradation will set in
	cascadingFailureThreshold = 150.0, -- Below this value, health cascading failure will set in
	engineSafeGuard = 50.0,          -- Final failure value. Set it too high, and the vehicle won't smoke when disabled. Set too low, and the car will catch fire from a single bullet to the engine. At health 100 a typical car can take 3-4 bullets to the engine before catching fire.
	torqueMultiplierEnabled = true,  -- Decrease engine torque as engine gets more and more damaged
	limpMode = false,                -- If true, the engine never fails completely, so you will always be able to get to a mechanic unless you flip your vehicle and preventVehicleFlip is set to true
	limpModeMultiplier = 0.15,       -- The torque multiplier to use when vehicle is limping. Sane values are 0.05 to 0.25
	preventVehicleFlip = true,       -- If true, you can't turn over an upside down vehicle
	sundayDriver = true,             -- If true, the accelerator response is scaled to enable easy slow driving. Will not prevent full throttle. Does not work with binary accelerators like a keyboard. Set to false to disable. The included stop-without-reversing and brake-light-hold feature does also work for keyboards.
	sundayDriverAcceleratorCurve = 7.5, -- The response curve to apply to the accelerator. Range 0.0 to 10.0. Higher values enables easier slow driving, meaning more pressure on the throttle is required to accelerate forward. Does nothing for keyboard drivers
	sundayDriverBrakeCurve = 5.0,    -- The response curve to apply to the Brake. Range 0.0 to 10.0. Higher values enables easier braking, meaning more pressure on the throttle is required to brake hard. Does nothing for keyboard drivers
	displayBlips = true,             -- Show blips for mechanics locations
	compatibilityMode = false,       -- prevents other scripts from modifying the fuel tank health to avoid random engine failure with BVA 2.01 (Downside is it disabled explosion prevention)
	randomTireBurstInterval = 0,     -- Number of minutes (statistically, not precisely) to drive above 22 mph before you get a tire puncture. 0=feature is disabled
	debug = {
		roadType = false
	},
	-- Class Damagefactor Multiplier
	-- The damageFactor for engine, body and Petroltank will be multiplied by this value, depending on vehicle class
	-- Use it to increase or decrease damage for each class

	classDamageMultiplier = {
		[0] = 1.0, --	0: Compacts
		1.0, --	1: Sedans
		1.0, --	2: SUVs
		1.0, --	3: Coupes
		1.0, --	4: Muscle
		1.0, --	5: Sports Classics
		1.0, --	6: Sports
		1.0, --	7: Super
		0.25, --	8: Motorcycles
		0.7, --	9: Off-road
		0.25, --	10: Industrial
		1.0, --	11: Utility
		1.0, --	12: Vans
		1.0, --	13: Cycles
		0.5, --	14: Boats
		1.0, --	15: Helicopters
		1.0, --	16: Planes
		1.0, --	17: Service
		0.75, --	18: Emergency
		0.75, --	19: Military
		1.0, --	20: Commercial
		1.0  --	21: Trains
	}
}

local vehicleClass
local fCollisionDamageMult = 0.0
local fDeformationDamageMult = 0.0
local fEngineDamageMult = 0.0

local healthEngineLast = 1000.0
local healthEngineCurrent = 1000.0
local healthEngineNew = 1000.0
local healthEngineDelta = 0.0
local healthEngineDeltaScaled = 0.0

local healthBodyLast = 1000.0
local healthBodyCurrent = 1000.0
local healthBodyNew = 1000.0
local healthBodyDelta = 0.0
local healthBodyDeltaScaled = 0.0

local healthPetrolTankLast = 1000.0
local healthPetrolTankCurrent = 1000.0
local healthPetrolTankNew = 1000.0
local healthPetrolTankDelta = 0.0
local healthPetrolTankDeltaScaled = 0.0


-- Export function to set the current health of the engine
function setEngineHealth(vehicle, health)
	healthEngineLast = health
	healthEngineCurrent = health
	healthEngineNew = health
	SetVehicleEngineHealth(vehicle, health)
end

exports("setEngineHealth", setEngineHealth)

-- Damage Multiplier

-- Add the following function into the driverTicks table.
-- This function will be called every frame that the player is in the driver seats (up to 500ms delay)
AddEventHandler("vehicleDriverTick", function(vehicle)
	-- Check vehicle class
	vehicleClass = GetVehicleClass(vehicle)
	if vehicleClass == 15 or vehicleClass == 16 or vehicleClass == 21 or vehicleClass == 13 then
		return true
	end

	-- Check engine health
	healthEngineCurrent = GetVehicleEngineHealth(vehicle)
	if healthEngineCurrent == 1000 then healthEngineLast = 1000.0 end
	if healthEngineCurrent > healthEngineLast then healthEngineLast = healthEngineCurrent end
	healthEngineNew = healthEngineCurrent
	healthEngineDelta = healthEngineLast - healthEngineCurrent
	healthEngineDeltaScaled = healthEngineDelta * cfg.damageFactorEngine * cfg.classDamageMultiplier[vehicleClass]

	healthBodyCurrent = GetVehicleBodyHealth(vehicle)
	if healthBodyCurrent == 1000 then healthBodyLast = 1000.0 end
	healthBodyNew = healthBodyCurrent
	healthBodyDelta = healthBodyLast - healthBodyCurrent
	healthBodyDeltaScaled = healthBodyDelta * cfg.damageFactorBody * cfg.classDamageMultiplier[vehicleClass]

	healthPetrolTankCurrent = GetVehiclePetrolTankHealth(vehicle)
	if healthPetrolTankCurrent == 1000 then healthPetrolTankLast = 1000.0 end
	healthPetrolTankNew = healthPetrolTankCurrent
	healthPetrolTankDelta = healthPetrolTankLast - healthPetrolTankCurrent
	healthPetrolTankDeltaScaled = healthPetrolTankDelta * cfg.damageFactorPetrolTank * cfg.classDamageMultiplier[vehicleClass]

	if healthEngineCurrent > cfg.engineSafeGuard + 1 and Entity(vehicle).state.fuelAmount and Entity(vehicle).state.fuelAmount > 1 then
		SetVehicleUndriveable(vehicle, false)
	else
		SetVehicleUndriveable(vehicle, true)
	end

	-- Only do calculations if any damage is present on the car. Prevents weird behavior when fixing using trainer or other script
	if healthEngineCurrent ~= 1000.0 or healthBodyCurrent ~= 1000.0 or healthPetrolTankCurrent ~= 1000.0 then
		-- Combine the delta values (Get the largest of the three)
		local healthEngineCombinedDelta = math.max(healthEngineDeltaScaled, healthBodyDeltaScaled, healthPetrolTankDeltaScaled)

		-- If huge damage, scale back a bit
		if healthEngineCombinedDelta > (healthEngineCurrent - cfg.engineSafeGuard) then
			healthEngineCombinedDelta = healthEngineCombinedDelta * 0.7
		end

		-- If complete damage, but not catastrophic (ie. explosion territory) pull back a bit, to give a couple of seconds og engine runtime before dying
		if healthEngineCombinedDelta > healthEngineCurrent then
			healthEngineCombinedDelta = healthEngineCurrent - (cfg.cascadingFailureThreshold / 5)
		end


		------- Calculate new value

		healthEngineNew = healthEngineLast - healthEngineCombinedDelta


		------- Sanity Check on new values and further manipulations

		-- If somewhat damaged, slowly degrade until slightly before cascading failure sets in, then stop

		if healthEngineNew > (cfg.cascadingFailureThreshold + 5) and healthEngineNew < cfg.degradingFailureThreshold then
			healthEngineNew = healthEngineNew - (0.038 * cfg.degradingHealthSpeedFactor)
		end

		-- If Damage is near catastrophic, cascade the failure
		if healthEngineNew < cfg.cascadingFailureThreshold then
			healthEngineNew = healthEngineNew - (0.1 * cfg.cascadingFailureSpeedFactor)
		end

		-- Prevent Engine going to or below zero. Ensures you can reenter a damaged car.
		if healthEngineNew < cfg.engineSafeGuard then
			healthEngineNew = cfg.engineSafeGuard
		end

		-- Prevent Explosions
		if cfg.compatibilityMode == false and healthPetrolTankCurrent < 750 then
			healthPetrolTankNew = 750.0
		end

		-- Prevent negative body damage.
		if healthBodyNew < 0 then
			healthBodyNew = 0.0
		end
	end

	-- set the actual new values
	if healthEngineNew ~= healthEngineCurrent then
		SetVehicleEngineHealth(vehicle, healthEngineNew)
	end
	if healthBodyNew ~= healthBodyCurrent then SetVehicleBodyHealth(vehicle, healthBodyNew) end
	if healthPetrolTankNew ~= healthPetrolTankCurrent then SetVehiclePetrolTankHealth(vehicle, healthPetrolTankNew) end

	-- Store current values, so we can calculate delta next time around
	healthEngineLast = healthEngineNew
	healthBodyLast = healthBodyNew
	healthPetrolTankLast = healthPetrolTankNew
	lastVehicle = vehicle

	-- Tire popping disabled here
	--if cfg.randomTireBurstInterval ~= 0 and GetEntitySpeed(vehicle) > 10 then tireBurstLottery() end
end)

AddEventHandler("vehicleEnter", function(vehicle)
	-- weapons don't damage vehicles
	local model = GetEntityModel(vehicle)

	if IsThisModelAHeli(model) then
		SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fWeaponDamageMult', 1.5)
	else
		SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fWeaponDamageMult', 0.15)
	end

	-- Just got in the vehicle. Damage can not be multiplied this round
	-- Set vehicle handling data
	fDeformationDamageMult = GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fDeformationDamageMult')
	local newFDeformationDamageMult = fDeformationDamageMult ^ cfg.deformationExponent                                                                                          -- Pull the handling file value closer to 1
	if cfg.deformationMultiplier ~= -1 then SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fDeformationDamageMult', newFDeformationDamageMult * cfg.deformationMultiplier) end -- Multiply by our factor

	--Get the CollisionDamageMultiplier
	fCollisionDamageMult = GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fCollisionDamageMult')
	--Modify it by pulling all number a towards 1.0
	local newFCollisionDamageMultiplier = fCollisionDamageMult ^ cfg.collisionDamageExponent -- Pull the handling file value closer to 1
	SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fCollisionDamageMult', newFCollisionDamageMultiplier)

	--Get the EngineDamageMultiplier
	fEngineDamageMult = GetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fEngineDamageMult')
	--Modify it by pulling all number a towards 1.0
	local newFEngineDamageMult = fEngineDamageMult ^ cfg.engineDamageExponent -- Pull the handling file value closer to 1
	SetVehicleHandlingFloat(vehicle, 'CHandlingData', 'fEngineDamageMult', newFEngineDamageMult)

	-- If body damage catastrophic, reset somewhat so we can get new damage to multiply
	if healthBodyCurrent < cfg.cascadingFailureThreshold then
		healthBodyNew = cfg.cascadingFailureThreshold
	end

	-- Reset Values
	healthEngineLast = GetVehicleEngineHealth(vehicle)
	healthBodyLast = GetVehicleBodyHealth(vehicle)
	healthPetrolTankLast = GetVehiclePetrolTankHealth(vehicle)
end)

AddEventHandler("vehicleExit", function(lastVehicle)
  if DoesEntityExist(lastVehicle) then
    SetVehicleHandlingFloat(lastVehicle, 'CHandlingData', 'fCollisionDamageMult', fCollisionDamageMult) -- Restore the original CollisionDamageMultiplier
    SetVehicleHandlingFloat(lastVehicle, 'CHandlingData', 'fEngineDamageMult', fEngineDamageMult)    -- Restore the original EngineDamageMultiplier
  end
end)



----------------------------------------------
local floats = {}
local maxFloats = {}
local offRoad = {}
local terrains = {
	['road'] = {
		282940568,
		-**********,
		**********,
		**********,
		-**********,
		-**********,
		-754997699,
		**********,
		-399872228,
	},
	['dirt'] = {
		-**********,
		-**********,
		509508168,
		510490462,
		-**********
	}
}

local dbOffroadVehicles = {}

-- Handle the synced data for offroad_vehicles
RegisterDataSyncHandler("offroad_vehicles", function(result)
	dbOffroadVehicles = {}
	for i, v in ipairs(result) do
		dbOffroadVehicles[v.modelHash] = true
	end
end)

function IsVehicleOffroadCapable(veh)
	local model = GetEntityModel(veh)
	return GetVehicleClass(veh) == 9 or dbOffroadVehicles[model] or false
end

function GetEmergencyVehicleClass(veh)
	local class = GetVehicleClass(veh)
	if (class ~= 18) then
		return -1
	end
	for i = 1, #offRoad do
		if GetEntityModel(veh) == offRoad[i].modelHash then
			return 9
		end
	end
	return -1
end

function GetGroundHash(veh)
	local coords = GetEntityCoords(veh)
	local num = StartShapeTestCapsule(coords.x, coords.y, coords.z + 4, coords.x, coords.y, coords.z - 2.0, 1, 1, veh, 7)
	local arg1, arg2, arg3, arg4, arg5 = GetShapeTestResultEx(num)
	return arg5
end

function GetGroundId(veh)
	local hash = GetGroundHash(veh)

	for k, v in pairs(terrains['road']) do
		if (hash == v) then
			return 1
		end
	end

	for k, v in pairs(terrains['dirt']) do
		if (hash == v) then
			return 2
		end
	end

	return -1
end

function isOffroad(veh)
	local id = GetGroundId(veh)
	if (id == 1) then
		return false
	end
	return true
end

local whitelist_models_tilt = {
	[GetHashKey("deluxo")] = true,
	[GetHashKey("ruiner2")] = true,
	[GetHashKey("oppressor")] = true,
	[GetHashKey("oppressor2")] = true,
}


-- Add the following function into the driverTicks table.
-- This function will be called every frame that the player is in the driver seats (up to 500ms delay)
AddEventHandler("vehicleDriverTick", function(veh)
	local model = GetEntityModel(veh)

	-- Control
	if (not whitelist_models_tilt[model]) then
		local roll = GetEntityRoll(veh)

		if (roll > 75.0 or roll < -75.0) and GetEntitySpeed(veh) < 2 then
			DisableControlAction(2, 59, true) -- Disable left/right
			DisableControlAction(2, 60, true) -- Disable up/down
		end

		-- If it's not a boat, plane or helicopter, and the vehilce is off the ground with ALL wheels, then block steering/leaning left/right/up/down.
		if not IsThisModelABoat(model) and not IsThisModelAHeli(model) and not IsThisModelAPlane(model) and not IsThisModelABike(model) and IsEntityInAir(veh) then
			DisableControlAction(0, 59) -- leaning left/right
			DisableControlAction(0, 60) -- leaning up/down
		end
	end

	local terrainId = GetGroundId(veh)

	-- If terrain is road, reset original handling
	if (terrainId == 1) then
		if (floats[veh]) then
			SetVehicleHandlingFloat(veh, 'CHandlingData', 'fTractionCurveMin', floats[veh])
			SetVehicleHandlingFloat(veh, 'CHandlingData', 'fTractionCurveMax', maxFloats[veh])
			floats[veh] = nil
			maxFloats[veh] = nil
		end
		return true
	end

	-- Check if the vehicle is offroad capable
	local offroadVehicle = (GetVehicleClass(veh) == 9) or dbOffroadVehicles[model] or false
	local off_wheel = (GetVehicleWheelType(veh) == 4)

	if offroadVehicle or (terrainId == 2 and off_wheel) then
		return true
	end

	-- Save original handling
	if (floats[veh] == nil) then
		floats[veh] = GetVehicleHandlingFloat(veh, 'CHandlingData', 'fTractionCurveMin')
		maxFloats[veh] = GetVehicleHandlingFloat(veh, 'CHandlingData', 'fTractionCurveMax')
	end

	local pitch = GetEntityPitch(veh)
	local set = floats[veh]

	-- print("Vehicle is off road, but not offroad!", pitch)

	-- If the car is accelerating, calculate traction
	if (GetVehicleCurrentRpm(veh) > 0.22) then
		-- Dirt Road
		if (terrainId == 2) then
			if (math.abs(pitch) > 15.0) then
				set = floats[veh] - math.abs(math.floor(pitch)) / 50
			end

			-- Full Offroad Terrain
		else
			if (math.abs(pitch) > 30.0) then
				set = floats[veh] - math.abs(math.floor(pitch)) / 5
			elseif (math.abs(pitch) > 20.0) then
				set = floats[veh] - math.abs(math.floor(pitch)) / 9
			elseif (math.abs(pitch) > 15.0) then
				set = floats[veh] - math.abs(math.floor(pitch)) / 14
			elseif (math.abs(pitch) > 12.0) then
				set = floats[veh] - math.abs(math.floor(pitch)) / 19
			elseif (math.abs(pitch) > 8.0) then
				set = floats[veh] - math.abs(math.floor(pitch)) / 24
			elseif (pitch > 0.0) then
				set = floats[veh] - math.abs(math.floor(pitch)) / 28
			else
				set = floats[veh] - math.abs(math.floor(pitch)) / 35
			end
		end
	end

	-- print("Setting handling to ", math.max(set, 0.0), "from", floats[veh])

	SetVehicleHandlingFloat(veh, 'CHandlingData', 'fTractionCurveMin', math.max(set, 0.0))
	SetVehicleHandlingFloat(veh, 'CHandlingData', 'fTractionCurveMax', math.max(set + 0.25, 0.25))
end)

AddEventHandler("vehicleExit", function(lastVehicle)
	if (floats[lastVehicle]) then
		SetVehicleHandlingFloat(lastVehicle, 'CHandlingData', 'fTractionCurveMin', floats[lastVehicle])
		SetVehicleHandlingFloat(lastVehicle, 'CHandlingData', 'fTractionCurveMax', maxFloats[lastVehicle])
		floats[lastVehicle] = nil
		maxFloats[lastVehicle] = nil
		--print("[vehicles] RESET VEHICLE TRACTION")
	end
end)

RegisterCommand("groundhash", function()
	local veh = GetVehiclePedIsIn(PlayerPedId(), false)
	print(GetGroundHash(veh))
	print(GetGroundId(veh))
end, false)
