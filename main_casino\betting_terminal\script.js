var LOTTORATES = {}
var minBetAmount = 0;
var maxBetAmount = 999999999999;
var currentGame = 0;
var currentNumbersBet = [];
var currentSidesBet = "";
var nextStartTime;
var timer;
var duration;

// Populates the numbers grid in default unslected state.
function populateNumbers() {
    
    $("#numbers-grid-container").empty()

    for (i = 1; i <= 40; i++) {
        $("#numbers-grid-container").append('<div class="grid-element heads-unselected" id="ge-' + i + '">' + i + '</div>')
    }

    for (i = 41; i <= 80; i++) {
        $("#numbers-grid-container").append('<div class="grid-element tails-unselected" id="ge-' + i + '">' + i + '</div>')
    }

}

// Resets sides button states and bet state.
function resetSides() {
    $('.side-option').removeClass('selected')
    $('.splash-sides').html('Select a side to begin.')
    currentSidesBet = null
    slipState("sides", "deactivate")
    $('#bet-amount-sides').val('')
    $('#place-bet-sides').html("Confirm Bet")
    $('#place-bet-sides').removeClass("input-closed")
    $('#place-bet-sides').addClass("input-open")
}

// Resets numbers grid and bet state.
function resetNumbers() {
    $('.splash-numbers').html('Select numbers to begin. Max ten selections.')
    populateNumbers()
    currentNumbersBet = []
    slipState("numbers", "deactivate")
    $('#bet-amount-numbers').val('')
    $('#place-bet-numbers').html("Confirm Bet")
    $('#place-bet-numbers').removeClass("input-closed")
    $('#place-bet-numbers').addClass("input-open")
}

// Resets numbers and sides.
function resetAll() {
    updateHeader()
    resetNumbers()
    resetSides()
}

// Data validation for bet amount.
function checkBetAmount(amount, betType) {
    displayError(betType) // clear the error message first

    if (isNaN(amount)) {
        displayError(betType, "INVALID AMOUNT", "This does not look like a valid number.");
        return false

    } else if (amount != Math.floor(amount)) {
        displayError(betType, "INVALID AMOUNT", "Enter a whole number only. No decimal values.");
        return false

    } else if (amount < minBetAmount) {
        displayError(betType, "INVALID AMOUNT", "Amount below minimum ($" + minBetAmount + ")");
        return false

    } else if (amount > maxBetAmount) {
        displayError(betType, "INVALID AMOUNT", "Amount above maximum ($" + maxBetAmount + ")");
        return false

    } else if (amount == 0) {
        displayError(betType, "Enter Bet Amount", "You bet amount must be a whole number between " + minBetAmount + " and " + maxBetAmount + ".");
        return false

    } else {
        return amount

    }

}

// Displays error messages in the relevant bet slip.
function displayError(betType, title, msg) {

    // if no title is specified, find and delete the error message for the specified betType
    if (!title) {
        if (betType == "numbers") {
            $("#errors-numbers").find(".error-message").remove();
        } else {
            $("#errors-sides").find(".error-message").remove();
        }
        return;
    }

    // otherwise, create the error element
    var element = $('<div class="error-message"><label>' + title.toUpperCase() + '</label><span>' + msg + '</span></div>')

    if (betType == "numbers") {
        $("#errors-numbers").empty()
        $("#errors-numbers").append(element)

    } else if (betType == "sides") {
        $("#errors-sides").empty()
        $("#errors-sides").append(element)

    } else {
        console.log("Error displaying an error. Oh the irony!")
    }

}

// Updates the terminal header.
function updateHeader() {
    $('#now-playing').html(currentGame)
    $('#now-selling').html(currentGame + 1)
    $('#next-timer').html()

}

// Hides or shows the relevant bet slip.
function slipState(betType, state) {

    if (betType == "numbers") {

        if (state == "deactivate") {
            $('.bs-main-numbers').hide()
            $('.splash-numbers').show()

        } else if (state == "activate") {
            $('.bs-main-numbers').show()
            $('.splash-numbers').hide()

        }

    } else if (betType == "sides") {

        if (state == "deactivate") {
            $('.bs-main-sides').hide()
            $('.splash-sides').show()

        } else if (state == "activate") {
            $('.bs-main-sides').show()
            $('.splash-sides').hide()

        }
    }

}

// Updates the content of the numbers slip.
function updateNumbersSlip() {
    var currentNumbersBetSorted = currentNumbersBet.sort(function(a, b){return a-b})
    var inputAmount = checkBetAmount($("#bet-amount-numbers").val(), "numbers") 
    var amountOfNumbers = currentNumbersBet.length

    $('#bs-numbers').html(currentNumbersBetSorted.join(', '))
    $('#bs-amount-title').html('Amount of Numbers: ' + amountOfNumbers)

    if (inputAmount != false) {
        var potentialWinnings = LOTTORATES[String(amountOfNumbers)][String(amountOfNumbers)] * inputAmount
        $('.calc-potwin-numbers').html('$' + potentialWinnings)

    } else {
        $('.calc-potwin-numbers').html('-')

    }

}

// Updates the content of the sides slip.
function updateSidesSlip() {
    var inputAmount = checkBetAmount($("#bet-amount-sides").val(), "sides")
    var potentialWinnings = null

    $('#bs-sides').html(currentSidesBet.toUpperCase())

    // Clear the error message

    if (inputAmount != false) {

        if (currentSidesBet == "draw") {
            potentialWinnings = inputAmount * 4
        
        } else {
            potentialWinnings = inputAmount * 2

        }

        $('.calc-potwin-sides').html('$' + potentialWinnings)

    } else {
        $('.calc-potwin-sides').html('-')

    }
}

// Timer handler.
function handleTimer() {

    if (duration > 0) {

        var minutes = Math.floor((duration % (60 * 60) / 60))
        var seconds = Math.floor(duration % 60)

        if (seconds > 9) {
            $('#next-timer').html(minutes + ':' + seconds)
            timer = setTimeout(function() {
                duration--
                handleTimer()
            }, 1000)

        } else {
            $('#next-timer').html(minutes + ':0' + seconds)
            timer = setTimeout(function() {
                duration--
                handleTimer()
            }, 1000)

        }

        

    } else {
        $('#next-timer').html('0:00')
    }

}

// ON-LOAD DEFAULTS
populateNumbers()
resetAll()
slipState("sides", "deactivate")
slipState("numbers", "deactivate")
$("#viewport-container").hide();
duration = 40
handleTimer()

// ON-CLICK EVENTS

// Handles use of the numbers grid.
$('#numbers-grid-container').on("click", ".grid-element", function() {
    var number =  parseInt($(this).html())

    if ($(this).hasClass("heads-unselected") || $(this).hasClass("tails-unselected")) {

        if (currentNumbersBet.length >= 10) {
            return
    
        } else {
    
            if (number <= 40) {
                $(this).removeClass("heads-unselected")
                $(this).addClass("heads-selected")
                currentNumbersBet.push(number)
    
            } else {
                $(this).removeClass("tails-unselected")
                $(this).addClass("tails-selected")
                currentNumbersBet.push(number)
            }
    
        }

    } else if ($(this).hasClass("heads-selected")) {
        $(this).removeClass("heads-selected")
        $(this).addClass("heads-unselected")
        var index = currentNumbersBet.indexOf(number)
        if (index > -1) {
            currentNumbersBet.splice(index, 1)
        }


    } else if ($(this).hasClass("tails-selected")) {
        $(this).removeClass("tails-selected")
        $(this).addClass("tails-unselected")
        var index = currentNumbersBet.indexOf(number)
        if (index > -1) {
            currentNumbersBet.splice(index, 1)
        }

    }

    if (currentNumbersBet.length > 0) {
        updateNumbersSlip()
        slipState("numbers", "activate")
    } else if (currentNumbersBet.length == 0) {
        slipState("numbers", "deactivate")
    }
    
})

// Handles use of the clear button on the numbers slip.
$('#clear-bet-numbers').click(function() {
    resetNumbers()
})

// Handles use of the clear button on the sides slip.
$('#clear-bet-sides').click(function() {
    resetSides()
})

// Handles the selection of a side.
$('.side-option').click(function() {
    var side = $(this).find("label").html().toLowerCase()
    
    if ($(this).hasClass('selected')) {
        $('.side-option').removeClass('selected')
        currentSidesBet = null

    } else {
        $('.side-option').removeClass('selected')
        $(this).addClass('selected')
        currentSidesBet = side

    }

    if (currentSidesBet != null) {
        updateSidesSlip()
        slipState("sides", "activate")
    } else if (currentSidesBet == null) {
        slipState("sides", "deactivate")
    }

})

// Ensures bet slips update on change.
$("#bet-amount-sides").on("change", updateSidesSlip)
$("#bet-amount-sides").on("keyup", updateSidesSlip)
$("#bet-amount-numbers").on("change", updateNumbersSlip)
$("#bet-amount-numbers").on("keyup", updateNumbersSlip)

// Confirm bet button handlers
$("#place-bet-numbers").on("click", function() {
    
    if (checkBetAmount($("#bet-amount-numbers").val(), "numbers") != false) {

        $('#place-bet-numbers').addClass("input-closed")
        $('#place-bet-numbers').removeClass("input-open")
        $('#place-bet-numbers').html("Placing Bet...")

        $.post('http://main_casino/placeNumbersBet', JSON.stringify({
            numbers: currentNumbersBet,
            amount: $("#bet-amount-numbers").val()
        }));

    }

})

$("#place-bet-sides").on("click", function() {

    if (checkBetAmount($("#bet-amount-sides").val(), "sides") != false) {

        $('#place-bet-sides').addClass("input-closed")
        $('#place-bet-sides').removeClass("input-open")
        $('#place-bet-sides').html("Placing Bet...")

        $.post('http://main_casino/placeSidesBet', JSON.stringify({
            side: currentSidesBet,
            amount: $("#bet-amount-sides").val()
        }));

    }

})

// Closes the betting app
$("#close-terminal").on("click", function() {
    resetAll()
    $.post('http://main_casino/closeTerminal');
})

// Event listeners
window.addEventListener('message', function(event) {
    var item = event.data;

    if (item.LOTTORATES != null) {
        LOTTORATES = item.LOTTORATES
    }

    if (item.currentGame != null) {
        currentGame = item.currentGame
        updateHeader()
    }

    if (item.minBetAmount !=null) {
        minBetAmount = item.minBetAmount
    }

    if (item.maxBetAmount != null) {
        maxBetAmount = item.maxBetAmount
    }

    if (item.showTerminal != null) {
        if (item.showTerminal) {
            $("#viewport-container").show();
        } else {
            $("#viewport-container").hide();
        }
    }

    if (item.waitTime != null) {
        clearTimeout(timer)
        duration = item.waitTime
        handleTimer()
        
    }

    if (item.betType != null && item.msg != null) {
        displayError(item.betType)
        displayError(item.betType, "ERROR PLACING BET", item.msg)
        $('#place-bet-' + item.betType).removeClass("input-closed")
        $('#place-bet-' + item.betType).addClass("input-open")
        $('#place-bet-' + item.betType).html("Confirm Bet")

    }

    if (item.betSlip != null) {
        
        if (item.betSlip == "numbers") {

            resetNumbers()
            $('.splash-numbers').html('Your bet has been placed!<br><br>A confirmation text has been sent to your mobile. The result of your bet will be texted to you. Select numbers to place another bet and gamble responsibly.<br><br>Note, you cannot place multiple bets of the same type on the same game.')
            $('#place-bet-numbers').html("Confirm Bet")
            $('#place-bet-numbers').removeClass("input-closed")
            $('#place-bet-numbers').addClass("input-open")

        } else if (item.betSlip == "sides") {

            resetSides()
            $('.splash-sides').html('Your bet has been placed!<br><br>A confirmation text has been sent to your mobile. The result of your bet will be texted to you. Select a side to place another bet and gamble responsibly.<br><br>Note, you cannot place multiple bets of the same type on the same game.')
            $('#place-bet-sides').html("Confirm Bet")
            $('#place-bet-sides').removeClass("input-closed")
            $('#place-bet-sides').addClass("input-open")

        } else {
            console.log("An error occured changing bet slip state. Contact the developer, this is game-breaking.")
        }
        
    }



})