Base.Effects = {}

Base.Effects.registeredEffects = {}

-- main register
Base.Effects.register = function(id, max, time, start_m, finish_m, loop_m, multi_m)
    if (id and max) then
        Base.Effects.registeredEffects[id] = {
            looping = false,
            loopTime = 0,
            strength = 0,
            count = 0,
            time = time,
            max = max,
            start = start_m or false,
            finish = finish_m or false,
            multi = multi_m or false,
            loop_m = loop_m or false,
        }
        Base.Effects.registeredEffects[id].loop = function(_id)
            if (not Base.Effects.registeredEffects[_id].looping) then
                Base.Effects.registeredEffects[_id].looping = true
                while (Base.Effects.registeredEffects[_id].looping) do
                    Wait(0)
                    if (GetGameTimer() > Base.Effects.registeredEffects[_id].loopTime) then
                        Base.Effects.registeredEffects[_id].loopTime = GetGameTimer() +
                            Base.Effects.registeredEffects[_id].time

                        -- Run loop function
                        if (Base.Effects.registeredEffects[_id].loop_m) then
                            Base.Effects.registeredEffects[_id].loop_m(Base.Effects.registeredEffects[_id])
                        end

                        -- Increment counter
                        Base.Effects.registeredEffects[_id].count = Base.Effects.registeredEffects[_id].count + 1

                        -- Calculcate End
                        if (Base.Effects.registeredEffects[_id].count >= Base.Effects.registeredEffects[_id].max * Base.Effects.registeredEffects[_id].strength) then
                            Base.Effects.registeredEffects[_id].looping = false
                            Base.Effects.registeredEffects[_id].strength = 0
                            Base.Effects.registeredEffects[_id].count = 0
                            Base.Effects.registeredEffects[_id].finish(Base.Effects.registeredEffects[_id])
                        end
                    end
                end
            end
        end
    end
end

-- methods
Base.Effects.triggerEffect = function(id, time)
    local effect = Base.Effects.registeredEffects[id]
    -- start effect
    if (effect) then
        -- multi effect timer
        if (time) then
            if (effect.looping) then
                effect.time = (effect.time < time and time or effect.time)
            else
                effect.time = time
            end
        end
        -- start
        if (effect.strength == 0) then
            effect.strength = 1
            effect.startTime = GetGameTimer()
            if (effect.start) then
                effect.start(effect)
            end
            if (effect.loop) then
                Citizen.CreateThread(function()
                    effect.loop(id)
                end)
            end
        else
            effect.strength = effect.strength + 1
            if (effect.multi) then
                effect.multi(effect)
            end
        end
    end
end

Base.Effects.getActiveEffects = function()
    local active = {}
    for id, effect in pairs(Base.Effects.registeredEffects) do
        if (effect.strength > 0) then
            active[id] = effect.strength
        end
    end
    return active
end

Base.Effects.getEffectCurrentStrength = function(id)
    if (Base.Effects.registeredEffects[id]) then
        return Base.Effects.registeredEffects[id].strength
    end
    return nil
end

Base.Effects.isEffectMaxed = function(id)
    if (Base.Effects.registeredEffects[id]) then
        return Base.Effects.registeredEffects[id].strength >= Base.Effects.registeredEffects[id].max
    end
    return true
end

Base.Effects.stopEffect = function(id)
    Base.Effects.registeredEffects[id].looping = false
    Base.Effects.registeredEffects[id].strength = 0
    Base.Effects.registeredEffects[id].count = 0
    Base.Effects.registeredEffects[id].finish(Base.Effects.registeredEffects[id])
end

-- events
RegisterNetEvent("base_scripting:effect")
AddEventHandler("base_scripting:effect", Base.Effects.triggerEffect)
