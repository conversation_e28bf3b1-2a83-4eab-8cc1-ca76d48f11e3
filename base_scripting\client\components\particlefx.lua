local pfxList = {}

function startEffect(details)
    if not HasNamedPtfxAssetLoaded(details.lib) then
        RequestNamedPtfxAsset(details.lib)
        while not HasNamedPtfxAssetLoaded(details.lib) do
            Wait(1)
        end
    end
    
    UseParticleFxAssetNextCall(details.lib)

    if details.entity then
        if details.entityBone then
            return StartParticleFxLoopedOnEntityBone(details.effectName, details.entity, 
                details.coords.x, details.coords.y, details.coords.z, 
                details.rotation.x, details.rotation.y, details.rotation.z, 
                details.entityBone, details.scale, false, false, false
            )
        else
            return StartParticleFxLoopedOnEntity(details.effectName, details.entity, 
                details.coords.x, details.coords.y, details.coords.z, 
                details.rotation.x, details.rotation.y, details.rotation.z, 
                details.scale, false, false, false
            )
        end
    else
        return StartParticleFxLoopedAtCoord(details.effectName,
            details.coords.x, details.coords.y, details.coords.z,
            details.rotation.x, details.rotation.y, details.rotation.z,
            details.scale, false, false, false, false
        )
    end
end

function stopEffect(pftxHandle)
    StopParticleFxLooped(pftxHandle)
end

RegisterNetEvent("base:pfx:start")
AddEventHandler("base:pfx:start", function(id, details)
    pfxList[id] = startEffect(details)
end)

RegisterNetEvent("base:pfx:stop")
AddEventHandler("base:pfx:stop", function(id)
    stopEffect(pfxList[id])
end)

RegisterNetEvent("base:pfx:sync")
AddEventHandler("base:pfx:sync", function(particles)    
    for id, pfx in pairs(pfxList) do
        stopEffect(pfx)
    end

    particles = {}

    for id, details in pairs(particles) do
        pfxList[id] = startEffect(details)
    end
end)