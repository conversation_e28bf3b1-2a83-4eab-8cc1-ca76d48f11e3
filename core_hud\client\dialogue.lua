RegisterNUICallback("characterDialogueResponse", function(data, cb)
    TriggerEvent("charDialog:" .. data.event, data.ped, data.data)
    cb("ok")
end)

RegisterNUICallback("characterDialogueClose", function(data, cb)
    SetNuiFocus(false, false)
    Base.Camera.destroy("ped_dialogue_cam", 650)
    cb("ok")
end)

function openDialogue(options)
    SendNUIMessage(options)
    SetNuiFocus(true, true)
end

exports("openDialogue", openDialogue)
