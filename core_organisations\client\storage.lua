local activeStorage = nil

AddEventHandler("org:markerInteraction", function(type, marker)
    if type ~= "storage" then return end
    print("Open Storage", marker.organisation, marker.identifier, marker.data.storageID)
    activeStorage = { org = marker.organisation, storageID = marker.data.storageID }
    TriggerServerEvent("orgs:openStorage", marker.organisation, marker.data.storageID)
end)

RegisterInteraction("vehicle", "storage", { label = "Access Mobile Storage" }, function(vehicle)
    local vehState = Entity(vehicle).state

    if not vehState.job or not vehState.storage then
        return false
    end

    activeStorage = { org = vehState.job, storageID = vehState.storage }
    TriggerServerEvent("orgs:openStorage", vehState.job, vehState.storage)
    Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
end, function(vehicle)
    local vehState = Entity(vehicle).state
    local job = vehState.job

    if not job or not vehState.storage then
        return false
    end

    return DoesPlayerHaveOrgPermission("storage.full", job) or DoesPlayerHaveOrgPermission("storage.access", job)
end)

RegisterNetEvent("inventory:clientSync")
AddEventHandler('inventory:clientSync', function(name, inv, money, dirty, capacity)
    if name ~= "org_storage" then return end

    exports["core_inventory"]:loadExternalInventory("Storage", inv, money, dirty, capacity,
        function(itemIndex, count)
            TriggerServerEvent("orgs:addStorageItem", activeStorage, itemIndex, count)
        end,

        function(itemIndex, count)
            TriggerServerEvent("orgs:remStorageItem", activeStorage, itemIndex, count)
        end,

        function()
            TriggerServerEvent("orgs:closeStorage", activeStorage)
            activeStorage = nil
        end
    )
end)

-- Trash/Incinerator

AddEventHandler("org:markerInteraction", function(type, marker)
    if type ~= "incinerator" then return end
    print("Open Incinerator", marker.organisation, marker.identifier)
    TriggerServerEvent("orgs:openIncinerator")
end)

RegisterNetEvent("inventory:clientSync")
AddEventHandler('inventory:clientSync', function(name, inv, money, dirty, capacity)
    if name ~= "org_incinerator" then return end

    exports["core_inventory"]:loadExternalInventory("Incinerator", inv, money, dirty, capacity,
        function(itemIndex, count)
            TriggerServerEvent("orgs:addIncineratorItem", itemIndex, count)
        end,

        function(itemIndex, count)
            TriggerServerEvent("orgs:remIncineratorItem", itemIndex, count)
        end,

        function()
            TriggerServerEvent("orgs:closeIncinerator")
        end
    )
end)
