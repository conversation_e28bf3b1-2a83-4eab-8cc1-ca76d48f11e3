--[[
    Authors: <AUTHORS>
        @Pizza
]]

local DOOR_STATES = {
	[0] = { keyIndex = 46, status = "Unlocked", key = "E" },
	[1] = { keyIndex = 47, status = "Locked", key = "G" },
	[2] = { keyIndex = 47, status = "Locking", key = "G" },
}

local DOOR_DEBUG = false
local weapon = GetHashKey("weapon_breachshotgun")

-- variable for holding the registered doors
local registered_doors = {}

-- varaiable for holding the ids of the doors in the chunks
local registered_doors_chunks = {}

-- variable for holding the doors we want to draw text for
local close_doors = {}

-- variable for holding any blocked doors
local blocked_doors = {}

-- variable for is we are currently touching a door
local touching_door = false

RegisterNetEvent("main_roleplay_doors:debug")
AddEventHandler("main_roleplay_doors:debug", function()
	DOOR_DEBUG = not DOOR_DEBUG
end)

-- function for adding a new door into our system
--[[
	id : id of the door we are registering
	model : model of the doors object
	coords : world position of the door
]]
function registerNewDoor(id, model, coords, offset, dist, jobs, property, areaLabel, lockpickable, chainable, blowable, gangDoor, items, blockBreachShotgun)
	id = tostring(id)
	-- retun the gridChunk for the coordinates of the door
	local gridChunk = tostring(Base.Worldgrid.getCurrentChunk(coords))

	-- check if the doors gridChunk exists already, if it does not; then create a new table for doors within that gridChunk
	if (registered_doors_chunks[gridChunk] == nil) then
		registered_doors_chunks[gridChunk] = {}
	end

	-- add door to the table of door ids in the chunk
	table.insert(registered_doors_chunks[gridChunk], id)

	if items then
		items = json.decode(items)
	end

	-- add the door details to the registered doors
	registered_doors[id] = { id = id, areaLabel = areaLabel, coords = coords, model = model, offset = offset, dist = dist, jobs = jobs, property = property, lockpickable =
	lockpickable, chainable = chainable, blowable = blowable, gangDoor = gangDoor, items = items, blockBreachShotgun = blockBreachShotgun }

	-- create door hash
	local hash = GetHashKey("door_" .. id)

	-- register the door with gta 5 if it isn't already
	if (not IsDoorRegisteredWithSystem(hash)) then
		AddDoorToSystem(
			hash,
			model,
			coords.x,
			coords.y,
			coords.z,
			false,
			false,
			false
		)
	end
end

-- function for updating the state of a door
--[[
	id : id of the door we are updating
	state : new state of the door
	isLocal : whether it is networked
]]
function updateDoorState(id, state, isLocal)
	if (isLocal) then
		local hash = GetHashKey("door_" .. id)
		if (IsDoorRegisteredWithSystem(hash)) then
			DoorSystemSetDoorState(hash, state, false, false)
			-- DoorSystemSetSpringRemoved(hash, false, false, false)
			-- DoorSystemSetAutomaticRate(hash, 1.5, false, false)

			-- local data = registered_doors[ id ]
			-- if ( data ) then
			-- 	-- mrpd gates
			-- 	if ( data.model == -1635161509 or data.model == -1868050792 ) then
			-- 		DoorSystemSetHoldOpen(hash, true)
			-- 		DoorSystemSetAutomaticRate(hash, 2.0, false, false)
			-- 	end
			-- end
		end
	else
		TriggerServerEvent("updateDoorState", id, state, LocalPlayer.state.instanceID)
	end
end

-- function returns the state of a door
--[[
	id : id of the door to return the state of
]]
function getDoorState(id)
	local hash = GetHashKey("door_" .. id)
	if (IsDoorRegisteredWithSystem(hash)) then
		if not IsDoorClosed(hash) and DoorSystemGetDoorState(hash) == 1 then --If door is locked but still open
			return 2
		end
		return DoorSystemGetDoorState(hash)
	end
	return -1
end

-- function tells the script to display the text for a door
--[[
	chunk : chunk of the door
	id : id of the door to display the text for
]]
function displayDoor(id, toggle)
	if (toggle) then
		local door = registered_doors[id]
		if (close_doors[id] == nil) then
			local object = GetClosestObjectOfType(door.coords.x, door.coords.y, door.coords.z, 2.0, door.model, 0, 0, 0)
			close_doors[id] = { id = id, object = object, time = GetGameTimer() }
		end
	else
		close_doors[id] = nil
	end
end

close_breach_door = {}
function breachableDoor(id, doorcoords, toggle)
	if toggle then
		local door = registered_doors[id]
		local chunk = Base.Worldgrid.getNearbyChunks(doorcoords)

		for _, chunks in pairs(chunk) do
			local door_ids = registered_doors_chunks[tostring(chunks)]
			if (door_ids) then
				if (close_breach_door[id] == nil) then
					local object = GetClosestObjectOfType(door.coords.x, door.coords.y, door.coords.z, 2.0, door.model, 0, 0, 0)
					close_breach_door[id] = { id = id, object = object, time = GetGameTimer() }
				end
			end
		end
	else
		close_breach_door[id] = nil
	end
end

-- function to tell the player if they have permissions to lock/unlock the door
--[[
	chunk : chunk of the door
	id : id of the door to check the permissions of
]]
function doesPlayerHavePermissionsToDoor(id)
	local door = registered_doors[id]

	--[[ if the door does not have door permissions, return true as they have permission to open. ]]
	if (door.jobs == nil) then
		return true
	end

	-- debug
	if DOOR_DEBUG then
		return true
	end

	-- loop through the jobs linked to the door
	for _, job in pairs(door.jobs) do
		if DoesPlayerHaveOrg(job) then
			return true
		end
	end

	if type(json.decode(door.property)) == "table" then
		for k, v in pairs(json.decode(door.property)) do
			if hasKeyToProperty(v) then
				return true
			end
		end
	else
		if hasKeyToProperty(door.property) then
			return true
		end
	end

	if door.items then
		for k, name in ipairs(door.items) do
			local item = exports["core_inventory"]:getItem(string.lower(name))
			if item then
				return true
			end
		end
	end

	-- if none of the checks passed, then the player does not have access to the door
	return false
end

-- Network Events
RegisterNetEvent("updateDoorState", function(id, state)
	updateDoorState(id, state, true)
end)

RegisterNetEvent("toggleDoorBlock", function(id, state)
	if state then
		blocked_doors[id] = state
	else
		blocked_doors[id] = nil
	end
end)

local function loadDoors(results)
	registered_doors_chunks = {}
	registered_doors = {}
	for _, v in pairs(results) do
		registerNewDoor(v.id, v.object, vector3(v.x, v.y, v.z), vector3(v.handle, 0.0, v.handlez), v.size, json.decode(v.restrictedJob), v.property, v.area,
			v.lockpickable, v.chainable, v.blowable, v.gangDoor, v.items, v.blockBreachShotgun)
		updateDoorState(v.id, v.state or v.locked, true)
	end
end

-- thread to init doors
CreateThread(function()
	Wait(1000)
	TriggerLatentServerCallback("main_roleplay:getAllDoors", loadDoors)
end)
RegisterNetEvent("main_roleplay:syncDoors", loadDoors)

RegisterNetEvent("core_world:syncBattleDoors", function(results)
	-- Get rid of all existing doors from system before adding new ones otherwise we reach the limit
	for k, v in pairs(registered_doors) do
		local hash = GetHashKey("door_" .. k)
		if (IsDoorRegisteredWithSystem(hash)) then
			RemoveDoorFromSystem(hash)
		end
	end
	registered_doors = {}
	close_doors = {}
	
	if LocalPlayer.state.inBattle then
		loadDoors(results)
	end
end)

RegisterNetEvent("core_world:removeBattleDoors", function()
	-- Get rid of all existing doors from system before adding new ones otherwise we reach the limit
	for k, v in pairs(registered_doors) do
		local hash = GetHashKey("door_" .. k)
		if (IsDoorRegisteredWithSystem(hash)) then
			RemoveDoorFromSystem(hash)
		end
	end
	registered_doors = {}
	close_doors = {}
	TriggerLatentServerCallback("main_roleplay:getAllDoors", loadDoors)
end)

-- thread to handle door distances
local closestDoor = nil
local breachDoor = false
onBaseReady(function()
	while true do
		Wait(200)
		local coords = GetEntityCoords(PlayerPedId())
		local chunks = Base.Worldgrid.getNearbyChunks(coords)

		local i = 0
		local _closestDoor = nil
		local closestDist = 1000

		for _, chunk in pairs(chunks) do
			local door_ids = registered_doors_chunks[tostring(chunk)]
			if (door_ids) then
				for _, id in pairs(door_ids) do
					i = i + 1
					if (i % 10 == 0) then Wait(0) end

					local door = registered_doors[id]
					if (door) then
						local dist = #(coords - door.coords)

						if (dist < door.dist) and (doesPlayerHavePermissionsToDoor(id) or blocked_doors[id] == "Chained" or blocked_doors[id] == "Lock Broken") then
							displayDoor(id, true)
						else
							displayDoor(id, false)
						end

						if dist < closestDist then
							closestDist = dist
							_closestDoor = door
						end

						if breachDoorObject then
							if GetSelectedPedWeapon(PlayerPedId()) == GetHashKey("weapon_breachshotgun") then
								breachableDoor(id, door.coords, true)

								if breachDoorObject == close_breach_door[id].object then
									breachDoor = id
								end
							end
						else
							breachableDoor(id, nil, false)
						end
					end
				end
			end
		end
		closestDoor = _closestDoor
		-- PlayerData = Base.GetPlayerData()
	end
end)

-- thread to handle door texts
onBaseReady(function()
	while true do
		Wait(0)
		local ped = PlayerPedId()
		for _, door in pairs(close_doors) do
			local doorData = registered_doors[door.id]
			local displayCoords = GetOffsetFromEntityInWorldCoords(door.object, doorData.offset.x, doorData.offset.y, doorData.offset.z)
			local playerCoords = GetEntityCoords(GetPlayerPed(-1))
			local textColour = getDoorState(door.id) == 1 and "~r~" or getDoorState(door.id) == 2 and "~o~" or "~g~"
			local debugText = DOOR_DEBUG and tostring(door.id) .. " | " .. tostring(IsDoorClosed(GetHashKey("door_" .. door.id))) .. " " or ""

			if GetDistanceBetweenCoords(displayCoords.x, displayCoords.y, displayCoords.z, playerCoords.x, playerCoords.y, playerCoords.z, true) < doorData.dist then
				local doorStateIndex = getDoorState(door.id)
				local doorState = DOOR_STATES[doorStateIndex]

				if not blocked_doors[door.id] then
					Base.DrawText(displayCoords.x, displayCoords.y, displayCoords.z,
						debugText .. "[" .. textColour .. doorState.key .. "~s~] " .. (doorState.status or ""))

					if (IsControlJustPressed(0, doorState.keyIndex) and (doesPlayerHavePermissionsToDoor(door.id))) then -- G
						updateDoorState(door.id, (getDoorState(door.id) > 0 and 0 or 1), false)
					end
				else
					Base.DrawText(displayCoords.x, displayCoords.y, displayCoords.z, debugText .. blocked_doors[door.id], 0.35, { ca = 150 })
				end
			end
		end
	end
end)

onBaseReady(function()
	while true do
		Wait(0)
		if GetSelectedPedWeapon(PlayerPedId()) == weapon then
			if IsPedShooting(PlayerPedId()) then
				local bool, object = GetEntityPlayerIsFreeAimingAt(PlayerId())
				if GetEntityType(object) == 3 then
					TriggerEvent("breachDoor", object)
				end
			end
		end
	end
end)

RegisterNetEvent('breachDoor')
AddEventHandler('breachDoor', function(obj)
	breachDoorObject = obj
	Wait(250)

	if breachDoor then
		local blockBreach = registered_doors[breachDoor].blockBreachShotgun
		if blocked_doors[breachDoor] ~= "Chained" and blockBreach ~= 1 then
			updateDoorState(breachDoor, 0, false)
			TriggerServerEvent("sv_breachDoor", breachDoor)
		end
	end

	breachDoor = false
	breachDoorObject = false
end)

RegisterNetEvent("lockpickClosestDoor", function()
	if not closestDoor then
		Base.Notification("You are not near any door")
		return
	end

	local door = closestDoor.id
	local doorAreaLabel = closestDoor.areaLabel
	local coords = closestDoor.coords

	-- Check if player is withing 3m of door
	if #(GetEntityCoords(PlayerPedId()) - coords) > 5.0 then
		Base.Notification("You are not close enough to this door")
		return
	end

	if blocked_doors[door] == "Lock Broken" then
		Base.Notification("This door is already broken")
		return
	end

	if blocked_doors[door] == "Door Broken" then
		Base.Notification("This door is already broken")
		return
	end

	if blocked_doors[door] == "Chained" then
		Base.Notification("Break the chain first")
		return
	end

	if not closestDoor.lockpickable then
		Base.Notification("You cannot lockpick this door")
		return
	end

	Base.Animation.StartForcedAnim(PlayerPedId(), "missheistfbisetup1", "unlock_loop_janitor", true, true, false, false)

	Wait(math.random(1000, 2000))

	local success = StartMinigame(3, 0.15)

	TriggerServerCallback("lockpickAttempted", function()
		if success then
			updateDoorState(door, 0, false)
		end
	end, door, success)

	if not GlobalState.PSBlackout then
		if (math.random(1, 4) == 1) or (closestDoor.areaLabel == "Arsenal") then
			if closestDoor.gangDoor then
				TriggerServerEvent("main_roleplay_doors:lockpickGangDoor", closestDoor, doorAreaLabel)
			else
				if door ~= "2168" then -- For powerstation main gate
					TriggerServerEvent("main_dispatch:StartCall", "unauthentry_" .. doorAreaLabel, "police", "Unauthorised Entry",
						"Intruders have broken into " .. doorAreaLabel,
						{
							location = doorAreaLabel,
							priority = "high",
							code = "Signal 1"
						},
						{
							theme = "error",
							gps = coords,
							blip = {
								label = "Unauthorised Entry : " .. doorAreaLabel,
								pos = coords
							},
						}
					)
				end
			end
		end
	end

	Base.Animation.StopForcedAnim(PlayerPedId())
end)

RegisterNetEvent("chainDoor")
AddEventHandler("chainDoor", function()
	if not closestDoor then
		Base.Notification("You are not near any door")
		return
	end

	local door = closestDoor.id
	local doorAreaLabel = closestDoor.areaLabel
	local coords = closestDoor.coords
	local doorState = getDoorState(door)

	if #(GetEntityCoords(PlayerPedId()) - coords) > 4.0 then
		Base.Notification("You are not close enough to this door")
		return
	end

	if not closestDoor.chainable then
		Base.Notification("You cannot chain this door")
		return
	end

	if blocked_doors[door] == "Door Broken" then
		Base.Notification("You cannot chain a door that is broken")
		return
	end

	if blocked_doors[door] == "Chained" then
		Base.Notification("This door is already chained")
		return
	end

	if IsPedInAnyVehicle(PlayerPedId(), false) then
		Base.Notification("You cannot chain a door while in a vehicle")
		return
	end

	Base.Animation.ActionAnimation("missmechanic", "work2_base", true, true, true, 5,
		function()
			TriggerServerCallback("chainDoorAttempted", function()
				Base.Notification("Door chained")
				updateDoorState(door, 1, false)
			end, door, doorState)
		end,
		function()
			Base.Notification("You have stopped chaining the door")
		end
	)
end)


RegisterNetEvent("boltcutClosestDoor")
AddEventHandler("boltcutClosestDoor", function()
	if not closestDoor then
		Base.Notification("You are not near any door")
		return
	end

	local door = closestDoor.id
	local doorAreaLabel = closestDoor.areaLabel
	local coords = closestDoor.coords

	if #(GetEntityCoords(PlayerPedId()) - coords) > 4.0 then
		Base.Notification("You are not close enough to this door")
		return
	end

	if blocked_doors[door] ~= "Chained" then
		Base.Notification("This door is not chained")
		return
	end

	if IsPedInAnyVehicle(PlayerPedId(), false) then
		Base.Notification("You cannot boltcut a door while in a vehicle")
		return
	end

	exports["main_progressbar"]:start(30000, "Cutting Chain")
	Base.Animation.ActionAnimation("anim@amb@business@cfm@cfm_cut_sheets@", "cut_guilotine_v1_billcutter", true, true, true, 30,
		function()
			Base.Notification("Chain cut")
			TriggerServerEvent("boltcutAttempted", door)
		end,
		function()
			Base.Notification("You have stopped cutting the chain")
			exports["main_progressbar"]:stop()
		end
	)
end)

RegisterNetEvent("explosiveClosestObject")
AddEventHandler("explosiveClosestObject", function()
	local veh = Base.Vehicles.GetClosestVehicle(PlayerPedId(), 5.0, false)
	if veh then
		Base.Notification("Setting charge on vehicle")
		Base.Animation.ActionAnimation("anim@heists@ornate_bank@thermal_charge", "thermal_charge", true, true, true, 6,
			function()
				TriggerServerCallback("explosiveAttemptedOnVehicle", function()
					Base.Notification("Charge set for 15 seconds, stand back!")
					PlaySoundEffectOnEntity(veh, "c2", false, 15.0)
					
					Wait(15000)

					local vehCoords = GetEntityCoords(veh)
					AddExplosion(vehCoords.x, vehCoords.y, vehCoords.z, 84, 1.0, true, false, 0.0)
				end)
			end,
			function()
				Base.Notification("You have stopped setting the charge")
			end
		)
	else
		if not closestDoor then
			Base.Notification("You are not near any door or vehicle")
			return
		end

		local closestDoorInfo = closestDoor
		local doorID = closestDoorInfo.id
		local doorAreaLabel = closestDoorInfo.areaLabel
		local gangDoor = closestDoorInfo.gangDoor
		local coords = closestDoorInfo.coords
		local blowable = closestDoorInfo.blowable

		if not blowable then
			Base.Notification("You cannot set an explosive on this door")
			return
		end

		if #(GetEntityCoords(PlayerPedId()) - coords) > 4.0 then
			Base.Notification("You are not close enough to this door")
			return
		end

		if blocked_doors[doorID] == "Lock Broken" then
			Base.Notification("You cannot set an explosive on a door that is already broken")
			return
		end

		if blocked_doors[doorID] == "Door Broken" then
			Base.Notification("You cannot set an explosive on a door that is already broken")
			return
		end

		if IsPedInAnyVehicle(PlayerPedId(), false) then
			Base.Notification("You cannot set an explosive on a door while in a vehicle")
			return
		end

		local doorData = registered_doors[doorID]
		local doorObject = GetClosestObjectOfType(doorData.coords.x, doorData.coords.y, doorData.coords.z, 2.0, doorData.model, 0, 0, 0)
		local explosionCoords = GetOffsetFromEntityInWorldCoords(doorObject, doorData.offset.x, doorData.offset.y, doorData.offset.z)

		Base.Notification("Setting charge on door")
		Base.Animation.ActionAnimation("anim@heists@ornate_bank@thermal_charge", "thermal_charge", true, true, true, 6,
			function()
				TriggerServerCallback("explosiveAttempted", function()
					Base.Notification("Charge set for 15 seconds, stand back!")
					PlaySoundEffectAtLocation(explosionCoords, "c2", false, 15.0)
					Wait(15000)
					AddExplosion(explosionCoords.x, explosionCoords.y, explosionCoords.z, 84, 1.0, true, false, 0.0)

					Wait(5000)

					if not GlobalState.PSBlackout then
						if gangDoor then
							TriggerServerEvent("main_roleplay_doors:lockpickGangDoor", closestDoorInfo, doorAreaLabel)
						else
							if doorID ~= "2168" then -- For powerstation main gate
								if DoesPlayerHaveOrg("police") then return end
								TriggerServerEvent("main_dispatch:StartCall", "unauthentry_" .. doorAreaLabel, "police", "Unauthorised Entry",
									"Explosion detected at " .. doorAreaLabel,
									{
										location = doorAreaLabel,
										priority = "high",
										code = "Signal 1"
									},
									{
										theme = "error",
										gps = coords,
										blip = {
											label = "Unauthorised Entry : " .. doorAreaLabel,
											pos = coords
										},
									}
								)
							end
						end
					end
				end, doorID, coords)
			end,
			function()
				Base.Notification("You have stopped setting the charge")
			end
		)
	end
end)

RegisterNetEvent("base:characterLoaded", function()
	TriggerServerCallback("getAllBlockedDoors", function(data)
		blocked_doors = data
	end)
end)

function StartMinigame(dif, inc) -- minigame, possibly add variables that can be set for different cars?
	local started      = true
	local won          = false

	local Config       = {
		Right = true,
		Inc = inc,
		WinOp = 0.0,
		Pins = 0,
		MaxPin = dif,
		Square = { CurPos = 0.575, MaxLef = 0.5, MaxRig = 0.64 },
		Possible = { Square = { 0.51, 0.52, 0.53, 0.54, 0.55, 0.56, 0.57, 0.58, 0.59, 0.60, 0.61, 0.62, 0.63 }, Speed = { 0.001, 0.0015, 0.002, 0.0025 } }
	}

	local CurrentPos   = math.random(1, #Config.Possible.Square);
	local CurrentSpeed = 0.135
	while started and not isDead do
		if Config.WinOp > 0.0 then
			Config.WinOp = Config.WinOp - 2;
		end

		-- qwick maphs
		if Config.Square.CurPos >= Config.Square.MaxRig and Config.Right then
			Config.Right = false;
		elseif Config.Square.CurPos <= Config.Square.MaxLef and not Config.Right then
			Config.Right = true;
		end

		if Config.Right then -- makes it move // multiplying the value by the distance between frames will mean regardless of your fps it won't go too quick
			Config.Square.CurPos = Config.Square.CurPos + CurrentSpeed * GetFrameTime()
		else
			Config.Square.CurPos = Config.Square.CurPos - CurrentSpeed * GetFrameTime()
		end

		-- square shit
		drawRct(0.5, 0.8, 0.15, 0.015, 0, 0, 0, 200)                             -- main square
		drawRct(0.5, 0.8, 0.15, 0.015, 0, 200, 0, Config.WinOp)                  -- green shit
		drawRct(Config.Possible.Square[CurrentPos], 0.8, 0.010, 0.015, 150, 0, 0, 150) -- red squares in the background
		drawRct(Config.Square.CurPos, 0.8, 0.010, 0.015, 200, 200, 0, 180)       -- yellow square

		if (IsControlJustPressed(0, 18)) then
			Base.Sound.PlayOnEntity(PlayerPedId(), 6.0, "russle", 1.0)

			local dif = Config.Square.CurPos - Config.Possible.Square[CurrentPos];

			if ((dif > -0.015) and (dif < 0.015)) then
				Config.WinOp = 200;
				Config.Pins = Config.Pins + 1;
				if (Config.Pins >= Config.MaxPin) then
					started = false;
					won = true;
					return true;
				end
			else
				started = false;
				won = false;
				return false;
			end
			CurrentPos   = math.random(1, #Config.Possible.Square);
			CurrentSpeed = CurrentSpeed + Config.Inc;
		end
		Wait(0)
	end

	return false
end

function drawRct(x, y, width, height, r, g, b, a)
	DrawRect(x + width / 2, y + height / 2, width, height, r, g, b, a)
end

onBaseReady(function()
	RegisterClientCallback("evidence:closest_door?", function(cb)
		Base.Animation.ActionAnimation("mp_arresting", "a_uncuff", true, true, true, 2,
			function()

			end,
			function()

			end
		)

		cb(closestDoor)
	end)
end)

--- stop spamming of doors only send if you have not touched this in last 15 seconds
local nearbyDoors = {}
local touched_doors = {}

CreateThread(function()
	while true do
		Wait(5000)
		local playerPed = GetPlayerPed(-1)
		nearbyDoors = GetDoorsInRadius(GetEntityCoords(playerPed), 20.0)
	end
end)


--create a thread for touching a door
Citizen.CreateThread(function()
	while true do
		Wait(100)
		local playerPed = GetPlayerPed(-1)
		for k, v in ipairs(nearbyDoors) do
			if IsEntityTouchingEntity(playerPed, v) then
				if closestDoor then
					if touched_doors[closestDoor.id] then
						if (GetGameTimer() - touched_doors[closestDoor.id]) < 15000 then
							break
						end
					end
					TriggerServerEvent("evidence_touch_door", closestDoor.id)
					touched_doors[closestDoor.id] = GetGameTimer()
					Wait(1000)
					break
				end
			end
		end
	end
end)


--[[

	plan:
			Using Base.Controls instead of a loop

			Base.Controls.register(control, label, onDown, onUp, onHold)

			try and get door object from door system???


]]
