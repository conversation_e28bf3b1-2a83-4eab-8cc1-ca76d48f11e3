-- this file handles the data synchronization between the server and the client

local registeredData = {}
local dataHandlers = {}

-- handle set data from the server
RegisterNetEvent("data:" .. GetCurrentResourceName() .. ":set", function(identifier, key, value)
    print("set!", identifier, key, value)
    registeredData[identifier][key] = value

    if dataHandlers[identifier] then
        dataHandlers[identifier](registeredData[identifier], key)
    end
end)

-- handle synced data from the server
RegisterNetEvent("data:" .. GetCurrentResourceName() .. ":sync", function(identifier, data)
    print("sync!", identifier, data, registeredData)
    registeredData[identifier] = data

    if dataHandlers[identifier] then
        dataHandlers[identifier](registeredData[identifier])
    end
end)

-- handle removed data from the server
RegisterNetEvent("data:" .. GetCurrentResourceName() .. ":remove", function(identifier)
    registeredData[identifier] = nil

    if dataHandlers[identifier] then
        dataHandlers[identifier](registeredData[identifier])
    end
end)

-- handle updated data from the server
RegisterNetEvent("data:" .. GetCurrentResourceName() .. ":update", function(identifier, itemIdentifier, data)

    print("update!", identifier, itemIdentifier, data)

    -- if the data doesn't exist, create it
    if not registeredData[identifier] then
        registeredData[identifier] = {}
    end

    for key, value in pairs(data) do
        registeredData[identifier][itemIdentifier][key] = value
    end

    if dataHandlers[identifier] then
        dataHandlers[identifier](registeredData[identifier], itemIdentifier)
    end
end)

-- handle synced changes from the server
RegisterNetEvent("data:" .. GetCurrentResourceName() .. ":syncChanges", function(identifier, changes)
    print("syncChanges!", identifier, changes)
    -- if the data doesn't exist, create it
    if not registeredData[identifier] then
        registeredData[identifier] = {}
    end

    for _, change in pairs(changes) do
        if change.action == "set" then
            registeredData[identifier][change.key] = change.value
        elseif change.action == "remove" then
            registeredData[identifier][change.key] = nil
        elseif change.action == "update" then
            for key, value in pairs(change.value) do
                registeredData[identifier][change.key][key] = value
            end
        end
    end

    if dataHandlers[identifier] then
        dataHandlers[identifier](registeredData[identifier])
    end
end)

-- function to add a handler for a synced data
function AddSyncedDataHandler(identifier, cb)
    dataHandlers[identifier] = cb
end

-- function to remove a handler for a synced data
function RemoveSyncedDataHandler(identifier)
    if not registeredData[identifier] then
        return false
    end

    dataHandlers[identifier] = nil
end
