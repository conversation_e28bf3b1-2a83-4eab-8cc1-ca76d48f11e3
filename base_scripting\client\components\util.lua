Base.Util = {}

Base.Util.formatNumber = function(number)
    local i, j, minus, int, fraction = tostring(number):find('([-]?)(%d+)([.]?%d*)')
    int = int:reverse():gsub("(%d%d%d)", "%1,")
    return minus .. int:reverse():gsub("^,", "") .. fraction
end

Base.Util.wait = function(condition, interval, loops)
    while not condition() and loops > 0 do
        Wait(interval)
        loops = loops - 1
    end

    if condition or loops > 0 then
        return true
    end

    return false
end

Base.Util.fadeScreen = function(state, fadeTime)
    if state then
        DoScreenFadeOut(fadeTime)
        while not IsScreenFadedOut() do Wait(50) end
    else
        DoScreenFadeIn(fadeTime)
        while not IsScreenFadedIn() do Wait(50) end
    end
end

-- Verbose Logging Command
RegisterCommand("verbose", function(source, args, raw)
    if args[1] then
		TriggerEvent("verboseLogging", args[1])
    end
end)