Base.Skin = {}

Base.Skin.currSkin = {} -- What the entity is currently wearing
Base.Skin.charSkin = {} -- What the characters saved skin in

Base.Skin.indexes = {
    clothing = {
        tshirt = {
            label = "Undershirt",
            type = "drawable",
            index = 8
        },
        torso = {
            label = "Torso",
            type = "drawable",
            index = 11
        },
        arms = {
            label = "Arms",
            type = "drawable",
            index = 3
        },
        decals = {
            label = "Decals",
            type = "drawable",
            index = 10
        },
        pants = {
            label = "Pants",
            type = "drawable",
            index = 4
        },
        shoes = {
            label = "Shoes",
            type = "drawable",
            index = 6
        },
        mask = {
            label = "Mask",
            type = "drawable",
            index = 1
        },
        bproof = {
            label = "Vest",
            type = "drawable",
            index = 9
        },
        chain = {
            label = "Chain",
            type = "drawable",
            index = 7
        },
        watch = {
            label = "Watches",
            type = "prop",
            index = 6
        },
        bracelet = {
            label = "Bracelet",
            type = "prop",
            index = 7
        },
        bag = {
            label = "Bag",
            type = "drawable",
            index = 5
        },
        helmet = {
            label = "Helmet",
            type = "prop",
            index = 0
        },
        ears = {
            label = "Ear Accessories",
            type = "prop",
            index = 2
        },
        glasses = {
            label = "Glasses",
            type = "prop",
            index = 1
        },
    }
}

HAIR_FADES = {
    [0] = {
        { "multiplayer_overlays", "FM_M_Hair_001_a" },
        { "multiplayer_overlays", "FM_M_Hair_001_b" },
        { "multiplayer_overlays", "FM_M_Hair_001_c" },
        { "multiplayer_overlays", "FM_M_Hair_001_d" },
        { "multiplayer_overlays", "FM_M_Hair_001_e" },
        { "multiplayer_overlays", "FM_M_Hair_003_a" },
        { "multiplayer_overlays", "FM_M_Hair_003_b" },
        { "multiplayer_overlays", "FM_M_Hair_003_c" },
        { "multiplayer_overlays", "FM_M_Hair_003_d" },
        { "multiplayer_overlays", "FM_M_Hair_003_e" },
        { "multiplayer_overlays", "FM_M_Hair_006_a" },
        { "multiplayer_overlays", "FM_M_Hair_006_b" },
        { "multiplayer_overlays", "FM_M_Hair_006_c" },
        { "multiplayer_overlays", "FM_M_Hair_006_d" },
        { "multiplayer_overlays", "FM_M_Hair_006_e" },
        { "multiplayer_overlays", "FM_M_Hair_008_a" },
        { "multiplayer_overlays", "FM_M_Hair_008_b" },
        { "multiplayer_overlays", "FM_M_Hair_008_c" },
        { "multiplayer_overlays", "FM_M_Hair_008_d" },
        { "multiplayer_overlays", "FM_M_Hair_008_e" },
        { "multiplayer_overlays", "FM_M_Hair_long_a" },
        { "multiplayer_overlays", "FM_M_Hair_long_b" },
        { "multiplayer_overlays", "FM_M_Hair_long_c" },
        { "multiplayer_overlays", "FM_M_Hair_long_d" },
        { "multiplayer_overlays", "FM_M_Hair_long_e" },
        { "mpHipster_overlays", "FM_Hip_M_Hair_000_a" },
        { "mpHipster_overlays", "FM_Hip_M_Hair_000_b" },
        { "mpHipster_overlays", "FM_Hip_M_Hair_000_c" },
        { "mpHipster_overlays", "FM_Hip_M_Hair_000_d" },
        { "mpHipster_overlays", "FM_Hip_M_Hair_000_e" },
        { "mpHipster_overlays", "FM_Hip_M_Hair_001_a" },
        { "mpHipster_overlays", "FM_Hip_M_Hair_001_b" },
        { "mpHipster_overlays", "FM_Hip_M_Hair_001_c" },
        { "mpHipster_overlays", "FM_Hip_M_Hair_001_d" },
        { "mpHipster_overlays", "FM_Hip_M_Hair_001_e" },
        { "mpHipster_overlays", "FM_Disc_M_Hair_001_a" },
        { "mpHipster_overlays", "FM_Disc_M_Hair_001_b" },
        { "mpHipster_overlays", "FM_Disc_M_Hair_001_c" },
        { "mpHipster_overlays", "FM_Disc_M_Hair_001_d" },
        { "mpHipster_overlays", "FM_Disc_M_Hair_001_e" },
        { "multiplayer_overlays", "NG_M_Hair_001" },
        { "multiplayer_overlays", "NG_M_Hair_002" },
        { "multiplayer_overlays", "NG_M_Hair_003" },
        { "multiplayer_overlays", "NG_M_Hair_004" },
        { "multiplayer_overlays", "NG_M_Hair_005" },
        { "multiplayer_overlays", "NG_M_Hair_006" },
        { "multiplayer_overlays", "NG_M_Hair_007" },
        { "multiplayer_overlays", "NG_M_Hair_008" },
        { "multiplayer_overlays", "NG_M_Hair_009" },
        { "multiplayer_overlays", "NG_M_Hair_010" },
        { "multiplayer_overlays", "NG_M_Hair_011" },
        { "multiplayer_overlays", "NG_M_Hair_012" },
        { "multiplayer_overlays", "NG_M_Hair_013" },
        { "multiplayer_overlays", "NG_M_Hair_014" },
        { "multiplayer_overlays", "NG_M_Hair_015" },
        { "multiplayer_overlays", "NGBea_M_Hair_000" },
        { "multiplayer_overlays", "NGBea_M_Hair_001" },
        { "multiplayer_overlays", "NGBus_M_Hair_000" },
        { "multiplayer_overlays", "NGBus_M_Hair_001" },
        { "multiplayer_overlays", "NGHip_M_Hair_000" },
        { "multiplayer_overlays", "NGHip_M_Hair_001" },
        { "multiplayer_overlays", "NGInd_M_Hair_000" },
    },
    [1] = {
        { "multiplayer_overlays", "FM_F_Hair_003_a" },
        { "multiplayer_overlays", "FM_F_Hair_003_b" },
        { "multiplayer_overlays", "FM_F_Hair_003_c" },
        { "multiplayer_overlays", "FM_F_Hair_003_d" },
        { "multiplayer_overlays", "FM_F_Hair_003_e" },
        { "multiplayer_overlays", "FM_F_Hair_005_a" },
        { "multiplayer_overlays", "FM_F_Hair_005_b" },
        { "multiplayer_overlays", "FM_F_Hair_005_c" },
        { "multiplayer_overlays", "FM_F_Hair_005_d" },
        { "multiplayer_overlays", "FM_F_Hair_005_e" },
        { "multiplayer_overlays", "FM_F_Hair_006_a" },
        { "multiplayer_overlays", "FM_F_Hair_006_b" },
        { "multiplayer_overlays", "FM_F_Hair_006_c" },
        { "multiplayer_overlays", "FM_F_Hair_006_d" },
        { "multiplayer_overlays", "FM_F_Hair_006_e" },
        { "multiplayer_overlays", "FM_F_Hair_013_a" },
        { "multiplayer_overlays", "FM_F_Hair_013_b" },
        { "multiplayer_overlays", "FM_F_Hair_013_c" },
        { "multiplayer_overlays", "FM_F_Hair_013_d" },
        { "multiplayer_overlays", "FM_F_Hair_013_e" },
        { "multiplayer_overlays", "FM_F_Hair_014_a" },
        { "multiplayer_overlays", "FM_F_Hair_014_b" },
        { "multiplayer_overlays", "FM_F_Hair_014_c" },
        { "multiplayer_overlays", "FM_F_Hair_014_d" },
        { "multiplayer_overlays", "FM_F_Hair_014_e" },
        { "multiplayer_overlays", "FM_F_Hair_long_a" },
        { "multiplayer_overlays", "FM_F_Hair_long_b" },
        { "multiplayer_overlays", "FM_F_Hair_long_c" },
        { "multiplayer_overlays", "FM_F_Hair_long_d" },
        { "multiplayer_overlays", "FM_F_Hair_long_e" },
        { "mpHipster_overlays", "FM_Hip_F_Hair_000_a" },
        { "mpHipster_overlays", "FM_Hip_F_Hair_000_b" },
        { "mpHipster_overlays", "FM_Hip_F_Hair_000_c" },
        { "mpHipster_overlays", "FM_Hip_F_Hair_000_d" },
        { "mpHipster_overlays", "FM_Hip_F_Hair_000_e" },
        { "mpHipster_overlays", "FM_F_Hair_017_a" },
        { "mpHipster_overlays", "FM_F_Hair_017_b" },
        { "mpHipster_overlays", "FM_F_Hair_017_c" },
        { "mpHipster_overlays", "FM_F_Hair_017_d" },
        { "mpHipster_overlays", "FM_F_Hair_017_e" },
        { "mpHipster_overlays", "FM_F_Hair_020_a" },
        { "mpHipster_overlays", "FM_F_Hair_020_b" },
        { "mpHipster_overlays", "FM_F_Hair_020_c" },
        { "mpHipster_overlays", "FM_F_Hair_020_d" },
        { "mpHipster_overlays", "FM_F_Hair_020_e" },
        { "multiplayer_overlays", "NG_F_Hair_001" },
        { "multiplayer_overlays", "NG_F_Hair_002" },
        { "multiplayer_overlays", "NG_F_Hair_003" },
        { "multiplayer_overlays", "NG_F_Hair_004" },
        { "multiplayer_overlays", "NG_F_Hair_005" },
        { "multiplayer_overlays", "NG_F_Hair_006" },
        { "multiplayer_overlays", "NG_F_Hair_007" },
        { "multiplayer_overlays", "NG_F_Hair_008" },
        { "multiplayer_overlays", "NG_F_Hair_009" },
        { "multiplayer_overlays", "NG_F_Hair_010" },
        { "multiplayer_overlays", "NG_F_Hair_011" },
        { "multiplayer_overlays", "NG_F_Hair_012" },
        { "multiplayer_overlays", "NG_F_Hair_013" },
        { "multiplayer_overlays", "NG_F_Hair_014" },
        { "multiplayer_overlays", "NG_F_Hair_015" },
        { "multiplayer_overlays", "NGBea_F_Hair_000" },
        { "multiplayer_overlays", "NGBea_F_Hair_001" },
        { "multiplayer_overlays", "NGBus_F_Hair_000" },
        { "multiplayer_overlays", "NGBus_F_Hair_001" },
        { "multiplayer_overlays", "NGHip_F_Hair_000" },
        { "multiplayer_overlays", "NGHip_F_Hair_001" },
        { "multiplayer_overlays", "NGInd_F_Hair_000" }
    }
}

-- Clothing Toggles
CLOTHING_TOGGLES = {
    [0] = {
        helmet = -1,
        ears = -1,
        glasses = 0,
        mask = 0,
        chain = 0,
        watch = -1,
        bracelet = -1,
        tshirt = 15,
        torso = 15,
        arms = 15,
        shoes = 34,
        pants = 61,
        bproof = 0,
        decals = 0,
        bag = 0,
    },

    [1] = {
        helmet = -1,
        ears = -1,
        glasses = 5,
        mask = 0,
        chain = 0,
        watch = -1,
        bracelet = -1,
        tshirt = 14,
        torso = 15,
        arms = 15,
        shoes = 35,
        pants = 15,
        bproof = 0,
        decals = 0,
        bag = 0,
    }
}

MASK_MORPH_BLACKLIST = {
    [GetHashKey("mp_m_freemode_01")] = {
        -- vanilla
        [-1] = true,
        [0] = true,
        [11] = true,
        [12] = true,
        [27] = true,
        [33] = true,
        [36] = true,
        [73] = true,
        [74] = true,
        [75] = true,
        [109] = true,
        [114] = true,
        [120] = true,
        [121] = true,
        [122] = true,
        [145] = true,

        -- beards
        [148] = true,
        [149] = true,
        [150] = true,
        [151] = true,
        [152] = true,
        [153] = true,

        -- custom
        [156] = true,

        -- reserved slots 157
    },
    [GetHashKey("mp_f_freemode_01")] = {
        [-1] = true,
        [0] = true,
        [11] = true,
        [12] = true,
        [27] = true,
        [33] = true,
        [36] = true,
        [73] = true,
        [74] = true,
        [75] = true,
        [109] = true,
        [114] = true,
        [120] = true,
        [121] = true,
        [122] = true,
        [145] = true,

        -- custom
        [148] = true,

        -- reserved slots 150
    }
}

GLOVE_DATA = {

    MIN_MAX = { -- the max and min of arms that are gloves
        [0] = { min = 15, max = 167 },
        [1] = { min = 15, max = 208 }
    },

    ARM_VALUES = {
        [0] = { -- fixed values for the meth addict rockstart employees
            [0] = 0,
            [1] = 1,
            [2] = 2,
            [3] = 4,
            [4] = 5,
            [5] = 6,
            [6] = 8,
            [7] = 11,
            [8] = 12,
            [9] = 14,
            [10] = 15
        },
        [1] = {
            [0] = 0,
            [1] = 1,
            [2] = 2,
            [3] = 3,
            [4] = 4,
            [5] = 5,
            [6] = 6,
            [7] = 7,
            [8] = 9,
            [9] = 11,
            [10] = 12,
            [11] = 14,
            [12] = 15
        }
    },

    SINGLES = {
        [0] = {
            16,
            17,
            18,
            96,
            97,
            98,
            110,
            111,
            165,
            166,
            167
        },
        [1] = {
            17,
            18,
            19,
            111,
            112,
            113,
            127,
            128,
            206,
            207,
            208
        }
    },

    SETS = {
        [0] = {
            { start = 19, finish = 29 },
            { start = 30, finish = 40 },
            { start = 41, finish = 51 },
            { start = 52, finish = 62 },
            { start = 63, finish = 73 },
            { start = 74, finish = 84 },
            { start = 85, finish = 95 },
            { start = 99, finish = 109 },
            -- 112 greater is different
        },
        [1] = {
            { start = 20, finish = 32 },
            { start = 33, finish = 45 },
            { start = 46, finish = 58 },
            { start = 59, finish = 71 },
            { start = 72, finish = 84 },
            { start = 85, finish = 97 },
            { start = 98, finish = 110 },
            { start = 114, finish = 126 },
            -- 129 greater is different
        }
    },

    -- fuck the system
    -- also fuck the camo gloves
    VARIANTS = {
        [0] = {
            { start = 115, finish = 121, value = 112 },
            { start = 122, finish = 128, value = 113 },
            { start = 129, finish = 135, value = 114 },
        },
        [1] = {
            { start = 16, finish = 16, value = 11, },
            { start = 132, finish = 138, value = 129, },
            { start = 139, finish = 145, value = 130, },
            { start = 146, finish = 152, value = 131, },
            { start = 154, finish = 160, value = 153, },
            { start = 162, finish = 168, value = 161, },
        }
    },

    -- i hate everything
    VARIANTS_ERP = {
        [0] = {
            [0] = 29,
            [1] = 40,
            [2] = 51,
            [3] = 62,
            [4] = 73,
            [5] = 84,
            [6] = 95
        },
        [1] = {
            [0] = 32,
            [1] = 45,
            [2] = 58,
            [3] = 71,
            [4] = 84,
            [5] = 97,
            [6] = 110
        }
    }

}

local hiddenClothing = {}

-- Misc Functions
Base.Skin.getCurrentSkin = function()
    return Base.Skin.currSkin
end

Base.Skin.getCharSkin = function()
    return Base.Skin.charSkin
end

Base.Skin.getDefault = function()
    return {
        model = "mp_m_freemode_01",
        sex = 0,

        skin = Base.Skin.getDefaultSkin(),
        cosmetics = Base.Skin.getDefaultCosmetics(),
        clothing = Base.Skin.getDefaultClothing(),
        tattoos = {}
    }
end

Base.Skin.getDefaultSkin = function()
    return {
        mother = 0,
        father = 0,
        shapemix = 50,
        colour = 0,
        lip_thickness = 0,
        neck_thickness = 0,

        freckles = { texture = 0, opacity = 0 },
        age = { texture = 0, opacity = 0 },
        damage = { texture = 0, opacity = 0 },
        complexion = { texture = 0, opacity = 0 },
        blemish = { texture = 0, opacity = 0 },

        nose = { width = 0, heigh = 0, twist = 0, peak_height = 0, peak_length = 0, peak_lowering = 0 },
        cheeks = { width = 0, height = 0, chub = 0 },
        eyes = { size = 0, colour = 0, brow_height = 0, brow_forward = 0 },
        jaw = { width = 0, length = 0 },
        chin = { lower = 0, length = 0, width = 0, tip = 0 }
    }
end

Base.Skin.getDefaultCosmetics = function()
    return {
        head = { style = 1, thickness = 100, colour = 0, highlights = 0, fade = -1 },
        beard = { style = 0, thickness = 0, colour = 0, highlights = 0 },
        chest = { style = 0, thickness = 0, colour = 0, highlights = 0 },
        eyebrows = { style = 0, thickness = 0, colour = 0, highlights = 0 },
        lipstick = { style = 0, thickness = 0, colour = 0, highlights = 0 },
        makeup = { style = 0, thickness = 0, colour = 0, highlights = 0 },
        blush = { style = 0, thickness = 0, colour = 0, highlights = 0 },
    }
end

Base.Skin.getDefaultClothing = function()
    return {
        helmet = { model = -1, texture = 0 },
        glasses = { model = 0, texture = 0 },
        ears = { model = -1, texture = 0 },
        mask = { model = 0, texture = 0 },
        torso = { model = 0, texture = 0 },
        tshirt = { model = 0, texture = 0 },
        arms = { model = 0, texture = 0 },
        chain = { model = 0, texture = 0 },
        watch = { model = -1, texture = 0 },
        bracelet = { model = -1, texture = 0 },
        pants = { model = 0, texture = 0 },
        shoes = { model = 0, texture = 0 },
        decals = { model = 0, texture = 0 },
        bproof = { model = 0, texture = 0 },
        bag = { model = 0, texture = 0 },
    }
end

Base.Skin.getFeatureLabel = function(category, feature)
    return Base.Skin.indexes[category][feature].label
end

Base.Skin.getMinValue = function(feature)
    if feature == "helmet" or feature == "ears" then
        return -1
    else
        return 0
    end
end

Base.Skin.getMaxValue = function(category, feature, texture)
    if category == "clothing" then
        local playerPed = PlayerPedId()
        local elem = Base.Skin.indexes[category][feature]

        if elem then
            if not texture then
                if elem.type == "prop" then
                    return GetNumberOfPedPropDrawableVariations(playerPed, elem.index) - 1
                else
                    return GetNumberOfPedDrawableVariations(playerPed, elem.index) - 1
                end
            else
                if elem.type == "prop" then
                    return GetNumberOfPedPropTextureVariations(playerPed, elem.index, Base.Skin.currSkin.clothing[feature].model) - 1
                else
                    return GetNumberOfPedTextureVariations(playerPed, elem.index, Base.Skin.currSkin.clothing[feature].model) - 1
                end
            end
        end
    end
end

Base.Skin.getGloveToggle = function(sex, current, hasGloves, hasShirt)
    -- basic check for skin with no gloves and no shirt
    if (current <= GLOVE_DATA.MIN_MAX[sex].min and not hasShirt) then
        return GLOVE_DATA.ARM_VALUES[sex][#GLOVE_DATA.ARM_VALUES[sex]]
    end

    -- what the actual fuck is wrong with these torsos END ME
    -- I have smashed by keyboard now as the female indexes are also different, what the fuck
    if (current <= GLOVE_DATA.MIN_MAX[sex].min or current >= GLOVE_DATA.MIN_MAX[sex].max) then
        return false
    end

    for _, key in pairs(GLOVE_DATA.SINGLES[sex]) do
        if (key == current) then
            return (hasShirt and (sex == 0 and 4 or 3) or GLOVE_DATA.ARM_VALUES[sex][#GLOVE_DATA.ARM_VALUES[sex]])
        end
    end

    for _, set in pairs(GLOVE_DATA.SETS[sex]) do
        --[[
            if the current arm is greater or equal to the glove start and is less or equal to the finish then we know that the player is using these gloves.
            Because each arm set with gloves has the same amount which is 10, if we subtract the start index from the current index we will get the correct arms.

            Because the Rockstar devs are on crack cocaine or something, i've had to correctly index the arms above as there is a bunch missing.

            ... i think
        ]]
        if (current >= set.start and current <= set.finish) then
            -- hasShirt means you are wearing shirt yis
            return (hasShirt and GLOVE_DATA.ARM_VALUES[sex][current - set.start] or (hasGloves and set.finish or (GLOVE_DATA.ARM_VALUES[sex][set.finish - set.start]))) -- set finish is full torso
        end
    end

    --[[
        this crap is really really stupid, basically rockstar decided instead of keeping things in order they'd just slap some random new stuff in and hope for the best.

        So I have had to write a whole seperate bunch of logic in order to deal with it.
    ]]
    for _, set in pairs(GLOVE_DATA.VARIANTS[sex]) do
        if (current >= set.start and current <= set.finish) then
            if (hasShirt) then
                return set.value
            else
                if (hasGloves) then
                    return GLOVE_DATA.VARIANTS_ERP[sex][math.abs(set.start - current)]
                else
                    local _current = GLOVE_DATA.VARIANTS_ERP[sex][math.abs(set.start - current)]
                    for _, _set in pairs(GLOVE_DATA.SETS[sex]) do
                        if (_current >= _set.start and _current <= _set.finish) then
                            return GLOVE_DATA.ARM_VALUES[sex][_set.finish - _set.start]
                        end
                    end
                end
            end
        end
    end

    return false
end

-- Skin Setter
Base.Skin.setSkin = function(skin, saveSkin, entity)
    -- print("[Skin] Setting Skin")
    -- print("[Skin] Loading Model")
    if (entity == nil) then
        hiddenClothing = {}
        if skin.model then
            Base.Skin.applyModel(skin.model, saveSkin, entity)
        else
            skin.model = "mp_m_freemode_01"
            Base.Skin.applyModel(skin.model, saveSkin, entity)
        end
    end
    -- print("[Skin] Loading Skin")
    if skin.skin then Base.Skin.applySkin(skin.skin, saveSkin, entity, false, skin.sex) end
    -- print("[Skin] Loading Clothing")
    if skin.clothing then Base.Skin.applyClothing(skin.clothing, saveSkin, entity, skin.sex) end
    -- print("[Skin] Loading Tattoos")
    if skin.tattoos then Base.Skin.applyTattoos(skin.tattoos, saveSkin, entity, skin.sex) end
    -- print("[Skin] Loading Cosmetics")
    if skin.cosmetics then Base.Skin.applyCosmetics(skin.cosmetics, saveSkin, entity, skin.sex) end
    -- print("[Skin] Skin Loaded!")
    if (entity and skin.cosmetics) then Base.Skin.reloadDecorations(skin.cosmetics.head.fade, skin.tattoos, entity) end

    if saveSkin then
        Base.Skin.saveCharSkin()
    end
end

Base.Skin.applyModel = function(model, saveSkin, entity)
    -- print("[Skin] Applying model now!")

    local ped = entity or PlayerPedId()
    local health = GetEntityHealth(ped)
    local modelHash = GetHashKey(model)

    -- print("[skin] Trying to load model! Model: "..modelHash)

    if not IsModelValid(modelHash) then
        -- print("[skin] Model didn't exist! Model: "..modelHash)
        return false
    end

    RequestModel(modelHash)
    -- print("Model Requested: "..modelHash)

    while not HasModelLoaded(modelHash) do
        RequestModel(modelHash)
        Citizen.Wait(0)
    end

    -- print("Model loaded!")

    if IsModelInCdimage(modelHash) and HasModelLoaded(modelHash) then
        -- print("Model is valid! Setting now!", ped)
        SetPlayerModel(PlayerId(), modelHash)
        SetPedDefaultComponentVariation(PlayerPedId())
        TriggerEvent("onPlayerModelUpdated")
    end

    SetModelAsNoLongerNeeded(modelHash)

    Citizen.CreateThread(function()
        local _health = health
        Wait(150) -- there seems to be a delay between when you set the ped and the game updating whether it's a player ped; so you cannot set things like health straight away.
        TriggerEvent("test_ac:heal")
        SetEntityHealth(PlayerPedId(), _health)
    end)

    -- Save the skin
    if not entity then
        Base.Skin.currSkin.model = model

        if model == "mp_m_freemode_01" then
            Base.Skin.currSkin.sex = 0
        elseif model == "mp_f_freemode_01" then
            Base.Skin.currSkin.sex = 1
        else
            Base.Skin.currSkin.sex = 2
        end

        if saveSkin then
            Base.Skin.charSkin.model = model
            Base.Skin.charSkin.sex = Base.Skin.currSkin.sex
        end
    end
end

Base.Skin.applySkin = function(skin, saveSkin, entity, ignoreSkin)
    local ped = entity or PlayerPedId()

    -- Head Manipulation
    SetPedHeadBlendData(ped, skin.mother, skin.father, 0.0, skin.colour, skin.colour, 0.0, (skin.shapemix or 50) / 100.0, 0.5, 0.0, true)

    -- Nose Features
    if skin.nose then
        SetPedFaceFeature(ped, 0, (skin.nose.width or 0.0) / 100.0)
        SetPedFaceFeature(ped, 1, (skin.nose.height or 0.0) / 100.0)
        SetPedFaceFeature(ped, 2, (skin.nose.peak_length or 0.0) / 100.0)
        SetPedFaceFeature(ped, 3, (skin.nose.peak_lowering or 0.0) / 100.0)
        SetPedFaceFeature(ped, 4, (skin.nose.peak_height or 0.0) / 100.0)
        SetPedFaceFeature(ped, 5, (skin.nose.twist or 0.0) / 100.0)
    end

    -- Eyes
    if skin.eyes then
        SetPedEyeColor(ped, skin.eyes.colour)
        SetPedFaceFeature(ped, 6, (skin.eyes.brow_height or 0.0) / 100.0)
        SetPedFaceFeature(ped, 7, (skin.eyes.brow_forward or 0.0) / 100.0)
        SetPedFaceFeature(ped, 11, (skin.eyes.opening or 0.0) / 100.0)
    end

    -- Cheeks
    if skin.cheeks then
        SetPedFaceFeature(ped, 8, (skin.cheeks.height or 0.0) / 100.0)
        SetPedFaceFeature(ped, 9, (skin.cheeks.width or 0.0) / 100.0)
        SetPedFaceFeature(ped, 10, (skin.cheeks.chub or 0.0) / 100.0)
    end

    -- Lips
    SetPedFaceFeature(ped, 12, (skin.lip_thickness or 0.0) / 100.0)

    -- Jaw
    if skin.jaw then
        SetPedFaceFeature(ped, 13, (skin.jaw.width or 0.0) / 100.0)
        SetPedFaceFeature(ped, 14, (skin.jaw.length or 0.0) / 100.0)
    end

    -- Cheeks
    if skin.chin then
        SetPedFaceFeature(ped, 15, (skin.chin.lower or 0.0) / 100.0)
        SetPedFaceFeature(ped, 16, (skin.chin.length or 0.0) / 100.0)
        SetPedFaceFeature(ped, 17, (skin.chin.width or 0.0) / 100.0)
        SetPedFaceFeature(ped, 18, (skin.chin.tip or 0.0) / 100.0)
    end

    -- Neck
    SetPedFaceFeature(ped, 19, (skin.neck_thickness or 0.0) / 100.0)

    -- Facial Features
    SetPedHeadOverlay(ped, 0, skin.blemish.texture, (skin.blemish.opacity or 0.0) / 100.0) -- Blemishes
    SetPedHeadOverlay(ped, 3, skin.age.texture, (skin.age.opacity or 0.0) / 100.0) -- Aging
    SetPedHeadOverlay(ped, 6, skin.complexion.texture, (skin.complexion.opacity or 0.0) / 100.0) -- Complexion
    SetPedHeadOverlay(ped, 7, skin.damage.texture, (skin.damage.opacity or 0.0) / 100.0) -- Sun Damage
    SetPedHeadOverlay(ped, 9, skin.freckles.texture, (skin.freckles.opacity or 0.0) / 100.0) -- Moles-Freckles

    -- Save the skin
    if not entity and not ignoreSkin then
        Base.Skin.currSkin.skin = skin
        if saveSkin then
            Base.Skin.charSkin.skin = table.clone(skin)
        end
    end
end

Base.Skin.applyClothing = function(clothing, saveSkin, entity)
    local ped = entity or PlayerPedId()
    local clothing = clothing
    local startClothing = table.clone(clothing)

    if (entity == nil) then
        for k, state in pairs(hiddenClothing) do
            if (k == "arms") then
                local shirtState = (hiddenClothing["torso"] == nil and false or hiddenClothing["torso"])
                local newIndex = Base.Skin.getGloveToggle(Base.Skin.getCurrentSkin().sex, clothing[k].model, not state, not shirtState)
                if (newIndex and (state or shirtState)) then
                    clothing[k].model = newIndex
                end
            else
                if state then
                    clothing[k].model = CLOTHING_TOGGLES[Base.Skin.getCurrentSkin().sex][k]
                    clothing[k].texture = 0
                end
            end
        end
    end

    -- Clothing Props
    if clothing.helmet.model == -1 then
        ClearPedProp(ped, 0)
    else
        SetPedPropIndex(ped, 0, clothing.helmet.model, clothing.helmet.texture, 2)
    end

    if clothing.ears.model == -1 then
        ClearPedProp(ped, 2)
    else
        SetPedPropIndex(ped, 2, clothing.ears.model, clothing.ears.texture, 2)
    end

    if clothing.watch == nil or clothing.watch.model == -1 then
        ClearPedProp(ped, 6)
    else
        SetPedPropIndex(ped, 6, clothing.watch.model, clothing.watch.texture, 2)
    end

    if clothing.bracelet == nil or clothing.bracelet.model == -1 then
        ClearPedProp(ped, 7)
    else
        SetPedPropIndex(ped, 7, clothing.bracelet.model, clothing.bracelet.texture, 2)
    end

    SetPedPropIndex(ped, 1, clothing.glasses.model, clothing.glasses.texture, 2) -- Glasses

    -- Component Variations
    SetPedComponentVariation(ped, 8, clothing.tshirt.model, clothing.tshirt.texture, 2) -- Undershirt
    SetPedComponentVariation(ped, 11, clothing.torso.model, clothing.torso.texture, 2) -- Torso
    SetPedComponentVariation(ped, 3, clothing.arms.model, clothing.arms.texture, 2) -- Arms
    SetPedComponentVariation(ped, 10, clothing.decals.model, clothing.decals.texture, 2) -- decals
    SetPedComponentVariation(ped, 4, clothing.pants.model, clothing.pants.texture, 2) -- pants
    SetPedComponentVariation(ped, 6, clothing.shoes.model, clothing.shoes.texture, 2) -- shoes
    SetPedComponentVariation(ped, 1, clothing.mask.model, clothing.mask.texture, 2) -- mask
    SetPedComponentVariation(ped, 9, clothing.bproof.model, clothing.bproof.texture, 2) -- bulletproof
    SetPedComponentVariation(ped, 7, clothing.chain.model, clothing.chain.texture, 2) -- chain
    SetPedComponentVariation(ped, 5, clothing.bag.model, clothing.bag.texture, 2) -- Bag

    Citizen.CreateThread(function()
        Wait(100)
        Base.Skin.setMaskFace(hiddenClothing["mask"] or false, clothing.mask.model, entity)
    end)

    -- Save the skin
    if not entity then
        Base.Skin.currSkin.clothing = startClothing
        if saveSkin then Base.Skin.charSkin.clothing = table.clone(startClothing) end
    end

    SetPedConfigFlag(PlayerPedId(), 409, true) --Disables scuba gear auto take off
end

Base.Skin.applyTattoos = function(tattoos, saveSkin, entity)
    local ped = entity or PlayerPedId()

    Base.Skin.reloadDecorations(nil, tattoos, entity, ped)

    -- Save the skin
    if not entity then
        Base.Skin.currSkin.tattoos = tattoos
        if saveSkin then
            Base.Skin.charSkin.tattoos = table.clone(tattoos)
        end
    end
end

Base.Skin.applyCosmetics = function(cosmetics, saveSkin, entity, sex)
    local ped = entity or PlayerPedId()

    if cosmetics.head then
        SetPedComponentVariation(ped, 2, cosmetics.head.style, 0, 2)
        SetPedHairColor(ped, cosmetics.head.colour, cosmetics.head.highlights)
        Base.Skin.reloadDecorations(cosmetics.head.fade, nil, ped)
    end

    -- Eyebrows
    if cosmetics.eyebrows then
        SetPedHeadOverlay(ped, 2, cosmetics.eyebrows.style, (cosmetics.eyebrows.thickness / 100) + 0.0)
        SetPedHeadOverlayColor(ped, 2, 1, cosmetics.eyebrows.colour, cosmetics.eyebrows.highlights)
    end

    -- Beard
    if cosmetics.beard then
        SetPedHeadOverlay(ped, 1, cosmetics.beard.style, (cosmetics.beard.thickness / 100) + 0.0)
        SetPedHeadOverlayColor(ped, 1, 1, cosmetics.beard.colour, cosmetics.beard.highlights)
    end

    -- Chest Hair
    if cosmetics.chest then
        SetPedHeadOverlay(ped, 10, cosmetics.chest.style, (cosmetics.chest.thickness / 100) + 0.0)
        SetPedHeadOverlayColor(ped, 10, 1, cosmetics.chest.colour, cosmetics.chest.highlights)
    end

    -- Makeup
    if cosmetics.makeup then
        SetPedHeadOverlay(ped, 4, cosmetics.makeup.style, (cosmetics.makeup.thickness / 100) + 0.0)
        SetPedHeadOverlayColor(ped, 4, 1, cosmetics.makeup.colour, cosmetics.makeup.highlights)
    end

    -- Lipstick
    if cosmetics.lipstick then
        SetPedHeadOverlay(ped, 8, cosmetics.lipstick.style, (cosmetics.lipstick.thickness / 100) + 0.0)
        SetPedHeadOverlayColor(ped, 8, 2, cosmetics.lipstick.colour, cosmetics.lipstick.highlights)
    end

    -- Blush
    if cosmetics.blush then
        SetPedHeadOverlay(ped, 5, cosmetics.blush.style, (cosmetics.blush.thickness / 100) + 0.0)
        SetPedHeadOverlayColor(ped, 5, 2, cosmetics.blush.colour, cosmetics.blush.highlights)
    end

    -- Save the cosmetics
    if not entity then
        Base.Skin.currSkin.cosmetics = cosmetics
        if saveSkin then
            Base.Skin.charSkin.cosmetics = table.clone(cosmetics)
        end
    end
end

Base.Skin.reloadDecorations = function(fadeId, tattoosTable, entity)
    local ped = entity or PlayerPedId()
    local sex = (GetEntityModel(entity) == GetHashKey("mp_m_freemode_01") and 0 or 1)
    local fade = fadeId or (Base.Skin.currSkin.cosmetics and Base.Skin.currSkin.cosmetics.head.fade or 0)
    local tattoos = tattoosTable or (Base.Skin.currSkin.tattoos and Base.Skin.currSkin.tattoos or {})

    -- clear old
    ClearPedDecorations(ped)

    -- hair fade
    if (fade and fade ~= 0) then
        if (HAIR_FADES[sex][fade]) then
            AddPedDecorationFromHashesInCorona(
                ped,
                GetHashKey(HAIR_FADES[sex][fade][1]),
                GetHashKey(HAIR_FADES[sex][fade][2])
            )
        end
    end

    -- tattoos
    local appliedTattoos = {}
    for _, tattoo in ipairs(tattoos) do
        if (appliedTattoos[tattoo.collection .. "_" .. tattoo.hash] == nil) then
            SetPedDecoration(ped, GetHashKey(tattoo.collection), GetHashKey(tattoo.hash))
            appliedTattoos[tattoo.collection .. "_" .. tattoo.hash] = true
        end
    end
    appliedTattoos = nil
end

Base.Skin.saveCurrSkin = function()
    Base.Skin.charSkin = table.clone(Base.Skin.currSkin)
    TriggerServerEvent("scripting:skin:saveSkin", Base.Skin.charSkin)
end

Base.Skin.saveCharSkin = function()
    TriggerServerEvent("scripting:skin:saveSkin", Base.Skin.charSkin)
end

-- External Setters for skin items
Base.Skin.setClothingItem = function(category, key, value, saveSkin, entity)
    if Base.Skin.currSkin.clothing[category] == nil then Base.Skin.currSkin.clothing[category] = {} end
    Base.Skin.currSkin.clothing[category][key] = value
    Base.Skin.applyClothing(Base.Skin.currSkin.clothing, saveSkin, entity)
    if saveSkin then Base.Skin.saveCharSkin() end
end

Base.Skin.setSkinItem = function(category, key, value, saveSkin, entity)
    if key then
        Base.Skin.currSkin.skin[category][key] = value
    else
        Base.Skin.currSkin.skin[category] = value
    end

    Base.Skin.applySkin(Base.Skin.currSkin.skin, saveSkin, entity, false)
    if saveSkin then Base.Skin.saveCharSkin() end
end

Base.Skin.setCosmeticItem = function(category, key, value, saveSkin, entity)
    if not Base.Skin.currSkin.cosmetics[category] then
        Base.Skin.currSkin.cosmetics[category] = {}
    end
    Base.Skin.currSkin.cosmetics[category][key] = value
    Base.Skin.applyCosmetics(Base.Skin.currSkin.cosmetics, saveSkin, entity, Base.Skin.currSkin.sex)
    if saveSkin then Base.Skin.saveCharSkin() end
end

Base.Skin.refreshTattoos = function()
    if (Base.Skin.currSkin.tattoos) then
        Base.Skin.applyTattoos(Base.Skin.currSkin.tattoos, false, PlayerPedId())
    end
end

Base.Skin.addTattoo = function(collection, hash, saveSkin, entity)
    for _, v in pairs(Base.Skin.currSkin.tattoos) do if (v.collection == collection and v.hash == hash) then return end end
    table.insert(Base.Skin.currSkin.tattoos, { collection = collection, hash = hash })
    Base.Skin.applyTattoos(Base.Skin.currSkin.tattoos, saveSkin, entity)
    if saveSkin then Base.Skin.saveCharSkin() end
end

Base.Skin.removeTattoo = function(collection, hash, saveSkin, entity)
    for i, v in pairs(Base.Skin.currSkin.tattoos) do
        if (v.collection == collection and v.hash == hash) then
            table.remove(Base.Skin.currSkin.tattoos, i)
            Base.Skin.applyTattoos(Base.Skin.currSkin.tattoos, saveSkin, entity)
            if saveSkin then Base.Skin.saveCharSkin() end
            return
        end
    end
end

Base.Skin.setModel = function(model, saveSkin, entity)
    if model ~= Base.Skin.currSkin.model then
        Base.Skin.currSkin.model = model
        Base.Skin.setSkin(Base.Skin.currSkin, saveSkin, entity)
    end
end

Base.Skin.setMaskFace = function(toggle, _maskId, entity)
    local ped = entity or PlayerPedId()
    local model = GetEntityModel(ped)
    local skin = Base.Skin.charSkin.skin
    local clothing = Base.Skin.charSkin.clothing
    local maskId = (_maskId and _maskId or clothing.mask.model)
    local applyFace = (MASK_MORPH_BLACKLIST[model][maskId] or (model == GetHashKey("mp_m_freemode_01") and maskId > 156) or (model == GetHashKey("mp_f_freemode_01") and maskId > 149))

    if skin == nil or (clothing == nil and _maskId == nil) then
        return
    end

    if (toggle or applyFace) then
        Base.Skin.applySkin(skin, false, PlayerPedId(), true)
    else
        if (model == GetHashKey("mp_m_freemode_01")) then
            SetPedHeadBlendData(ped, 0, 0, 0, skin.colour, skin.colour, 0.0, 0.0, 0.5, 0.0, true)
        elseif (model == GetHashKey("mp_f_freemode_01")) then
            SetPedHeadBlendData(ped, 21, 0, 0, skin.colour, skin.colour, 0.0, 0.0, 0.5, 0.0, true)
        end
        for i = 0, 20, 1 do SetPedFaceFeature(ped, i, 0.0) end
    end
end

Base.Skin.toggleItem = function(item, state)
    local PlayerData = Base.GetPlayerData()
    toggle = nil
    if item == "shirt" then
        if state == nil then
            if hiddenClothing.torso then
                toggle = false
            else
                toggle = true
            end
        else
            toggle = not state
        end

        hiddenClothing.torso = toggle
        hiddenClothing.tshirt = toggle
        -- hiddenClothing.chain = toggle
        --hiddenClothing.decals = toggle
        if (hiddenClothing.arms == nil) then
            hiddenClothing.arms = false
        end

        -- print("[scripting/skin] Toggling clothing item: shirt. State: ",toggle)
        if toggle then
            itemState = "off"
            colour = "red"
        else
            itemState = "on"
            colour = "green"
        end
        GeneralLog("Accessories Menu", PlayerData.logName .. " has toggled shirt | State: " .. itemState, colour, "accessories-menu")

    elseif CLOTHING_TOGGLES[Base.Skin.currSkin.sex][item] then
        if state == nil then
            if hiddenClothing[item] then
                toggle = false
            else
                toggle = true
            end
        else
            toggle = not state
        end

        -- this handles if the prop has been knocked off
        if (toggle) then
            local index, pType, default = Base.Skin.indexes.clothing[item].index, Base.Skin.indexes.clothing[item].type, CLOTHING_TOGGLES[Base.Skin.currSkin.sex][item]
            if (pType == "prop") then
                local propIndex = GetPedPropIndex(PlayerPedId(), index)
                if (propIndex == default or propIndex == -1) then
                    toggle = false
                end
            end
        end

        hiddenClothing[item] = toggle
        if toggle then
            itemState = "off"
            colour = "red"
        else
            itemState = "on"
            colour = "green"
        end

        if (item == "arms") then
            local clothing = Base.Skin.getCurrentSkin().clothing
            if (clothing and clothing.arms.model > 15) then
                TriggerServerEvent("scripting:skin:updateState", "gloves", not toggle)
            end
        end

        -- print("[scripting/skin] Toggling clothing item: "..item..". State: ",toggle)
        GeneralLog("Accessories Menu", PlayerData.logName .. " has toggled " .. item .. "| State: " .. itemState, colour, "accessories-menu")
    end

    Base.Skin.applyClothing(Base.Skin.getCurrentSkin().clothing, false)
    return not toggle
end

Base.Skin.isItemHidden = function(item)
    if item == "shirt" then item = "torso" end
    return hiddenClothing[item]
end

--
RegisterNetEvent("base_scripting:setSkin")
AddEventHandler("base_scripting:setSkin", function(skin, save)
    Base.Skin.setSkin(skin, save)
end)
