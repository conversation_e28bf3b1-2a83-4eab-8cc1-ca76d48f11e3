--[====[
    @filename Player (Client)
    @filebrief Centralized player control library
--]====]


function AddPlayerSpeedBuff(identifier, increasePercent)
    if not increasePercent then increasePercent = 0 end
    exports["base"]:addPlayerSpeedBuff(GetCurrentResourceName(), identifier, increasePercent)
end

function RemoveSpeedBuff(identifier)
    exports["base"]:removeSpeedBuff(GetCurrentResourceName(), identifier)
end

-- AddStaminaConstraint(identifier, maxValue)

-- RemoveStaminaConstraint(identifier)

---Sets player proofs given a unique identifier. This allows for multiple scripts / functions to set/remove player proofs.
---@param identifier string Identifier used to set multiple proofs
---@param playerProofs table Table value for player proof
function SetPlayerProofs(identifier, playerProofs)
    exports["base"]:setPlayerProofs(GetCurrentResourceName(), identifier, playerProofs)
end

local healthBuffs = {}

function SetPlayerMaxHealthBuff(identifier, increaseAmount)
    healthBuffs[identifier] = true
    if not increaseAmount then increaseAmount = 0 end
    exports["base_player"]:setHealthModifier(identifier, increaseAmount)
end

function RemoveMaxHealthBuff(identifier)
    healthBuffs[identifier] = nil
    exports["base_player"]:setHealthModifier(identifier, nil)
end

AddEventHandler("onClientResourceStop", function(resource)
    if resource == GetCurrentResourceName() then
        for k, v in pairs(healthBuffs) do
            RemoveMaxHealthBuff(k)
        end
    end
end)

---Sets the maximum amount the player will heal to. The maximum buff value is used.
---@param identifier string Identifier used to set multiple buffs
---@param amount number Value between 0.0 and 1.0 representing the health percentage
function SetPlayerHealthRegenCap(identifier, amount)
    if not amount then return end
    exports["base"]:setPlayerHealthRegenCap(GetCurrentResourceName(), identifier, amount)
end

---Sets the health recharge multiplier, a value between 0.0 and 1.0.
---@param identifier string Identifier used to set multiple buffs
---@param amount number Value between 0.0 and 1.0 representing the amount healed
function SetPlayerHealthRegenBuff(identifier, amount)
    if not amount then return end
    exports["base"]:setPlayerHealthRegenBuff(GetCurrentResourceName(), identifier, amount)
end

--[[
    @type func
    @name BlockInfiniteStamina(state)
    @param state: bool - Whether to block infinite stamina
    @brief Blocks the player from having infinite stamina
    @description [
        Test
    ]
]]
--

---Blocks infinite stamina given a unique identifier. This allows for multiple scripts / functions to block stamina without affecting each other.
---@type function
---@param identifier string Identifier which is used to block stamina. The specific identifier must be re-used to unblock
---@param state boolean Toggles whether infinite stamina is being blocked by this script
function BlockInfiniteStamina(identifier, state)
    exports["base"]:blockInfiniteStamina(GetCurrentResourceName(), identifier, state)
end

---@class vector3
---@field x number
---@field y number
---@field z number

---@class TeleportOptions
---@field allowVehicle? boolean
---@field instance? string

---Teleports the player to coords with advanced options for more
---@param coords vector3 Destination coords
---@param heading number Direction the entity will be facing
---@param options TeleportOptions? Various settings affecting the teleport (detailed below)
function TeleportPlayerToCoords(coords, heading, options)
    --vlog("[TP] Beginning teleport.", coords, heading)

    -- Parameter Cleaning
    coords = vector3(coords.x, coords.y, coords.z)
    heading = heading or 0.0
    options = options or {}

    DoScreenFadeOut(500)
    while (not IsScreenFadedOut()) do Wait(0) end
    NetworkFadeOutEntity(PlayerPedId(), false, true)
    Wait(100)

    -- Select the entity to teleport
    local entity = PlayerPedId()
    if IsPedInAnyVehicle(entity, false) then
        --vlog("[TP] Player is in vehicle")

        if options.allowVehicle then
            entity = GetVehiclePedIsIn(entity, false)
            --vlog("[TP] Setting teleport entity to ", entity)
        else
            SendNotification("You can't do this in a car")
            --vlog("[TP] Teleport blocked due to vehicle")
            return
        end
    end

    SetEntityVisible(PlayerPedId(), false, 0)
    SetEntityVisible(entity, false, 0)

    SetEntityCoords(entity, coords, 0.0, 0.0, 0.0, false)
    FreezeEntityPosition(entity, true)
    SetEntityHeading(entity, heading or 0.0)
    ClearAreaOfPeds(coords, 10.0, 1)

    RequestCollisionAtCoord(coords)
    while (not HasCollisionLoadedAroundEntity(entity)) do
        Wait(10)
    end

    FreezeEntityPosition(entity, false)
    SetGameplayCamRelativeHeading(0.0)
    SetGameplayCamRelativePitch(0.0, 0.0)

    SetEntityVisible(PlayerPedId(), true)
    NetworkFadeInEntity(PlayerPedId(), true)
    SetEntityVisible(entity, true)
    NetworkFadeInEntity(entity, true)

    Wait(500)
    DoScreenFadeIn(500)
end
