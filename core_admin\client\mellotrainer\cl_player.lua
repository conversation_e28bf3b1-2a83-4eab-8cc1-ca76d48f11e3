--[[--------------------------------------------------------------------------
	*
	* <PERSON><PERSON> Trainer
	* (C) <PERSON> 2017
	* http://github.com/thestonedturtle/mellotrainer/releases
	*
	* This menu used the Scorpion Trainer as a framework to build off of.
	* https://github.com/pongo1231/ScorpionTrainer
	* (C) Emre Cürgül 2017
	*
	* A lot of useful functionality has been converted from the lambda menu.
	* https://lambda.menu
	* (C) Oui 2017
	*
	* Additional Contributors:
	* WolfKnight (https://forum.fivem.net/u/WolfKnight)
	*
---------------------------------------------------------------------------]]



RegisterNUICallback("player", function(data, cb)
	local name  = GetPlayerName(PlayerId())
	local playerPed = GetPlayerPed(-1)
	local action = data.action
	local newstate = data.newstate

	local text = "OFF"
	if(newstate) then
		text = "ON"
	end


	-- Heal Player
	if action == "heal" then

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name..' healed to 100', 'purple', 'admin-functions')

		SetEntityHealth(playerPed, 200)
		ClearPedBloodDamage(playerPed)
		ClearPedWetness(playerPed)

		if(featureKeepWet)then -- Keep Wet Check.
			SetPedWetnessHeight(playerPed, 2.0)
		end
		drawNotification("~g~Cleaned & Healed.")

	-- Body Armor
	elseif action == "armor" then

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name..' set armour to 100', 'purple', 'admin-functions')

		SetPedArmour(playerPed, 100)
		drawNotification("~g~Armor Added.")

	-- Suicide
	elseif action == "suicide" then
		SetEntityHealth(playerPed, 0)
		drawNotification("~r~You Committed Suicide.")

	-- God Mode/Invincibility
	elseif action == "god" then

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name..' has toggled godmode ['..text..']', 'red', 'admin-functions')

		featurePlayerInvincible = newstate
		drawNotification("God Mode: "..tostring(text))

		featurePlayerInvincibleUpdated = true;

	-- Infinite Stamina
	elseif action == "stamina" then
		featurePlayerInfiniteStamina = newstate
		drawNotification("Unlimited Stamina: "..tostring(text))

	-- Drunk Mode
	elseif action == "drunk" then
		featurePlayerDrunk = newstate
		drawNotification("Drunk Mode: "..tostring(text))

		featurePlayerDrunkUpdated = true;


	-- Invsibility Toggle
	elseif action == "invisible" then

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name..' has toggled Invisibility ['..text..']', 'purple', 'admin-functions')

		featurePlayerInvisible = newstate
		drawNotification("Invisibility: "..tostring(text))

		featurePlayerInvisibleUpdated = true;


	-- Silent Mode
	elseif action == "silent" then
		featurePlayerNoNoise = newstate

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name..' has toggled silent mode ['..text..']', 'orange', 'admin-functions')

		drawNotification("Silent Mode: "..tostring(text))

		featurePlayerNoNoiseUpdated = true;


	-- Everyone Ignores you
	elseif action == "everyone" then
		featurePlayerIgnoredByAll = newstate
		drawNotification("Everyone Ignores You: "..tostring(text))

		featurePlayerIgnoredByAllUpdated = true;


	-- Police Ignore You
	elseif action == "police" then
		featurePlayerIgnoredByPolice = newstate
		drawNotification("Police Ignore You: "..tostring(text))

		featurePlayerIgnoredByPoliceUpdated = true;


	-- Never Wanted
	elseif action == "wanted" then
		featurePlayerNeverWanted = newstate
		drawNotification("Never Wanted: "..tostring(text))

		featurePlayerNeverWantedUpdated = true;

	elseif action == "keepwet" then
		featureKeepWet = newstate
		featureKeepWetUpdated = true;
		drawNotification("Keep Wet: "..tostring(text))

	elseif action == "fastswim" then

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name..' has toggled fastswim ['..text..']', 'orange', 'admin-functions')

		featurePlayerFastSwim = newstate
		featurePlayerFastSwimUpdated = true;
		drawNotification("Fast Swim: "..tostring(text))

	elseif action == "fastrun" then

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name..' has toggled fastrun ['..text..']', 'orange', 'admin-functions')

		featurePlayerFastRun = newstate
		featurePlayerFastRunUpdated = true;
		drawNotification("Fast Run: "..tostring(text))

	elseif action == "superjump" then

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name..' has toggled superjump ['..text..']', 'red', 'admin-functions')

		featurePlayerSuperJump = newstate

		drawNotification("Super Jump: "..tostring(text))

	elseif action == "noragdoll" then

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name..' has toggled no ragdoll ['..text..']', 'orange', 'admin-functions')

		featureNoRagDoll = newstate
		featureNoRagDollUpdated = true
		drawNotification("No Ragdoll: "..tostring(text))

	elseif action == "nightvision" then

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name..' has toggled night vision ['..text..']', 'orange', 'admin-functions')

		featureNightVision = newstate
		featureNightVisionUpdated = true;
		drawNotification("Night Vision: "..tostring(text))

	elseif action == "thermalvision" then

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name..' has toggled thermal vision ['..text..']', 'orange', 'admin-functions')

		featureThermalVision = newstate
		featureThermalVisionUpdated = true;
		drawNotification("Thermal Vision: "..tostring(text))

	elseif action == "keepclean" then
		featureKeepClean = newstate
		drawNotification("Keep Clean: "..tostring(text))
	elseif action == "restoreappearance"then
		featureRestoreAppearance = newstate
		drawNotification("Restore Appearance: "..tostring(text))
	end

	if(cb)then cb("ok") end
end)



onBaseReady(function()
	while true do
		Wait(1)

		local playerPed = GetPlayerPed(-1)
		local playerID = PlayerId()
		if playerPed then


			-- Keep Wet
			if(featureKeepWet and featureKeepWetUpdated) then
				SetPedWetnessHeight(playerPed, 2.0)
				featureKeepWetUpdated = false;
			end


			-- Fast Swim
			if(featurePlayerFastSwimUpdated)then
				if(featurePlayerFastSwim)then
					SetSwimMultiplierForPlayer(playerID, 1.49)
				else
					SetSwimMultiplierForPlayer(playerID, 1.0)
				end
				featurePlayerFastSwimUpdated = false
			end


			-- Fast Run
			if(featurePlayerFastRunUpdated)then
				if(featurePlayerFastRun)then
					SetRunSprintMultiplierForPlayer(playerID, 1.49)
				else
					SetRunSprintMultiplierForPlayer(playerID, 1.0)
				end
				featurePlayerFastRunUpdated = false
			end


			-- Super Jump
			if (featurePlayerSuperJump)then
				SetSuperJumpThisFrame(playerID)
			end


			-- No Ragdoll
			if ( featureNoRagDoll ) then
				SetPedCanRagdoll( playerPed, false )
			else
				SetPedCanRagdoll( playerPed, true )
			end


			-- Night Vision
			if(featureNightVisionUpdated)then
				SetNightvision(featureNightVision)

				featureNightVisionUpdated = false
			end


			-- Thermal Vision
			if (featureThermalVisionUpdated)then
				SetSeethrough(featureThermalVision)

				featureThermalVisionUpdated = false
			end

			SetEntityInvincible( playerPed, featurePlayerInvincible )

			-- Keep Clean
			if featureKeepClean then
				ClearPedBloodDamage(playerPed)
			end


			-- Drunk
			if ( featurePlayerDrunkUpdated ) then
				SetPedIsDrunk( playerPed, drunk )

				if ( featurePlayerDrunk ) then
					_LoadClipSet( "move_m@drunk@verydrunk" )

					SetPedMovementClipset( playerPed, "move_m@drunk@verydrunk", 1.0 )
					ShakeGameplayCam( "DRUNK_SHAKE", 1.0 )
				else
					ResetPedMovementClipset( playerPed, 1.0 )
					StopGameplayCamShaking( true )
					RemoveClipSet( "move_m@drunk@verydrunk" )
				end

				featurePlayerDrunkUpdated = false;
			end


			-- Invisibility
			if(featurePlayerInvisibleUpdated)then
				if featurePlayerInvisible then
					Base.Player.setEntityVisible(false)
					TriggerEvent("core_admin:toggleInvis")
				else
					Base.Player.setEntityVisible(true)
					TriggerEvent("core_admin:toggleInvis")
					TriggerEvent("admin:overhead:macro", false)
				end
				featurePlayerInvisibleUpdated = false;
			end


		end
	end
end)