local elevators = {}

local activeMarkers = {}

-- Event listener to update the list of elevators
RegisterDataSyncHandler("elevatorList", function(data)
	print("Loading elevators", data, #data, activeMarkers, #activeMarkers)
	for _, marker in ipairs(activeMarkers) do
		RemoveInteractableMarker(marker)
	end

	print('a')

	activeMarkers = {}

	-- Loop through the data from the server
	for _, elevator in ipairs(data) do
		local coords = (elevator.enterCoords)
		coords = ParseVector3(coords)
		elevator.enterCoords = coords
		local maxDistance = 2.0


		local marker = CreateInteractableMarker(coords, function()
			-- Open the elevator menu
			if not (CheckDistance(coords, maxDistance)) then
				TriggerEvent("fdg_ui:SendNotification", "You are too far away from the elevator")
				return
			end
			openElevatorMenu(elevator)
		end, { text = elevator.name }, { instance = elevator.instance })

		table.insert(activeMarkers, marker)
	end


	elevators = data or {}
end)


function CheckDistance(coords, maxDistance)
	local playerCoords = GetEntityCoords(PlayerPedId())
	local distance = #(playerCoords - coords)
	if (distance > maxDistance) then
		return false
	end
	return true
end

function ParseVector3(vectorString)
	-- Remove the "vector3" prefix and the parentheses
	local coords = string.sub(vectorString, 9, -2)

	-- Split the string into x, y, and z components
	local x, y, z = string.match(coords, "([^,]+),([^,]+),([^,]+)")

	-- Convert the components from strings to numbers (floats)
	x = tonumber(x)
	y = tonumber(y)
	z = tonumber(z)

	-- Return the coordinates as a vector3 object
	return vector3(x, y, z)
end

-- Event listener to teleport the player to the specified coordinates
RegisterNetEvent("teleportToLocation")
AddEventHandler("teleportToLocation", function(coords, heading, options)
	local player = PlayerId()


	--Notify player of teleport

	TeleportPlayerToCoords(coords, heading, options)
end)


-- d is current item, m is menu
function openElevatorMenu(selectedElevator)
	local elevatorList = {}

	-- Check if player is in vehicle, if so, return
	if IsPedInAnyVehicle(PlayerPedId(), false) then
		TriggerEvent("fdg_ui:SendNotification", "You cannot use this while in a vehicle")
		return
	end

	for i, elevator in ipairs(elevators) do
		if (elevator.enabled) and (elevator.currentElevator == selectedElevator.currentElevator) then
			table.insert(elevatorList,
				{
					label = elevator.enterLabel,
					id = elevator.name,
					enterCoords = elevator.enterCoords,
					heading = elevator.heading,
					options = {},
					restrictedJob =
						elevator.restrictedJob,
					restrictedItem = elevator.restrictedItem,
					instance = elevator.instance
				})
		end
	end



	Base.UI.Menu.Open(
		'default', GetCurrentResourceName(), 'elevator_menu',
		{
			title    = 'Elevator Select',
			align    = 'bottom-right',
			elements = elevatorList,
		},
		function(d, m)
			m.close()
			local hasAccess = false
			local teleportDuration = 3

			--Get players job and grade
			local PlayerData = Base.GetPlayerData()
			local allowedJobs = {}
			local allowedItems = {}

			allowedJobs = json.decode(d.current.restrictedJob)
			allowedItems = json.decode(d.current.restrictedItem) or {}

			for _, job in ipairs(allowedJobs) do
				if DoesPlayerHaveOrg(job) then
					hasAccess = true
					print("job access")
					break
				end
			end

			-- Items
			local accessItems = {}
			for i, item in ipairs(allowedItems) do
				accessItems[item] = true
			end

			for i, item in ipairs(PlayerData.inventory) do
				if accessItems[item.name] and item.count > 0 then
					hasAccess = true
					break
				end
			end

			if #allowedJobs == 0 and #allowedItems == 0 then
				hasAccess = true
			end

			if not hasAccess then
				TriggerEvent("fdg_ui:SendNotification", "You do not have access to this elevator")
				print("no item!")
				return
			end


			-- Trigger the event
			exports["main_progressbar"]:start(teleportDuration * 1000, "Travelling...")
			Wait((teleportDuration) * 1000)

			-- Check if player is nearby at time of teleport

			if not (CheckDistance(selectedElevator.enterCoords, 200.0)) then
				TriggerEvent("fdg_ui:SendNotification", "If the elevator is appearing from a large distance, you should refly.")
				GeneralLog("Anticheat", PlayerData.logName .. " triggered 'Elevator TP' from an unrealistic distance.", "red", "anticheat", PlayerData)
				exports["main_progressbar"]:stop()
				return
			elseif not (CheckDistance(selectedElevator.enterCoords, 2.0)) then
				TriggerEvent("fdg_ui:SendNotification", "You are too far away from the elevator")
				GeneralLog("ELEVATOR", "Player " .. PlayerData.logName .. " failed to catch the elevator " .. d.current.label .. " as they were too far away.",
					"green", "transport")
				exports["main_progressbar"]:stop()
				return
			elseif IsPedBeingStunned(GetPlayerPed(-1), 0) or IsPedCuffed(GetPlayerPed(-1)) then
				TriggerEvent('fdg_ui:SendNotification', '<font color="red">You fail to catch the elevator...</font>', { layout = 'bottom-right' })
				GeneralLog("ELEVATOR", "Player " .. PlayerData.logName .. " failed to catch the elevator " .. d.current.label, "green", "transport")
				exports["main_progressbar"]:stop()
				return
			elseif isDead then
				TriggerEvent('fdg_ui:SendNotification', '<font color="red">You fail to catch the elevator...</font>', { layout = 'bottom-right' })
				GeneralLog("ELEVATOR", "Player " .. PlayerData.logName .. " attempted to catch the elevator " .. d.current.label .. " while dead.", "green",
					"transport")
				exports["main_progressbar"]:stop()
				return
			end
			GeneralLog("ELEVATOR", "Player " .. PlayerData.logName .. " used elevator " .. d.current.label, "green", "transport")

			TriggerEvent("teleportToLocation", d.current.enterCoords, d.current.heading, d.current.options)
			TriggerServerEvent("elevator:useElevator", d.current.instance)

			exports["main_progressbar"]:stop()
		end,
		function(d, m)
			m.close()
		end
	)
end
