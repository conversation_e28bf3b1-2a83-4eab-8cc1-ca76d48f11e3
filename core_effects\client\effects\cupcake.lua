local cupcakeEffect = TimedStatusEffect(StatusEffect("high", "High",
    {
        onStart = function(effect)
            TriggerServerEvent("main_crime_drugs:addHigh")
        end,

        onTick = function(effect)
        end,

        onStop = function(effect)
            TriggerServerEvent("main_crime_drugs:removeHigh")
        end,
    },
    {
        alert = false,
        desc = "You're feeling high!",
        subdesc = "Extra XP for drug deals"
    }
), { maxTime = 30 * 60 })

RegisterNetEvent("core_effects:cupcakeEffect")
AddEventHandler("core_effects:cupcakeEffect", function()
    ClearPedTasks(PlayerPedId())
    Wait(100)

    cupcakeEffect.addTime(600) -- 10 minutes
end)