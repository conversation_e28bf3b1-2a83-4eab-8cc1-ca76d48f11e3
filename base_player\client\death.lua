-- implement player death functionality here
-- Terms:
-- - Knockout: The player is knocked out and can be revived by another player OR after 60 seconds
-- - Minor: The player is conscious, but cannot move. They can (Respawn after 5 minutes with their items) OR (Be stabilised by E<PERSON> in the field) OR (Be taken to hospital by a friend)
-- - Severe: The player is unconscious. They can (Respawn after 5 minutes without their items) OR (Be stabilised by EMS and taken to hospital, allowing respawn with items after 60 seconds)

local healthModifiers = {}
local healthModifier = 0

local function setHealthModifier(identifier, amount)
    healthModifiers[identifier] = amount
    local total = 0
    for _, v in pairs(healthModifiers) do
        total = total + v
    end
    healthModifier = total
    SetPedMaxHealth(PlayerPedId(), 200 + healthModifier)

    if GetEntityHealth(PlayerPedId()) > 200 + healthModifier then
        SetEntityHealth(PlayerPedId(), 200 + healthModifier)
    end
end
exports("setHealthModifier", setHealthModifier)

local lastWound

local function tableEqual(a, b)
    for k, v in pairs(a) do
        if type(v) == "table" then
            if type(b[k]) ~= "table" or not tableEqual(v, b[k]) then
                return false
            end
        elseif b[k] ~= v then
            return false
        end
    end
    return true
end

local function getPlayerAliveStatus()
    -- Loop through each wound and calculate zone damage
    local zones = table.clone(zoneHP)
    local worstSeverity = 0

    local woundStats = {
        gunshots = {},
        stabWounds = {},
        bluntTrauma = {},
        zones = {},
        worstSeverity = worstSeverity,
        burns = false,
        gas = false,
        impact = false,
        drown = false,
    }

    -- Detect gunshots and stab wounds
    for _, wound in ipairs(wounds) do
        worstSeverity = math.max(worstSeverity, wound.severity)

        -- Count types of damage wounds
        if wound.boneLabel then
            local area = wound.boneLabel

            if string.sub(wound.weaponType, 1, 4) == "GSW_" then
                woundStats.gunshots[area] = (woundStats.gunshots[area] or 0) + 1
            elseif wound.weaponType == "MELEE_STAB" then
                woundStats.stabWounds[area] = (woundStats.stabWounds[area] or 0) + 1
            elseif wound.weaponType == "MELEE_BLUNT" then
                woundStats.bluntTrauma[area] = (woundStats.bluntTrauma[area] or 0) + 1
            end
        end

        if wound.zone then
            local zone = wound.zone

            local damage = WEAPON_TYPE_DAMAGE[wound.weaponType .. "_" .. wound.zone] or WEAPON_TYPE_DAMAGE[wound.weaponType] or 0
            if zone and damage > 0 then
                zones[zone] = zones[zone] - damage
            end
        end

        if wound.weaponType == "ENV_BURNS" then
            woundStats.burns = wound.severity or false
        elseif wound.weaponType == "ENV_GAS" then
            woundStats.gas = wound.severity or true
        elseif wound.weaponType == "ENV_FALL" then
            woundStats.impact = math.max(woundStats.impact or 0, wound.severity or 0)
        elseif wound.weaponType == "ENV_VEHICLE" then
            woundStats.impact = math.max(woundStats.impact or 0, wound.severity or 0)
        elseif wound.weaponType == "ENV_DROWNING" then
            woundStats.drown = math.max(woundStats.drown or 0, wound.severity or 0)
        elseif wound.weaponType == "BITE" then
            woundStats.bites = (woundStats.bites or 0) + 1
        end
    end

    woundStats.worstSeverity = worstSeverity
    woundStats.zones = zones
    woundStats.lastWound = lastWound

    if not LocalPlayer.state.woundStats or not tableEqual(LocalPlayer.state.woundStats, woundStats) then
        LocalPlayer.state:set("woundStats", woundStats, true)
    end

    -- Loop through each zone, if any are below 0 then the player is dead
    for _, hp in pairs(zones) do
        if hp <= 0 then
            return "dead"
        end
    end

    if worstSeverity == 1 then
        return "knockedout"
    elseif worstSeverity == 2 then
        return "minor"
    elseif worstSeverity == 3 then
        return "severe"
    elseif worstSeverity == 4 then
        return "dead"
    else
        return "unk"
    end
end

local function getSeatPedIsIn(ped)
    local veh = GetVehiclePedIsIn(ped, false)
    if not veh then
        return nil
    end

    local seat = GetPedInVehicleSeat(veh, -1)
    if seat == ped then
        return -1
    end

    for i = 0, GetVehicleMaxNumberOfPassengers(veh) do
        if GetPedInVehicleSeat(veh, i) == ped then
            return i
        end
    end

    return nil
end

-- Will wait before respawning the player until they have stoppped or other conditions are met
-- Returns any of the details which may be relevant to the respawn
local function awaitDeathStart()
    while true do
        Wait(100)
        local ped = PlayerPedId()

        -- Start death
        local veh = GetVehiclePedIsIn(ped, false)
        if DoesEntityExist(veh) then
            local seat = getSeatPedIsIn(ped)
            return { inVehicle = veh, seat = seat }
        end

        if GetEntitySpeed(ped) < 0.2 then
            return {}
        end
    end
end

local awaitingWound = false

---Sets the player as wounded (ragdoll) and handles the logic for it
---@param wounded boolean Whether the player should be wounded or not
---@param options { forceRespawn: boolean, retainWounds: boolean, respawnHealth: number } -- various options relating to respawn
function setPlayerAsWounded(wounded, options)
    if not options then options = {} end
    local playerState = LocalPlayer.state
    local ped = PlayerPedId()

    playerState:set('wounded', wounded, true)
    playerState:set("stabilised", nil, true)
    playerState:set("bodybagged", nil, true)
    playerState:set("treatments", nil, true)
    playerState:set("treatmentMedics", nil, true)

    if wounded then
        if awaitingWound then
            return
        end

        awaitingWound = true
        local respawnDetails = awaitDeathStart()
        local coords = GetEntityCoords(ped)

        -- Respawn the player so they keep taking damage
        NetworkResurrectLocalPlayer(coords.x, coords.y, coords.z, 0.0, true, false)
        if respawnDetails.inVehicle then
            SetPedIntoVehicle(PlayerPedId(), respawnDetails.inVehicle, respawnDetails.seat)
        end

        -- Set the max health
        SetPedMaxHealth(PlayerPedId(), 10000)
        SetEntityHealth(PlayerPedId(), 10000)

        -- Record the time the player was wounded
        playerState:set("woundedTime", GetGameTimer())
        TriggerEvent("playerWounded")
        TriggerServerEvent("playerWounded")
        awaitingWound = false
    else
        Base.Player.blockActions(false)
        EnableControlAction(1, 24, true)

        if options.forceRespawn then
            local coords = GetEntityCoords(ped)
            NetworkResurrectLocalPlayer(coords.x, coords.y, coords.z, 0.0, true, false)
        end

        -- Reset the max health
        SetPedMaxHealth(ped, 200 + healthModifier)
        SetEntityHealth(ped, options.respawnHealth or math.max(GetPedMaxHealth(ped), 200))

        -- Reset flags
        playerState:set("woundedSeverity", nil, true)
        playerState:set("examined", false, true)
        playerState:set("dead", false, true)
        playerState:set("unconscious", false, true)
        playerState:set("hospitalised", false, true)
        if not options.retainWounds then
            wounds = {}
        end

        -- Facial animation
        ClearFacialIdleAnimOverride(PlayerPedId())

        -- Backwards compat
        Base.Player.setEntityVisible(true)
        FreezeEntityPosition(ped, false)
        TriggerServerEvent('core_citizen:setDeathStatus', false)
        TriggerEvent("playerRevived")
        TriggerServerEvent("playerRevived")
        TriggerServerEvent("main_dispatch:ResolveCall", "lses", "unconcious_" .. GetPlayerServerId(PlayerId()), "DISPATCH", true)
    end
end

-- loop to handle checking injuries and updating the players alive state
local function startPlayerAliveStateThread()
    while LocalPlayer.state.wounded do
        local aliveState = getPlayerAliveStatus()
        LocalPlayer.state:set("woundedSeverity", aliveState, true)

        -- State flags
        if aliveState == "dead" then
            LocalPlayer.state:set("dead", true, true)
        end

        if aliveState == "dead" or aliveState == "severe" or aliveState == "knockedout" then
            LocalPlayer.state:set("unconscious", true, true)
            SetFacialIdleAnimOverride(PlayerPedId(), "dead_1", 0)
        else
            SetFacialIdleAnimOverride(PlayerPedId(), "mood_injured_1", 0)
        end

        if aliveState == "knockedout" then
            LocalPlayer.state:set("knockedout", true, true)
        end

        Wait(1000)
    end
end

-- Loop which will ensure the latest wound is a knockout, otherwise it will set them as fully wounded
local function startPlayerKnockoutStateThread()
    LocalPlayer.state:set("woundedSeverity", "knockedout", true)
    while LocalPlayer.state.wounded do
        local lastWound = wounds[#wounds]

        if lastWound.severity ~= 1 then
            startPlayerAliveStateThread()
            return
        end

        local timeElapsed = GetGameTimer() - LocalPlayer.state.woundedTime
        if timeElapsed > (KNOCKOUT_TIME * 1000) then
            setPlayerAsWounded(false, { respawnHealth = 110, retainWounds = true })
        end

        SetFacialIdleAnimOverride(PlayerPedId(), "dead_1", 0)

        Wait(1000)
    end
end

-- entry point for a player dying
function handlePlayerDeath(_lastWound)
    lastWound = _lastWound
    -- set the player as wounded to start ragdoll and other logic
    setPlayerAsWounded(true)

    -- calculate how wounded the player is for respawn check and UI
    if lastWound.severity == 1 then
        startPlayerKnockoutStateThread()
    else
        startPlayerAliveStateThread()
    end
end

local vehicleDeathAnims = {
    default = {
        driver = {
            dict = "veh@std@ds@base",
            name = "die",
        },
        passenger = {
            dict = "veh@std@ps@base",
            name = "die",
        },
    }
}

local function handleDeadAnim()
    local playerPed = PlayerPedId()
    local veh = GetVehiclePedIsIn(playerPed, false)
    if DoesEntityExist(veh) then
        local isDriver = GetPedInVehicleSeat(veh, -1) == playerPed

        local animGroup = vehicleDeathAnims.default
        local anim = animGroup[isDriver and "driver" or "passenger"]

        while not HasAnimDictLoaded(anim.dict) do
            RequestAnimDict(anim.dict)
            Wait(0)
        end

        local inAnim = IsEntityPlayingAnim(playerPed, anim.dict, anim.name, 3)
        if not inAnim then
            TaskPlayAnim(playerPed, anim.dict, anim.name, 8.0, 1.0, -1, 2, 0, false, false, false)
        end
    end
end

CreateThread(function()
    Wait(500)
    while true do
        Wait(0)
        local state = LocalPlayer.state
        local playerPed = PlayerPedId()
        local isMuted = state.forceMute

        -- Ragdoll the player if they are wounded
        if state.wounded then
            ClearPedSecondaryTask(playerPed)
            SetPedToRagdoll(playerPed, 100, 100, 0, false, false, false)
            ResetPedRagdollTimer(playerPed)
            SetPlayerCanDoDriveBy(PlayerId(), false)

            local canPTT = state.unconscious and not state.stabilised
            DisablePlayerControls({ general = true, sprint = true, vehicle = true, voice = canPTT or state.dead })

            if canPTT or state.dead then
                state:set("forceMute", true, true)
            else
                state:set("forceMute", false, true)
            end
            handleDeadAnim()
        elseif isMuted then
            state:set("forceMute", false, true)
        end
    end
end)

CreateThread(function()
    Wait(500)
    while true do
        Wait(100)
        local state = LocalPlayer.state
        local playerPed = PlayerPedId()

        -- Ragdoll the player if they are wounded
        if state.wounded then
            SetEntityHealth(playerPed, 10000)
            handleDeadAnim()
        elseif not state.wounded and GetEntityHealth(playerPed) > 500 then
            SetEntityHealth(playerPed, 110)
        end
    end
end)

CreateThread(function()
    Wait(1000)
    setPlayerAsWounded(false)
end)


--#region Respawning Logic

timeUntilRespawn = RESPAWN_TIMER

local function getNearestHospital()
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local nearestHospital, secondNearestHospital = nil, nil
    local nearestDistance, secondNearestDistance = math.huge, math.huge

    for _, hospital in ipairs(HOSPITALS) do
        local distance = #(coords - hospital.coords)
        if distance < nearestDistance then
            secondNearestHospital, secondNearestDistance = nearestHospital, nearestDistance
            nearestHospital, nearestDistance = hospital, distance
        elseif distance < secondNearestDistance then
            secondNearestHospital, secondNearestDistance = hospital, distance
        end
    end

    if nearestDistance < 500 and secondNearestHospital then -- set nearest hospital to second nearest if it is within 500m
        nearestHospital = secondNearestHospital
    end

    if not nearestHospital then
        error("No hospitals found")
    end

    return nearestHospital
end

local function respawnPlayerAtHospital(options)
    options = options or {}

    local ped = PlayerPedId()
    SwitchOutPlayer(ped, 0, 2)
    local hospital = getNearestHospital()
    Base.Player.setEntityVisible(false)
    FreezeEntityPosition(ped, true)
    TriggerEvent("playerRespawned")
    TriggerServerEvent("playerRespawned")
    Citizen.Wait(1000)

    -- Teleport to hospital before inventory for accurate log location
    SetEntityCoords(ped, hospital.coords.x, hospital.coords.y, hospital.coords.z, false, false, false, false)
    SetEntityHeading(ped, hospital.heading)

    -- Inventory clear
    if options.forceWipe or (LocalPlayer.state.woundedSeverity ~= "knockedout" and LocalPlayer.state.woundedSeverity ~= "minor") then
        RemoveAllPedWeapons(ped, true)
        TriggerServerEvent("player:clearInventory")
    end

    -- Reset player state
    LocalPlayer.state:set("disarmed", false, true)
    LocalPlayer.state:set("cuff", false, true)
    TriggerEvent("core_vehicles:clearKeys")
    TriggerServerEvent("instance:leaveInstance")
    TriggerEvent("core_hud:disableGPS")
    Base.Player.detachPlayer()
    Base.UI.Menu.CloseAll()
    setPlayerAsWounded(false)

    while not HasCollisionLoadedAroundEntity(ped) do
        Wait(1000)
    end

    -- Switch back to player
    Wait(1000)
    SwitchInPlayer(ped)
    Base.Player.setEntityVisible(true)
    FreezeEntityPosition(PlayerPedId(), false)
    TriggerEvent("core_citizen:uncuff", GetPlayerServerId(PlayerPedId()))
    Base.Animation.StopForcedAnim(ped)
end

RegisterNetEvent("player:respawnPlayerAtHospital", respawnPlayerAtHospital)

local function startRespawnTimerThread()
    CreateThread(function()
        while LocalPlayer.state.wounded and timeUntilRespawn > 0 do
            timeUntilRespawn = timeUntilRespawn - 1
            Wait(1000)
        end
    end)
end

AddEventHandler("playerWounded", function()
    -- Set up respawn timer
    timeUntilRespawn = RESPAWN_TIMER
    if LocalPlayer.state.atCayo then
        timeUntilRespawn = 120
    end
    startRespawnTimerThread()

    -- Start threads
    while LocalPlayer.state.wounded do
        Wait(0)

        -- Handle input
        if LocalPlayer.state.woundedSeverity ~= "knockedout" then
            if IsControlJustReleased(0, 38) and timeUntilRespawn <= 0 and LocalPlayer.state.hospitalised ~= true then
                OpenConfirmMenu("Are you sure you want to respawn?", function(confirmed)
                    if confirmed then
                        respawnPlayerAtHospital()
                    end
                end)
            end
        end
    end
end)

--#endregion
