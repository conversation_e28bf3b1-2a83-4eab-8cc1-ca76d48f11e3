var menuLabels = {
    basic: "Basic Face",
    adv: "Advanced Face",
    cos: "Cosmetics",
    clothing: "Clothing",
    ['clothing-restricted']: "Clothing",
    charCreate: "Basic Settings"
}

$(function() {
    $("input[type='range']").each(function(i) {
        var val = ($(this).val() - $(this).attr('min')) / ($(this).attr('max') - $(this).attr('min'));
        var percent = val * 100;

        $(this).css('background-image',
            '-webkit-gradient(linear, left top, right top, ' +
            'color-stop(' + percent + '%, rgb(11,102,190)), ' +
            'color-stop(' + percent + '%, rgba(11,102,190, 0.38))' +
            ')'
        );
    })

    $("input[type='range']").on("input", function (e) {
        var val = ($(this).val() - $(this).attr('min')) / ($(this).attr('max') - $(this).attr('min'));
        var percent = val * 100;

        $(this).css('background-image',
            '-webkit-gradient(linear, left top, right top, ' +
            'color-stop(' + percent + '%, rgb(11,102,190)), ' +
            'color-stop(' + percent + '%, rgba(11,102,190, 0.38))' +
            ')'
        );

        if ($(this).attr("category")) {
            $.post('http://core_skin/setSkinValue', JSON.stringify({
                category: $(this).attr("category"),
                feature: $(this).attr("feature"),
                key: $(this).attr("key"),
                value: $(this).val()
            }));

        } else if ($(this).attr("id") == "rotationBar") {
            $.post('http://core_skin/setRotation', JSON.stringify($(this).val()));
        }
    });

    $("input[type='text']").on("input", function (e) {
        var elem = $(this)

        if (!isNaN(elem.val())) {
            elem.css("border", "0px solid transparent")
            $.post('http://core_skin/setSkinValue', JSON.stringify({
                category: elem.attr("category"),
                feature: elem.attr("feature"),
                key: elem.attr("key"),
                value: elem.val()
            }));            
        } else {elem.css("border", "2px solid red")}
    });

    $("#resetSkin").on("click", function (e) {
        $.post('http://core_skin/resetSkin');
        $("body").hide();
    });

    $("#saveSkin").on("click", function (e) {
        $.post('http://core_skin/saveSkin');
        $("body").hide();
    });

    $(".modelButton").on("click", function (e) {
        $.post('http://core_skin/setModel', JSON.stringify($(this).attr("model")));
    });

    $("#cameraBar button").on("click", function (e) {$.post('http://core_skin/toggleClothing', JSON.stringify($(this).attr("item")))});

    $("#cameraViewBar button").on("click", function (e) {$.post('http://core_skin/clothingCamera', JSON.stringify($(this).attr("item")))});

    $("#handsUp").on("click", function (e) {
        $.post('http://core_skin/handsUp');
    });

    $(".raise").on("click", function (e) {changeInput($(this).parent().parent().find("input"), 1)});
    $(".lower").on("click", function (e) {changeInput($(this).parent().parent().find("input"), -1)});
    $(".raiseLarge").on("click", function (e) {changeInput($(this).parent().parent().find("input"), 10)});
    $(".lowerLarge").on("click", function (e) {changeInput($(this).parent().parent().find("input"), -10)});
});

function submitButtonStyle(_this) {
    console.log(_this.id)
    var background = document.getElementById(_this.id).style;

    console.log(background)
    console.log(background.backgroundColor)

    if (background.backgroundColor == "red") {
        background.backgroundColor = "rgb(11,102,190)";
    }
    else
    {
        background.backgroundColor = "red";
    }
}

function changeInput(elem, increment) {
    var val = parseInt(elem.val());
    var max = parseInt(elem.attr("max"));
    var min = parseInt(elem.attr("min"));

    val += increment;

    var newValue = Math.min(val, max)     
    newValue = Math.max(newValue, min);

    if (!isNaN(parseInt(newValue))) {
        elem.val(newValue)

        if (elem.attr("key") == "model") {
            var textureElem = $("[category='" + elem.attr("category") + "'][feature='" + elem.attr("feature") + "'][key='texture']")
            textureElem.val("0")
            textureElem.trigger("input")
        }
    }    
    elem.trigger("input");    
}

function loadSkinValues(skin) {
    for (const category in skin) {
        for (const featureName in skin[category]) {                
            if (typeof(skin[category][featureName]) == "number") {
                $("[category='" + category + "'][feature='" + featureName + "']").val(skin[category][featureName])
                $("[category='" + category + "'][feature='" + featureName + "']").trigger("input")
            } else {
                for (const key in skin[category][featureName]) {
                    $("[category='" + category + "'][feature='" + featureName + "'][key='" + key + "']").val(skin[category][featureName][key])
                    $("[category='" + category + "'][feature='" + featureName + "'][key='" + key + "']").trigger("input")
                }
            }
        }
    }
}

function loadMaxValues(maxValues) {
    for (const category in maxValues) {
        for (const featureName in maxValues[category]) {                
            for (const key in maxValues[category][featureName]) {
                var elem = $("[category='" + category + "'][feature='" + featureName + "'][key='" + key + "']")
                elem.attr("max", maxValues[category][featureName][key])
                if (parseInt(elem.val()) > elem.attr("max")) {elem.val("0")}
            }
        }
    }
}

function loadMinValues(minValues) {
    for (const category in minValues) {
        for (const featureName in minValues[category]) {                
            for (const key in minValues[category][featureName]) {
                $("[category='" + category + "'][feature='" + featureName + "'][key='" + key + "']").attr("min", minValues[category][featureName][key])
            }
        }
    }
}

window.addEventListener('message', function(event) {
    var item = event.data;

    if (item.showMenu) {
        $("#rotationBar").val("0");
        $("#rotationBar").trigger("input");

        $("#skin-tablist").empty()

        item.showMenu.forEach(menu => {
            $("#skin-tablist").append($("<li><a tab='skin-" + menu + "'>" + menuLabels[menu] + "</a></li>"));
        });

        $("#skin-tablist a").on("click", function (e) {
            $("#skin-tablist a").removeAttr("selected");
            $(this).attr("selected", "selected");
    
            $(".skin-container").hide();
            $("[tab=" + $(this).attr("tab") + "]").show();

            $.post('http://core_skin/menuChanged', JSON.stringify($(this).attr("tab")))
        });

        $("#skin-tablist a").first().trigger("click");
        $("body").show();

        if (item.blockReset) {
            $("#resetSkin").hide();
        } else {
            $("#resetSkin").show();
        }
    }

    if (item.hideMenu) {
        $("body").hide();
    }

    if (item.skin) {
        loadSkinValues(item.skin)
    }

    if (item.maxValues) {
        loadMaxValues(item.maxValues)
    }

    if (item.minValues) {
        console.log("MIN!")
        loadMinValues(item.minValues)
    }
});