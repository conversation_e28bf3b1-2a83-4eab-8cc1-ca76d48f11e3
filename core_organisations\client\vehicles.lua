---
--- DB Vehicle Spanwer Menu
---

local ghost_vehicles = {}
local local_cam = nil

Citizen.CreateThread(function()
    DecorRegister("SpawnedBy", 3)
end)

function clearGhostVehicles()
    for i = #ghost_vehicles, 1, -1 do
        DeleteEntity(ghost_vehicles[i])
        ghost_vehicles[i] = nil
    end
end

function createGhostVehicle(data, coords, heading, cb)
    clearGhostVehicles()
    exports["base_vehicle"]:createFromModel(data, coords, heading + 0.0, {}, function(vehicle)
        SetEntityCollision(vehicle, false, false)
        SetEntityAlpha(vehicle, 230, false)
        FreezeEntityPosition(vehicle, true)
        SetVehicleEngineOn(vehicle, true, true, false)
        SetVehicleLights(vehicle, 2)
        SetVehicleDirtLevel(vehicle, 0.0)
        table.insert(ghost_vehicles, vehicle)

        cb(vehicle)
    end, true)
end

local function OpenVehicleSpawnMenu(org, vehicles, coords, heading, searchVehicles)
    -- local groupFilter = searchVehicles == nil
    vehicles = searchVehicles or vehicles
    Base.UI.Menu.CloseAll()

    if (not Base.Camera.exists("job_garage_cam")) then
        Base.Camera.create("job_garage_cam", 1, GetObjectOffsetFromCoords(coords.x, coords.y, coords.z, heading + 0.0, -3.5, 4.0, 2.0), vector3(-20, 0, heading + 225), 70.0)
        Base.Camera.render("job_garage_cam", true, 0)
    end

    local menuItems = vehicles
    local categorisedVehicles = {}
    local groupedVehicles = {}

    -- if groupFilter then
    menuItems = {}
    local categorisedMenuItems = {}
    local groupedMenuItems = {}
    local uncategorisedMenuItems = {}

    for i, veh in ipairs(vehicles) do
        if veh.category then
            local category = veh.category

            if not categorisedVehicles[category] then
                categorisedVehicles[category] = {}
                table.insert(categorisedMenuItems, { label = category, submenu = true })
            end

            veh.category = nil
            table.insert(categorisedVehicles[category], veh)
        elseif veh.vehicleGroup then
            local group = veh.vehicleGroup

            if not groupedVehicles[group] then
                groupedVehicles[group] = {}
                table.insert(groupedMenuItems, { label = group, vehiclegroup = true })
            end
            veh.vehicleGroup = nil
            table.insert(groupedVehicles[group], veh)
        else
            table.insert(uncategorisedMenuItems, veh)
        end
    end

    if #categorisedMenuItems > 0 then
        table.insert(menuItems, { label = "== Categories ==" })
        for i, v in ipairs(categorisedMenuItems) do table.insert(menuItems, v) end
        table.insert(menuItems, { label = "== Vehicles ==" })
    end

    for i, v in ipairs(groupedMenuItems) do table.insert(menuItems, v) end
    for i, v in ipairs(uncategorisedMenuItems) do table.insert(menuItems, v) end
    -- end

    table.insert(menuItems, 1, { label = "Search", value = "search_vehicle" })

    Base.UI.Menu.Open(
        'default', GetCurrentResourceName(), 'vehicle_spawner',
        {
            title    = 'Vehicle List',
            align    = 'bottom-right',
            elements = menuItems,
        },

        function(data, menu)
            if (data.current.submenu) then
                OpenVehicleSpawnMenu(org, categorisedVehicles[data.current.label], coords, heading)
                return
            end

            if (data.current.vehiclegroup) then
                OpenVehicleSpawnMenu(org, groupedVehicles[data.current.label], coords, heading)
                return
            end

            if (data.current.value == "search_vehicle") then
                local searchedVehicles = {}
                OpenDialog("Search by vehicle name", function(input)
                    for k, v in pairs(vehicles) do
                        if string.find(string.lower(v.label), string.lower(input)) then
                            table.insert(searchedVehicles, 1, v)
                        end
                    end
                    OpenVehicleSpawnMenu(org, type, coords, heading, searchedVehicles)
                    return
                end)
            end

            if data.current.spawnBlocked then
                Base.Notification("Vehicle " .. data.current.id .. " is currently out!")
                return
            end

            menu.close()

            while (Base.Camera.exists("job_garage_cam")) do
                Base.Camera.destroy("job_garage_cam", 0)
                Wait(50)
            end
            clearGhostVehicles()

            local vehicleData = {
                ["job"] = org.name,
                ["job_label"] = org.label,
                ["job_vehicle"] = true
            }

            if data.current.settings.veh_callsign then
                vehicleData["veh_callsign"] = data.current.settings.veh_callsign
            end

            if data.current.settings.equipment then
                vehicleData["equipment"] = true
            end
            if data.current.settings.gear_forensics then
                vehicleData["gear_forensics"] = true
            end
            if data.current.settings.gear_riot then
                vehicleData["gear_riot"] = true
            end
            if data.current.settings.gear_srt then
                vehicleData["gear_srt"] = true
            end
            if data.current.settings.storage then
                vehicleData["storage"] = data.current.settings.storage
            end
            if data.current.settings.radar then
                vehicleData["radar"] = true
            end
            if data.current.settings.alpr then
                vehicleData["alpr"] = true
            end
            if data.current.settings.canBoltCutter then
                vehicleData["canBoltCutter"] = true
            end

            exports["base_vehicle"]:createFromModel(
                data.current.value,
                coords,
                heading,
                vehicleData,
                function(vehicle)
                    local settings = data.current.settings
                    if (settings) then
                        -- clean vehicle
                        SetVehicleDirtLevel(vehicle, 0.0)

                        -- apply modkit to vehicle
                        SetVehicleModKit(vehicle, 0)
                        if (settings.engine) then
                            SetVehicleMod(vehicle, 11, settings.engine, false)
                        end
                        if (settings.brakes) then
                            SetVehicleMod(vehicle, 12, settings.brakes, false)
                        end
                        if (settings.transmission) then
                            SetVehicleMod(vehicle, 13, settings.transmission, false)
                        end
                        if (settings.suspension) then
                            SetVehicleMod(vehicle, 15, settings.suspension, false)
                        end
                        if (settings.armour) then
                            SetVehicleMod(vehicle, 16, settings.armour, false)
                        end
                        if (settings.turbo) then
                            ToggleVehicleMod(vehicle, 18, settings.turbo)
                        end

                        -- Apply Livery
                        if (settings.livery) then
                            SetVehicleLivery(vehicle, settings.livery)
                            SetVehicleMod(vehicle, 48, settings.livery, false)
                        end

                        -- Apply Paint
                        if (settings.color1) then
                            SetVehicleColours(vehicle, settings.color1, settings.color2)
                        end

                        if (settings.pearl) then
                            local _, wheelColor = GetVehicleExtraColours(vehicle)
                            SetVehicleExtraColours(vehicle, settings.pearl, wheelColor)
                        end

                        if (settings.front_bumper) then
                            SetVehicleMod(vehicle, 1, settings.front_bumper)
                        end

                        if (settings.rear_bumper) then
                            SetVehicleMod(vehicle, 2, settings.rear_bumper)
                        end

                        if (settings.roof) then
                            SetVehicleMod(vehicle, 10, settings.roof)
                        end

                        -- Apply Extras
                        if (settings.extras) then
                            Citizen.CreateThread(function()
                                for i = 1, 30, 1 do
                                    SetVehicleExtra(vehicle, i, 1)
                                end

                                for i = 1, #settings.extras do
                                    SetVehicleExtra(vehicle, settings.extras[i], 0)
                                end
                            end)
                        end

                        -- Apply Tints
                        if (settings.tint) then
                            SetVehicleWindowTint(vehicle, settings.tint)
                        end

                        TaskWarpPedIntoVehicle(PlayerPedId(), vehicle, -1)
                        TriggerServerEvent("orgs:trackVehicle", NetworkGetNetworkIdFromEntity(vehicle), org.name)
                        TriggerServerEvent("orgs:spawnJobVehicle", NetworkGetNetworkIdFromEntity(vehicle), data.current.id)
                    end
                end,
                false
            )
        end,

        function(data, menu)
            menu.close()

            Base.Camera.destroy("job_garage_cam", 0)
            clearGhostVehicles()
        end,

        function(data, menu)
            local spawncode = data.current.value
            local settings = data.current.settings
            if (data.current.submenu) then
                local vehicles = categorisedVehicles[data.current.label]
                local veh = vehicles[1]
                spawncode = veh.value
                settings = veh.settings
            end

            if (data.current.vehiclegroup) then
                local vehicles = groupedVehicles[data.current.label]
                local veh = vehicles[1]
                spawncode = veh.value
                settings = veh.settings
            end

            createGhostVehicle(spawncode, vector3(coords.x, coords.y, coords.z), heading, function(vehicle)
                -- apply modkit to vehicle
                SetVehicleModKit(vehicle, 0)

                -- Apply Livery
                if (settings.livery) then
                    SetVehicleLivery(vehicle, settings.livery)
                    SetVehicleMod(vehicle, 48, settings.livery, false)
                end

                -- Apply Paint
                if (settings.color1) then
                    SetVehicleColours(vehicle, settings.color1, settings.color2)
                end

                if (settings.pearl) then
                    local _, wheelColor = GetVehicleExtraColours(vehicle)
                    SetVehicleExtraColours(vehicle, settings.pearl, wheelColor)
                end

                -- Apply Extras
                if (settings.extras) then
                    Citizen.CreateThread(function()
                        for i = 1, 30, 1 do
                            SetVehicleExtra(vehicle, i, 1)
                        end

                        for i = 1, #settings.extras do
                            SetVehicleExtra(vehicle, settings.extras[i], 0)
                        end
                    end)
                end

                -- Apply Tints
                if (settings.tint) then
                    SetVehicleWindowTint(vehicle, settings.tint)
                end
            end)
        end
    )
end

AddEventHandler("org:markerInteraction", function(type, marker)
    if type ~= "vehiclespawn" then return end
    print("Open Vehicle Menu", marker.organisation, marker.identifier, marker.data.vehicleType, marker.data.spawnPoint)

    local vehicleType = marker.data.vehicleType
    local spawnPoint = marker.data.spawnPoint
    local spawnCoords = vector3(spawnPoint.x, spawnPoint.y, spawnPoint.z)
    local spawnHeading = spawnPoint.r

    if not vehicleType or not spawnPoint then return end

    local vehicles = AwaitServerCallback("orgs:getVehicles", marker.organisation, vehicleType)
    OpenVehicleSpawnMenu({ name = marker.organisation }, vehicles, spawnCoords, spawnHeading)
end)

AddEventHandler("org:markerInteraction", function(type, marker)
    if type ~= "deleter" then return end
    print("Delete Vehicle", marker.organisation, marker.identifier)

    local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)

    -- check if driver
    if not DoesEntityExist(vehicle) or GetPedInVehicleSeat(vehicle, -1) ~= PlayerPedId() then
        Base.Notification("You must be the driver to delete the vehicle.")
        return
    end

    DeleteEntity(vehicle)
end)
