-- #region Offers
local function dialogOfferPrice()
    local awaitingInput = true
    local amount = false

    OpenDialog("Offer Price (how much they receive)", function(dialogAmount)
        amount = dialogAmount
        awaitingInput = false
    end, function()
        awaitingInput = false
    end, "number", 1)

    while awaitingInput do
        Wait(0)
    end

    return amount
end

local function dialogListPrice()
    local awaitingInput = true
    local amount = false

    OpenDialog("List Price (base cost for reselling)", function(dialogAmount)
        amount = dialogAmount
        awaitingInput = false
    end, function()
        awaitingInput = false
    end, "number", 1)

    while awaitingInput do
        Wait(0)
    end

    return amount
end

local function purchaseUsedVehicle(vehicle)
    local offerPrice = dialogOfferPrice()
    if not offerPrice then
        return Base.Notification("Please enter a valid offer price.")
    end

    local listPrice = dialogListPrice()
    if not listPrice then
        return Base.Notification("Please enter a valid list price.")
    end

    TriggerServerEvent("usedcar_purchaseorder", NetworkGetNetworkIdFromEntity(vehicle), offerPrice, listPrice)
end

RegisterInteraction("vehicle", "usedcar_purchaseorder", {
    label = "Make Purchase Offer"
}, purchaseUsedVehicle, "usedcar.purchase")

-- #endregion

-- #region Accepting Offers
local function dialogAcceptOffer(vehicleId, modelName, sellerName, jobName, offerPrice)
    local message = sellerName .. " (" .. jobName .. ") has offered $" .. offerPrice .. " for your " .. modelName .. ". Do you accept?"

    OpenConfirmMenu(message, function(status)
        if status then
            TriggerServerEvent("usedcar_acceptpurchase", jobName, vehicleId)
        else
            TriggerServerEvent("usedcar_declinepurchase", jobName, vehicleId)
        end
    end)
end

RegisterNetEvent("usedcar_offerpurchase", dialogAcceptOffer)
