--[====[
    @filename Core / Utility (Client)
    @filebrief Core client utilties essential for almost every script.
--]====]

---Returns a single merged table, by appending all values from t2 on to t1
---@param t1 table
---@param t2 table
---@return table
function table.merge(t1, t2)
    for i = 1, #t2 do
        t1[#t1 + 1] = t2[i]
    end
    return t1
end

---Returns a single merged table, by appending all values from t2 on to t1
---@param t1 table
---@param t2 table
---@param deep? boolean If true, will merge tables within tables
---@return table resultTable Returns the merged table, but also modifies t1
function table.assign(t1, t2, deep)
    for k, v in pairs(t2) do
        if deep and type(v) == 'table' then
            if not t1[k] then
                t1[k] = {}
            end
            table.assign(t1[k], v, deep)
        else
            t1[k] = v
        end
    end
    return t1
end

------------------------------
-- Server Callbacks --
------------------------------
local currentRequestId = 0
local serverCallbacks = {}

local function triggerServerCallback(name, cb, latent, ...)
    serverCallbacks[currentRequestId] = cb

    TriggerServerEvent(name .. ':triggerServerCallback', GetCurrentResourceName(), currentRequestId, latent, ...)

    if currentRequestId < 65535 then
        currentRequestId = currentRequestId + 1
    else
        currentRequestId = 0
    end
end

---Triggers a server callback, where a request is sent to the server and a response is returned
---@param name string The unique name of the callbacks
---@param cb function The callback function to be triggered, with the response from the server as the arguments
---@param ... any The arguments to be passed to the server callback
function TriggerServerCallback(name, cb, ...)
    triggerServerCallback(name, cb, false, ...)
end

---Works the same as TriggerServerCallback, but the callback is triggered in low-priority, useful for big requests or non-essential data
---@param name string The unique name of the callbacks
---@param cb function The callback function to be triggered, with the response from the server as the arguments
---@param ... any The arguments to be passed to the server callback
function TriggerLatentServerCallback(name, cb, ...)
    triggerServerCallback(name, cb, true, ...)
end

RegisterNetEvent(GetCurrentResourceName() .. ':serverCallback')
AddEventHandler(GetCurrentResourceName() .. ':serverCallback', function(requestId, ...)
    serverCallbacks[requestId](...)
    serverCallbacks[requestId] = nil
end)

---Performs a blocking call to a server callback
---@param name string
---@param ... any
---@return any
function AwaitServerCallback(name, ...)
    local waiting = true
    local response = nil

    TriggerServerCallback(name, function(...)
        response = { ... }
        waiting = false
    end, ...)

    while waiting do
        Wait(0)
    end

    return table.unpack(response)
end

------------------------------
-- Client Callbacks --
------------------------------

local clientCallbacks = {}

--[====[
    @type func
    @name RegisterClientCallback(name, cb)
    @param name: string - Unique callback identifier
    @param cb(source,cb,...): function - Callback when the callback is triggered
    @brief Defines the behaviour of a client callback
    @description [
        Client callbacks are used to request server data from the client.

        This defines the server sided behaviour which takes place within the callback function
    ]
--]====]
RegisterClientCallback = function(name, cb, ...)
    if not clientCallbacks[name] then
        RegisterNetEvent(name .. ':triggerClientCallback')
        AddEventHandler(name .. ':triggerClientCallback', function(resourcename, requestId, ...)
            if clientCallbacks[name] ~= nil then
                clientCallbacks[name](function(...)
                    TriggerServerEvent(resourcename .. ':clientCallback', requestId, ...)
                end, ...)
            end
        end)
    end

    clientCallbacks[name] = cb
end

------------------------------
-- Data Sync --
------------------------------

local dataSyncHandlers = {}
local dataSyncCache = {}

function RegisterDataSyncHandler(identifier, cb, resource)
    if resource ~= nil and resource ~= GetCurrentResourceName() then
        return exports[resource]:RegisterDataSyncHandler(identifier, cb)
    end

    if not dataSyncHandlers[identifier] then
        dataSyncHandlers[identifier] = { cb }
    else
        table.insert(dataSyncHandlers[identifier], cb)
    end

    if dataSyncCache[identifier] then
        cb(dataSyncCache[identifier])
    end
end

exports('RegisterDataSyncHandler', RegisterDataSyncHandler)

RegisterNetEvent(GetCurrentResourceName() .. ':datasync')
AddEventHandler(GetCurrentResourceName() .. ':datasync', function(identifier, data, source)
    for _, cb in ipairs(dataSyncHandlers[identifier] or {}) do
        cb(data)
    end

    dataSyncCache[identifier] = data

    if source and source == GetPlayerServerId(PlayerId()) then
        print("Received data sync for " .. identifier)
    end
end)

function GeneralLog(name, message, color, channel)
    TriggerServerEvent(GetCurrentResourceName() .. ':log', name, message, color, channel)
end

function DevelopDebugLog(message, resource, ...)
    TriggerServerEvent("base:DevelopDebugLog", message, resource, ...)
end

---Sanitize a text string to remove html elements
---@param value string
function SanitizeHtml(value)
    return string.gsub(value, "<[^>]*>", "")
end

-- Entity functions
local entityEnumerator = {
    __gc = function(enum)
        if enum.destructor and enum.handle then
            enum.destructor(enum.handle)
        end
        enum.destructor = nil
        enum.handle = nil
    end
}

local function EnumerateEntities(initFunc, moveFunc, disposeFunc)
    return coroutine.wrap(function()
        local iter, id = initFunc()
        if not id or id == 0 then
            disposeFunc(iter)
            return
        end

        local enum = { handle = iter, destructor = disposeFunc }
        setmetatable(enum, entityEnumerator)

        local next = true
        repeat
            coroutine.yield(id)
            next, id = moveFunc(iter)
        until not next

        enum.destructor, enum.handle = nil, nil
        disposeFunc(iter)
    end)
end

function EnumerateObjects()
    return EnumerateEntities(FindFirstObject, FindNextObject, EndFindObject)
end

function EnumeratePeds()
    return EnumerateEntities(FindFirstPed, FindNextPed, EndFindPed)
end

function EnumerateVehicles()
    return EnumerateEntities(FindFirstVehicle, FindNextVehicle, EndFindVehicle)
end

function EnumeratePickups()
    return EnumerateEntities(FindFirstPickup, FindNextPickup, EndFindPickup)
end

---Returns all vehicles within a radius1
---@param coords vector3
---@param radius number
---@param options {includeCurrentVehicle: boolean}?
---@return table
function GetVehiclesInRadius(coords, radius, options)
    local vehicles = {}

    local veh = GetVehiclePedIsIn(PlayerPedId(), false)

    for vehicle in EnumerateVehicles() do
        local dist = #(coords - GetEntityCoords(vehicle))
        if dist <= radius then
            if options and options.includeCurrentVehicle then
                table.insert(vehicles, vehicle)
            elseif veh ~= vehicle then
                table.insert(vehicles, vehicle)
            end
        end
    end

    return vehicles
end

---Returns all vehicles within a radius1
---@param coords vector3
---@param radius number
---@param options {}?
---@return table
function GetDoorsInRadius(coords, radius, options)
    local doors = {}

    for obj in EnumerateObjects() do
        if GetEntityType(obj) == 3 then
            local dist = #(coords - GetEntityCoords(obj))
            if dist <= radius then
                table.insert(doors, obj)
            end
        end
    end

    return doors
end

---Returns the closest vehicle and distance to that vehicle
---@param coords vector3
---@param radius number?
---@return number?, number?
function GetClosestVehicleToCoords(coords, radius)
    local closestVehicle = nil
    local closestDist = 999999

    if not radius then radius = 999999 end

    for vehicle in EnumerateVehicles() do
        local dist = #(coords - GetEntityCoords(vehicle))
        if dist <= radius and dist < closestDist then
            closestVehicle = vehicle
            closestDist = dist
        end
    end

    return closestVehicle, closestDist
end

-- Controls
---Disabled groups of player controls
---@param options {general?:boolean, vehicle?:boolean, sprint?:boolean, voice?:boolean, enterVehicle?:boolean, attack?:boolean, mouse?:boolean}
function DisablePlayerControls(options)
    if options.general then
        DisableControlAction(2, 24, true)  -- Attack
        DisableControlAction(2, 257, true) -- Attack 2
        DisableControlAction(2, 25, true)  -- Aim
        DisableControlAction(2, 68, true)  -- Aim in car 2
        DisableControlAction(2, 91, true)  -- Aim in car 2
        DisableControlAction(2, 263, true) -- Melee Attack 1
        DisableControlAction(2, 45, true)  -- Reload ["R"]
        DisableControlAction(2, 80, true)  -- Reload ["R"]
        DisableControlAction(2, 140, true) -- Reload ["R"]
        DisableControlAction(2, 250, true) -- Reload ["R"]
        DisableControlAction(2, 263, true) -- Reload ["R"]
        DisableControlAction(2, 310, true) -- Reload ["R"]
        DisableControlAction(2, 22, true)  -- Jump ['SPACE']
        DisableControlAction(2, 44, true)  -- Cover ["Q"]
        DisableControlAction(2, 37, true)  -- Select Weapon ["TAB"]
        DisableControlAction(0, 23, true)  -- Also 'enter'?
        DisableControlAction(2, 288, true) -- Disable phone ["F1"]
        DisableControlAction(2, 289, true) -- Inventory ["F2"]
        DisableControlAction(2, 170, true) -- Animations ["F3"]
        DisableControlAction(2, 167, true) -- Disable F6 menu ["F6"]
        DisableControlAction(2, 323, true) -- Hands up while cuffed ["X"]
        DisableControlAction(2, 73, true)  -- Hands up while cuffed ["X"]
        DisableControlAction(2, 29, true)  -- Disables pointing ["B"]
    end

    if options.attack then
        DisableControlAction(2, 24, true)  -- Attack
        DisableControlAction(2, 257, true) -- Attack 2
        DisableControlAction(2, 25, true)  -- Aim
        DisableControlAction(2, 68, true)  -- Aim in car 2
        DisableControlAction(2, 91, true)  -- Aim in car 2
    end

    if options.enterVehicle then
        DisableControlAction(2, 23, true) -- Attack
    end

    if options.vehicle then
        -- Vehicle controls
        DisableControlAction(0, 59, true)
        DisableControlAction(0, 60, true)
        DisableControlAction(0, 61, true)
        DisableControlAction(0, 62, true)
        DisableControlAction(0, 63, true)
        DisableControlAction(0, 64, true)

        -- Vehicle weapons
        DisableControlAction(0, 65, true)
        DisableControlAction(0, 66, true)
        DisableControlAction(0, 67, true)
        DisableControlAction(0, 68, true)
        DisableControlAction(0, 69, true)
        DisableControlAction(0, 70, true)

        -- Attacking
        DisableControlAction(0, 71, true)
        DisableControlAction(0, 72, true)
        DisableControlAction(0, 73, true)

        -- Passenger attacking
        DisableControlAction(0, 91, true)
        DisableControlAction(0, 92, true)

        -- Exit vehicle
        DisableControlAction(0, 75, true)
        DisableControlAction(27, 75, true)

        -- Radio controls
        DisableControlAction(0, 81, true)
        DisableControlAction(0, 82, true)
        DisableControlAction(0, 83, true)
        DisableControlAction(0, 84, true)
        DisableControlAction(0, 85, true)

        -- Other misc
        DisableControlAction(0, 86, true) -- horn
        DisableControlAction(0, 74, true) -- headlights

        -- Plane controls
        DisableControlAction(0, 87, true)
        DisableControlAction(0, 88, true)
        DisableControlAction(0, 89, true)
        DisableControlAction(0, 90, true)
    end

    if options.sprint then
        -- Disable sprinting
        DisableControlAction(2, 21, true) -- Disables sprinting ["LEFTSHIFT"]
    end

    if options.voice then
        -- Disable voice
        DisableControlAction(0, 249, true) -- Push to talk ["Z"]
    end

    if options.mouse then
        -- Disable mouse
        DisableControlAction(0, 1, true) -- Mouse 1
        DisableControlAction(0, 2, true) -- Mouse 2
    end
end

---Requests a screenshot upload to the FDG CDN, and returns the links
---@param timeout? number Amount of MS to wait for the screenshot to be uploaded, default 5000
---@return string|nil
function RequestScreenshotUpload(timeout)
    local link = exports.base_screencapture:requestScreenshot(timeout)
    return link
end

---Gets all organizations the player is in
---@return { [string]: string } Returns a table of all organizations the player. Key is the organization name, value is the rank name
function GetPlayerOrgs()
    if GetResourceState("base") ~= "started" then return {} end
    return exports.base:getPlayerOrgs()
end

---Checks if the player is in a specific organization
---@param orgName any
---@return boolean
function DoesPlayerHaveOrg(orgName)
    if GetResourceState("base") ~= "started" then return false end
    return exports.base:doesPlayerHaveOrg(orgName)
end

---Gets the player's rank in a specific organization
---@param orgName string
---@return string?
function GetPlayerOrgRank(orgName)
    if GetResourceState("base") ~= "started" then return nil end
    return exports.base:getPlayerOrgRank(orgName)
end

---Checks if a player has a role permissions
---@param permission string
---@param orgName string? Optional - require the permission to be specific to a role
function DoesPlayerHaveOrgPermission(permission, orgName)
    if GetResourceState("base") ~= "started" then return false end
    return exports.base:doesPlayerHaveOrgPermission(permission, orgName)
end

local interactions = {}

---Adds an option to the interaction menu
---@param typeId string ped, player, vehicle, object
---@param identifier string unique identifier for the interaction
---@param displayData {label: string, emoji?: string, order?: number} display data for the interaction
---@param onInteract fun(entity: any) function to call when the interaction is selected, passing the entity
---@param condition? string | fun(entity: any): boolean can be either a permission string (e.g. handcuff) OR a function to call to determine if the interaction should be displayed OR nil for always allow
function RegisterInteraction(typeId, identifier, displayData, onInteract, condition)
    interactions[identifier] = { typeId = typeId, displayData = displayData, onInteract = onInteract, condition = condition }

    if exports["base_interaction"].registerInteraction then
        exports["base_interaction"]:registerInteraction(typeId, identifier, displayData, onInteract, condition)
    end
end

---Removes an interaction from the interaction menu
---@param identifier string
function RemoveInteraction(type, identifier)
    interactions[identifier] = nil

    if exports["base_interaction"].removeInteraction then
        exports["base_interaction"]:removeInteraction(type, identifier)
    end
end

-- reloads interactions when base_interaction is reloaded
AddEventHandler("interactionsReady", function()
    for identifier, data in pairs(interactions) do
        exports["base_interaction"]:registerInteraction(data.typeId, identifier, data.displayData, data.onInteract, data.condition)
    end
end)

---Opens a dialog
---@param question string
---@param onComplete fun(data: any) function to call when the dialog is completed with the entered value
---@param onCancel? fun() function to call when the dialog is cancelled
---@param datattype? string "number" or "string"
---@param min? number
---@param max? number
function OpenDialog(question, onComplete, onCancel, datattype, min, max)
    Base.UI.Menu.CloseAll()

    Base.UI.Menu.Open("dialog", GetCurrentResourceName(), "dialog",
        {
            title = question,
        },

        function(data, menu)
            if datattype == "number" then
                local num = tonumber(data.value)
                if num then
                    if min and num < min then
                        TriggerEvent("fdg_ui:SendNotification", "Enter a number more than " .. min - 1)
                    elseif max and num > max then
                        TriggerEvent("fdg_ui:SendNotification", "Enter a number less than " .. max + 1)
                    else
                        menu.close()
                        onComplete(num)
                    end
                else
                    TriggerEvent("fdg_ui:SendNotification", "Please enter a valid number")
                end
            else
                menu.close()
                onComplete(data.value)
            end
        end,

        function(data, menu)
            menu.close()
            if onCancel then
                onCancel()
            end
        end
    )
end

---Opens a confirm menu
---@param title string
---@param onDone fun(confirmed: boolean) function to call when the confirm menu is completed
function OpenConfirmMenu(title, onDone)
    Base.UI.Menu.Open('default', GetCurrentResourceName(), 'confirmmenu',
        {
            title = title,
            align = 'center',
            elements = {
                { label = 'Yes', value = 'yes' },
                { label = 'No',  value = 'no' },
            },
        },

        function(data, menu)
            menu.close()
            if data.current.value == "yes" then
                onDone(true)
            else
                onDone(false)
            end
        end,

        function(data, menu)
            menu.close()
            onDone(false)
        end
    )
end

---Sends a notification to the player (client-side)
---@param message string
---@param options {}? Additional notif options
function SendNotification(message, options)
    TriggerEvent("fdg_ui:SendNotification", message, options)
end
