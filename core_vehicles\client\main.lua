local lastVehicle = 0

Citizen.CreateThread(function()
    while (true) do
        Wait(0)
        local ped = PlayerPedId()

        if IsPedInAnyVehicle(ped, true) then
            local veh = GetVehiclePedIsIn(ped, false)
            local isDriver = GetPedInVehicleSeat(veh, -1) == ped

            -- Player entered vehicle handlers
            if lastVehicle ~= veh then
                TriggerEvent("vehicleEnter", veh, isDriver)
                lastVehicle = veh
            end

            -- If player is not the driver, long wait
            if isDriver then
                TriggerEvent("vehicleDriverTick", veh)
            end
        elseif lastVehicle and lastVehicle ~= 0 then
            TriggerEvent("vehicleExit", lastVehicle, DoesEntityExist(lastVehicle))
            lastVehicle = 0
        end
    end
end)
