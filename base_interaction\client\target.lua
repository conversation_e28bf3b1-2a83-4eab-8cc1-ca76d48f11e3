------------------------------------------------------------------
--                     Target System
------------------------------------------------------------------

-- Get entity in front of player
local function RotationToDirection(rotation)
    local adjustedRotation =
    {
        x = (math.pi / 180) * rotation.x,
        y = (math.pi / 180) * rotation.y,
        z = (math.pi / 180) * rotation.z
    }
    local direction =
    {
        x = -math.sin(adjustedRotation.z) * math.abs(math.cos(adjustedRotation.x)),
        y = math.cos(adjustedRotation.z) * math.abs(math.cos(adjustedRotation.x)),
        z = math.sin(adjustedRotation.x)
    }
    return direction
end

-- <PERSON>'s coords
local function GetCoordsFromCam(distance)
    local cameraRotation = GetGameplayCamRot()
    local cameraCoord = GetGameplayCamCoord()
    local direction = RotationToDirection(cameraRotation)
    local destination = {
        x = cameraCoord.x + direction.x * distance,
        y = cameraCoord.y + direction.y * distance,
        z = cameraCoord.z + direction.z * distance
    }
    return vector3(destination.x, destination.y, destination.z)
end

local function GetAddedCamDistance()
    local camZoom = GetFollowPedCamViewMode()
    if camZoom == 0 then
        return 4.0
    elseif camZoom == 1 then
        return 6.5
    elseif camZoom == 2 then
        return 7.5
    elseif camZoom == 4 then
        return 2.0
    end
    return 0.0
end

-- Get entity's ID and coords from where player sis targeting
function GetEntityFromCamera()
    local camCoords = GetFinalRenderedCamCoord()
    local farCoords = GetCoordsFromCam(GetAddedCamDistance())
    local handle = StartShapeTestLosProbe(camCoords.x, camCoords.y, camCoords.z, farCoords.x, farCoords.y, farCoords.z, 31, PlayerPedId(), 0)
    while (true) do
        Wait(0)
        local result, hit, endCoords, surfaceNormal, entityHit = GetShapeTestResult(handle)
        if (result ~= 1) then
            return hit, entityHit
        end
    end
end
