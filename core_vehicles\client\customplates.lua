RegisterNetEvent('plate:client:setplate', function(plateMod)
    local playerPed = PlayerPedId()
    local vehicle = GetVehiclePedIsIn(playerPed, false)

    if plateMod == 'plateticket1' then
        plateModNum = 7
    elseif plateMod == 'plateticket2' then
        plateModNum = 8
    elseif plateMod == 'plateticket3' then
        plateModNum = 9
    elseif plateMod == 'plateticket4' then
        plateModNum = 10
    elseif plateMod == 'event7' then -- YANK(me)TON
        plateModNum = 5
    elseif plateMod == 'plateticket6' then
        plateModNum = 11
    elseif plateMod == 'staffplateticket' then
        plateModNum = 6
    elseif plateMod == 'plateticket5' then
        plateModNum = 12
    else
        return
    end

    if vehicle and vehicle ~= 0 then
        SetVehicleNumberPlateTextIndex(vehicle, plateModNum)
        props = Base.Vehicles.Props.Get(vehicle)
        TriggerServerEvent('plate:server:updateveh', vehicle, props.plateIndex, plateMod)
    else
        TriggerEvent("fdg_ui:SendNotification", "You are not in a vehicle.")
    end
end)