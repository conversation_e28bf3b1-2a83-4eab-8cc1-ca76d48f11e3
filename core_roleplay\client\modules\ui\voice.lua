local voiceCirclesEnabled = false
local playerNamesDist = 10
local takeaway = 0.95
local closePlayers = {}

showVoiceCircles = function()
    voiceCirclesEnabled = true

    Citizen.CreateThread(function()
        while voiceCirclesEnabled do
            local coords = GetEntityCoords(PlayerPedId())
            closePlayers = {}

            for _,p in ipairs(GetActivePlayers()) do
                Wait(1)
                local dist = GetDistanceBetweenCoords(coords, GetEntityCoords(GetPlayerPed(p)))
                if dist < playerNamesDist then
                    table.insert(closePlayers, p)
                end
            end
            Wait(1)
        end
    end)

    Citizen.CreateThread(function()
        while voiceCirclesEnabled do
            for i,p in ipairs(closePlayers) do

                if NetworkIsPlayerTalking(p) then
                    local coords = GetEntityCoords(GetPlayerPed(p))
                    DrawMarker(25, coords.x, coords.y, coords.z - takeaway, 0, 0, 0, 0, 0, 0, 1.0, 1.0, 10.3, 0, 115, 230, 105, 0, 0, 2, 0, 0, 0, 0)
                end
            end
            Citizen.Wait(1)
        end
    end)
end

hideVoiceCircles = function()
    voiceCirclesEnabled = false
end

toggleVoiceCircles = function()
    if voiceCirclesEnabled then
        hideVoiceCircles()
    else
        showVoiceCircles()
    end
end

QUI.registerMenuAction("toggle_voice_circles", toggleVoiceCircles)