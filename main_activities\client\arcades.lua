-- local arcades = {
--     [-1501557515] = {
--         location = "cyberbar",
--         coords = {x = 323.02, y = -916.41, z = 28.24},
--         label = "Play Penetrator",
--         game = "penetrator",
--     }
-- }

-- local closeArcade = false
-- local playingArcade = false

-- onBaseReady(function()
--     Citizen.CreateThread(function()
--         while true do Wait(1)

--             for modelHash, model in pairs(arcades) do
--                 local coords = GetEntityCoords(PlayerPedId())
--                 local closeArcade = GetClosestObjectOfType(coords, 3.0, modelHash, false)

--                 if closeArcade then
--                     local arcadeCoords = GetEntityCoords(closeArcade)
--                     if (GetDistanceBetweenCoords(coords, arcadeCoords.x, arcadeCoords.y, arcadeCoords.z, true) < 1.3) and not isDead and not isCuffed then
--                         Base.ShowHelpNotification('Press ~INPUT_CONTEXT~ to play '..model.label)
--                         if IsControlJustReleased(0, Keys['E']) then
--                             playArcadeGame(model, modelHash)
--                         end
--                     end
--                 end
--             end
--         end
    
--     end)
-- end)

-- function playArcadeGame(gameInfo, modelHash)
--     playingArcade = true
--     TriggerEvent("stream_arcades:startGame", "penetrator")
-- end

-- Citizen.CreateThread(function()
--     while true do Wait(1)
--         if playingArcade then
--             if IsControlJustReleased(0, Keys['ESC']) or IsControlJustReleased(0, Keys['BACKSPACE']) then
--                 TriggerEvent("stream_arcades:endGame")
--                 playingArcade = false
--             end
--         end
--     end

-- end)
