local menuOpen = false
local activeMenuOptions = {}
local activeMenuData = data

function OpenInteractMenu(options, data)
    activeMenuOptions = options
    activeMenuData = data

    -- Convert the options into a format that the NUI can handle
    local displayOptions = {}
    for action, option in pairs(options) do
        local display = option.displayData

        table.insert(displayOptions, {
            label = display.label,
            emoji = display.emoji,
            order = display.order or 999,
            action = action
        })
    end

    -- Sort the options by order and label
    table.sort(displayOptions, function(a, b)
        if a.order == b.order then
            return a.label < b.label
        end

        return a.order < b.order
    end)

    -- Open the NUI menu
    SetCursorLocation(0.5, 0.5)
    SetNuiFocus(true, true)
    SendNUIMessage({
        menuName = "base",
        type = 'openMenu',
        menuElements = displayOptions
    })

    menuOpen = true
end

exports("OpenInteractMenu", OpenInteractMenu)

RegisterNUICallback('disablenuifocus', function(data)
    SetNuiFocus(false, false)
    menuOpen = false
end)

RegisterNUICallback('optionSelected', function(data)
    SetNuiFocus(false, false)
    menuOpen = false

    local action = data.action
    local option = activeMenuOptions[action]

    if not option then
        return print("Failed to find option for action", action)
    end

    print("Selected Option", option.label)
    option.onInteract(activeMenuData)
end)

-- Disable controls when the menu is open
CreateThread(function()
    while true do
        Wait(0)
        if menuOpen then
            DisableControlAction(1, 1)
            DisableControlAction(1, 2)
            DisableControlAction(1, 23)
            DisableControlAction(1, 24)
            DisableControlAction(1, 30)
            DisableControlAction(1, 31)
            DisableControlAction(1, 32)
            DisableControlAction(1, 33)
            DisableControlAction(1, 34)
            DisableControlAction(1, 35)
        end
    end
end)
