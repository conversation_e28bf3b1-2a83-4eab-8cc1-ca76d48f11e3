-- Player Proofs
local proofs = {}
local combinedProofs = {
    bulletProof = false,
    fireProof = false,
    explosionProof = false,
    collisionProof = false,
    meleeProof = false,
    steamProof = false,
    p7 = true,
    drownProof = false
}

local function updateCombinedProofs()
    local playerPed = PlayerPedId()

    -- Reset combined proofs that should be false
    for proof, value in pairs(combinedProofs) do
        if proof ~= "p7" then
            combinedProofs[proof] = false
        end
    end

    -- Update combined proofs based on current player proofs
    for resourcename, resourceProofs in pairs(proofs) do
        for identifier, playerProofs in pairs(resourceProofs) do
            for proof, value in pairs(playerProofs) do
                if value then
                    combinedProofs[proof] = true
                end
            end
        end
    end

    -- Apply combined proofs
    SetEntityProofs(playerPed,
        combinedProofs.bulletProof, combinedProofs.fireProof,
        combinedProofs.explosionProof, combinedProofs.collisionProof,
        combinedProofs.meleeProof, combinedProofs.steamProof,
        combinedProofs.p7, combinedProofs.drownProof
    )
end

function setPlayerProofs(resourcename, identifier, playerProofs)
    if not proofs[resourcename] then proofs[resourcename] = {} end
    proofs[resourcename][identifier] = playerProofs or nil
    updateCombinedProofs()
end

exports("setPlayerProofs", setPlayerProofs)

AddEventHandler("onClientResourceStop", function(resourcename)
    proofs[resourcename] = nil
    updateCombinedProofs()
end)

-- Infinite Stamina
local infiniteStamina = true
local staminaBlockers = {}
function blockInfiniteStamina(resourcename, identifier, state)
    if not state then
        if staminaBlockers[resourcename] then
            staminaBlockers[resourcename][identifier] = nil
            if next(staminaBlockers[resourcename]) == nil then staminaBlockers[resourcename] = nil end
        end
    else
        if not staminaBlockers[resourcename] then staminaBlockers[resourcename] = {} end
        staminaBlockers[resourcename][identifier] = true
    end

    infiniteStamina = next(staminaBlockers) == nil
end

exports("blockInfiniteStamina", blockInfiniteStamina)

AddEventHandler("onClientResourceStop", function(resourcename)
    staminaBlockers[resourcename] = nil
    infiniteStamina = next(staminaBlockers) == nil
end)

-- Health Regen
local healthRegen = 0.0
local healthRegens = {}
function setPlayerHealthRegenBuff(resourcename, identifier, amount)
    if not healthRegens[resourcename] then healthRegens[resourcename] = {} end
    healthRegens[resourcename][identifier] = amount or nil

    healthRegen = 0.0
    for _, buffs in pairs(healthRegens) do
        for _, amount in pairs(buffs) do
            healthRegen = healthRegen + amount
        end
    end
end

exports("setPlayerHealthRegenBuff", setPlayerHealthRegenBuff)

local healthRegenCap = 0.5
local healthRegenCaps = {}

function setPlayerHealthRegenCap(resourcename, identifier, amount)
    if not healthRegenCaps[resourcename] then healthRegenCaps[resourcename] = {} end
    healthRegenCaps[resourcename][identifier] = amount or nil

    healthRegenCap = 0.5
    for _, buffs in pairs(healthRegenCaps) do
        for _, amount in pairs(buffs) do
            healthRegenCap = math.max(healthRegenCap, amount)
        end
    end
end

exports("setPlayerHealthRegenCap", setPlayerHealthRegenCap)

-- Speed
local speedModifiers = {}

local function calculateSpeedModifier()
    local speedModifier = 1.0
    for resourcename, modifiers in pairs(speedModifiers) do
        for identifier, amount in pairs(modifiers) do
            speedModifier = speedModifier + amount
        end
    end
    return math.min(speedModifier, 1.49)
end

function addPlayerSpeedBuff(resourcename, identifier, amount)
    if not speedModifiers[resourcename] then speedModifiers[resourcename] = {} end
    speedModifiers[resourcename][identifier] = amount or nil
end

exports("addPlayerSpeedBuff", addPlayerSpeedBuff)

function removeSpeedBuff(resourcename, identifier)
    if speedModifiers[resourcename] then
        speedModifiers[resourcename][identifier] = nil
    end
end

exports("removeSpeedBuff", removeSpeedBuff)

AddEventHandler("onClientResourceStop", function(resourcename)
    speedModifiers[resourcename] = nil
end)

-- Master Player Thread
Citizen.CreateThread(function()
    while true do
        Wait(0)

        local playerID = PlayerId()
        -- local playerPed = PlayerPedId()

        -- Infinite Stamina
        if infiniteStamina then
            RestorePlayerStamina(playerID, 1.0)
        end

        if healthRegen then
            SetPlayerHealthRechargeMultiplier(playerID, healthRegen)
            SetPlayerHealthRechargeLimit(playerID, healthRegenCap)
            TriggerEvent("test_ac:heal")
        end

        local speedModifier = calculateSpeedModifier()
        SetRunSprintMultiplierForPlayer(playerID, speedModifier)
    end
end)
