@import url("https://fonts.googleapis.com/css2?family=Abel&display=swap");
/* Business Cards */
.quokkacard .container {
  width: 80vh;
  height: 50vh;
  border-radius: 18px;
  background-image: url(../../assets/quokkam.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  /* background-attachment: fixed;
    background-blend-mode: lighten; */

  position: relative;
}

.quokkacard .name {
  position: absolute;
  left: 8%;
  top: 73%;
  width: 75%;
  word-wrap: normal;

  font-family: Abel;
  font-weight: bold;
  font-size: 3vh;
  color: #ffffff;
  text-align: left;
}

.quokkacard .name-edit {
  position: absolute;
  left: 7%;
  top: 35%;
  width: 75%;
  word-wrap: normal;

  font-family: Abel;
  font-weight: bold;
  font-size: 4vh;
  color: #000000;
  text-align: left;
}

.quokkacard .subtitle {
  position: absolute;
  left: 7%;
  top: 47%;
  width: 75%;
  word-wrap: normal;

  font-family: Abel;
  font-size: 3vh;
  color: #ffffff;
  text-align: left;
}

.quokkacard .subtitle-edit {
  position: absolute;
  left: 7%;
  top: 47%;
  width: 75%;
  word-wrap: normal;

  font-family: <PERSON>;
  font-size: 3vh;
  color: #000000;
  text-align: left;
}

.quokkacard .phone {
  position: absolute;
  left: 8.2%;
  top: 85%;

  font-family: Abel;
  font-size: 2vh;
  color: #ffffff;
  text-align: left;
}

.quokkacard .phone-edit {
  position: absolute;
  left: 7%;
  top: 66%;

  font-family: Abel;
  font-size: 2vh;
  color: #000000;
  text-align: left;
}

.quokkacard .email {
  position: absolute;
  left: 28.6%;
  top: 85%;

  font-family: Abel;
  font-size: 2vh;
  color: #ffffff;
  text-align: left;
}

.quokkacard .email-edit {
  position: absolute;
  left: 7%;
  top: 60%;

  font-family: Abel;
  font-size: 2vh;
  color: #000000;
  text-align: left;
}

.quokkacard .controls {
  margin-top: 2vh;
  padding: 8px;
  width: 45vh;
  height: 3vh;
  /* background-color: white; */
  border-radius: 8px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  float: right;
}

.quokkacard .controls button {
  padding: 4px;
  /* background-color: #04254F; */
  /* color: white; */
  /* border: 0px; */
  border-radius: 4px;
  min-width: 20vh;
}
