(function(t){function e(e){for(var i,r,c=e[0],o=e[1],l=e[2],f=0,h=[];f<c.length;f++)r=c[f],Object.prototype.hasOwnProperty.call(a,r)&&a[r]&&h.push(a[r][0]),a[r]=0;for(i in o)Object.prototype.hasOwnProperty.call(o,i)&&(t[i]=o[i]);u&&u(e);while(h.length)h.shift()();return s.push.apply(s,l||[]),n()}function n(){for(var t,e=0;e<s.length;e++){for(var n=s[e],i=!0,c=1;c<n.length;c++){var o=n[c];0!==a[o]&&(i=!1)}i&&(s.splice(e--,1),t=r(r.s=n[0]))}return t}var i={},a={app:0},s=[];function r(e){if(i[e])return i[e].exports;var n=i[e]={i:e,l:!1,exports:{}};return t[e].call(n.exports,n,n.exports,r),n.l=!0,n.exports}r.m=t,r.c=i,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="";var c=window["webpackJsonp"]=window["webpackJsonp"]||[],o=c.push.bind(c);c.push=e,c=c.slice();for(var l=0;l<c.length;l++)e(c[l]);var u=o;s.push([0,"chunk-vendors"]),n()})({0:function(t,e,n){t.exports=n("56d7")},"014a":function(t,e,n){},"04ed":function(t,e,n){"use strict";n("8c0f")},2972:function(t,e,n){},"39d8":function(t,e,n){},"450e":function(t,e,n){"use strict";n("2972")},4709:function(t,e,n){var i=n("c973").default;n("96cf");var a=n("7338"),s=[],r=[];t.exports=function(t){return{TriggerServerEvent:function(e){for(var n=[],i=1;i<arguments.length;i++)n.push(arguments[i]);a.post("http://"+t+"/triggerServerEvent",{eventName:e,data:n})},TriggerServerCallback:function(e,n){for(var i=[],s=2;s<arguments.length;s++)i.push(arguments[s]);a.post("http://"+t+"/triggerServerCallback",{eventName:e,data:i}).then((function(t){"OK"==t.data?n():n.apply(this,JSON.parse(t.data))})).catch((function(t){console.log(t)}))},RegisterNetEvent:function(e,n){a.post("http://"+t+"/registerNetEvent",{eventName:e}).catch((function(t){console.log(t)})),s[e]=n},TriggerClientEvent:function(e){for(var n=[],i=1;i<arguments.length;i++)n.push(arguments[i]);a.post("http://"+t+"/triggerClientEvent",{eventName:e,data:n})},TriggerNUICallback:function(){var e=i(regeneratorRuntime.mark((function e(n,i){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,a.post("http://"+t+"/"+n,i);case 2:return e.abrupt("return",e.sent);case 3:case"end":return e.stop()}}),e)})));function n(t,n){return e.apply(this,arguments)}return n}(),RegisterNUIEvent:function(t,e){r[t]=e}}},window.addEventListener("message",(function(t){var e=t.data;e.netEvent&&s[e.netEvent]&&s[e.netEvent].apply({},e.data),e.nuiEvent&&r[e.nuiEvent]&&r[e.nuiEvent].apply({},e.data)}))},5350:function(t,e,n){},"56d7":function(t,e,n){"use strict";n.r(e);var i=n("2909"),a=(n("e260"),n("e6cf"),n("cca6"),n("a79d"),n("2b0e")),s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-app",{attrs:{id:"v-app-container"}},[n("main",{attrs:{id:"ui-container"}},[t._e()],1),n("main",{attrs:{id:"fullscren-container"}},[n("effect-display"),n("radial-menu"),t._e()],1)])},r=[],c=function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.uiEnabled?n("div",{attrs:{id:"effect-container"}},[t.miniDisplay?n("effect-mini-display",{attrs:{effects:t.effects}}):t._e(),t.miniDisplay?t._e():n("effect-list",{attrs:{effects:t.effects}})],1):t._e()},o=[],l=(n("4de4"),n("b0c0"),n("159b"),function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{attrs:{id:"effect-details-container"}},t._l(t.effects,(function(e,i){var a;return n("section",{key:i,class:(a={},a["status-effect"]=!0,a.alert=e.alert,a)},[n("header",[n("effect-icon",{attrs:{effect:e}}),n("h2",[t._v(" "+t._s(e.label)+" ")]),e.remainingTime?n("h3",[t._v(" "+t._s(new Date(1e3*e.remainingTime).toISOString().substr(14,5))+" ")]):t._e()],1),n("main",[n("span",[t._v(t._s(e.desc))]),e.subdesc?n("i",[t._v(t._s(e.subdesc))]):t._e()])])})),0)}),u=[],f=function(){var t,e=this,n=e.$createElement,i=e._self._c||n;return i("section",{class:(t={},t["status-effect-icon"]=!0,t.alert=e.effect.alert,t)},[i("img",{attrs:{src:"effects/"+e.effect.name+".png"},on:{error:e.setAltImg}})])},h=[],d={name:"EffectIcon",props:{effect:Object},methods:{setAltImg:function(t){t.target.src="effects/default.png"}}},p=d,g=(n("8f84"),n("2877")),v=Object(g["a"])(p,f,h,!1,null,"1dd5a8f5",null),b=v.exports,m={components:{EffectIcon:b},name:"EffectDetailsDisplay",props:{effects:Array},methods:{setAltImg:function(t){t.target.src="effects/default.png"}}},_=m,y=(n("fa5c"),Object(g["a"])(_,l,u,!1,null,"2ebade28",null)),M=y.exports,k=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{attrs:{id:"effect-icon-container"}},t._l(t.effects,(function(t,e){return n("effect-icon",{key:e,staticStyle:{height:"4vh",width:"4vh","margin-top":"6px"},attrs:{effect:t}})})),1)},E=[],x={components:{EffectIcon:b},name:"EffectMiniDisplay",props:{effects:Array},methods:{setAltImg:function(t){t.target.src="effects/default.png"}}},w=x,C=(n("fa81"),Object(g["a"])(w,k,E,!1,null,"26e9f461",null)),O=C.exports,I={components:{EffectMiniDisplay:O,EffectList:M},name:"App",data:function(){return{uiEnabled:!0,miniDisplay:!0,effects:[]}},methods:{},created:function(){var t=this;this.$eb.RegisterNUIEvent("addEffect",(function(e){t.effects.push(e)})),this.$eb.RegisterNUIEvent("removeEffect",(function(e){t.effects=t.effects.filter((function(t){return t.name!==e}))})),this.$eb.RegisterNUIEvent("updateEffectTime",(function(e,n){var i=t.effects.filter((function(t){return t.name==e}));i.forEach((function(t){t.remainingTime=n}))})),this.$eb.RegisterNUIEvent("clearEffects",(function(){t.effects=[]})),this.$eb.RegisterNUIEvent("toggleEffectUI",(function(e){t.uiEnabled=e})),this.$eb.RegisterNUIEvent("toggleEffectMini",(function(e){t.miniDisplay=e}))}},P=I,L=(n("5eaa"),Object(g["a"])(P,c,o,!1,null,"74536f75",null)),S=L.exports,R=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"radial-container noselect"},[t.isVisible?n("svg",{staticClass:"svg__container",attrs:{xmlns:"http://www.w3.org/2000/svg",width:"100%",height:"100%",version:"1.1",viewBox:"0 0 100 100"}},[t._l(t.sectorMarkers,(function(e,i){return[n("text",{key:i+"t1",staticClass:"label__icon",attrs:{x:e.mX,y:e.mY,"text-anchor":"middle"},domProps:{innerHTML:t._s(e.icon)}}),n("text",{key:i+"t2",staticClass:"label__text",attrs:{x:e.mX,y:e.mY+5,"text-anchor":"middle"}},[t._v(" "+t._s(e.text)+" ")])]})),t._l(t.sectorMarkers,(function(e,i){return n("line",{key:i+"l",attrs:{stroke:"rgba(255, 255, 255, 0.5)","stroke-width":t.strokeWidth,x1:e.sX,y1:e.sY,x2:e.lX,y2:e.lY}})})),t._l(t.sectorMarkers,(function(e,i){return n("path",{key:i+"s",staticClass:"sector",class:{clicked:t.clickedSector===i},attrs:{fill:"transparent",d:"M "+e.sX+" "+e.sY+", L "+e.x+" "+e.y+", L "+e.nX+" "+e.nY+", L "+e.eX+" "+e.eY+", A "+t.radius+" "+t.radius+" 0 0 0 "+e.sX+" "+e.sY},on:{mouseenter:t.playSfx,click:function(n){return t.handleClick(e,i)},mousedown:function(e){t.clickedSector=i},mouseup:function(e){t.clickedSector=null},mouseleave:function(e){t.clickedSector=null}}})})),0===t.subMenus.length?[n("circle",{attrs:{r:t.radius,fill:"transparent",stroke:"white","stroke-width":t.strokeWidth,cx:"50",cy:"50"}}),n("circle",{attrs:{r:t.radius-1,fill:"transparent",stroke:"rgba(58, 154, 248, 0.5)","stroke-width":.3,cx:"50",cy:"50"}}),n("circle",{attrs:{r:t.radius-(t.radius-1),fill:"transparent",stroke:"rgba(58, 154, 248, 0.5)","stroke-width":.1,cx:"50",cy:"50"}})]:[n("text",{staticClass:"label__text",style:{"font-size":"0.1rem"},attrs:{x:"50",y:"50","text-anchor":"middle",fill:"white","dominant-baseline":"middle"}},[t._v(" Back ")]),n("circle",{staticClass:"center_back",attrs:{r:t.radius,fill:"transparent",stroke:"white","stroke-width":t.strokeWidth,cx:"50",cy:"50"},on:{click:function(e){return t.subMenus.splice(t.subMenus.length-1,1)}}})],t.pagesCount>1&&1!==t.page?[n("path",{staticClass:"paginate_btn",attrs:{d:t.paginatePath.left,"stroke-width":t.strokeWidth},on:{click:function(e){t.page--}}}),n("text",{staticClass:"label__text",attrs:{x:t.paginatePath.iLX,y:t.paginatePath.iLY,"text-anchor":"middle","dominant-baseline":"middle"}},[t._v(" Q ")]),n("rect",{attrs:{x:t.paginatePath.iLX-1.5,y:t.paginatePath.iLY-1.5,rx:"1",ry:"1",width:"3",height:"3",stroke:"rgba(255, 255, 255, 0.5)","stroke-width":"0.1",fill:"transparent",anchor:"middle"}})]:t._e(),t.pagesCount>1&&t.page!==t.pagesCount?[n("path",{staticClass:"paginate_btn",attrs:{d:t.paginatePath.right,"stroke-width":t.strokeWidth},on:{click:function(e){t.page++}}}),n("text",{staticClass:"label__text",attrs:{x:t.paginatePath.iRX,y:t.paginatePath.iRY,"text-anchor":"middle","dominant-baseline":"middle"}},[t._v(" E ")]),n("rect",{attrs:{x:t.paginatePath.iRX-1.5,y:t.paginatePath.iRY-1.5,rx:"1",ry:"1",width:"3",height:"3",stroke:"rgba(255, 255, 255, 0.5)","stroke-width":"0.1",fill:"transparent",anchor:"middle"}})]:t._e()],2):t._e()])},$=[],N=n("5530"),j=(n("fb6a"),n("99af"),n("a434"),{data:function(){return{isVisible:!1,allSectors:[],radius:5,lineLength:30,strokeWidth:.25,paginateOffset:15,iconOffset:.75,controlOffset:.3,clickedSector:null,subMenus:[],page:1}},computed:{sectorsData:function(){if(Array.isArray(this.subMenus)&&0!==this.subMenus.length){for(var t=this.allSectors,e=0;e<this.subMenus.length;e++){if(!("subMenu"in t[this.subMenus[e]]))break;t=t[this.subMenus[e]].subMenu}return t}return this.allSectors},sectors:function(){var t=performance.now(),e=Object(i["a"])(this.sectorsData);if(e.length>8){for(var n=[],a=8,s=0;s<e.length;s+=a)n.push(e.slice(s,s+a));var r=performance.now();return console.info("sectors took ".concat(r-t,"ms")),n[this.page-1]}return e},pagesCount:function(){return Math.ceil(this.sectorsData.length/8)},sectorCount:function(){var t;return null!==(t=this.sectors.length)&&void 0!==t?t:0},sectorMarkers:function(){for(var t=performance.now(),e=[],n=360/this.sectorCount,i=0;i<this.sectorCount;i++){var a=i*n-90,s=500*Math.cos(a*(Math.PI/180)),r=500*Math.sin(a*(Math.PI/180)),c=this.lineLength*Math.cos(a*(Math.PI/180)),o=this.lineLength*Math.sin(a*(Math.PI/180)),l=this.radius*Math.cos(a*(Math.PI/180)),u=this.radius*Math.sin(a*(Math.PI/180)),f=this.lineLength*this.iconOffset*Math.cos((a+n/2)*(Math.PI/180)),h=this.lineLength*this.iconOffset*Math.sin((a+n/2)*(Math.PI/180)),d=this.lineLength*this.controlOffset*Math.cos((a+n/2)*(Math.PI/180)),p=this.lineLength*this.controlOffset*Math.sin((a+n/2)*(Math.PI/180)),g=(i+1)*n-90,v=500*Math.cos(g*(Math.PI/180)),b=500*Math.sin(g*(Math.PI/180)),m=this.radius*Math.cos(g*(Math.PI/180)),_=this.radius*Math.sin(g*(Math.PI/180));e.push(Object(N["a"])({x:s+50,y:r+50,nX:v+50,nY:b+50,sX:l+50,sY:u+50,eX:m+50,eY:_+50,lX:c+50,lY:o+50,mX:f+50,mY:h+50,mcX:d+50,mcY:p+50},this.sectors[i]))}var y=performance.now();return console.info("sector markers took ".concat(y-t,"ms")),e},paginatePath:function(){var t=performance.now(),e=2,n=3,i={left:"M ".concat(50-(this.lineLength+this.paginateOffset)," ").concat(50-this.radius*e,", L ").concat(50-(this.lineLength+this.paginateOffset)-this.radius*e," 50, L  ").concat(50-(this.lineLength+this.paginateOffset)," ").concat(50+this.radius*e),right:"M ".concat(50+(this.lineLength+this.paginateOffset)," ").concat(50-this.radius*e,", L ").concat(50+(this.lineLength+this.paginateOffset)+this.radius*e," 50, L  ").concat(50+(this.lineLength+this.paginateOffset)," ").concat(50+this.radius*e),iLX:50-(this.lineLength+this.paginateOffset)-this.radius*e-n,iLY:50,iRX:50+(this.lineLength+this.paginateOffset)+this.radius*e+n,iRY:50},a=performance.now();return console.info("paginatePath took ".concat(a-t,"ms")),i}},methods:{handleClick:function(t,e){if("subMenu"in t)this.subMenus.push(e);else if("cb"in t)t.cb(),t.preventClose||(this.isVisible=!1,this.$eb.TriggerNUICallback("toggleRadialMenu",!1));else if("action"in t){var n;this.$eb.TriggerNUICallback("triggerRadialAction",{action:t.action,data:null!==(n=t.data)&&void 0!==n?n:{}}),t.preventClose||(this.isVisible=!1,this.$eb.TriggerNUICallback("toggleRadialMenu",!1))}},reset:function(){this.subMenus.splice(0,this.subMenus.length),this.page=1},playSfx:function(){}},created:function(){var t=this;this.$eb.RegisterNUIEvent("toggleRadialMenu",(function(e){t.isVisible=e})),this.$eb.RegisterNUIEvent("setRadialMenuItems",(function(e){t.allSectors=e||[],t.subMenus=[]}))}}),X=j,Y=(n("aab8"),Object(g["a"])(X,R,$,!1,null,null,null)),T=Y.exports,U=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"crosshair-container"},[t.isVisible?n("svg",{attrs:{width:"100",height:"100"}},[n("circle",{attrs:{cx:"50",cy:"50",r:"5",stroke:"white",fill:"transparent","stroke-width":"1.5"}},[n("animate",{attrs:{attributeType:"xml",attributeName:"r",from:"0",to:"5",dur:"0.1s"}})])]):t._e()])},D=[],A={data:function(){return{isVisible:!1}},created:function(){var t=this;this.$eb.RegisterNUIEvent("toggleCrosshair",(function(e){t.isVisible=e}))}},V=A,W=(n("04ed"),Object(g["a"])(V,U,D,!1,null,null,null)),B=W.exports,J=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"cm__container",style:"left: calc(50% - (80px * "+t.sounds.length+" ));"},[t._l(t.sounds,(function(e,i){return n("div",{key:e.label,staticClass:"cm__container__button",class:{"cm__container__button--active":e.active},on:{click:function(n){return n.stopPropagation(),t.playSound(i,e.sound,e.duration)}}},[n("span",[t._v(" "+t._s(e.label)+" ("+t._s(e.duration)+"s) ")])])})),t._m(0)],2)},z=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"cm__container__button cm__container__button--close"},[n("span",[t._v(" CLOSE MENU ")])])}],F={data:function(){return{sounds:[{label:"Sinus to Brady",sound:"sinus",active:!1,duration:23},{label:"Crashing",sound:"crashing",active:!1,duration:38},{label:"Flatline",sound:"flatline",active:!1,duration:8},{label:"Defib",sound:"defib",active:!1,duration:9}]}},methods:{playSound:function(t,e,n){var i=this;this.sounds[t].active||(this.sounds[t].active=!this.sounds[t].active,setTimeout((function(){i.sounds[t].active=!i.sounds[t].active}),1e3*n),this.$eb.TriggerNUICallback("medical:playCardiacSound",{sound:e,duration:n}))}}},H=F,K=(n("450e"),Object(g["a"])(H,J,z,!1,null,"97beb420",null)),Q=K.exports,q={components:{EffectDisplay:S,RadialMenu:T,Crosshair:B,CardioMonitorContainer:Q},name:"App"},G=q,Z=(n("5c0b"),n("8cf3"),n("6544")),tt=n.n(Z),et=n("7496"),nt=Object(g["a"])(G,s,r,!1,null,"aac45bb2",null),it=nt.exports;tt()(nt,{VApp:et["a"]});n("b7e3");var at=n("e37d"),st=n("4709"),rt=n.n(st),ct=n("f309");n("5363");a["a"].use(at["a"]),a["a"].prototype.$eb=new rt.a("base_ui");var ot=[];window.addEventListener("message",(function(t){var e=t.data;void 0!=e.clientEvent&&ot[e.clientEvent]&&ot[e.clientEvent].apply(ot,Object(i["a"])(e.data))})),a["a"].prototype.$registerClientEvent=function(t,e){ot[t]=e},a["a"].use(ct["a"]);var lt=new ct["a"]({theme:{themes:{light:{primary:"#44515c",secondary:"#282828",accent:"#44515c"}}}});a["a"].config.productionTip=!1,new a["a"]({vuetify:lt,render:function(t){return t(it)}}).$mount("#app")},"5c0b":function(t,e,n){"use strict";n("9c0c")},"5eaa":function(t,e,n){"use strict";n("39d8")},"65c9":function(t,e,n){},"8c0f":function(t,e,n){},"8cf3":function(t,e,n){"use strict";n("014a")},"8f84":function(t,e,n){"use strict";n("e209")},"9c0c":function(t,e,n){},"9ef5":function(t,e,n){},aab8:function(t,e,n){"use strict";n("9ef5")},b7e3:function(t,e,n){},e209:function(t,e,n){},fa5c:function(t,e,n){"use strict";n("65c9")},fa81:function(t,e,n){"use strict";n("5350")}});
//# sourceMappingURL=app.f181df6c.js.map