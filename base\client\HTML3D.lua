local txd = GetCurrentResourceName() .. "_HTML3D"
local textureDict = CreateRuntimeTxd(txd)
local textureCount = 0
local activeHTML = {}
local networkedHTML = {}

---@class HTML3DOptions
---@field coords vector3? coordinates to draw the HTML on. (Required if entity is not specified)
---@field entity number? entity to draw the HTML on. Defaults to the player. (Required if coords is not specified)
---@field serverId number? server ID of the player to draw the HTML on. (Required if entity/coords is not specified)
---@field textureSizeX number? width of the texture to draw the HTML on. Defaults to 512.
---@field textureSizeY number? height of the texture to draw the HTML on. Defaults to 512.
---@field heading number? direction the HTML element will face. Defaults to 0.0.
---@field offsetZ number? vertical offset to draw the HTML. Defaults to 0.0.
---@field maxDist number? maximum distance to draw the HTML. Defaults to 10.0.
---@field scaleX number? X scale of the HTML. Defaults to 1.0.
---@field scaleY number? Y scale of the HTML. Defaults to 1.0.
---@field scale number? scale of X and Y of the HTML. Defaults to 1.0. Only works when scaleX and scaleY are not specified.
---@field duiMessage table? message to send to the HTML when it is created.
---@field timeout number? timeout in milliseconds to remove the HTML after. Defaults to no timeout.
---@field faceCamera boolean? whether or not the HTML should face the camera. Defaults to false.
---@field bobbing boolean? whether or not the HTML should bob up and down. Defaults to false.
---@field rotate boolean? whether or not the HTML should rotate (spin). Defaults to false.
---@field persistent boolean? whether or not the HTML should be stored on the server and re-rendered when a player joins. Only works when networked. Defaults to false.

---Specifies a URL to draw in 3D space, similar to 3D text.
---@param url string The URL to draw.
---@param options HTML3DOptions
---@return string txn Handle for the created HTML.
---@return number dui The DUI for the created HTML.
function DrawHTML3D(url, options)
    if not options.coords and not DoesEntityExist(options.entity) then
        error("Not rendering HTML3D because no coords or entity was specified")
    end

    local dui = CreateDui(url, options.textureSizeX or 512, options.textureSizeY or 512)
    local duiHandle = GetDuiHandle(dui)

    local txn = "txn_" .. textureCount
    textureCount = textureCount + 1
    -- create non blocking thread for all of these operations as they need waits
    CreateThread(function()
        CreateRuntimeTextureFromDuiHandle(textureDict, txn, duiHandle)
        Wait(500)

        if textureCount >= 512 then
            textureCount = 0
        end

        activeHTML[txn] = true

        if options.duiMessage then
            SendDuiMessage(dui, json.encode(options.duiMessage))
        end

        if options.timeout then
            CreateThread(function()
                Wait(options.timeout)
                RemoveHTML3D(txn)
            end)
        end

        -- please do not touch these rotation values I almost cried setting this up - Martibo
        local heading = options.heading or 0.0

        local xRot = nil
        local yRot = nil
        local zRot = nil

        if options.faceCamera then
            xRot = -90.0
            yRot = 0.0
            zRot = -0.01
        else
            xRot = 90.0
            yRot = -180.0
            zRot = 360.0 - heading
        end

        -- actively will render the 3d html
        CreateThread(function()
            while activeHTML[txn] do
                Wait(0)
                
                local drawCoords = options.coords or GetEntityCoords(options.entity)

                if options.maxDist then
                    local dist = #(drawCoords - GetEntityCoords(PlayerPedId()))
                    if dist > options.maxDist then
                        drawCoords = nil
                        Wait(500)
                    end
                end

                if drawCoords then
                    local scaleX = options.scaleX or options.scale or 1.0
                    local scaleY = options.scaleY or options.scale or 1.0
                    DrawMarker(43, drawCoords.x, drawCoords.y, drawCoords.z + (options.offsetZ or 0.0), 0.0, 0.0, 0.0, xRot, yRot, zRot, scaleX, scaleY, 0.0, 255, 255, 255, 1.0, options.bobbing or false, options.faceCamera or false, 5, options.rotate or false, txd, txn, false)
                end
            end

            -- print(">>> DESTROYING DUI: ", txn, dui, duiHandle)
            DestroyDui(dui)
        end)
    end)

    return txn, dui
end

---Removes a 3D HTML from the world.
---@param txn string The handle of the HTML to remove.
function RemoveHTML3D(txn)
    -- If this is a networked HTML, remove it from the server
    if networkedHTML[txn] then
        TriggerServerEvent("html3d:remove", GetCurrentResourceName(), txn)
    else
        activeHTML[txn] = false
    end
end

---Draws a 3D HTML on the specified entity or coordinates, networked to other players.
---@param url string The URL to draw.
---@param options HTML3DOptions
function DrawHTML3DNetworked(url, options)
    local serverID = GetPlayerServerId(PlayerId())
    local netDrawID = serverID .. "_" .. textureCount
    textureCount = textureCount + 1

    local networkSettings = {
        netDrawID = netDrawID,
        source = GetPlayerServerId(PlayerId()),
        persistent = options.persistent or false
    }

    options.persistent = nil

    if options.serverId then
        networkSettings.serverId = options.serverId
        options.serverId = nil
    elseif options.entity then
        if IsPedAPlayer(options.entity) then
            networkSettings.serverId = GetPlayerServerId(NetworkGetPlayerIndexFromPed(options.entity))
            print("Drawing HTML3D on player ", networkSettings.serverId)
        else
            networkSettings.netId = NetworkGetNetworkIdFromEntity(options.entity)
            print("Drawing HTML3D on entity ", networkSettings.netId)
        end

        options.entity = nil
    end

    TriggerServerEvent("html3d:draw", GetCurrentResourceName(), url, options, networkSettings)
    return netDrawID
end

-- Handle network requests, parsing netIDs and serverIDs into entities
local function handleNetworkedHTML3D(url, options, networkOptions)
    if networkOptions.serverId then
        local player = GetPlayerFromServerId(networkOptions.serverId)

        -- Allow -1 if it's the local player
        if player ~= -1 or networkOptions.source == GetPlayerServerId(PlayerId()) then
            options.entity = GetPlayerPed(player)
        end
    elseif networkOptions.netId then
        if NetworkDoesNetworkIdExist(networkOptions.netId) then
            options.entity = NetworkGetEntityFromNetworkId(networkOptions.netId)
        end
    end

    if not options.entity and not options.coords then
        print("Skipping render - no entity or coords", networkOptions.netDrawID)
        return
    end

    if networkedHTML[networkOptions.netDrawID] then
        local handle = networkedHTML[networkOptions.netDrawID].handle
        RemoveHTML3D(handle)
        networkedHTML[networkOptions.netDrawID] = nil
        return
    end

    local handle, dui = DrawHTML3D(url, options)
    networkedHTML[networkOptions.netDrawID] = {
        handle = handle,
        dui = dui
    }
end
RegisterNetEvent(GetCurrentResourceName() .. ":html3d:draw", handleNetworkedHTML3D)

local function handleNetworkedHTML3DRemove(txn)
    local html = networkedHTML[txn]
    if html then
        RemoveHTML3D(html.handle)
        networkedHTML[txn] = nil
    end
end
RegisterNetEvent(GetCurrentResourceName() .. ":html3d:remove", handleNetworkedHTML3DRemove)

local function handleNetworkedHTML3DMessage(txn, data)
    local html = networkedHTML[txn]
    if html then
        SendDuiMessage(html.dui, json.encode(data))
    end
end
RegisterNetEvent(GetCurrentResourceName() .. ":html3d:message", handleNetworkedHTML3DMessage)
