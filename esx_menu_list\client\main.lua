local MenuType    = 'list'
local OpenedMenus = {}

local openMenu = function(namespace, name, data)

	OpenedMenus[namespace .. '_' .. name] = true

	SendNUIMessage({
		action    = 'openMenu',
		namespace = namespace,
		name      = name,
		data      = data,
	})

	
	Wait(200)
	SetNuiFocus(true, true)
end

local closeMenu = function(namespace, name)

	OpenedMenus[namespace .. '_' .. name] = nil
	local OpenedMenuCount = 0

	SendNUIMessage({
		action    = 'closeMenu',
		namespace = namespace,
		name      = name,
		data      = value,
	})

	for k,v in pairs(OpenedMenus) do
		if v == true then
			OpenedMenuCount = OpenedMenuCount + 1
		end
	end

	if OpenedMenuCount == 0 then
		SetNuiFocus(false)
	end
end



RegisterNUICallback('menu_submit', function(data, cb)
	local menu = Base.UI.Menu.GetOpened(MenuType, data._namespace, data._name)

	if menu.submit ~= nil then
		menu.submit(data, menu)
	end

	cb('OK')
end)

RegisterNUICallback('menu_cancel', function(data, cb)
	local menu = Base.UI.Menu.GetOpened(MenuType, data._namespace, data._name)

	if menu.cancel ~= nil then
		menu.cancel(data, menu)
	end

	cb('OK')
end)

onBaseReady(function()
	Base.UI.Menu.RegisterType(MenuType, openMenu, closeMenu)
end)