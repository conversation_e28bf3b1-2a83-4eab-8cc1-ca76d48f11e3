onBaseReady(function()
  -- Base.Markers.Delete("casinoblacklister")
end)

-- function OpenBlacklistMenu()
--   TriggerServerCallback("main_casino:openBlacklistMenu", function(result)
--     Base.UI.Menu.CloseAll()
--     Base.UI.Menu.Open("default", GetCurrentResourceName(), "casino_blacklist_menu",
--       {
--         title = "Select Casino Blacklist Duration",
--         align = "bottom-right",
--         elements = result,
--       },
--       function(d)
--         OpenConfirmMenu("Are you sure you want to blacklist yourself for " .. d.current.label .. " real time?", function(confirm1)
--           if confirm1 then
--             OpenConfirmMenu("Do you understand that this is irreversible?", function(confirm2)
--               if confirm2 then
--                 TriggerServerEvent("main_casino:BlacklistPlayer", d.current.value)
--                 Base.UI.Menu.CloseAll()
--               end
--             end)
--           end
--         end)
--       end,
--       function()
--         Base.UI.Menu.CloseAll()
--       end
--     )
--   end)
-- end
