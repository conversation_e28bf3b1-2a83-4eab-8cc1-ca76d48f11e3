﻿<?xml version="1.0" encoding="UTF-8"?>
<CPedModelInfo__InitDataList>
  <residentTxd>comp_peds_generic</residentTxd>
  <residentAnims />
  <InitDatas>
<Item>
      <Name>mp_m_roxnatguardu</Name>
      <PropsName>mp_m_roxnatguardu_p</PropsName>
      <ClipDictionaryName>move_m@generic</ClipDictionaryName>
      <BlendShapeFileName>null</BlendShapeFileName>
      <ExpressionSetName>expr_set_ambient_male_skirt</ExpressionSetName>
      <ExpressionDictionaryName>null</ExpressionDictionaryName>
      <ExpressionName>null</ExpressionName>
      <Pedtype>ARMY</Pedtype>
      <MovementClipSet>move_chubby</MovementClipSet>
      <StrafeClipSet>move_ped_strafing</StrafeClipSet>
      <MovementToStrafeClipSet>move_ped_to_strafe</MovementToStrafeClipSet>
      <InjuredStrafeClipSet>move_strafe_injured</InjuredStrafeClipSet>
      <FullBodyDamageClipSet>dam_ko</FullBodyDamageClipSet>
      <AdditiveDamageClipSet>dam_ad</AdditiveDamageClipSet>
      <DefaultGestureClipSet>ANIM_GROUP_GESTURE_M_GENERIC</DefaultGestureClipSet>
      <FacialClipsetGroupName>facial_clipset_group_gen_male</FacialClipsetGroupName>
      <DefaultVisemeClipSet>ANIM_GROUP_VISEMES_M_LO</DefaultVisemeClipSet>
      <SidestepClipSet>CLIP_SET_ID_INVALID</SidestepClipSet>
      <PoseMatcherName>Male</PoseMatcherName>
      <PoseMatcherProneName>Male_prone</PoseMatcherProneName>
      <GetupSetHash>NMBS_SLOW_GETUPS</GetupSetHash>
      <CreatureMetadataName>null</CreatureMetadataName>
      <DecisionMakerName>COP</DecisionMakerName>
      <MotionTaskDataSetName>STANDARD_PED</MotionTaskDataSetName>
      <DefaultTaskDataSetName>STANDARD_PED</DefaultTaskDataSetName>
      <PedCapsuleName>STANDARD_MALE</PedCapsuleName>
      <PedLayoutName />
      <PedComponentSetName />
      <PedComponentClothName />
      <PedIKSettingsName />
      <TaskDataName />
      <IsStreamedGfx value="false" />
      <AmbulanceShouldRespondTo value="false" />
      <CanRideBikeWithNoHelmet value="true" />
      <CanSpawnInCar value="true" />
      <IsHeadBlendPed value="false" />
      <bOnlyBulkyItemVariations value="false" />
      <RelationshipGroup>ARMY</RelationshipGroup>
      <NavCapabilitiesName>STANDARD_PED</NavCapabilitiesName>
      <PerceptionInfo>DEFAULT_PERCEPTION</PerceptionInfo>
      <DefaultBrawlingStyle>BS_AI</DefaultBrawlingStyle>
      <DefaultUnarmedWeapon>WEAPON_UNARMED</DefaultUnarmedWeapon>
      <Personality>ARMY</Personality>
      <CombatInfo>ARMY</CombatInfo>
      <VfxInfoName>VFXPEDINFO_HUMAN_GENERIC</VfxInfoName>
      <AmbientClipsForFlee>FLEE</AmbientClipsForFlee>
      <Radio1>RADIO_GENRE_OFF</Radio1>
      <Radio2>RADIO_GENRE_OFF</Radio2>
      <FUpOffset value="0.000000" />
      <RUpOffset value="0.000000" />
      <FFrontOffset value="0.000000" />
      <RFrontOffset value="0.147000" />
      <MinActivationImpulse value="20.000000" />
      <Stubble value="0.000000" />
      <HDDist value="3.000000" />
      <TargetingThreatModifier value="1.000000" />
      <KilledPerceptionRangeModifer value="-1.000000" />
      <Sexiness>SF_JEER_AT_HOT_PED</Sexiness>
      <Age value="0" />
      <MaxPassengersInCar value="0" />
      <ExternallyDrivenDOFs />
      <PedVoiceGroup>G_M_M_X17_RSO_PVG</PedVoiceGroup>
      <AnimalAudioObject />
      <AbilityType>SAT_NONE</AbilityType>
      <ThermalBehaviour>TB_WARM</ThermalBehaviour>
      <SuperlodType>SLOD_HUMAN</SuperlodType>
      <ScenarioPopStreamingSlot>SCENARIO_POP_STREAMING_NORMAL</ScenarioPopStreamingSlot>
      <DefaultSpawningPreference>DSP_NORMAL</DefaultSpawningPreference>
      <DefaultRemoveRangeMultiplier value="1.000000" />
      <AllowCloseSpawning value="false" />
</Item>    
<Item>
      <Name>amb_rox_m_paramedic_01</Name>
      <PropsName>null</PropsName>
      <ClipDictionaryName>move_m@generic</ClipDictionaryName>
      <BlendShapeFileName>null</BlendShapeFileName>
      <ExpressionSetName>expr_set_ambient_male</ExpressionSetName>
      <ExpressionDictionaryName>null</ExpressionDictionaryName>
      <ExpressionName>null</ExpressionName>
      <Pedtype>MEDIC</Pedtype>
      <MovementClipSet>MOVE_PARAMEDIC</MovementClipSet>
      <MovementClipSets/>
      <StrafeClipSet>MOVE_PED_STRAFING</StrafeClipSet>
      <MovementToStrafeClipSet>MOVE_PED_TO_STRAFE</MovementToStrafeClipSet>
      <InjuredStrafeClipSet>MOVE_STRAFE_INJURED</InjuredStrafeClipSet>
      <FullBodyDamageClipSet>DAM_KO</FullBodyDamageClipSet>
      <AdditiveDamageClipSet>DAM_AD</AdditiveDamageClipSet>
      <DefaultGestureClipSet>ANIM_GROUP_GESTURE_M_GENERIC</DefaultGestureClipSet>
      <FacialClipsetGroupName>facial_clipset_group_gen_male</FacialClipsetGroupName>
      <DefaultVisemeClipSet>ANIM_GROUP_VISEMES_M_LO</DefaultVisemeClipSet>
      <SidestepClipSet>CLIP_SET_ID_INVALID</SidestepClipSet>
      <PoseMatcherName>Male</PoseMatcherName>
      <PoseMatcherProneName>Male_prone</PoseMatcherProneName>
      <GetupSetHash>NMBS_SLOW_GETUPS</GetupSetHash>
      <CreatureMetadataName>ambientPed_upperWrinkles</CreatureMetadataName>
      <DecisionMakerName>OFFDUTY_EMT</DecisionMakerName>
      <MotionTaskDataSetName>STANDARD_PED</MotionTaskDataSetName>
      <DefaultTaskDataSetName>STANDARD_PED</DefaultTaskDataSetName>
      <PedCapsuleName>STANDARD_MALE</PedCapsuleName>
      <PedLayoutName/>
      <PedComponentSetName/>
      <PedComponentClothName/>
      <PedIKSettingsName/>
      <TaskDataName/>
      <IsStreamedGfx value="false"/>
      <AmbulanceShouldRespondTo value="true"/>
      <CanRideBikeWithNoHelmet value="false"/>
      <CanSpawnInCar value="true"/>
      <IsHeadBlendPed value="false"/>
      <bOnlyBulkyItemVariations value="false"/>
      <RelationshipGroup>MEDIC</RelationshipGroup>
      <NavCapabilitiesName>STANDARD_PED</NavCapabilitiesName>
      <PerceptionInfo>DEFAULT_PERCEPTION</PerceptionInfo>
      <DefaultBrawlingStyle>BS_AI</DefaultBrawlingStyle>
      <DefaultUnarmedWeapon>WEAPON_UNARMED</DefaultUnarmedWeapon>
      <Personality>ServiceMales</Personality>
      <CombatInfo>default</CombatInfo>
      <VfxInfoName>VFXPEDINFO_HUMAN_GENERIC</VfxInfoName>
      <AmbientClipsForFlee>FLEE</AmbientClipsForFlee>
      <Radio1>RADIO_GENRE_OFF</Radio1>
      <Radio2>RADIO_GENRE_OFF</Radio2>
      <FUpOffset value="0.00000000"/>
      <RUpOffset value="0.00000000"/>
      <FFrontOffset value="0.00000000"/>
      <RFrontOffset value="0.14700000"/>
      <MinActivationImpulse value="20.00000000"/>
      <Stubble value="0.00000000"/>
      <HDDist value="3.00000000"/>
      <TargetingThreatModifier value="1.00000000"/>
      <KilledPerceptionRangeModifer value="-1.00000000"/>
      <Sexiness/>
      <Age value="0"/>
      <MaxPassengersInCar value="0"/>
      <ExternallyDrivenDOFs/>
      <PedVoiceGroup>S_M_M_PARAMEDIC_01_R2PVG</PedVoiceGroup>
      <AnimalAudioObject/>
      <AbilityType>SAT_NONE</AbilityType>
      <ThermalBehaviour>TB_WARM</ThermalBehaviour>
      <SuperlodType>SLOD_HUMAN</SuperlodType>
      <ScenarioPopStreamingSlot>SCENARIO_POP_STREAMING_NORMAL</ScenarioPopStreamingSlot>
      <DefaultSpawningPreference>DSP_NORMAL</DefaultSpawningPreference>
      <DefaultRemoveRangeMultiplier value="1.00000000"/>
      <AllowCloseSpawning value="false"/>
    </Item>
    <Item>
      <Name>S_M_Y_RXFireman_01</Name>
      <PropsName>S_M_Y_RXFireman_01_p</PropsName>
      <ClipDictionaryName>move_m@generic</ClipDictionaryName>
      <BlendShapeFileName>null</BlendShapeFileName>
      <ExpressionSetName>expr_set_firemen</ExpressionSetName>
      <ExpressionDictionaryName>null</ExpressionDictionaryName>
      <ExpressionName>null</ExpressionName>
      <Pedtype>FIREMAN</Pedtype>
      <MovementClipSet>MOVE_M@TOOL_BELT@A</MovementClipSet>
      <MovementClipSets/>
      <StrafeClipSet>MOVE_PED_STRAFING</StrafeClipSet>
      <MovementToStrafeClipSet>MOVE_PED_TO_STRAFE</MovementToStrafeClipSet>
      <InjuredStrafeClipSet>MOVE_STRAFE_INJURED</InjuredStrafeClipSet>
      <FullBodyDamageClipSet>DAM_KO</FullBodyDamageClipSet>
      <AdditiveDamageClipSet>DAM_AD</AdditiveDamageClipSet>
      <DefaultGestureClipSet>ANIM_GROUP_GESTURE_M_GENERIC</DefaultGestureClipSet>
      <FacialClipsetGroupName>facial_clipset_group_gen_male</FacialClipsetGroupName>
      <DefaultVisemeClipSet>ANIM_GROUP_VISEMES_M_LO</DefaultVisemeClipSet>
      <SidestepClipSet>CLIP_SET_ID_INVALID</SidestepClipSet>
      <PoseMatcherName>Male</PoseMatcherName>
      <PoseMatcherProneName>Male_prone</PoseMatcherProneName>
      <GetupSetHash>NMBS_SLOW_GETUPS</GetupSetHash>
      <CreatureMetadataName>fx_fire_torch</CreatureMetadataName>
      <DecisionMakerName>OFFDUTY_EMT</DecisionMakerName>
      <MotionTaskDataSetName>STANDARD_PED</MotionTaskDataSetName>
      <DefaultTaskDataSetName>STANDARD_PED</DefaultTaskDataSetName>
      <PedCapsuleName>STANDARD_MALE</PedCapsuleName>
      <PedLayoutName/>
      <PedComponentSetName/>
      <PedComponentClothName/>
      <PedIKSettingsName/>
      <TaskDataName/>
      <IsStreamedGfx value="false"/>
      <AmbulanceShouldRespondTo value="true"/>
      <CanRideBikeWithNoHelmet value="false"/>
      <CanSpawnInCar value="true"/>
      <IsHeadBlendPed value="false"/>
      <bOnlyBulkyItemVariations value="false"/>
      <RelationshipGroup>FIREMAN</RelationshipGroup>
      <NavCapabilitiesName>STANDARD_PED</NavCapabilitiesName>
      <PerceptionInfo>DEFAULT_PERCEPTION</PerceptionInfo>
      <DefaultBrawlingStyle>BS_AI</DefaultBrawlingStyle>
      <DefaultUnarmedWeapon>WEAPON_UNARMED</DefaultUnarmedWeapon>
      <Personality>ServiceMales</Personality>
      <CombatInfo>default</CombatInfo>
      <VfxInfoName>VFXPEDINFO_HUMAN_GENERIC</VfxInfoName>
      <AmbientClipsForFlee>FLEE</AmbientClipsForFlee>
      <Radio1>RADIO_GENRE_OFF</Radio1>
      <Radio2>RADIO_GENRE_OFF</Radio2>
      <FUpOffset value="0.00000000"/>
      <RUpOffset value="0.00000000"/>
      <FFrontOffset value="0.00000000"/>
      <RFrontOffset value="0.14700000"/>
      <MinActivationImpulse value="20.00000000"/>
      <Stubble value="0.00000000"/>
      <HDDist value="3.00000000"/>
      <TargetingThreatModifier value="1.00000000"/>
      <KilledPerceptionRangeModifer value="-1.00000000"/>
      <Sexiness/>
      <Age value="0"/>
      <MaxPassengersInCar value="0"/>
      <ExternallyDrivenDOFs/>
      <PedVoiceGroup>S_M_Y_FIREMAN_01_R2PVG</PedVoiceGroup>
      <AnimalAudioObject/>
      <AbilityType>SAT_NONE</AbilityType>
      <ThermalBehaviour>TB_WARM</ThermalBehaviour>
      <SuperlodType>SLOD_HUMAN</SuperlodType>
      <ScenarioPopStreamingSlot>SCENARIO_POP_STREAMING_NORMAL</ScenarioPopStreamingSlot>
      <DefaultSpawningPreference>DSP_NORMAL</DefaultSpawningPreference>
      <DefaultRemoveRangeMultiplier value="1.00000000"/>
      <AllowCloseSpawning value="false"/>
    </Item>
    <Item>
      <Name>amb_rox_m_y_baywatch_01</Name>
      <PropsName>null</PropsName>
      <ClipDictionaryName>move_m@generic</ClipDictionaryName>
      <BlendShapeFileName>null</BlendShapeFileName>
      <ExpressionSetName>expr_set_ambient_male</ExpressionSetName>
      <ExpressionDictionaryName>null</ExpressionDictionaryName>
      <ExpressionName>null</ExpressionName>
      <Pedtype>CIVMALE</Pedtype>
      <MovementClipSet>MOVE_M@GENERIC</MovementClipSet>
      <MovementClipSets/>
      <StrafeClipSet>MOVE_PED_STRAFING</StrafeClipSet>
      <MovementToStrafeClipSet>MOVE_PED_TO_STRAFE</MovementToStrafeClipSet>
      <InjuredStrafeClipSet>MOVE_STRAFE_INJURED</InjuredStrafeClipSet>
      <FullBodyDamageClipSet>DAM_KO</FullBodyDamageClipSet>
      <AdditiveDamageClipSet>DAM_AD</AdditiveDamageClipSet>
      <DefaultGestureClipSet>ANIM_GROUP_GESTURE_M_GENERIC</DefaultGestureClipSet>
      <FacialClipsetGroupName>facial_clipset_group_gen_male</FacialClipsetGroupName>
      <DefaultVisemeClipSet>ANIM_GROUP_VISEMES_M_LO</DefaultVisemeClipSet>
      <SidestepClipSet>CLIP_SET_ID_INVALID</SidestepClipSet>
      <PoseMatcherName>Male</PoseMatcherName>
      <PoseMatcherProneName>Male_prone</PoseMatcherProneName>
      <GetupSetHash>NMBS_SLOW_GETUPS</GetupSetHash>
      <CreatureMetadataName>null</CreatureMetadataName>
      <DecisionMakerName>default</DecisionMakerName>
      <MotionTaskDataSetName>STANDARD_PED</MotionTaskDataSetName>
      <DefaultTaskDataSetName>STANDARD_PED</DefaultTaskDataSetName>
      <PedCapsuleName>STANDARD_MALE</PedCapsuleName>
      <PedLayoutName/>
      <PedComponentSetName/>
      <PedComponentClothName/>
      <PedIKSettingsName/>
      <TaskDataName/>
      <IsStreamedGfx value="false"/>
      <AmbulanceShouldRespondTo value="true"/>
      <CanRideBikeWithNoHelmet value="false"/>
      <CanSpawnInCar value="true"/>
      <IsHeadBlendPed value="false"/>
      <bOnlyBulkyItemVariations value="false"/>
      <RelationshipGroup>CIVMALE</RelationshipGroup>
      <NavCapabilitiesName>STANDARD_PED</NavCapabilitiesName>
      <PerceptionInfo>DEFAULT_PERCEPTION</PerceptionInfo>
      <DefaultBrawlingStyle>BS_AI</DefaultBrawlingStyle>
      <DefaultUnarmedWeapon>WEAPON_UNARMED</DefaultUnarmedWeapon>
      <Personality>ServiceMales</Personality>
      <CombatInfo>default</CombatInfo>
      <VfxInfoName>VFXPEDINFO_HUMAN_GENERIC</VfxInfoName>
      <AmbientClipsForFlee>FLEE</AmbientClipsForFlee>
      <Radio1>RADIO_GENRE_REGGAE</Radio1>
      <Radio2>RADIO_GENRE_SURF</Radio2>
      <FUpOffset value="0.00000000"/>
      <RUpOffset value="0.00000000"/>
      <FFrontOffset value="0.00000000"/>
      <RFrontOffset value="0.14700000"/>
      <MinActivationImpulse value="20.00000000"/>
      <Stubble value="0.00000000"/>
      <HDDist value="3.00000000"/>
      <TargetingThreatModifier value="1.00000000"/>
      <KilledPerceptionRangeModifer value="-1.00000000"/>
      <Sexiness>SF_JEER_AT_HOT_PED</Sexiness>
      <Age value="0"/>
      <MaxPassengersInCar value="0"/>
      <ExternallyDrivenDOFs/>
      <PedVoiceGroup>S_M_Y_BAYWATCH_01_R2PVG</PedVoiceGroup>
      <AnimalAudioObject/>
      <AbilityType>SAT_NONE</AbilityType>
      <ThermalBehaviour>TB_WARM</ThermalBehaviour>
      <SuperlodType>SLOD_HUMAN</SuperlodType>
      <ScenarioPopStreamingSlot>SCENARIO_POP_STREAMING_NORMAL</ScenarioPopStreamingSlot>
      <DefaultSpawningPreference>DSP_NORMAL</DefaultSpawningPreference>
      <DefaultRemoveRangeMultiplier value="1.00000000"/>
      <AllowCloseSpawning value="false"/>
    </Item>
    <Item>
      <Name>amb_rox_m_y_uscg_01</Name>
      <PropsName>null</PropsName>
      <ClipDictionaryName>move_m@generic</ClipDictionaryName>
      <BlendShapeFileName>null</BlendShapeFileName>
      <ExpressionSetName>expr_set_ambient_male</ExpressionSetName>
      <ExpressionDictionaryName>null</ExpressionDictionaryName>
      <ExpressionName>null</ExpressionName>
      <Pedtype>COP</Pedtype>
      <MovementClipSet>MOVE_M@TOOL_BELT@A</MovementClipSet>
      <MovementClipSets/>
      <StrafeClipSet>MOVE_STRAFE@COP</StrafeClipSet>
      <MovementToStrafeClipSet>MOVE_PED_TO_STRAFE</MovementToStrafeClipSet>
      <InjuredStrafeClipSet>MOVE_STRAFE_INJURED</InjuredStrafeClipSet>
      <FullBodyDamageClipSet>DAM_KO</FullBodyDamageClipSet>
      <AdditiveDamageClipSet>DAM_AD</AdditiveDamageClipSet>
      <DefaultGestureClipSet>ANIM_GROUP_GESTURE_M_GENERIC</DefaultGestureClipSet>
      <FacialClipsetGroupName>facial_clipset_group_gen_male</FacialClipsetGroupName>
      <DefaultVisemeClipSet>ANIM_GROUP_VISEMES_M_LO</DefaultVisemeClipSet>
      <SidestepClipSet>CLIP_SET_ID_INVALID</SidestepClipSet>
      <PoseMatcherName>Male</PoseMatcherName>
      <PoseMatcherProneName>Male_prone</PoseMatcherProneName>
      <GetupSetHash>NMBS_SLOW_GETUPS</GetupSetHash>
      <CreatureMetadataName>null</CreatureMetadataName>
      <DecisionMakerName>COP</DecisionMakerName>
      <MotionTaskDataSetName>STANDARD_PED</MotionTaskDataSetName>
      <DefaultTaskDataSetName>STANDARD_PED</DefaultTaskDataSetName>
      <PedCapsuleName>STANDARD_MALE</PedCapsuleName>
      <PedLayoutName/>
      <PedComponentSetName/>
      <PedComponentClothName/>
      <PedIKSettingsName/>
      <TaskDataName/>
      <IsStreamedGfx value="false"/>
      <AmbulanceShouldRespondTo value="true"/>
      <CanRideBikeWithNoHelmet value="false"/>
      <CanSpawnInCar value="true"/>
      <IsHeadBlendPed value="false"/>
      <bOnlyBulkyItemVariations value="false"/>
      <RelationshipGroup>COP</RelationshipGroup>
      <NavCapabilitiesName>STANDARD_PED</NavCapabilitiesName>
      <PerceptionInfo>DEFAULT_PERCEPTION</PerceptionInfo>
      <DefaultBrawlingStyle>BS_AI</DefaultBrawlingStyle>
      <DefaultUnarmedWeapon>WEAPON_UNARMED</DefaultUnarmedWeapon>
      <Personality>POLICE</Personality>
      <CombatInfo>COP</CombatInfo>
      <VfxInfoName>VFXPEDINFO_HUMAN_GENERIC</VfxInfoName>
      <AmbientClipsForFlee>FLEE</AmbientClipsForFlee>
      <Radio1>RADIO_GENRE_PUNK</Radio1>
      <Radio2>RADIO_GENRE_JAZZ</Radio2>
      <FUpOffset value="0.00000000"/>
      <RUpOffset value="0.00000000"/>
      <FFrontOffset value="0.00000000"/>
      <RFrontOffset value="0.14700000"/>
      <MinActivationImpulse value="20.00000000"/>
      <Stubble value="0.00000000"/>
      <HDDist value="3.00000000"/>
      <TargetingThreatModifier value="1.00000000"/>
      <KilledPerceptionRangeModifer value="-1.00000000"/>
      <Sexiness/>
      <Age value="0"/>
      <MaxPassengersInCar value="0"/>
      <ExternallyDrivenDOFs/>
      <PedVoiceGroup>S_M_Y_HWAYCOP_01_R2PVG</PedVoiceGroup>
      <AnimalAudioObject/>
      <AbilityType>SAT_NONE</AbilityType>
      <ThermalBehaviour>TB_WARM</ThermalBehaviour>
      <SuperlodType>SLOD_HUMAN</SuperlodType>
      <ScenarioPopStreamingSlot>SCENARIO_POP_STREAMING_NORMAL</ScenarioPopStreamingSlot>
      <DefaultSpawningPreference>DSP_NORMAL</DefaultSpawningPreference>
      <DefaultRemoveRangeMultiplier value="1.00000000"/>
      <AllowCloseSpawning value="false"/>
    </Item>
    <Item>
      <Name>s_m_y_roxcitycop_01</Name>
      <PropsName>null</PropsName>
      <ClipDictionaryName>move_m@generic</ClipDictionaryName>
      <BlendShapeFileName>null</BlendShapeFileName>
      <ExpressionSetName>expr_set_ambient_male</ExpressionSetName>
      <ExpressionDictionaryName>null</ExpressionDictionaryName>
      <ExpressionName>null</ExpressionName>
      <Pedtype>COP</Pedtype>
      <MovementClipSet>MOVE_M@TOOL_BELT@A</MovementClipSet>
      <MovementClipSets/>
      <StrafeClipSet>MOVE_STRAFE@COP</StrafeClipSet>
      <MovementToStrafeClipSet>MOVE_PED_TO_STRAFE</MovementToStrafeClipSet>
      <InjuredStrafeClipSet>MOVE_STRAFE_INJURED</InjuredStrafeClipSet>
      <FullBodyDamageClipSet>DAM_KO</FullBodyDamageClipSet>
      <AdditiveDamageClipSet>DAM_AD</AdditiveDamageClipSet>
      <DefaultGestureClipSet>ANIM_GROUP_GESTURE_M_GENERIC</DefaultGestureClipSet>
      <FacialClipsetGroupName>facial_clipset_group_gen_male</FacialClipsetGroupName>
      <DefaultVisemeClipSet>ANIM_GROUP_VISEMES_M_LO</DefaultVisemeClipSet>
      <SidestepClipSet>CLIP_SET_ID_INVALID</SidestepClipSet>
      <PoseMatcherName>Male</PoseMatcherName>
      <PoseMatcherProneName>Male_prone</PoseMatcherProneName>
      <GetupSetHash>NMBS_SLOW_GETUPS</GetupSetHash>
      <CreatureMetadataName>null</CreatureMetadataName>
      <DecisionMakerName>COP</DecisionMakerName>
      <MotionTaskDataSetName>STANDARD_PED</MotionTaskDataSetName>
      <DefaultTaskDataSetName>STANDARD_PED</DefaultTaskDataSetName>
      <PedCapsuleName>STANDARD_MALE</PedCapsuleName>
      <PedLayoutName/>
      <PedComponentSetName/>
      <PedComponentClothName/>
      <PedIKSettingsName/>
      <TaskDataName/>
      <IsStreamedGfx value="false"/>
      <AmbulanceShouldRespondTo value="true"/>
      <CanRideBikeWithNoHelmet value="false"/>
      <CanSpawnInCar value="true"/>
      <IsHeadBlendPed value="false"/>
      <bOnlyBulkyItemVariations value="false"/>
      <RelationshipGroup>COP</RelationshipGroup>
      <NavCapabilitiesName>STANDARD_PED</NavCapabilitiesName>
      <PerceptionInfo>DEFAULT_PERCEPTION</PerceptionInfo>
      <DefaultBrawlingStyle>BS_AI</DefaultBrawlingStyle>
      <DefaultUnarmedWeapon>WEAPON_UNARMED</DefaultUnarmedWeapon>
      <Personality>POLICE</Personality>
      <CombatInfo>COP</CombatInfo>
      <VfxInfoName>VFXPEDINFO_HUMAN_GENERIC</VfxInfoName>
      <AmbientClipsForFlee>FLEE</AmbientClipsForFlee>
      <Radio1>RADIO_GENRE_OFF</Radio1>
      <Radio2>RADIO_GENRE_OFF</Radio2>
      <FUpOffset value="0.00000000"/>
      <RUpOffset value="0.00000000"/>
      <FFrontOffset value="0.00000000"/>
      <RFrontOffset value="0.14700000"/>
      <MinActivationImpulse value="20.00000000"/>
      <Stubble value="0.00000000"/>
      <HDDist value="3.00000000"/>
      <TargetingThreatModifier value="1.00000000"/>
      <KilledPerceptionRangeModifer value="-1.00000000"/>
      <Sexiness/>
      <Age value="0"/>
      <MaxPassengersInCar value="0"/>
      <ExternallyDrivenDOFs/>
      <PedVoiceGroup>S_M_Y_COP_01_R2PVG</PedVoiceGroup>
      <AnimalAudioObject/>
      <AbilityType>SAT_NONE</AbilityType>
      <ThermalBehaviour>TB_WARM</ThermalBehaviour>
      <SuperlodType>SLOD_HUMAN</SuperlodType>
      <ScenarioPopStreamingSlot>SCENARIO_POP_STREAMING_NORMAL</ScenarioPopStreamingSlot>
      <DefaultSpawningPreference>DSP_NORMAL</DefaultSpawningPreference>
      <DefaultRemoveRangeMultiplier value="1.00000000"/>
      <AllowCloseSpawning value="false"/>
    </Item>
    <Item>
      <Name>s_m_y_roxsheriff_01</Name>
      <PropsName>s_m_y_roxsheriff_01_p</PropsName>
      <ClipDictionaryName>move_m@generic</ClipDictionaryName>
      <BlendShapeFileName>null</BlendShapeFileName>
      <ExpressionSetName>expr_set_ambient_male</ExpressionSetName>
      <ExpressionDictionaryName>null</ExpressionDictionaryName>
      <ExpressionName>null</ExpressionName>
      <Pedtype>COP</Pedtype>
      <MovementClipSet>MOVE_CHUBBY</MovementClipSet>
      <MovementClipSets/>
      <StrafeClipSet>MOVE_STRAFE@COP</StrafeClipSet>
      <MovementToStrafeClipSet>MOVE_PED_TO_STRAFE</MovementToStrafeClipSet>
      <InjuredStrafeClipSet>MOVE_STRAFE_INJURED</InjuredStrafeClipSet>
      <FullBodyDamageClipSet>DAM_KO</FullBodyDamageClipSet>
      <AdditiveDamageClipSet>DAM_AD</AdditiveDamageClipSet>
      <DefaultGestureClipSet>ANIM_GROUP_GESTURE_M_GENERIC</DefaultGestureClipSet>
      <FacialClipsetGroupName>facial_clipset_group_gen_male</FacialClipsetGroupName>
      <DefaultVisemeClipSet>ANIM_GROUP_VISEMES_M_LO</DefaultVisemeClipSet>
      <SidestepClipSet>CLIP_SET_ID_INVALID</SidestepClipSet>
      <PoseMatcherName>Male</PoseMatcherName>
      <PoseMatcherProneName>Male_prone</PoseMatcherProneName>
      <GetupSetHash>NMBS_SLOW_GETUPS</GetupSetHash>
      <CreatureMetadataName>ambientPed_upperWrinkles</CreatureMetadataName>
      <DecisionMakerName>default</DecisionMakerName>
      <MotionTaskDataSetName>STANDARD_PED</MotionTaskDataSetName>
      <DefaultTaskDataSetName>STANDARD_PED</DefaultTaskDataSetName>
      <PedCapsuleName>STANDARD_MALE</PedCapsuleName>
      <PedLayoutName/>
      <PedComponentSetName/>
      <PedComponentClothName/>
      <PedIKSettingsName/>
      <TaskDataName/>
      <IsStreamedGfx value="false"/>
      <AmbulanceShouldRespondTo value="true"/>
      <CanRideBikeWithNoHelmet value="false"/>
      <CanSpawnInCar value="true"/>
      <IsHeadBlendPed value="false"/>
      <bOnlyBulkyItemVariations value="false"/>
      <RelationshipGroup>COP</RelationshipGroup>
      <NavCapabilitiesName>STANDARD_PED</NavCapabilitiesName>
      <PerceptionInfo>DEFAULT_PERCEPTION</PerceptionInfo>
      <DefaultBrawlingStyle>BS_AI</DefaultBrawlingStyle>
      <DefaultUnarmedWeapon>WEAPON_UNARMED</DefaultUnarmedWeapon>
      <Personality>POLICE</Personality>
      <CombatInfo>COP</CombatInfo>
      <VfxInfoName>VFXPEDINFO_HUMAN_GENERIC</VfxInfoName>
      <AmbientClipsForFlee>FLEE</AmbientClipsForFlee>
      <Radio1>RADIO_GENRE_OFF</Radio1>
      <Radio2>RADIO_GENRE_OFF</Radio2>
      <FUpOffset value="0.00000000"/>
      <RUpOffset value="0.00000000"/>
      <FFrontOffset value="0.00000000"/>
      <RFrontOffset value="0.14700000"/>
      <MinActivationImpulse value="20.00000000"/>
      <Stubble value="0.00000000"/>
      <HDDist value="3.00000000"/>
      <TargetingThreatModifier value="1.00000000"/>
      <KilledPerceptionRangeModifer value="-1.00000000"/>
      <Sexiness/>
      <Age value="0"/>
      <MaxPassengersInCar value="0"/>
      <ExternallyDrivenDOFs/>
      <PedVoiceGroup>S_M_Y_SHERIFF_01_R2PVG</PedVoiceGroup>
      <AnimalAudioObject/>
      <AbilityType>SAT_NONE</AbilityType>
      <ThermalBehaviour>TB_WARM</ThermalBehaviour>
      <SuperlodType>SLOD_HUMAN</SuperlodType>
      <ScenarioPopStreamingSlot>SCENARIO_POP_STREAMING_NORMAL</ScenarioPopStreamingSlot>
      <DefaultSpawningPreference>DSP_NORMAL</DefaultSpawningPreference>
      <DefaultRemoveRangeMultiplier value="1.00000000"/>
      <AllowCloseSpawning value="false"/>
    </Item>
    <Item>
      <Name>S_M_Y_Forest_01</Name>
      <PropsName>null</PropsName>
      <ClipDictionaryName>move_m@generic</ClipDictionaryName>
      <BlendShapeFileName>null</BlendShapeFileName>
      <ExpressionSetName>expr_set_ambient_male</ExpressionSetName>
      <ExpressionDictionaryName>null</ExpressionDictionaryName>
      <ExpressionName>null</ExpressionName>
      <Pedtype>COP</Pedtype>
      <MovementClipSet>MOVE_M@BUSINESS@C</MovementClipSet>
      <MovementClipSets/>
      <StrafeClipSet>MOVE_STRAFE@COP</StrafeClipSet>
      <MovementToStrafeClipSet>MOVE_PED_TO_STRAFE</MovementToStrafeClipSet>
      <InjuredStrafeClipSet>MOVE_STRAFE_INJURED</InjuredStrafeClipSet>
      <FullBodyDamageClipSet>DAM_KO</FullBodyDamageClipSet>
      <AdditiveDamageClipSet>DAM_AD</AdditiveDamageClipSet>
      <DefaultGestureClipSet>ANIM_GROUP_GESTURE_M_GENERIC</DefaultGestureClipSet>
      <FacialClipsetGroupName>facial_clipset_group_gen_male</FacialClipsetGroupName>
      <DefaultVisemeClipSet>ANIM_GROUP_VISEMES_M_LO</DefaultVisemeClipSet>
      <SidestepClipSet>CLIP_SET_ID_INVALID</SidestepClipSet>
      <PoseMatcherName>Male</PoseMatcherName>
      <PoseMatcherProneName>Male_prone</PoseMatcherProneName>
      <GetupSetHash>NMBS_SLOW_GETUPS</GetupSetHash>
      <CreatureMetadataName>null</CreatureMetadataName>
      <DecisionMakerName>COP</DecisionMakerName>
      <MotionTaskDataSetName>STANDARD_PED</MotionTaskDataSetName>
      <DefaultTaskDataSetName>STANDARD_PED</DefaultTaskDataSetName>
      <PedCapsuleName>STANDARD_MALE</PedCapsuleName>
      <PedLayoutName/>
      <PedComponentSetName/>
      <PedComponentClothName/>
      <PedIKSettingsName/>
      <TaskDataName/>
      <IsStreamedGfx value="false"/>
      <AmbulanceShouldRespondTo value="true"/>
      <CanRideBikeWithNoHelmet value="false"/>
      <CanSpawnInCar value="true"/>
      <IsHeadBlendPed value="false"/>
      <bOnlyBulkyItemVariations value="false"/>
      <RelationshipGroup>COP</RelationshipGroup>
      <NavCapabilitiesName>STANDARD_PED</NavCapabilitiesName>
      <PerceptionInfo>DEFAULT_PERCEPTION</PerceptionInfo>
      <DefaultBrawlingStyle>BS_AI</DefaultBrawlingStyle>
      <DefaultUnarmedWeapon>WEAPON_UNARMED</DefaultUnarmedWeapon>
      <Personality>POLICE</Personality>
      <CombatInfo>COP</CombatInfo>
      <VfxInfoName>VFXPEDINFO_HUMAN_GENERIC</VfxInfoName>
      <AmbientClipsForFlee>FLEE</AmbientClipsForFlee>
      <Radio1>RADIO_GENRE_OFF</Radio1>
      <Radio2>RADIO_GENRE_OFF</Radio2>
      <FUpOffset value="0.00000000"/>
      <RUpOffset value="0.00000000"/>
      <FFrontOffset value="0.00000000"/>
      <RFrontOffset value="0.14700000"/>
      <MinActivationImpulse value="20.00000000"/>
      <Stubble value="0.00000000"/>
      <HDDist value="3.00000000"/>
      <TargetingThreatModifier value="1.00000000"/>
      <KilledPerceptionRangeModifer value="-1.00000000"/>
      <Sexiness/>
      <Age value="0"/>
      <MaxPassengersInCar value="0"/>
      <ExternallyDrivenDOFs/>
      <PedVoiceGroup>S_M_Y_COP_01_R2PVG</PedVoiceGroup>
      <AnimalAudioObject/>
      <AbilityType>SAT_NONE</AbilityType>
      <ThermalBehaviour>TB_WARM</ThermalBehaviour>
      <SuperlodType>SLOD_HUMAN</SuperlodType>
      <ScenarioPopStreamingSlot>SCENARIO_POP_STREAMING_NORMAL</ScenarioPopStreamingSlot>
      <DefaultSpawningPreference>DSP_NORMAL</DefaultSpawningPreference>
      <DefaultRemoveRangeMultiplier value="1.00000000"/>
      <AllowCloseSpawning value="false"/>
    </Item>
    <Item>
      <Name>amb_m_y_pwr</Name>
      <PropsName>amb_m_y_pwr_p</PropsName>
      <ClipDictionaryName>move_m@generic</ClipDictionaryName>
      <BlendShapeFileName>null</BlendShapeFileName>
      <ExpressionSetName>expr_set_ambient_male</ExpressionSetName>
      <ExpressionDictionaryName>null</ExpressionDictionaryName>
      <ExpressionName>null</ExpressionName>
      <Pedtype>CIVMALE</Pedtype>
      <MovementClipSet>MOVE_M@TOOL_BELT@A</MovementClipSet>
      <MovementClipSets/>
      <StrafeClipSet>MOVE_PED_STRAFING</StrafeClipSet>
      <MovementToStrafeClipSet>MOVE_PED_TO_STRAFE</MovementToStrafeClipSet>
      <InjuredStrafeClipSet>MOVE_STRAFE_INJURED</InjuredStrafeClipSet>
      <FullBodyDamageClipSet>DAM_KO</FullBodyDamageClipSet>
      <AdditiveDamageClipSet>DAM_AD</AdditiveDamageClipSet>
      <DefaultGestureClipSet>ANIM_GROUP_GESTURE_M_GENERIC</DefaultGestureClipSet>
      <FacialClipsetGroupName>facial_clipset_group_gen_male</FacialClipsetGroupName>
      <DefaultVisemeClipSet>ANIM_GROUP_VISEMES_M_LO</DefaultVisemeClipSet>
      <SidestepClipSet>CLIP_SET_ID_INVALID</SidestepClipSet>
      <PoseMatcherName>Male</PoseMatcherName>
      <PoseMatcherProneName>Male_prone</PoseMatcherProneName>
      <GetupSetHash>NMBS_SLOW_GETUPS</GetupSetHash>
      <CreatureMetadataName>null</CreatureMetadataName>
      <DecisionMakerName>default</DecisionMakerName>
      <MotionTaskDataSetName>STANDARD_PED</MotionTaskDataSetName>
      <DefaultTaskDataSetName>STANDARD_PED</DefaultTaskDataSetName>
      <PedCapsuleName>STANDARD_MALE</PedCapsuleName>
      <PedLayoutName/>
      <PedComponentSetName/>
      <PedComponentClothName/>
      <PedIKSettingsName/>
      <TaskDataName/>
      <IsStreamedGfx value="false"/>
      <AmbulanceShouldRespondTo value="true"/>
      <CanRideBikeWithNoHelmet value="false"/>
      <CanSpawnInCar value="false"/>
      <IsHeadBlendPed value="false"/>
      <bOnlyBulkyItemVariations value="false"/>
      <RelationshipGroup>CIVMALE</RelationshipGroup>
      <NavCapabilitiesName>STANDARD_PED</NavCapabilitiesName>
      <PerceptionInfo>DEFAULT_PERCEPTION</PerceptionInfo>
      <DefaultBrawlingStyle>BS_AI</DefaultBrawlingStyle>
      <DefaultUnarmedWeapon>WEAPON_UNARMED</DefaultUnarmedWeapon>
      <Personality>CONSTRUCTION</Personality>
      <CombatInfo>default</CombatInfo>
      <VfxInfoName>VFXPEDINFO_HUMAN_GENERIC</VfxInfoName>
      <AmbientClipsForFlee>FLEE</AmbientClipsForFlee>
      <Radio1>RADIO_GENRE_PUNK</Radio1>
      <Radio2>RADIO_GENRE_CLASSIC_ROCK</Radio2>
      <FUpOffset value="0.00000000"/>
      <RUpOffset value="0.00000000"/>
      <FFrontOffset value="0.00000000"/>
      <RFrontOffset value="0.14700000"/>
      <MinActivationImpulse value="20.00000000"/>
      <Stubble value="0.00000000"/>
      <HDDist value="3.00000000"/>
      <TargetingThreatModifier value="1.00000000"/>
      <KilledPerceptionRangeModifer value="-1.00000000"/>
      <Sexiness>SF_JEER_AT_HOT_PED</Sexiness>
      <Age value="0"/>
      <MaxPassengersInCar value="0"/>
      <ExternallyDrivenDOFs/>
      <PedVoiceGroup>MALE_GENERICWORKER_R2PVG</PedVoiceGroup>
      <AnimalAudioObject/>
      <AbilityType>SAT_NONE</AbilityType>
      <ThermalBehaviour>TB_WARM</ThermalBehaviour>
      <SuperlodType>SLOD_HUMAN</SuperlodType>
      <ScenarioPopStreamingSlot>SCENARIO_POP_STREAMING_NORMAL</ScenarioPopStreamingSlot>
      <DefaultSpawningPreference>DSP_NORMAL</DefaultSpawningPreference>
      <DefaultRemoveRangeMultiplier value="1.00000000"/>
      <AllowCloseSpawning value="false"/>
    </Item>
    <Item>
      <Name>s_f_m_railwait_01</Name>
      <PropsName>s_f_m_railwait_01_p</PropsName>
      <ClipDictionaryName>move_f@generic</ClipDictionaryName>
      <BlendShapeFileName>null</BlendShapeFileName>
      <ExpressionSetName>expr_set_ambient_female_heels</ExpressionSetName>
      <ExpressionDictionaryName>null</ExpressionDictionaryName>
      <ExpressionName>null</ExpressionName>
      <Pedtype>CIVFEMALE</Pedtype>
      <MovementClipSet>MOVE_F@HEELS@C</MovementClipSet>
      <MovementClipSets/>
      <StrafeClipSet>MOVE_PED_STRAFING</StrafeClipSet>
      <MovementToStrafeClipSet>MOVE_PED_TO_STRAFE</MovementToStrafeClipSet>
      <InjuredStrafeClipSet>MOVE_STRAFE_INJURED</InjuredStrafeClipSet>
      <FullBodyDamageClipSet>DAM_KO</FullBodyDamageClipSet>
      <AdditiveDamageClipSet>DAM_AD</AdditiveDamageClipSet>
      <DefaultGestureClipSet>ANIM_GROUP_GESTURE_F_GENERIC</DefaultGestureClipSet>
      <FacialClipsetGroupName>facial_clipset_group_gen_female</FacialClipsetGroupName>
      <DefaultVisemeClipSet>ANIM_GROUP_VISEMES_F_LO</DefaultVisemeClipSet>
      <SidestepClipSet>CLIP_SET_ID_INVALID</SidestepClipSet>
      <PoseMatcherName>Male</PoseMatcherName>
      <PoseMatcherProneName>Male_prone</PoseMatcherProneName>
      <GetupSetHash>NMBS_SLOW_GETUPS</GetupSetHash>
      <CreatureMetadataName>AmbientHeels</CreatureMetadataName>
      <DecisionMakerName>default</DecisionMakerName>
      <MotionTaskDataSetName>STANDARD_PED</MotionTaskDataSetName>
      <DefaultTaskDataSetName>STANDARD_PED</DefaultTaskDataSetName>
      <PedCapsuleName>STANDARD_FEMALE</PedCapsuleName>
      <PedLayoutName/>
      <PedComponentSetName/>
      <PedComponentClothName/>
      <PedIKSettingsName/>
      <TaskDataName/>
      <IsStreamedGfx value="false"/>
      <AmbulanceShouldRespondTo value="true"/>
      <CanRideBikeWithNoHelmet value="false"/>
      <CanSpawnInCar value="true"/>
      <IsHeadBlendPed value="false"/>
      <bOnlyBulkyItemVariations value="false"/>
      <RelationshipGroup>CIVFEMALE</RelationshipGroup>
      <NavCapabilitiesName>STANDARD_PED</NavCapabilitiesName>
      <PerceptionInfo>DEFAULT_PERCEPTION</PerceptionInfo>
      <DefaultBrawlingStyle>BS_AI</DefaultBrawlingStyle>
      <DefaultUnarmedWeapon>WEAPON_UNARMED</DefaultUnarmedWeapon>
      <Personality>ServiceFemales</Personality>
      <CombatInfo>default</CombatInfo>
      <VfxInfoName>VFXPEDINFO_HUMAN_GENERIC</VfxInfoName>
      <AmbientClipsForFlee>FLEE</AmbientClipsForFlee>
      <Radio1>RADIO_GENRE_POP</Radio1>
      <Radio2>RADIO_GENRE_DANCE</Radio2>
      <FUpOffset value="0.00000000"/>
      <RUpOffset value="0.00000000"/>
      <FFrontOffset value="0.00000000"/>
      <RFrontOffset value="0.14700000"/>
      <MinActivationImpulse value="20.00000000"/>
      <Stubble value="0.00000000"/>
      <HDDist value="3.00000000"/>
      <TargetingThreatModifier value="1.00000000"/>
      <KilledPerceptionRangeModifer value="-1.00000000"/>
      <Sexiness>SF_HOT_PERSON</Sexiness>
      <Age value="0"/>
      <MaxPassengersInCar value="0"/>
      <ExternallyDrivenDOFs/>
      <PedVoiceGroup>S_F_Y_AIRHOSTESS_01_R2PVG</PedVoiceGroup>
      <AnimalAudioObject/>
      <AbilityType>SAT_NONE</AbilityType>
      <ThermalBehaviour>TB_WARM</ThermalBehaviour>
      <SuperlodType>SLOD_HUMAN</SuperlodType>
      <ScenarioPopStreamingSlot>SCENARIO_POP_STREAMING_NORMAL</ScenarioPopStreamingSlot>
      <DefaultSpawningPreference>DSP_NORMAL</DefaultSpawningPreference>
      <DefaultRemoveRangeMultiplier value="1.00000000"/>
      <AllowCloseSpawning value="false"/>
    </Item>
  </InitDatas>
	<multiTxdRelationships>
		<Item>
			<parent>comp_peds_helmets_moped</parent>
			<children>
			</children>
		</Item>
		<Item>
			<parent>comp_peds_helmets_motox</parent>
			<children>
			</children>
		</Item>
		<Item>
			<parent>comp_peds_helmets_sports</parent>
			<children>
			</children>
		</Item>
		<Item>
			<parent>comp_peds_helmets_shorty</parent>
			<children>
			</children>
		</Item>
		<Item>
			<parent>strm_peds_mpTattRTs</parent>
			<children>
			</children>
		</Item>
		<Item>
			<parent>strm_peds_mpShare</parent>
			<children>
			</children>
		</Item>
	</multiTxdRelationships>
</CPedModelInfo__InitDataList>
