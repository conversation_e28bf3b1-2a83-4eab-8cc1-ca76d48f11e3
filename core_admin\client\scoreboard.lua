-- -- Scoreboard Control Thread
-- Citizen.CreateThread(function()
--     while true do Wait(0)

--         if IsControlPressed(0, 178) and GetLastInputMethod(2) then
--             listOn = true
--             SendNUIMessage({action = 'open'})

--             while listOn do Wait(0)
--                 if not IsControlPressed(0, 178) then
--                     listOn = false
--                     SendNUIMessage({action = 'close'})
--                 end
--             end
--         end
--     end
-- end)

-- local updateCooldown = false

-- RegisterNetEvent("base:updateLocalInfo")
-- AddEventHandler("base:updateLocalInfo", function(players, jobs, name, maxPlayers)
--     if not updateCooldown then
--         updateCooldown = true
--         updateServerInfo(name, #players, maxPlayers)
--         updatePlayerList(players)
--         updateJobCount(jobs)
--         Wait(10000)
--         updateCooldown = false
--     else
--         CancelEvent()
--     end
-- end)

-- local listOn = false

-- function updateServerInfo(name, playerCount, maxPlayers)
--     SendNUIMessage({
--         action = 'updateServerInfo',
--         serverName = name,
--         playerCount = playerCount,
--         maxPlayers = maxPlayers,
--     })
-- end

-- function updatePlayerList(players)
--     local formattedPlayerList = {}
--     local playerCount = #players

--     table.sort(players, sortPlayerList)

--     local column = 1

--     for i, player in ipairs(players) do
--         if column == 1 then
--             table.insert(formattedPlayerList, ('<tr><td>%s</td><td>%s</td>'):format(player.accountid, player.name))
--         elseif column == 6 then
--             table.insert(formattedPlayerList, ('<td>%s</td><td>%s</td></tr>'):format(player.accountid, player.name))
--         else
--             table.insert(formattedPlayerList, ('<td>%s</td><td>%s</td>'):format(player.accountid, player.name))            
--         end

--         column = column + 1

--         if column > 6 then column = 1 end
--     end

--     SendNUIMessage({
--         action  = 'updatePlayerList',
--         players = table.concat(formattedPlayerList)
--     })
-- end

-- function sortPlayerList(a, b)
--     if (a.accountid < b.accountid) then
--         return true
--     else
--         return false
--     end
-- end

-- function updateJobCount(jobs)
--     SendNUIMessage({ 
--         action = 'updatePlayerJobs',
--         jobs = jobs
--     })
-- end


-- RegisterNetEvent('uptime:tick')
-- AddEventHandler('uptime:tick', function(uptime)
--     local uptimeMinute, uptimeHour = 0, 0

--     while true do
--         Citizen.Wait(1000 * 60) -- every minute
--         uptimeMinute = uptimeMinute + 1
    
--         if uptimeMinute == 60 then
--             uptimeMinute = 0
--             uptimeHour = uptimeHour + 1
--         end

--         SendNUIMessage({
--             action = 'updateServerInfo',
--             uptime = string.format("%02dh %02dm", uptimeHour, uptimeMinute)
--         })
--     end
-- end)

-- Citizen.CreateThread(function()
--     local playMinute, playHour = 0, 0

--     while true do
--         Citizen.Wait(1000 * 60) -- every minute
--         playMinute = playMinute + 1
    
--         if playMinute == 60 then
--             playMinute = 0
--             playHour = playHour + 1
--         end

--         SendNUIMessage({
--             action = 'updateServerInfo',
--             playTime = string.format("%02dh %02dm", playHour, playMinute)
--         })
--     end
-- end)

