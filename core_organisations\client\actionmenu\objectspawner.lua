local function spawnGhostObject(model, rotation)
    local playerPed  = GetPlayerPed(-1)
    local heading    = GetEntityHeading(playerPed)

    local coords     = GetEntityCoords(playerPed)
    local forward    = GetEntityForwardVector(playerPed)
    local x, y, z    = table.unpack(coords + forward * 1.0)
    local hash       = GetHash<PERSON><PERSON>(model)
    local dimensions = GetModelDimensions(hash, First, Second)

    local obj        = CreateObject(hash, x, y, z, 0, 0, 1)
    SetEntityAlpha(obj, 150, 0)
    SetEntityHeading(obj, heading)
    SetEntityCollision(obj, false, false)

    local offset = 2.0 + (dimensions.y * -1)

    -- if rotation == 1 then
    -- 	AttachEntityToEntity(obj, playerPed, 0, 0.0, offset, -0.95, 0.0, 0.0, 0.0, true, true, false, false, 0, true)
    -- elseif rotation == 2 then
    -- 	AttachEntityToEntity(obj, playerPed, 0, 0.0, offset, -0.95, 0.0, 0.0, 90.0,  true, true, false, false, 0, true)
    -- elseif rotation == 3 then
    -- 	AttachEntityToEntity(obj, playerPed, 0, 0.0, offset, -0.95, 0.0, 0.0, 180.0, true, true, false, false, 0, true)
    -- elseif rotation == 4 then
    -- 	AttachEntityToEntity(obj, playerPed, 0, 0.0, offset, -0.95, 0.0, 0.0, 270.0,  true, true, false, false, 0, true)
    -- end

    Citizen.CreateThread(function()
        while DoesEntityExist(obj) do
            local coords  = GetEntityCoords(playerPed)
            local forward = GetEntityForwardVector(playerPed)
            local heading = GetEntityHeading(playerPed)
            local x, y, z = table.unpack(coords + forward * offset)

            if rotation == 2 then
                heading = heading + 90.0
            elseif rotation == 3 then
                heading = heading + 180.0
            elseif rotation == 4 then
                heading = heading - 90.0
            end

            SetEntityCoords(obj, x, y, z, 1, 1, 1)
            SetEntityHeading(obj, heading)
            PlaceObjectOnGroundProperly(obj)
            -- Temp fix for being able to yeet around in first person while jumping with a ghost object out
            if (GetFollowPedCamViewMode() == 4) then
                DisableControlAction(0, 22, true)
                -- Check incase they're cheaky and try switch to first person mid jump
                if (IsPedJumping(PlayerPedId())) then
                    SetEntityVelocity(playerPed, 0)
                end
            end
            Wait(0)
        end
    end)

    return obj
end

local function getOrgObjects()
    local objects = AwaitServerCallback("orgs:getPlayerObjects")

    local elements = {}
    for _, obj in ipairs(objects) do
        table.insert(elements, {
            label = obj.label,
            model = obj.value,
            org = obj.organisation,
            type = "slider",
            value = 1,
            min = 1,
            max = 4
        })
    end

    return elements
end

function OpenOrgObjectSpawnerMenu()
    local displayingObject = false

    local elements = getOrgObjects()
    displayingObject = spawnGhostObject(elements[1].model, 1)

    Base.UI.Menu.Open("default", GetCurrentResourceName(), "objectspawner",
        {
            title = "Spawn Objects",
            align = "bottom-right",
            elements = elements,
        },

        function(data, menu)
            if displayingObject then
                local model = GetEntityModel(displayingObject)
                local coords = GetEntityCoords(displayingObject)
                local heading = GetEntityHeading(displayingObject)
                local x, y, z = table.unpack(coords)

                Base.Objects.Create(model, x, y, z, heading, data.current.org, true, false)
                GeneralLog("Object Spawn", PlayerData.logName .. " has spawned " .. data.current.label .. " at " .. math.floor(x) .. ", " .. math.floor(y) .. ", " .. math.floor(z), "green", "object-spawner")
            end
        end,

        function(data, menu)
            if displayingObject then
                DeleteEntity(displayingObject)
                displayingObject = false
            end
            menu.close()
        end,

        function(data, menu)
            if displayingObject then
                DeleteEntity(displayingObject)
            end

            print("data.current.model", data.current.model, data.current.value, data.current.label, data.current.org)

            displayingObject = spawnGhostObject(data.current.model, data.current.value)
        end
    )

    Citizen.CreateThread(function()
        while Base.UI.Menu.IsOpen("default", GetCurrentResourceName(), "objectspawner") do
            Wait(500)
        end

        if displayingObject then
            DeleteEntity(displayingObject)
        end
    end)
end
