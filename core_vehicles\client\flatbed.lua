CurrentlyTowedVehicle = nil
local flatbeds = {
	flatbed = {0.0, -3.0, 0.8, 0.0, 0.0, 0.0},
	mechanic1 = {-3.7, -1.42, 0.4, 0.0, 0.0, 0.0},
	mechanic2 = {-3.7, -1.42, 1.0, 0.0, 0.0, 0.0},
	mechanic3 = {0.0, -3.5, 0.8, 0.0, 0.0, 0.0},
	hvywrecker = {0.0, -8.0, 0.0, 0.0, 0.0, 0.0},
}

towRope = nil

function flatbedVehicle(targetVehicle)
	local playerped = GetPlayerPed(-1)
	local coords = GetEntityCoords(playerped)			
	local vehicles = Base.Vehicles.GetVehiclesInRadius(playerped, 18.0)

	if not DoesEntityExist(CurrentlyTowedVehicle) then
		CurrentlyTowedVehicle = nil
	end

	local params = {}
	local dist = 100.0

	if CurrentlyTowedVehicle == nil then
	    if targetVehicle then
	    	NetworkRequestControlOfEntity(targetVehicle)
			local count=0
			while not NetworkHasControlOfEntity(targetVehicle) and count <=5  do
				NetworkRequestControlOfEntity(targetVehicle)
				Wait(500)
				count=count+1
			end

			flatbed = nil
			flatbed2 = nil  
			wrecker = nil      
	        
        	for i = 1, #vehicles do
        		-- Flatbed
        	    if GetEntityModel(vehicles[i]) == 1353720154 then
    	        	if vehicles[i] ~= targetVehicle and GetDistanceBetweenCoords(coords, GetEntityCoords(vehicles[i])) < dist then
    	        		flatbed = vehicles[i]
    	        		params = flatbeds.flatbed
    	        		dist = GetDistanceBetweenCoords(coords, GetEntityCoords(vehicles[i]))
    	        	end

        		-- Mechanic1
        		elseif GetEntityModel(vehicles[i]) == -483383590 then	
        			if vehicles[i] ~= targetVehicle and GetDistanceBetweenCoords(coords, GetEntityCoords(vehicles[i])) < dist then
    	        		flatbed2 = vehicles[i]
    	        		params = flatbeds.mechanic1
    	        		dist = GetDistanceBetweenCoords(coords, GetEntityCoords(vehicles[i]))
    	        	end

        		-- Mechanic2
        		elseif GetEntityModel(vehicles[i]) == -1474547533 then
        			if vehicles[i] ~= targetVehicle and GetDistanceBetweenCoords(coords, GetEntityCoords(vehicles[i])) < dist then
    	        		flatbed2 = vehicles[i]
    	        		params = flatbeds.mechanic2
    	        		dist = GetDistanceBetweenCoords(coords, GetEntityCoords(vehicles[i]))
    	        	end

        		-- Mechanic3
        		elseif GetEntityModel(vehicles[i]) == -1085382889 then
        			if vehicles[i] ~= targetVehicle and GetDistanceBetweenCoords(coords, GetEntityCoords(vehicles[i])) < dist then
    	        		flatbed = vehicles[i]
    	        		params = flatbeds.mechanic3
    	        		dist = GetDistanceBetweenCoords(coords, GetEntityCoords(vehicles[i]))
    	        	end
				
				-- HVYWrecker
				
				elseif GetEntityModel(vehicles[i]) == 1719953772 then
        			if vehicles[i] ~= targetVehicle and GetDistanceBetweenCoords(coords, GetEntityCoords(vehicles[i])) < dist then
    	        		wrecker = vehicles[i]
    	        		params = flatbeds.hvywrecker
    	        		dist = GetDistanceBetweenCoords(coords, GetEntityCoords(vehicles[i]))
    	        	end

        	    end
			end

			if flatbed2 and targetVehicle then
				AttachEntityToEntity(targetVehicle, flatbed2, GetEntityBoneIndexByName(flatbed2, 'siren_glass2'), 0, params[1], params[2], params[3], params[4], params[5], params[6], false, false, false, false, 20, true)
				CurrentlyTowedVehicle = targetVehicle
				TriggerEvent("fdg_ui:SendNotification", "Vehicle Attached Successfully")
			end
			if flatbed and targetVehicle then
				AttachEntityToEntity(targetVehicle, flatbed, 0, params[1], params[2], params[3], params[4], params[5], params[6], false, false, false, false, 20, true)
				CurrentlyTowedVehicle = targetVehicle
				TriggerEvent("fdg_ui:SendNotification", "Vehicle Attached Successfully")
			end
			if wrecker and targetVehicle then
				local baseEnt = wrecker
				local carEnt = targetVehicle
				local baseEntPos = GetOffsetFromEntityInWorldCoords(baseEnt,0.0,0.0,0.0)
				local baseEntRopePos = GetOffsetFromEntityInWorldCoords(baseEnt,0.0,0.23,0.0)
				local carEntPos = GetEntityCoords(carEnt)
				local distance = GetDistanceBetweenCoords(baseEntPos.x, baseEntPos.y,baseEntPos.z, carEntPos.x, carEntPos.y, carEntPos.z,false)
				local startdistance = GetDistanceBetweenCoords(baseEntPos.x, baseEntPos.y,baseEntPos.z,carEntPos.x, carEntPos.y, carEntPos.z,false)

				rope = AddRope(baseEntPos.x, baseEntPos.y, baseEntPos.z, 0.0, 0.0, 0.0, distance+29, 1, distance, 0.25, 5.0, false, true, false, 5.0, false, 0)
				towRope = rope
				LoadRopeData(rope,"ropeFamily1")
				AttachEntitiesToRope(rope,baseEnt, carEnt,baseEntRopePos.x, baseEntRopePos.y, baseEntRopePos.z,carEntPos.x, carEntPos.y, carEntPos.z, distance, false, false, GetWorldPositionOfEntityBone(playerPed,GetPedBoneIndex(playerPed,boneID)), 0) 
				N_0xa1ae736541b0fca3(rope, true)
				GetRopeLastVertexCoord(rope)
				PinRopeVertex(rope, (GetRopeVertexCount(rope) - 1),baseEntRopePos)
				RopeSetUpdateOrder(rope, 0)

				CurrentlyTowedVehicle = targetVehicle
				TriggerEvent("fdg_ui:SendNotification", "Vehicle Attached Successfully")
			end
	    else
	      	TriggerEvent("fdg_ui:SendNotification", "There is no vehicle to be attached")
	    end
	else
		DeleteRope(towRope)
	   	AttachEntityToEntity(CurrentlyTowedVehicle, flatbed, 0, 0.0, -10.0, 0.8, 0.0, 0.0, 0.0, false, false, false, false, 20, true)
	    DetachEntity(CurrentlyTowedVehicle, true, true)
	    CurrentlyTowedVehicle = nil
	    TriggerEvent("fdg_ui:SendNotification", "Vehicle Detached Successfully") 
	end

	
end