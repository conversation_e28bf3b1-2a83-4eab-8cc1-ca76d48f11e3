local Keys = {
    ["E"] = 38,
    ["F"] = 49
}
local type1 = "cashToChips"
local type2 = "chipsToCash"

local exchangeLocations = {
    vector3(1116.078, 219.954, -49.43512),
    vector3(-168.219, -978.0518, 254.45),
    vector3(-301.6291, 6269.07, 31.52688),
    vector3(148.45, -3015.64, 7.04)
}

onBaseReady(function()
    Citizen.CreateThread(function()
        while true do
            Citizen.Wait(1)
            local coords = GetEntityCoords(PlayerPedId())

            for _, exchangeCoords in ipairs(exchangeLocations) do
                if GetDistanceBetweenCoords(coords, exchangeCoords.x, exchangeCoords.y, exchangeCoords.z, true) < 1.5 then
                    Base.ShowHelpNotification(
                    'Press ~INPUT_CONTEXT~ to exchange Cash for Chips Press ~INPUT_ARREST~ to exchange Chips for Cash')
                    if IsControlJustReleased(0, <PERSON>['E']) then
                        TriggerServerEvent("main_casino:Exchange", type1)
                    elseif IsControlJustReleased(0, Keys['F']) then
                        TriggerServerEvent("main_casino:Exchange", type2)
                    end
                end
            end
        end
    end)
end)
