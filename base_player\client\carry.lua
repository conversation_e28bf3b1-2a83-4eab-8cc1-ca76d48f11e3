local function canPlayerBeCarried(player)
    local serverID = GetPlayerServerId(player)
    local state = Player(serverID).state

    if not state.wounded then
        return false
    end

    local coords = GetEntityCoords(GetPlayerPed(player))
    local distance = #(coords - GetEntityCoords(PlayerPedId()))
    if distance > 10.0 then
        return false
    end
end

local function carryPlayer(player)
    if not canPlayerBeCarried(player) then
        Base.Notification("This player can't be carried")
        return
    end

    TriggerServerEvent("carry:start", GetPlayerServerId(player))
end
