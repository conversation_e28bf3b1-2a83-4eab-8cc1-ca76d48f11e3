function OpenOrgActionMenu()
    Base.UI.Menu.CloseAll()

    Base.UI.Menu.Open("default", GetCurrentResourceName(), "org_action_menu",
        {
            title = "Organisation Action Menu",
            align = "bottom-right",
            elements = {
                {
                    label = "Object Spawner",
                    value = "object_spawner",
                }
            }
        },
        function(data, menu)
            if data.current.value == "object_spawner" then
                if not IsPedInAnyVehicle(PlayerPedId(), false) then
                    OpenOrgObjectSpawnerMenu()
                else
                    TriggerEvent("fdg_ui:SendNotification", "You cannot use this while in a vehicle")
                end
            end
            menu.close()
        end,
        function(data, menu)
            menu.close()
        end
    )
end

Controls.register("F6", "Organisation Action Menu", function()
    if not isDead then
        OpenOrgActionMenu()
    end
end)
