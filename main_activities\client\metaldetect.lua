------
-- Config
------

local terrains = {
	['road'] = {
		282940568,
		-1084640111,
		1187676648,
		1639053622,
		-1301352528,
		-1775485061,
		-754997699,
		1755188853,
		-399872228,
	},
	['dirt'] = {
		-1885547121,
		-1942898710
	},
    ['sand'] = {
        -1595148316,
        510490462,
        1288448767,
        -1907520769,
        509508168,
        909950165,
    }
}

local entityOffsets = {
    ["w_am_metaldetector"] = {
		bone = 18905,
        offset = vector3(0.15, 0.1, 0.0),
        rotation = vector3(270.0, 90.0, 80.0),
	},
}



------
-- Storage Tables
------

local attachedEntities = {}
local previousAnim = nil
local isDetecting = false
local isDigging = false
local scannerAudio = true
local currentPoints = {}
local currentPointsCount = 0
local createRange = 35
local close = false
local allowPress = true
local pointOfFocus = false
local canDig = false

------
-- Functions
------

local function getGroundHash(coords)
    local num = StartShapeTestCapsule(coords.x,coords.y,coords.z+4,coords.x,coords.y,coords.z-2.0, 1,1,veh,7)
    local arg1, arg2, arg3, arg4, arg5 = GetShapeTestResultEx(num)
    return arg5
end

local function getGroundId(coords)
	local hash = getGroundHash(coords)

    for k,v in pairs(terrains['sand']) do
		if (hash == v) then
			return 3, "Sand"
		end
	end

	return -1
end

local function ensureAnimDict(dict)
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do
        Wait(0)
    end
end

local function stopAnim(ped)
    if previousAnim then
        StopEntityAnim(ped, previousAnim[2], previousAnim[1], true)
        previousAnim = nil
    end
end

local function playAnimFlags(ped, dict, anim, flags)
    stopAnim(ped)
    ensureAnimDict(dict)
    local len = GetAnimDuration(dict, anim)
    TaskPlayAnim(ped, dict, anim, 1.0, -1.0, len, flags, 1, 0, 0, 0)
    previousAnim = {dict, anim}
end

local function playAnimUpper(ped, dict, anim)
    playAnimFlags(ped, dict, anim, 49)
end

local function playAnim(ped, dict, anim)
    playAnimFlags(ped, dict, anim, 0)
end

local function isPedInWater(ped)
    return IsEntityInWater(ped)
end

local function ensureModel(model)
    if IsModelInCdimage(model) then
        if not HasModelLoaded(model) then
            RequestModel(model)
        	while not HasModelLoaded(model) do
        		Wait(0)
        	end
    	end
	end
end

local function metalDetectAnim(ped)
    if not IsEntityPlayingAnim(ped, "mini@golfai", "wood_idle_a", 3) then
        playAnimUpper(PlayerPedId(), "mini@golfai", "wood_idle_a")
    end
end

local function attachEntity(ped, model)
    if entityOffsets[model] then
        ensureModel(model)
        local pos = GetEntityCoords(PlayerPedId())
    	local ent = CreateObjectNoOffset(model, pos, 1, 1, 0)
    	AttachEntityToEntity(ent, ped, GetPedBoneIndex(ped, entityOffsets[model].bone), entityOffsets[model].offset, entityOffsets[model].rotation, 1, 1, 0, 0, 2, 1)
        scannerEntity = ent
        table.insert(attachedEntities, ent)
        metalDetectAnim(ped)
    end
end

local function cleanupModels()
    for _, ent in next, attachedEntities do
        DetachEntity(ent, 0, 0)
        DeleteEntity(ent)
    end
    attachedEntities = {}
end

local function breakMetalDetectThread()
    local ped = GetPlayerPed(-1)
    isDetecting = false
    canFind = false

    Wait(5)

    stopAnim(ped)
    for _, ent in next, attachedEntities do
        DetachEntity(ent, 0, 0)
        DeleteEntity(ent)
    end
    attachedEntities = {}
    currentPoints = {}
end

local function digSequence()
    cleanupModels()
    local ped = PlayerPedId()
    local pos = GetEntityCoords(ped)
    StopEntityAnim(ped, "wood_idle_a", "mini@golfai", true)
    Wait(1000)
    Base.Animation.ActionAnimation("scenario", "world_human_gardener_plant", false, false, false, 12, function()
        StopAnimPlayback(ped, 0, 0)
        ClearPedSecondaryTask(ped)
        if IsPedUsingAnyScenario(ped) then
            ClearPedTasks(ped)
        end

        TriggerServerEvent("main_activites:earnMetalDetect")
        breakMetalDetectThread()
    end)
        
end

local function digTarget(ident)
    if isDigging then return end
    canDig = false
    local ped = PlayerPedId()
    currentPoints = {}
    isDigging = true
    isDetecting = false
    digSequence()
end


------
-- Main Thread
------

local function startMetalDetectThread()
    Citizen.CreateThread(function()
        while isDetecting and not pointOfFocus do Wait(500)
            local pedCoords = GetEntityCoords(PlayerPedId())
            local terrainHash = getGroundHash(pedCoords)
            local terrainId, terrainType = getGroundId(pedCoords)

            if terrainId ~= 3 or isPedInWater(ped) then
                canFind = false
            else
                canFind = true
            end
        end
    end)

    Citizen.CreateThread( function()
        local framecount = 0
        local frametime = 0
        local circleScale = 0.0
        local circleR, circleG, circleB, circleA = 255, 255, 255, 255
        local _circleR, _circleG, _circleB = 255, 255, 255
        local circleScaleMultiplier = 1.5
        local renderCircle = false
        while isDetecting do
            Wait(0)
            if not isDigging then
                local ped = PlayerPedId()
                local pos = GetEntityCoords(ped) + vector3(GetEntityForwardX(ped) * 0.75, GetEntityForwardY(ped) * 0.75, -0.75)
                -- local pos = GetWorldPositionOfEntityBone(scannerEntity, 0)
                if scannerState == "none" then
                    renderCircle = false
                    circleR, circleG, circleB = 150, 255, 150
                    _circleR, _circleG, _circleB = 150, 255, 150
                elseif scannerState == "fast" then
                    renderCircle = true
                    circleScale = circleScale + scannerScale
                    circleR, circleG, circleB = 255, 150, 150
                    if frametime > scannerFrametime then
                        frametime = 0.0
                    end
                elseif scannerState == "medium" then
                    renderCircle = true
                    circleScale = circleScale + scannerScale
                    circleR, circleG, circleB = 255, 255, 150
                    if frametime > scannerFrametime then
                        frametime = 0.0
                    end
                elseif scannerState == "ultra" then
                    renderCircle = false
                    circleScale = circleScale + scannerScale
                    circleR, circleG, circleB = 255, 100, 100
                    if frametime > 0.125 then
                        frametime = 0.0
                        if scannerAudio then PlaySoundFrontend(-1, "ATM_WINDOW", "HUD_FRONTEND_DEFAULT_SOUNDSET", 0) end
                        -- PlaySoundFrontend(-1, "TIMER_STOP", "HUD_MINI_GAME_SOUNDSET", 0)
                        if scannerAudio then PlaySoundFrontend(-1, "BOATS_PLANES_HELIS_BOOM", "MP_LOBBY_SOUNDS", 0) end
                    end
                    -- Draw the triple "found it" marker
                    circleA = 150
                    circleSize = 1.20 * circleScaleMultiplier
                    DrawMarker(1, pos, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, circleSize, circleSize, 0.2, circleR, circleG, circleB, circleA)
                    DrawMarker(6, pos, 0.0, 0.0, 0.0, 270.0, 0.0, 0.0, circleSize, 0.1, circleSize, circleR, circleG, circleB, circleA)
                    circleA = 200
                    circleSize = 0.70 * circleScaleMultiplier
                    DrawMarker(1, pos, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, circleSize, circleSize, 0.2, circleR, circleG, circleB, circleA)
                    DrawMarker(6, pos, 0.0, 0.0, 0.0, 270.0, 0.0, 0.0, circleSize, 0.1, circleSize, circleR, circleG, circleB, circleA)
                    circleA = 255
                    circleSize = 0.20 * circleScaleMultiplier
                    DrawMarker(1, pos, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, circleSize, circleSize, 0.2, circleR, circleG, circleB, circleA)
                    DrawMarker(6, pos, 0.0, 0.0, 0.0, 270.0, 0.0, 0.0, circleSize, 0.1, circleSize, circleR, circleG, circleB, circleA)
                end
                if renderCircle then
                    if circleScale > 100 then
                        while circleScale > 100 do
                            circleScale = circleScale - 100
                        end
                        _circleR, _circleG, _circleB = circleR, circleG, circleB
                        -- PlaySoundFrontend(-1, "BOATS_PLANES_HELIS_BOOM", "MP_LOBBY_SOUNDS", 0)
                        if scannerAudio then PlaySoundFrontend(-1, "ATM_WINDOW", "HUD_FRONTEND_DEFAULT_SOUNDSET", 0) end
                    end
                    circleSize = ((circleScale % 100) / 100) * circleScaleMultiplier
                    circleA = math.floor(255 - ((circleScale % 100) / 100) * 155)
                    DrawMarker(1, pos, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, circleSize, circleSize, 0.2, _circleR, _circleG, _circleB, circleA)
                    DrawMarker(6, pos, 0.0, 0.0, 0.0, 270.0, 0.0, 0.0, circleSize, 0.1, circleSize, _circleR, _circleG, _circleB, circleA)
                end

                framecount = (framecount + 1) % 120
                frametime = frametime + Timestep()
            end
        end
    end)
    
    Citizen.CreateThread( function()
        local ped = GetPlayerPed(-1)
        local pedCoords = GetEntityCoords(ped)

        while isDetecting and not pointOfFocus do Wait(200)
            if not IsEntityPlayingAnim(ped, "mini@golfai", "wood_idle_a", 3) and not isDigging then
                playAnimUpper(PlayerPedId(), "mini@golfai", "wood_idle_a")
            end

            local restrictedMovement = false
            restrictedMovement = restrictedMovement or IsPedFalling(ped)
            restrictedMovement = restrictedMovement or IsPedJumping(ped)
            restrictedMovement = restrictedMovement or IsPedSprinting(ped)
            restrictedMovement = restrictedMovement or IsPedRunning(ped)
            restrictedMovement = restrictedMovement or IsPlayerFreeAiming(ply)
            restrictedMovement = restrictedMovement or IsPedRagdoll(ped)
            restrictedMovement = restrictedMovement or IsPedInAnyVehicle(ped)
            restrictedMovement = restrictedMovement or IsPedInCover(ped)
            restrictedMovement = restrictedMovement or IsPedInMeleeCombat(ped)

            if restrictedMovement then canFind = false end

            if not canFind then
                circleScale = 0.0
                scannerScale = 0.0
                scannerState = "none"
            end
            if not canFind then
                -- Ped is busy and can't prospect at this time (like falling or w/e)
                StopEntityAnim(ped, "wood_idle_a", "mini@golfai", true)
                circleScale = 0.0
                scannerScale = 0.0
                scannerState = "none"
            end
        end

    end)

    Citizen.CreateThread(function()
        while isDetecting and not isDigging do Wait(10)
            if currentPointsCount < 10 then
                local ped = GetPlayerPed(-1)
                local pedCoords = GetEntityCoords(PlayerPedId())
                local teX, teY = math.random(-1*createRange, createRange) + 0.0, math.random(-1*createRange, createRange) + 0.0
                local coords = GetEntityCoords(ped)
                local trX, trY = coords.x + teX, coords.y + teY
                local temp, zCoord = GetGroundZFor_3dCoord(trX, trY, 9999.9, 0)

                local terrainId, terrainType = getGroundId(vector3(trX, trY, zCoord + 1.5))

                local tPoint = GetOffsetFromEntityInWorldCoords(PlayerPedId(), teX, teY, 0.0)
                local water = GetWaterHeight(tPoint.x, tPoint.y, tPoint.z)

                if terrainId == 3 and not water then
                    local point = GetOffsetFromEntityInWorldCoords(PlayerPedId(), teX, teY, 0.0)

                    local dist = GetDistanceBetweenCoords(pedCoords.x, pedCoords.y, pedCoords.z, point.x, point.y, point.z, false)

                    if dist < 30.0 then
                        currentPoints["metal_detect_"..currentPointsCount] = ({id = "metal_detect_"..currentPointsCount, coords = {x = point.x + 0.0, y = point.y + 0.0, z = point.z + 0.0}})
                        currentPointsCount =  currentPointsCount + 1 
                    end
                end
            end
        end
    end)

    Citizen.CreateThread(function()
        while isDetecting and not isDigging do
            Wait(1000)

            for ident, point in pairs(currentPoints) do
                if point and ident then
                    pedCoords = GetEntityCoords(PlayerPedId())
                    local dist = GetDistanceBetweenCoords(pedCoords.x, pedCoords.y, pedCoords.z, point.coords.x, point.coords.y, point.coords.z, false)

                    if dist > 30.0 then
                        currentPoints[ident] = false
                        currentPointsCount = currentPointsCount - 1
                    end
                end
            end
        end
    end)

    Citizen.CreateThread(function()
        while isDetecting and not isDigging do
            Wait(100)
            
            for ident, point in pairs(currentPoints) do
                if point and ident then
                    pedCoords = GetEntityCoords(PlayerPedId())
                    local dist = GetDistanceBetweenCoords(pedCoords.x, pedCoords.y, pedCoords.z, point.coords.x, point.coords.y, point.coords.z, false)

                    if dist < 4.0 then
                        pointOfFocus = point
                        circleScale = 0.0
                        scannerScale = 0.0
                        scannerState = "none"
                    end

                    if pointOfFocus then
                        local waitPeriod = 0
                        while pointOfFocus and not isDigging do Wait(waitPeriod)
                            pedCoords = GetEntityCoords(PlayerPedId())
                            
                            if pointOfFocus then
                                local pointDistance = GetDistanceBetweenCoords(pedCoords.x, pedCoords.y, pedCoords.z, pointOfFocus.coords.x, pointOfFocus.coords.y, pointOfFocus.coords.z, false)

                                if pointDistance > 4.0 then
                                    pointOfFocus = false
                                    circleScale = 0.0
                                    scannerScale = 0.0
                                    scannerState = "none"
                                    
                                    canDig = false
                                elseif pointDistance <= 4.0 and pointDistance > 2.0 then
                                    scannerFrametime = 0.4
                                    scannerScale = 3.75
                                    scannerState = "fast"

                                    canDig = false
                                elseif pointDistance <= 2.0 and pointDistance > 0.0 then
                                    circleScale = 0.0
                                    scannerScale = 0.0
                                    scannerState = "ultra"
                                    waitPeriod = 1000
                                    canDig = ident
                                end
                            end
                        end
                    end
                end
            end
        end
    end)
end

AddPlayerInteractionOption(
    function() -- Condition
        return (canDig)
    end,

    function() -- Action (ignore if its a submenu)
        circleScale = 0.0
        scannerScale = 0.0
        scannerState = "none"
        isDetecting = false
        isDigging = true

        digTarget(canDig)
    end,

    { text = "Dig Ground", icon = "&#xF0710;", action = "metaldetect_dig"}, -- Menu Display,

    {} -- Requires

)

------
-- Net Events
------

RegisterNetEvent("main_activities:metalDetect")
AddEventHandler("main_activities:metalDetect", function()
    
    local ped = GetPlayerPed(-1)
    local pedCoords = GetEntityCoords(ped)

    local terrainHash = getGroundHash(pedCoords)
    local terrainId, terrainType = getGroundId(pedCoords)

    if isDetecting then
        breakMetalDetectThread()
        return
    end

    if terrainId == 3 and not isPedInWater(ped) then
        isDetecting = true
        canFind = true
        attachEntity(ped, "w_am_metaldetector")
        startMetalDetectThread()
    end
end)

AddEventHandler("onResourceStop", function(resource)
    if resource == GetCurrentResourceName() then
        cleanupModels()
        breakMetalDetectThread()
    end
end)

