onBaseReady(function()
    Base.Markers.Create("fdg_patreon:changePlateCity", {x = -897.4982, y = -2035.496, z = 9.29}, {r = 255, g = 0, b = 0}, "Change Plate", {}, function(data)
            ChangePlate()
    end)

    -- Base.Markers.Create("fdg_patreon:changePlateSandy", {x = -136.3364, y = 6309.191, z = 31.499}, {r = 255, g = 0, b = 0}, "Change Plate", {}, function(data)
    --         ChangePlate()
    -- end)
end)

function ChangePlate()
    PlayerData = Base.GetPlayerData()
    local veh = GetVehiclePedIsIn(PlayerPedId(), false) or false

    if GetVehicleNumberOfPassengers(veh) == 0 then
        TriggerServerCallback('core_vehicles:tebexCheck', function(success)
            if success then
                
                Base.Notification("Any vehicle displaying license plates that combine the letter 'O' and the number '0' are subject to permanent seizure. E.g. 0O000O0O")

                Base.UI.Menu.CloseAll()
                Base.UI.Menu.Open('dialog', GetCurrentResourceName(), 'change_plate_string_input', 
                    {
                        title = 'New Plate'
                    },

                    function(data, menu)
                        local plate = string.upper(data.value)

                        if string.match(plate, "%W") then
                            TriggerEvent("fdg_ui:SendNotification", "You can only use letters A-Z and numbers 0-9")
                            return
                        end

                        if string.len(plate) ~= 8 then
                            TriggerEvent("fdg_ui:SendNotification", "Your plate MUST be exactly 8 characters")
                            return
                        end

                        TriggerServerEvent('core_vehicles:changePlate', NetworkGetNetworkIdFromEntity(veh), plate)
                        Base.UI.Menu.CloseAll()
                    end,

                    function(data, menu) 
                        Base.UI.Menu.CloseAll()
                    end
                )        
            else
                Base.UI.Menu.CloseAll() 
                TriggerEvent('fdg_ui:SendNotification', "You need at least Tebex Tier 2 to change your plate")  
            end
        end)
    else
        TriggerEvent('fdg_ui:SendNotification', "You can not have a passanger in the car while changing the plate")  
    end
    
end

RegisterNetEvent('core_vehicles:changePlateText')
AddEventHandler('core_vehicles:changePlateText', function(veh, plate)
    SetVehicleNumberPlateText(NetworkGetEntityFromNetworkId(veh), plate)
end)
