local function createEffect(name, displayName, notificationMessage, clipset, effectDesc, effectSubDesc, ragdollChance, jumpFailChance)
    return StatusEffect(name, displayName, {
        onStart = function(effect)
            Base.Notification(notificationMessage)
            if clipset then
                AddClipset(effect.name, clipset, 50)
            end
        end,
        onTick = function(effect)
            local ped = PlayerPedId()
            if ragdollChance then
                if IsControlJustPressed(1, 21) then
                    Wait(2000)
                    SetPedToRagdoll(ped, 2000, 2000, 0, true, true, false)
                end

                if math.random() < ragdollChance then
                    SetPedToRagdoll(ped, 2000, 2000, 0, true, true, false)
                end
            end

            if IsControlJustPressed(1, 22) then
                if jumpFailChance and math.random() < jumpFailChance then
                    Wait(500)
                    SetPedToRagdoll(ped, 2000, 2000, 0, true, true, false)
                end
            end
        end,
        onStop = function(effect)
            if clipset then
                RemoveClipset(effect.name)
            end
        end,
    }, {
        alert = false,
        desc = effectDesc,
        subdesc = effectSubDesc,
        tickRate = 0
    })
end

local function consumeDrink(itemName, progressMessage, animDict, animName, onFinishMessage, effectCallback)
    ClearPedTasks(PlayerPedId())
    Wait(100)
    exports.main_progressbar:start(3000, progressMessage)
    Base.Animation.ActionAnimation(animDict, animName, false, false, false, 3, function()
        exports.main_progressbar:stop()
        Base.Notification(onFinishMessage)
        TriggerServerEvent("effects:removeItem", itemName)
        effectCallback()
    end, function()
        exports.main_progressbar:stop()
    end)
end

local tipsyEffect = createEffect("tipsy", "Tipsy", "You start to feel tipsy from the alcohol", nil, "You're feeling tipsy from the alcohol", "You are no longer scared of women")
local drunkEffect = createEffect("drunk", "Drunk", "You start to feel drunk from the alcohol", "move_m@drunk@verydrunk", "You're feeling drunk", "Where are your keys?", nil, 0.6)
local plasteredEffect = createEffect("plastered", "Plastered", "You start to feel plastered from the alcohol", "move_m@drunk@verydrunk", "You're feeling plastered", "Unable to run or jump", 0.001, 1)

local alcoholEffectManager = LinearStatusEffectManager("alcohol",
{
    startValue = 0,
    tickRate = 10000,
    min = 0,
    max = 100,
    databaseSave = false
}, {
    LinearStatusEffect(tipsyEffect, 1, 40),
    LinearStatusEffect(drunkEffect, 41, 70),
    LinearStatusEffect(plasteredEffect, 71, 100)
})

RegisterNetEvent("effects:drinkAlcohol", function(itemName)
    consumeDrink(itemName, "Drinking Alcohol", "anim@amb@nightclub@mini@drinking@drinking_shots@ped_b@drunk@", "drink", "You finished your drink", function()
        alcoholEffectManager.add(15)
    end)
end)

RegisterNetEvent("effects:drinkWater", function(itemName)
    consumeDrink(itemName, "Drinking Water", "amb@world_human_drinking@coffee@male@idle_a", "idle_a", "You finished your drink ", function()
        alcoholEffectManager.remove(5)
    end)
end)

RegisterNetEvent("effects:getBacLevelClient", function()
    local level = alcoholEffectManager.getValue()
    TriggerServerEvent("effects:returnBacLevel", level)
end)

RegisterInteraction("player", "breath_test", { label = "Breath Test", emoji = "🍺" }, function(player)
	local targetPlayer = GetPlayerServerId(player)
	exports.main_roleplay:startDatabaseAnim("breathtest")
	PlaySoundFrontend(-1, "5_Second_Timer", "DLC_HEISTS_GENERAL_FRONTEND_SOUNDS", false)
	exports["main_progressbar"]:start(7000, "Breath Testing")
	Wait(7000)
    TriggerServerCallback("effects:getBacLevel", function(result)
		local level = math.floor(result * 0.00122 * 10000 + 0.5) / 10000 -- Round to 4 decimal places.

		Wait(2000)

		Base.Notification("BAC: ".. level)

		if level > 0.05 then
			PlaySoundFrontend(-1, "Hack_Failed", "DLC_HEIST_BIOLAB_PREP_HACKING_SOUNDS", false)
		else
			PlaySoundFrontend(-1,  "Beep_Red", "DLC_HEIST_HACKING_SNAKE_SOUNDS", false)
		end
	end, targetPlayer)
	ClearPedTasks(PlayerPedId())
end, "breathtest")
