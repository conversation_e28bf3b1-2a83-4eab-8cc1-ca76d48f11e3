Vehicle = {}

Vehicle.callbacks = {}
Vehicle.callbackCount = 1

Vehicle.requestModel = function(model)
    if (not IsModelInCdimage(model)) then
        return false
    end

    if (not HasModelLoaded(model)) then
        RequestModel(model)
        local count = 0
        while not HasModelLoaded(model) and count < 100 do
            count = count + 1
            Wait(100)
        end
        return HasModelLoaded(model)
    end
    return true
end

Vehicle.createFromModel = function(model, coords, heading, data, cb, isLocal)
    if IsAnyVehicleNearPoint(coords.x, coords.y, coords.z, 3.0) then
        TriggerEvent('core_hud:sendNotification', "Error", 'Vehicle Spawn is blocked.')
        return
    end

    model = (type(model) == "string" and GetHashKey(model) or model)
    if (not IsModelInCdimage(model)) then
        return
    end

    if (isLocal) then
        if (Vehicle.requestModel(model)) then
            local vehicle = CreateVehicle(model, coords.x, coords.y, coords.z, heading, false, true)
            if (cb) then cb(vehicle) end
            SetModelAsNoLongerNeeded(model)
        end
    else
        if (Vehicle.callbackCount > 100) then Vehicle.callbackCount = 0 end
        Vehicle.callbackCount = Vehicle.callbackCount + 1
        Vehicle.callbacks[tostring(Vehicle.callbackCount)] = cb
        TriggerServerEvent("base_vehicle:createFromModel", model, coords, heading, data, Vehicle.callbackCount)
    end
end

Vehicle.createFromId = function(id, coords, heading, cb)
    if IsAnyVehicleNearPoint(coords.x, coords.y, coords.z, 3.0) then
        TriggerEvent('core_hud:sendNotification', "Error", 'Vehicle Spawn is blocked.')
        return
    end
    if (Vehicle.callbackCount > 100) then Vehicle.callbackCount = 0 end
    Vehicle.callbackCount = Vehicle.callbackCount + 1
    Vehicle.callbacks[tostring(Vehicle.callbackCount)] = cb
    TriggerServerEvent("base_vehicle:createFromId", id, coords, heading, Vehicle.callbackCount)
end

Vehicle.isOwner = function(vehicle, cb)
    local state = Entity(vehicle).state
    if (state.owner) then
        if (state.owner.accountid == PlayerData.identifier and state.owner.charid == PlayerData.charid) then
            cb(true, false)
            return
        end
    end

    if (state.keys) then
        for _, v in pairs(state.keys) do
            if (v.accountid == PlayerData.identifier and v.charid == PlayerData.charid) then
                cb(false, true)
                return
            end
        end
    end

    if (state.job) then
        if DoesPlayerHaveOrg(state.job) then
            cb(false, true)
            return
        end
    end

    TriggerServerCallback('core_vehicles:checkOwner', function(o, k)
        cb(o, k)
    end, NetworkGetNetworkIdFromEntity(vehicle))
end

Vehicle.getState = function(vehicle, id, cb)
    local state = Entity(vehicle).state
    if (state[id]) then
        cb(state[id])
    else
        TriggerServerCallback("base_vehicle:getState", function(result)
            cb(result)
        end, NetworkGetNetworkIdFromEntity(vehicle), id)
    end
end

RegisterNetEvent("base_vehicle:callbackModel")
AddEventHandler("base_vehicle:callbackModel", function(callback, netId)
    local start = GetGameTimer()
    while not NetworkDoesNetworkIdExist(netId) and GetGameTimer() - start < 15000 do
        Wait(0)
    end

    if (not NetworkDoesNetworkIdExist(netId)) then
        print("[ERROR] base_vehicle > Couldn't Retrive Network ID [" .. (netId or "nil") .. "] for Vehicle")
        return
    end

    local start = GetGameTimer()
    local vehicle = NetworkGetEntityFromNetworkId(netId)
    while (not NetworkHasControlOfEntity(vehicle) or not NetworkHasControlOfNetworkId(netId)) and GetGameTimer() - start < 15000 do
        Wait(0)
        NetworkRequestControlOfEntity(vehicle)
        NetworkRequestControlOfNetworkId(netId)
    end

    if (DoesEntityExist(vehicle) and NetworkHasControlOfEntity(vehicle) and NetworkHasControlOfNetworkId(netId)) then
        if IsThisModelAPlane(GetEntityModel(vehicle)) then
            SetPlaneTurbulenceMultiplier(vehicle, 0.0)
        end

        -- basic settings
        SetVehicleEngineCanDegrade(vehicle, true)
        SetVehicleCanBreak(vehicle, true)
        SetVehicleWheelsCanBreak(vehicle, true)

        if DecorIsRegisteredAsType("Player_Vehicle", 3) then
            DecorSetInt(vehicle, "Player_Vehicle", -1)
        end

        if (Vehicle.callbacks[tostring(callback)]) then
            Vehicle.callbacks[tostring(callback)](vehicle)
            Vehicle.callbacks[tostring(callback)] = nil
        end
    else
        DeleteEntity(vehicle)
    end
end)

RegisterNetEvent("base_vehicle:callbackId")
AddEventHandler("base_vehicle:callbackId", function(callback, netId, props, damage, plate)
    local start = GetGameTimer()
    while not NetworkDoesNetworkIdExist(netId) and GetGameTimer() - start < 15000 do
        Wait(0)
    end

    if (not NetworkDoesNetworkIdExist(netId)) then
        print("[ERROR] base_vehicle > Couldn't Retrive Network ID [" .. (netId or "nil") .. "] for Vehicle")
        return
    end

    local start = GetGameTimer()
    local vehicle = NetworkGetEntityFromNetworkId(netId)
    while (not NetworkHasControlOfEntity(vehicle) or not NetworkHasControlOfNetworkId(netId)) and GetGameTimer() - start < 15000 do
        Wait(0)
        NetworkRequestControlOfEntity(vehicle)
        NetworkRequestControlOfNetworkId(netId)
    end

    if (DoesEntityExist(vehicle) and NetworkHasControlOfEntity(vehicle) and NetworkHasControlOfNetworkId(netId)) then
        Base.Vehicles.Props.Set(vehicle, props)
        Base.Vehicles.Damage.Set(vehicle, damage)
        SetVehicleNumberPlateText(vehicle, plate)
        SetVehicleOnGroundProperly(vehicle)

        if IsThisModelAPlane(GetEntityModel(vehicle)) then
            SetPlaneTurbulenceMultiplier(vehicle, 0.0)
        end

        -- basic settings
        SetVehicleEngineCanDegrade(vehicle, true)
        SetVehicleCanBreak(vehicle, true)
        SetVehicleWheelsCanBreak(vehicle, true)

        if DecorIsRegisteredAsType("Player_Vehicle", 3) then
            DecorSetInt(vehicle, "Player_Vehicle", -1)
        end

        if (Vehicle.callbacks[tostring(callback)]) then
            Vehicle.callbacks[tostring(callback)](vehicle)
            Vehicle.callbacks[tostring(callback)] = nil
        end
    else
        DeleteEntity(vehicle)
    end
end)

RegisterNetEvent("base_vehicle:setVehicleDetails")
AddEventHandler("base_vehicle:setVehicleDetails", function(vehicle, props, damage, plate --[[ , wheelMods ]])
    local timeout = 0

    if not NetworkDoesNetworkIdExist(vehicle) then
        print("[ERROR] setVehicleDetails failed, netid " .. vehicle .. " didn't exist!")
        return false
    end

    local entity = NetworkGetEntityFromNetworkId(vehicle)

    if not DoesEntityExist(entity) then
        print("[ERROR] setVehicleDetails failed, entity " .. entity .. " didn't exist!")
        return false
    end


    SetVehicleOnGroundProperly(entity)

    if props then
        Base.Vehicles.Props.Set(entity, props)
    end

    if damage then
        Base.Vehicles.Damage.Set(entity, damage)
    end

    if plate then
        SetVehicleNumberPlateText(entity, plate)
    end

    -- basic settings
    SetVehicleEngineCanDegrade(entity, true)
    SetVehicleCanBreak(entity, true)
    SetVehicleWheelsCanBreak(entity, true)
    if DecorIsRegisteredAsType("Player_Vehicle", 3) then
        DecorSetInt(entity, "Player_Vehicle", -1)
    end
end)

onBaseReady(function()
    exports("createFromModel", Vehicle.createFromModel)
    exports("createFromId", Vehicle.createFromId)
    exports("isOwner", Vehicle.isOwner)
    exports("getState", Vehicle.getState)
end)