InvoiceTablet = {
    instantstopanim = false,
    objects = {},
    objSettings = {
        model = GetHash<PERSON>ey("hei_prop_dlc_tablet"),
        bone = 18905,
        offset = vector3(0.2, 0.05, 0.1),
        rotation = vector3(-25.0, -20.0, -140.0),
        dict = "anim@arena@amb@seat_drone_tablet@female@",
        anim = "idle_d",
    }
}



local function getOrgInvoices()
    -- Await the server callback to get the player's invoices for their organizations
    local invoices = AwaitServerCallback("orgs:getOrgInvoices")

    -- Group invoices by organization
    local orgInvoices = {}
    for _, invoice in ipairs(invoices) do
        local org = invoice.organisation
        if not orgInvoices[org] then
            orgInvoices[org] = {}
        end
        table.insert(orgInvoices[org], invoice)
    end

    -- Create menu elements for organization selection
    local elements = {}
    for org, _ in pairs(orgInvoices) do
        table.insert(elements, {
            label = org, -- Use organization name for the label
            org = org,
            submenu = true -- Flag for submenus
        })
    end

    return elements, orgInvoices
end

function OpenInvoicePlayerMenu(target)
    local serverID = GetPlayerServerId(target)
    Animation()

    -- Get organizations and their invoices
    local elements, orgInvoices = getOrgInvoices()

    -- First menu: Select organization
    Base.UI.Menu.Open("default", GetCurrentResourceName(), "invoices_orgs", {
        title = "Select Organisation",
        align = "bottom-right",
        elements = elements
    }, function(data, menu)
        if data.current.submenu then
            local org = data.current.org

            -- Build submenu for invoices in the selected organization
            local invoiceElements = {}
            for _, invoice in ipairs(orgInvoices[org]) do
                table.insert(invoiceElements, {
                    label = invoice.label,
                    organisation = invoice.organisation,
                    custom_amount = invoice.custom_amount
                })
            end

            -- Open submenu: Invoices for the selected organization
            Base.UI.Menu.Open("default", GetCurrentResourceName(), "invoices", {
                title = "Invoices - " .. org,
                align = "bottom-right",
                elements = invoiceElements
            }, function(subData, subMenu)
                local org = subData.current.organisation
                local label = subData.current.label

                -- Handle invoice payment with or without custom amount
                if subData.current.custom_amount then
                    OpenDialog("Invoice Amount", function(amount)
                        ClearAnimation()
                        TriggerServerEvent("orgs:sendInvoice", serverID, org, label, amount)
                        subMenu.close()
                    end, function()
                        subMenu.close()
                        ClearAnimation()
                    end, "number", 0)
                else
                    subMenu.close()
                    menu.close()
                    ClearAnimation()
                    TriggerServerEvent("orgs:sendInvoice", serverID, org, label)
                end
            end, function(subData, subMenu)
                subMenu.close()
            end)
        end
    end, function(data, menu)
        menu.close()
        InvoiceTablet.instantstopanim = true
        ClearAnimation()
    end)
end

-- Register interaction to open the invoice menu
RegisterInteraction("player", "invoice", {
    label = "Invoice",
    emoji = "💸"
}, function(player)
    OpenInvoicePlayerMenu(player)
end, "invoices.send")


function Animation()
    RequestModel(InvoiceTablet.objSettings.model)
    while not HasModelLoaded(InvoiceTablet.objSettings.model) do Citizen.Wait(150) end

    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)
    local bone = GetPedBoneIndex(playerPed, InvoiceTablet.objSettings.bone)
    local object = CreateObject(InvoiceTablet.objSettings.model, coords.x, coords.y, coords.z, true, true, false)

    table.insert(InvoiceTablet.objects, object)
    AttachEntityToEntity(object, playerPed, bone, InvoiceTablet.objSettings.offset.x, InvoiceTablet.objSettings.offset.y, InvoiceTablet.objSettings.offset.z, InvoiceTablet.objSettings.rotation.x, InvoiceTablet.objSettings.rotation.y, InvoiceTablet.objSettings.rotation.z, true, false, false, false, 2, true)
    SetModelAsNoLongerNeeded(InvoiceTablet.objSettings.handle)

    RequestAnimDict(InvoiceTablet.objSettings.dict)
    while not HasAnimDictLoaded(InvoiceTablet.objSettings.dict) do Wait(0) end
    TaskPlayAnim(playerPed, InvoiceTablet.objSettings.dict, InvoiceTablet.objSettings.anim, 3.0, -1, -1, 50, 0, false, false, false)
end

function ClearAnimation()
    if not InvoiceTablet.instantstopanim then
        Wait(3000)
    end
    for i, obj in ipairs(InvoiceTablet.objects) do DeleteEntity(obj) end
    StopAnimTask(PlayerPedId(), InvoiceTablet.objSettings.dict, InvoiceTablet.objSettings.anim, 1.0)
    InvoiceTablet.instantstopanim = false
end