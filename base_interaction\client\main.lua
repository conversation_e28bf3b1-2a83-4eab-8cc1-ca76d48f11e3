local hit, crosshairActive = false, false
local interactionEntity, entityType = false, 0
local inventoryOpen = false
local forcedMenu = false -- "deep_dryer"
local registeredOptions = {
    player = {},
    ped = {},
    vehicle = {
        -- exampleInteraction1 = {
        --     displayData = { label = "Test Interaction 1" },
        --     resource = "base_interaction",
        --     onInteract = function(entity)
        --         print("Example Interaction 1", entity)
        --     end,
        --     condition = function(entity)
        --         return true
        --     end
        -- },
        -- exampleInteraction2 = {
        --     displayData = { label = "Test Interaction 2" },
        --     resource = "base_interaction",
        --     onInteract = function(entity)
        --         print("Example Interaction 2", entity)
        --     end,
        --     condition = "handcuff"
        -- }
    },
    object = {},
    vehicle_override = {},
    furniture = {}
}

AddEventHandler("onInventoryOpen", function()
    inventoryOpen = true
end)

AddEventHandler("onInventoryClose", function()
    inventoryOpen = false
end)


-- Menu Option condition check
local function isMenuOptionValid(menuOption, entity)
    if not menuOption.condition then
        return true
    end

    -- Basic Permission
    if type(menuOption.condition) == "string" then
        --print("Checking permission", menuOption.condition, DoesPlayerHaveOrgPermission(menuOption.condition))
        return DoesPlayerHaveOrgPermission(menuOption.condition)
    end

    -- Custom Condition (TODO_INTERACTION: this will hide the error, find out how to handle it properly. probably a thread)
    local success, result = pcall(menuOption.condition, entity)

    if success then
        return result
    else
        print("[INTERACTION] Error in condition for menu option", menuOption.displayData.label)
        return false
    end
end

-- Filters a list of menu options based on their conditions
local function generateMenuOptions(options, entity)
    local menuOptions = {}
    local count = 0

    for action, option in pairs(options) do
        if isMenuOptionValid(option, entity) then
            menuOptions[action] = option
            count = count + 1
        end
    end

    return menuOptions, count
end

---Determines whether a player is able to interact with the environment
---@return boolean
local function canPlayerInteract()
    return not inventoryOpen and not isCuffed and not isDead and not IsPlayerFreeAiming(PlayerId()) and not IsPedRagdoll(PlayerPedId()) and not IsPedInAnyVehicle(PlayerPedId(), false)
end

---Displays the crosshair UI elements
---@param active boolean
local function setCrosshair(active)
    SendNUIMessage({ crosshair = active })
end

local function isPedInteractable(entity)
    return true
end

local function isVehInteractable(entity)
    return true
end

local function isObjInteractable(entity)
    local interaction

    local state = Entity(entity).state

    if state.interactEvent then return true end
    if state.furnitureId ~= nil then return true end

    interaction = registeredOptions.object
    for _, option in pairs(interaction) do
        if isMenuOptionValid(option, entity) then
            return true
        end
    end
end

-- Interface
local function registerInteraction(typeId, identifier, displayData, onInteract, condition)
    registeredOptions[typeId][identifier] = { onInteract = onInteract, condition = condition, displayData = displayData, resource = GetInvokingResource() }
end
exports("registerInteraction", registerInteraction)

local function unregisterInteraction(type, object)
    registeredOptions[type][tostring(object)] = nil
end
exports("unregisterInteraction", unregisterInteraction)

local function setForcedMenu(menu)
    forcedMenu = menu or false
end
exports("setForcedMenu", setForcedMenu)

-- Draw Crosshair when applicable, and remember the Entity
CreateThread(function()
    while true do
        if not canPlayerInteract() then
            setCrosshair(false)
            interactionEntity = false
            crosshairActive = false
            goto continue
        end

        -- -- Forced Menu
        if forcedMenu then
            setCrosshair(true)
            crosshairActive = true
            entityType = 4
            goto continue
        end

        -- -- Check if the player is aiming at an entity
        hit, interactionEntity = GetEntityFromCamera()
        if not hit or not DoesEntityExist(interactionEntity) then
            if crosshairActive then
                setCrosshair(false)
                interactionEntity = false
                crosshairActive = false
            end

            goto continue
        end
        entityType = GetEntityType(interactionEntity)

        if entityType == 1 then
            -- Player
            if IsPedAPlayer(interactionEntity) then
                setCrosshair(true)
                crosshairActive = true
                goto continue
            end

            -- Ped
            if isPedInteractable(interactionEntity) then
                setCrosshair(true)
                crosshairActive = true
                goto continue
            end

            goto continue
        end

        -- Vehicle
        if (entityType == 2) and isVehInteractable(interactionEntity) then
            setCrosshair(true)
            crosshairActive = true
            goto continue
        end

        -- Object
        if (entityType == 3) and isObjInteractable(interactionEntity) then
            setCrosshair(true)
            crosshairActive = true
            goto continue
            -- end

            -- goto continue
        end

        if crosshairActive then
            setCrosshair(false)
            interactionEntity = false
            crosshairActive = false
        end

        ::continue::
        Wait(100)
    end
end)

-- Handle pressing E when an entity is selected
local function openMenu()
    -- If an object has a state of attachedPlayer, then we can target the player instead of the object
    if interactionEntity and DoesEntityExist(interactionEntity) then
        local state = Entity(interactionEntity).state

        if state.attachedPlayer then
            local player = GetPlayerFromServerId(state.attachedPlayer)
            if player and player ~= PlayerId() then
                interactionEntity = GetPlayerPed(player)
                entityType = 1
            end
        end
    end

    if not canPlayerInteract() then
        return print("Cannot interact when cuffed or wounded")
    end

    -- Forced interaction menu (static locations)
    if forcedMenu and entityType == 4 then
        if not registeredOptions[forcedMenu] then
            print("[INTERACTION] Forced menu type '" .. forcedMenu .. "' does not exist")
            return
        end

        local menuOptions = generateMenuOptions(registeredOptions[forcedMenu], interactionEntity)
        OpenInteractMenu(menuOptions, false)

        return
    end

    -- Entity Menus
    if not interactionEntity or interactionEntity == 0 then
        return
    end

    -- Ped interaction
    if entityType == 1 then
        -- Player Interaction
        if IsPedAPlayer(interactionEntity) then
            local player = NetworkGetPlayerIndexFromPed(interactionEntity)
            local menuOptions, count = generateMenuOptions(registeredOptions.player, player)

            if not menuOptions or count == 0 then
                return
            end

            OpenInteractMenu(menuOptions, player)
            return

            -- Other Ped
        else
            -- Dialog Peds
            if Entity(interactionEntity).state.dialog then
                local dialogType = Entity(interactionEntity).state.dialog
                TriggerEvent("interactPedCharacter:" .. dialogType, interactionEntity)
                return
            end

            -- Menu Options
            local menuOptions, count = generateMenuOptions(registeredOptions.ped, interactionEntity)

            if not menuOptions or count == 0 then
                return
            end

            OpenInteractMenu(menuOptions, interactionEntity)
            return
        end

        return
    end

    -- Interacting with vehicle
    if entityType == 2 then
        local model = GetEntityModel(interactionEntity)

        local override = registeredOptions.vehicle_override[tostring(model)]
        if override then
            if isMenuOptionValid(override, interactionEntity) then
                override.onInteract(interactionEntity)
                return
            end

            return
        end

        local menuOptions = generateMenuOptions(registeredOptions.vehicle, interactionEntity)
        OpenInteractMenu(menuOptions, interactionEntity)

        return
    end

    -- Interacting with object
    if entityType == 3 then
        local menuOptions, count = generateMenuOptions(registeredOptions.object, interactionEntity)

        if not menuOptions or count == 0 then
            return
        end

        -- Default action
        if count == 1 then
            for _, option in pairs(menuOptions) do
                option.onInteract(interactionEntity)
                return
            end
        end

        OpenInteractMenu(menuOptions, interactionEntity)
        return
    end
end
AddEventHandler("interact", openMenu)

CreateThread(function()
    Wait(0)
    TriggerEvent("interactionsReady")
end)
