-- Ordered by priority in each group
-- For example, if a player could be in multiple zones, the zone closest to the top of the list will be used

local INTERIOR_INSTANCES = {
    -- [190722] = {
    --     instanced = true,
    --     rooms = {
    --         [1202677754] = {
    --             name = "SEWER_THRONEROOM",
    --         }
    --     }
    -- },
    {
        identifier = "VU",
        coords = vector3(106.57, -1297.71, 21.39),
        instanced = true,
    },
    {
        identifier = "MBA",
        coords = vector3(-328.64566040039, -1972.7590332031, 21.603458404541),
        instanced = true,
    },
    {
        identifier = "COURTHOUSE",
        coords = vector3(-553.74554443359, -189.13952636719, 38.221054077148),
        instanced = true,
        rooms = {
            [1558375666] = {
                name = "COURTHOUSE_COURTROOM",
            }
        }
    },
    {
        identifier = "ABOUTTHEART_NEW",
        coords = vector3(286.71, -179.96, 46.79),
        instanced = true,
    },
    -- {
    --     identifier = "BOD Boardroom",
    --     coords = vector3(-523.97210693359, -182.78485107422, 42.200485229492),
    --     instanced = true,
    -- }

}

-- Init config
local identifiedZones = {}
local loadedInteriors = {}
for i, zone in ipairs(INTERIOR_INSTANCES) do
    local id = zone.identifier
    local interiorId = GetInteriorAtCoords(zone.coords.x, zone.coords.y, zone.coords.z)
    if interiorId ~= 0 then
        zone.id = i * 100
        zone.name = id
        if zone.rooms then
            local roomCount = 1
            for roomID, room in pairs(zone.rooms) do
                if not identifiedZones[room] then
                    identifiedZones[room] = zone.id + roomCount
                    roomCount = roomCount + 1
                end

                zone.rooms[roomID] = identifiedZones[room]
            end
        end

        loadedInteriors[interiorId] = zone
    end
end


local function getZonePlayerIsIn()
    local playerInterior = GetInteriorFromEntity(PlayerPedId())
    if playerInterior == 0 then
        return nil
    end

    -- print("playerInterior", playerInterior)

    local interiorDetails = loadedInteriors[playerInterior]
    if not interiorDetails then
        return nil
    end

    -- print("interiorDetails", interiorDetails)

    local zone = nil
    if interiorDetails.instanced then
        zone = interiorDetails.id
    end

    if interiorDetails.rooms then
        local playerRoom = GetRoomKeyFromEntity(PlayerPedId())
        if playerRoom == 0 then
            return zone
        end

        local roomDetails = interiorDetails.rooms[playerRoom]
        if not roomDetails then
            return zone
        end

        return roomDetails
    end

    return zone
end

local currentZone = nil

CreateThread(function()
    while true do
        Wait(0)

        local zone = getZonePlayerIsIn()

        if not zone and currentZone then
            TriggerServerEvent("world:exitZone")
        end

        if zone and zone ~= currentZone then
            TriggerServerEvent("world:enterZone", zone)
        end

        currentZone = zone
    end
end)

RegisterNetEvent("base:characterLoaded", function()
    Wait(2000)
    currentZone = nil
end)
