local CHANGE_CHAR_TIME = 20
SwapCharacter = function()
    if not isCuffed and not isDead then
        local startTime = GetGameTimer()
        local startCoords = GetEntityCoords(PlayerPedId())

        TriggerEvent("fdg_ui:SendNotification", "Hold still for " .. CHANGE_CHAR_TIME .. " seconds", { queue = "swapchar", layout = "bottom-right", theme = "progress", timeout = CHANGE_CHAR_TIME * 1000 })
        TriggerServerEvent('fdg:savePlayer')
        while (GetGameTimer() - startTime < CHANGE_CHAR_TIME * 1000) do
            if (GetDistanceBetweenCoords(startCoords, GetEntityCoords(PlayerPedId()), true) > 5.0) then
                TriggerEvent("fdg_ui:SendNotification", "<font color='red'>You moved too far</font>", { layout = "bottom-right", timeout = 5000 })
                GeneralLog("Cancelled Changing Character", PlayerData.logName .. " has cancelled the change", 'red', 'change-character')
                return
            end
            Wait(0)
        end
        Wait(100)
        TriggerEvent("fdg_ui:ClearNotifications", "swapchar")
        TriggerEvent("dispatch:clearNotifications")
        TriggerEvent("core_hud:disableGPS")
        TriggerEvent("main_pd:clearbodycam")
        TriggerEvent("core_effects:clearStatusEffects")
        exports["main_charselect"]:openSelectMenu()
        GeneralLog("Changing Character", PlayerData.logName .. " has changed characters", 'blue', 'change-character')
    else
        TriggerEvent("fdg_ui:SendNotification", "<font color='red'>You cannot change character right now</font>", { layout = "bottom-right", timeout = 5000 })
    end
end

QUI.registerMenuAction("switch_character", SwapCharacter)
