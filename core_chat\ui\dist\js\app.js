(function(){"use strict";var t={3297:function(t,e,s){var n=s(5471),a=s(8505),i=s(5486),o=function(){var t=this,e=t._self._c;t._self._setupProxy;return e(a.A,{staticStyle:{"background-color":"transparent"}},[e(i.A,[e("div",{staticClass:"chat-wrapper"},[e("Chat")],1),e("div",{staticClass:"chat-helper-wrapper"},[e("ChatHelper")],1)])],1)},r=[],c=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticClass:"chat-container",class:{showing:t.chatShowing,hidden:t.chatHidden},style:{width:`${t.chatSettingsWidth}vw`}},[t.chatCursorActive?e("div",{staticClass:"settings-button-container"},[e("ChatSettings")],1):t._e(),e("ChatMessages"),e("div",{staticClass:"input-container"},[t.chatInputActive?e("PlayerInput"):t._e()],1),t.chatShowSuggestions?e("div",{staticClass:"suggestions-container"},[e("ChatSuggestions")],1):t._e()],1)},h=[],u=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticClass:"messages-container",class:{cursorActive:t.chatCursorActive},attrs:{id:"messages"}},t._l(t.messages,(function(t,s){return e("ChatMessage",{key:s,attrs:{message:t}})})),1)},l=[],g=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticClass:"message-container",class:{smallFont:t.smallFont}},[t.message.title?e("ChatMessageTitle",{attrs:{message:t.message}}):t._e(),t.message.value?e("ChatMessageValue",{attrs:{message:t.message}}):t._e()],1)},d=[],p=function(){var t=this,e=t._self._c;t._self._setupProxy;return t.message.options?.sanitizeTitle?e("span",{staticClass:"messageTitle",style:{fontSize:t.getFontSizeVh(t.chatSettingsFontSize,t.message.options?.fontSize),color:t.message.titleColor&&`rgb(${t.message.titleColor[0]}, ${t.message.titleColor[1]}, ${t.message.titleColor[2]})`}},[t._v(" "+t._s(t.title))]):e("span",{staticClass:"messageTitle",style:{fontSize:t.getFontSizeVh(t.chatSettingsFontSize,t.message.options?.fontSize),color:t.message.titleColor&&`rgb(${t.message.titleColor[0]}, ${t.message.titleColor[1]}, ${t.message.titleColor[2]})`},domProps:{innerHTML:t._s(t.title)}})},m=[];function v(t){return t.charAt(0).toUpperCase()+t.slice(1)}function f(t,e){let s=0,n=0;return t/=10,s="small"===e?1.1:"large"===e?1.5:1.3,n=s+t,`${n}vh`}function S(t){return t.replace(/\^[\d\*_~=r]/g,"")}var A=n.Ay.extend({name:"ChatMessageTitle",props:{message:{type:Object,required:!0}},data:()=>({}),methods:{capitalizeFirstLetter:v,getFontSizeVh:f},computed:{title(){let t=this.message.title;return t=v(t),t=S(t),t},chatSettingsFontSize(){return this.$store.state.chatSettings.fontSize}}}),C=A,_=s(1656),w=(0,_.A)(C,p,m,!1,null,"0a7d4e20",null),y=w.exports,x=function(){var t=this,e=t._self._c;t._self._setupProxy;return t.message.options?.sanitizeValue?e("span",{staticClass:"value",style:{fontSize:t.getFontSizeVh(t.chatSettingsFontSize,t.message.options?.fontSize)}},[t._v(t._s(t.value))]):e("span",{staticClass:"value",style:{fontSize:t.getFontSizeVh(t.chatSettingsFontSize,t.message.options?.fontSize)},domProps:{innerHTML:t._s(t.value)}})},I=[],$=n.Ay.extend({name:"ChatMessageValue",props:{message:{type:Object,required:!0}},data:()=>({}),methods:{getFontSizeVh:f},computed:{value(){let t=this.message.value;return t=S(this.message.value),t},chatSettingsFontSize(){return this.$store.state.chatSettings.fontSize}}}),b=$,O=(0,_.A)(b,x,I,!1,null,"38ec8f4d",null),E=O.exports,T=n.Ay.extend({name:"ChatMessage",components:{ChatMessageTitle:y,ChatMessageValue:E},props:{message:{type:Object,required:!0}},data:()=>({}),computed:{smallFont(){return"small"===this.message.options?.fontSize}}}),M=T,N=(0,_.A)(M,g,d,!1,null,"5ac944c3",null),k=N.exports,z=n.Ay.extend({name:"ChatMessages",components:{ChatMessage:k},data:()=>({manualScroll:!1}),created(){window.addEventListener("keydown",this.handleKeyDownEvent)},destroyed(){window.removeEventListener("keydown",this.handleKeyDownEvent)},mounted(){this.scrollToBottom()},methods:{handleKeyDownEvent(t){this.chatInputActive&&("PageUp"===t.key?this.onScroll("up"):"PageDown"===t.key&&this.onScroll("down"))},onScroll(t){var e=document.getElementById("messages");e.scrollTop-="up"===t?240:-240;const s=e.scrollHeight-e.offsetHeight+2;this.manualScroll=e.scrollTop!==s},scrollToBottom(){var t=document.getElementById("messages");t.scrollTop=t.scrollHeight,this.manualScroll=!1}},computed:{messages(){const t=this.$store.state.messages;return t.filter((t=>void 0!==this.chatSettings.categoriesShown.find((e=>e===t.options?.category))))},chatInputActive(){return this.$store.state.chat.inputActive},chatCursorActive(){return this.$store.state.chat.cursorActive},chatSettings(){return this.$store.state.chatSettings}},watch:{messages(){this.manualScroll||setTimeout(this.scrollToBottom,40)},chatInputActive(){setTimeout(this.scrollToBottom,40)}}}),P=z,D=(0,_.A)(P,u,l,!1,null,"21f36f14",null),L=D.exports,H=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticClass:"input-container",class:{cursorActive:t.chatCursorActive}},[e("input",{directives:[{name:"model",rawName:"v-model",value:t.value,expression:"value"}],ref:"inputRef",staticClass:"input",attrs:{type:"text"},domProps:{value:t.value},on:{focus:function(e){t.focused=!0},blur:function(e){t.focused=!1},input:function(e){e.target.composing||(t.value=e.target.value)}}})])},F=[];const V="setChatShowing",j="setChatHidden",G="setChatInputActive",R="setChatCursorActive",U="setChatShowSuggestions",K="sendMessage",B="setInputValue",W="addSuggestions",q="removeSuggestion";var J=n.Ay.extend({name:"PlayerInput",data:()=>({value:"",focused:!1,previousMessageIndex:-1,focusInputTimer:null}),created(){window.addEventListener("keydown",this.handleKeyDownEvent)},destroyed(){window.removeEventListener("keydown",this.handleKeyDownEvent),clearInterval(this.focusInputTimer)},mounted(){this.resetFocusInputTimer()},methods:{handleKeyDownEvent(t){this.focused&&("Enter"===t.key?this.sendMessage():"ArrowUp"===t.key?this.setSentMessage("up"):"ArrowDown"===t.key?this.setSentMessage("down"):"Tab"===t.key&&this.autocompleteCommand())},sendMessage(){this.$store.dispatch(K,this.value)},resetFocusInputTimer(){this.focusInputTimer=setInterval((()=>{this.focusInput()}),100)},focusInput(){this.$nextTick((()=>{const t=this.$refs.inputRef;setTimeout((()=>{t.focus()}))}))},setSentMessage(t){var e=this.sentMessages.length;if(-1===this.previousMessageIndex){if("up"!==t)return;this.previousMessageIndex=e-1}else"up"===t?this.previousMessageIndex-=1:"down"===t&&(this.previousMessageIndex+=1);this.previousMessageIndex>=e?this.previousMessageIndex-=1:this.previousMessageIndex<0&&(this.previousMessageIndex=0),this.$nextTick((()=>{const t=this.$refs.inputRef;setTimeout((()=>{const e=t.value.length;t.setSelectionRange(e,e)}))})),this.value=this.sentMessages[this.previousMessageIndex]},autocompleteCommand(){if(this.filteredSuggestions.length>0&&this.chatShowSuggestions){const t=this.filteredSuggestions[0],e=this.value.split(" ")[0];this.value=this.value.replace(e,`/${t.name}`)}}},computed:{playerId(){return this.$store.state.player.id},chatShowSuggestions(){return this.$store.state.chat.showSuggestions},filteredSuggestions(){return this.$store.getters.filteredSuggestions},inputArgIndex(){return this.$store.state.input.argIndex},inputValue(){return this.$store.state.input.value},sentMessages(){return this.$store.state.sentMessages},chatCursorActive(){return this.$store.state.chat.cursorActive},chatInputActive(){return this.$store.state.chat.inputActive}},watch:{value(){this.$store.dispatch(B,this.value);const t=this.value?.startsWith("/");t&&!this.chatShowSuggestions?this.$store.dispatch(U,!0):!t&&this.chatShowSuggestions&&this.$store.dispatch(U,!1)},inputValue(){this.value=this.inputValue},chatCursorActive(){this.chatCursorActive?clearInterval(this.focusInputTimer):this.resetFocusInputTimer()}}}),Q=J,X=(0,_.A)(Q,H,F,!1,null,"09ce8b34",null),Y=X.exports,Z=function(){var t=this,e=t._self._c;t._self._setupProxy;return t.filteredSuggestions?.length>0?e("div",{staticClass:"suggestions-container"},t._l(t.filteredSuggestions,(function(t,s){return e("ChatSuggestion",{key:s,attrs:{suggestion:t,index:s}})})),1):t._e()},tt=[],et=function(){var t=this,e=t._self._c;t._self._setupProxy;return e("div",{staticClass:"suggestion-container"},[e("div",{staticClass:"main-container"},[e("span",{staticClass:"name"},[t._v(" /"),e("span",{staticClass:"suggestion",class:{selected:0===t.inputArgIndex,cursorActive:0===t.inputArgIndex&&t.chatCursorActive}},[t._v(t._s(t.suggestion.name))])]),e("div",{staticClass:"params-container"},t._l(t.suggestion.params,(function(s,n){return e("span",{key:n,staticClass:"arg",class:{selected:t.inputArgIndex===n+1}},[t._v(t._s(s.name))])})),0)]),t.inputArgHelp?e("span",{staticClass:"param-help"},[t._v(t._s(t.inputArgHelp))]):t._e(),t.suggestion.help?e("span",{staticClass:"help"},[t._v(t._s(t.suggestion.help))]):t._e()])},st=[],nt=n.Ay.extend({name:"ChatSuggestion",props:{suggestion:{type:Object,required:!0},index:{type:Number,required:!0}},computed:{inputArgHelp(){const t=this.suggestion.params;if(this.inputArgIndex>0&&t?.length>0){const e=this.inputArgIndex-1,s=t[e]?.help;return s}},inputArgIndex(){return this.$store.state.input.argIndex},chatCursorActive(){return this.$store.state.chat.cursorActive}}}),at=nt,it=(0,_.A)(at,et,st,!1,null,"21d6b7f4",null),ot=it.exports;var rt=n.Ay.extend({name:"ChatSuggestions",components:{ChatSuggestion:ot},computed:{inputValue(){return this.$store.state.input.value},inputArgIndex(){return this.$store.state.input.argIndex},inputTabIndex(){return this.$store.state.input.tabIndex},chatShowSuggestions(){return this.$store.state.chat.showSuggestions},filteredSuggestions(){return this.$store.getters.filteredSuggestions}}}),ct=rt,ht=(0,_.A)(ct,Z,tt,!1,null,"17e7621b",null),ut=ht.exports,lt=s(586),gt=s(1689),dt=s(8834),pt=s(4384),mt=s(1526),vt=s(6278),ft=s(3970),St=s(9456),At=s(8412),Ct=s(1863),_t=s(7410),wt=function(){var t=this,e=t._self._c;t._self._setupProxy;return e(ft.A,{attrs:{"max-width":"400px",dark:""},scopedSlots:t._u([{key:"activator",fn:function({on:s,attrs:n}){return[e(lt.A,t._g(t._b({attrs:{text:"",icon:"",color:"red lighten-2"}},"v-btn",n,!1),s),[e(St.A,[t._v("mdi-cog")])],1)]}}]),model:{value:t.dialog,callback:function(e){t.dialog=e},expression:"dialog"}},[e(gt.A,[e(dt.ri,[e("span",{staticClass:"text-h5"},[t._v("Chat settings")])]),e(dt.OQ,[e(vt.A,[e(At.A,["user"!==t.player.group?e(mt.A,{attrs:{cols:"4"}},[e(pt.A,{attrs:{label:"System",color:"white",value:"system","hide-details":""},model:{value:t.form.categoriesShown,callback:function(e){t.$set(t.form,"categoriesShown",e)},expression:"form.categoriesShown"}})],1):t._e(),"user"!==t.player.group?e(mt.A,{attrs:{cols:"4"}},[e(pt.A,{attrs:{label:"Staff",color:"green",value:"staff","hide-details":""},model:{value:t.form.categoriesShown,callback:function(e){t.$set(t.form,"categoriesShown",e)},expression:"form.categoriesShown"}})],1):t._e(),"user"!==t.player.group?e(mt.A,{attrs:{cols:"4"}},[e(pt.A,{attrs:{label:"Connection",color:"black",value:"connection","hide-details":""},model:{value:t.form.categoriesShown,callback:function(e){t.$set(t.form,"categoriesShown",e)},expression:"form.categoriesShown"}})],1):t._e(),"user"!==t.player.group?e(mt.A,{attrs:{cols:"4"}},[e(pt.A,{attrs:{label:"Death",color:"red",value:"death","hide-details":""},model:{value:t.form.categoriesShown,callback:function(e){t.$set(t.form,"categoriesShown",e)},expression:"form.categoriesShown"}})],1):t._e(),e(mt.A,{attrs:{cols:"4"}},[e(pt.A,{attrs:{label:"Job",color:"orange",value:"job","hide-details":""},model:{value:t.form.categoriesShown,callback:function(e){t.$set(t.form,"categoriesShown",e)},expression:"form.categoriesShown"}})],1),e(mt.A,{attrs:{cols:"4"}},[e(pt.A,{attrs:{label:"Race",color:"info",value:"race","hide-details":""},model:{value:t.form.categoriesShown,callback:function(e){t.$set(t.form,"categoriesShown",e)},expression:"form.categoriesShown"}})],1),e(mt.A,{attrs:{cols:"12"}},[e(Ct.A,{attrs:{label:"Font size",max:"5",min:"-5"},model:{value:t.form.fontSize,callback:function(e){t.$set(t.form,"fontSize",e)},expression:"form.fontSize"}})],1),e(mt.A,{attrs:{cols:"12"}},[e(Ct.A,{attrs:{label:"Chat width",max:"32",min:"16"},model:{value:t.form.width,callback:function(e){t.$set(t.form,"width",e)},expression:"form.width"}})],1)],1)],1)],1),e(dt.SL,[e(_t.A),e(lt.A,{attrs:{color:"blue darken-1",text:""},on:{click:function(e){return t.handleClose()}}},[t._v(" Close ")]),e(lt.A,{attrs:{color:"blue darken-1",text:""},on:{click:function(e){return t.handleSave()}}},[t._v(" Save ")])],1)],1)],1)},yt=[];const xt="setPlayer",It="setChat",$t="setChatSettings",bt="addMessage",Ot="setMessages",Et="addSentMessage",Tt="setSuggestions",Mt="setInput";var Nt=n.Ay.extend({name:"ChatSettings",data:()=>({dialog:!1,form:{categoriesShown:[],fontSize:0,width:24}}),methods:{handleClose(){this.dialog=!1},handleSave(){this.handleClose(),this.$store.commit($t,this.form)}},computed:{chatSettings(){return this.$store.state.chatSettings},player(){return this.$store.state.player}},watch:{dialog(){this.dialog&&(this.form={...this.chatSettings})}}}),kt=Nt,zt=(0,_.A)(kt,wt,yt,!1,null,"532e3371",null),Pt=zt.exports;const Dt={environment:"production",showChatInterval:1e4};var Lt=n.Ay.extend({name:"Chat",components:{ChatMessages:L,PlayerInput:Y,ChatSuggestions:ut,ChatSettings:Pt},data:()=>({showChatTimer:null}),methods:{resetChatTimer(){this.clearChatTimer(),this.$store.dispatch(V,!0),-1!==Dt.showChatInterval&&(this.showChatTimer=setTimeout((()=>{this.$store.dispatch(V,!1)}),Dt.showChatInterval))},clearChatTimer(){-1!==Dt.showChatInterval&&(clearTimeout(this.showChatTimer),this.showChatTimer=!1)},openSettings(){}},computed:{chatShowing(){return this.$store.state.chat.showing},chatHidden(){return this.$store.state.chat.hidden},chatInputActive(){return this.$store.state.chat.inputActive},chatCursorActive(){return this.$store.state.chat.cursorActive},chatShowSuggestions(){return this.$store.state.chat.showSuggestions},messages(){return this.$store.state.messages},chatSettingsCategories(){return this.$store.state.chatSettings.categoriesShown},chatSettingsWidth(){return this.$store.state.chatSettings.width}},watch:{chatInputActive(){this.chatInputActive?this.clearChatTimer():this.resetChatTimer()},messages(){const t=this.messages[this.messages.length-1];this.chatSettingsCategories.includes(t.options.category)&&(this.chatInputActive||this.resetChatTimer())}}}),Ht=Lt,Ft=(0,_.A)(Ht,c,h,!1,null,"56d12976",null),Vt=Ft.exports,jt=s(4199);const Gt="http://core_chat";var Rt=function(){var t=this,e=t._self._c;t._self._setupProxy;return t.chatInputActive?e("div",{staticClass:"chat-helper-container",class:{cursorActive:t.chatCursorActive}},[t._m(0)]):t._e()},Ut=[function(){var t=this,e=t._self._c;t._self._setupProxy;return e("span",[t._v("Press "),e("span",{staticClass:"button-color"},[t._v("F2")]),t._v(" to enable cursor and view settings ")])}],Kt=n.Ay.extend({name:"ChatHelper",computed:{chatInputActive(){return this.$store.state.chat.inputActive},chatCursorActive(){return this.$store.state.chat.cursorActive}}}),Bt=Kt,Wt=(0,_.A)(Bt,Rt,Ut,!1,null,"6d9a26ec",null),qt=Wt.exports,Jt=n.Ay.extend({name:"App",components:{Chat:Vt,ChatHelper:qt},mounted(){jt.Ay.post(`${Gt}/chat:loaded`)},created(){"production"===Dt.environment&&window.addEventListener("message",this.handleMessageEvent),window.addEventListener("keydown",this.handleKeyDownEvent)},destroyed(){"production"===Dt.environment&&window.removeEventListener("message",this.handleMessageEvent),window.removeEventListener("keydown",this.handleKeyDownEvent)},methods:{handleMessageEvent(t){const e=t.data,s=e.type,n=e.payload;"ON_INIT"===s?this.ON_INIT(n):"ON_OPEN"===s?this.ON_OPEN_CHAT():"ON_CLOSE"===s?this.ON_CLOSE_CHAT():"ON_ADD_MESSAGE"===s?this.ON_ADD_MESSAGE(n):"ON_ADD_SUGGESTIONS"===s?this.ON_ADD_SUGGESTIONS(n):"ON_REMOVE_SUGGESTION"===s?this.ON_REMOVE_SUGGESTION(n):"ON_CLEAR"===s?this.ON_CLEAR():"ON_HIDE"===s&&this.ON_HIDE(n)},handleKeyDownEvent(t){"Escape"===t.key&&this.chatInputActive?this.ON_CLOSE_CHAT():"t"!==t.key||this.chatInputActive?"F2"===t.key&&this.chatInputActive&&this.ON_ENABLE_CURSOR(!this.chatCursorActive):this.ON_OPEN_CHAT()},fetchLocalSettings(){},ON_INIT(t){t.player&&this.$store.commit(xt,t.player);let e=localStorage.getItem("chatSettings");e&&(e=JSON.parse(e),this.$store.commit($t,e))},ON_OPEN_CHAT(){this.$store.dispatch(G,!0)},ON_CLOSE_CHAT(){this.$store.dispatch(G,!1)},ON_ENABLE_CURSOR(t){this.$store.dispatch(R,t)},ON_ADD_MESSAGE(t){this.$store.commit(bt,t.message)},ON_ADD_SUGGESTIONS(t){this.$store.dispatch(W,t.suggestions)},ON_REMOVE_SUGGESTION(t){this.$store.dispatch(q,t.name)},ON_CLEAR(){this.$store.commit(Ot,[])},ON_HIDE(t){this.$store.dispatch(j,t.chatHidden)}},computed:{chatShowing(){return this.$store.state.chat.showing},chatInputActive(){return this.$store.state.chat.inputActive},chatCursorActive(){return this.$store.state.chat.cursorActive}}}),Qt=Jt,Xt=(0,_.A)(Qt,o,r,!1,null,null,null),Yt=Xt.exports,Zt=(s(8743),s(5353));const te=new Set(["pnote","warn","kick","timeout","afk","ban","spectate","pinfo","slap","revive","explode","drop","commend","commendplus","flag","freeze","slay","crash","setname","search","history","cuff","setjob","setsubjob","hms","bring","goto","sethealth","setarmour"]),ee=t=>(t.startsWith("/")&&(t=t.slice(1)),te.has(t.toLowerCase()));n.Ay.use(Zt.Ay);var se=new Zt.Ay.Store({state:{input:{value:"",argIndex:0},chat:{showing:!1,hidden:!1,inputActive:!1,showSuggestions:!1,isAdminCommand:!1,cursorActive:!1},chatSettings:{width:24,fontSize:0,categoriesShown:["system","staff","job","race","connection","death"]},player:{},messages:[],sentMessages:[],suggestions:[]},getters:{filteredSuggestions(t){const e=t.input.value.split(" ");if(t.chat.showSuggestions&&e.length>0){const s=e[0].slice(1).toLowerCase();if(""===s)return[];let n=t.suggestions.filter((t=>t.name.toLowerCase().startsWith(s)));return t.input.argIndex>0&&n.length>1&&(n=n.filter((t=>t.name.toLowerCase()===s))),n.sort(((t,e)=>t.name.localeCompare(e.name))).slice(0,5)}return t.suggestions}},mutations:{setPlayer(t,e){t.player={...t.player,...e}},setInput(t,e){t.input={...t.input,...e}},setChat(t,e){t.chat={...t.chat,...e}},setMessages(t,e){t.messages=e},setSentMessages(t,e){t.sentMessages=e},setSuggestions(t,e){t.suggestions=e},addMessage(t,e){t.messages.push(e)},addSentMessage(t,e){t.sentMessages.push(e)},setChatSettings(t,e){t.chatSettings={...t.chatSettings,...e},localStorage.setItem("chatSettings",JSON.stringify(e))}},actions:{sendMessage(t,e){if(t.dispatch(G,!1),t.dispatch(U,!1),""===e)return;const s=e.replaceAll('"',"'");"production"===Dt.environment&&jt.Ay.post(`${Gt}/chat:sendMessage`,{value:s}),t.commit(Et,e)},setChatShowing(t,e){t.commit(It,{showing:e})},setChatHidden(t,e){t.commit(It,{hidden:e})},setChatCursorActive(t,e){t.commit(It,{cursorActive:e}),jt.Ay.post(`${Gt}/chat:enableCursor`,{value:e})},setChatInputActive(t,e){e?t.commit(It,{showing:!0,inputActive:!0}):(t.commit(It,{inputActive:!1,showSuggestions:!1,cursorActive:!1}),t.commit(It,{isAdminCommand:!1}),jt.Ay.post(`${Gt}/chat:typingAdminCommand`,{isAdminCommand:!1}),"production"===Dt.environment&&jt.Ay.post(`${Gt}/chat:chatInputNotActive`),window.getSelection()?.empty&&window.getSelection()?.empty())},setChatShowSuggestions(t,e){t.commit(It,{showSuggestions:e})},setInputValue(t,e){if(!e)return;const s=e.split(" "),n=s.length-1;t.commit(Mt,{value:e,argIndex:n});const a=ee(s[0]);this.state.chat.isAdminCommand!==a&&(t.commit(It,{isAdminCommand:a}),jt.Ay.post(`${Gt}/chat:typingAdminCommand`,{isAdminCommand:a}))},addSuggestions(t,e){const s=t.state.suggestions;e.forEach((t=>{const e=s.findIndex((e=>e.name===t.name));if(-1!==e){const n=s[e];n.help||(n.help=t.help),n.params||(n.params=t.params)}else{if(t.name.startsWith("+")||t.name.startsWith("-")||t.name.startsWith("_"))return;s.push(t)}})),t.commit(Tt,[...s])},removeSuggestion(t,e){const s=t.state.suggestions,n=s.findIndex((t=>t.name===e));s.splice(n,1),t.commit(Tt,[...s])}},modules:{}}),ne=s(792);n.Ay.use(ne.A);var ae=new ne.A({});n.Ay.config.productionTip=!1,new n.Ay({store:se,vuetify:ae,render:t=>t(Yt)}).$mount("#app")}},e={};function s(n){var a=e[n];if(void 0!==a)return a.exports;var i=e[n]={exports:{}};return t[n](i,i.exports,s),i.exports}s.m=t,function(){var t=[];s.O=function(e,n,a,i){if(!n){var o=1/0;for(u=0;u<t.length;u++){n=t[u][0],a=t[u][1],i=t[u][2];for(var r=!0,c=0;c<n.length;c++)(!1&i||o>=i)&&Object.keys(s.O).every((function(t){return s.O[t](n[c])}))?n.splice(c--,1):(r=!1,i<o&&(o=i));if(r){t.splice(u--,1);var h=a();void 0!==h&&(e=h)}}return e}i=i||0;for(var u=t.length;u>0&&t[u-1][2]>i;u--)t[u]=t[u-1];t[u]=[n,a,i]}}(),function(){s.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return s.d(e,{a:e}),e}}(),function(){s.d=function(t,e){for(var n in e)s.o(e,n)&&!s.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}}(),function(){s.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"===typeof window)return window}}()}(),function(){s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),function(){s.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}}(),function(){var t={524:0};s.O.j=function(e){return 0===t[e]};var e=function(e,n){var a,i,o=n[0],r=n[1],c=n[2],h=0;if(o.some((function(e){return 0!==t[e]}))){for(a in r)s.o(r,a)&&(s.m[a]=r[a]);if(c)var u=c(s)}for(e&&e(n);h<o.length;h++)i=o[h],s.o(t,i)&&t[i]&&t[i][0](),t[i]=0;return s.O(u)},n=self["webpackChunkchat"]=self["webpackChunkchat"]||[];n.forEach(e.bind(null,0)),n.push=e.bind(null,n.push.bind(n))}();var n=s.O(void 0,[504],(function(){return s(3297)}));n=s.O(n)})();