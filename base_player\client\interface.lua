-- This file contains any event handlers and sync state from other resources which affect base_player

-- Allow players to respawn immediately after being bodybagged
RegisterNetEvent("bodybag:getBodybagged", function()
    timeUntilRespawn = 0
    LocalPlayer.state:set("bodybagged", true, true)
end)

-- Revive from other resources
RegisterNetEvent("base_player:revive", function(options)
    if not options then options = {} end
    options.respawnHealth = options.respawnHealth or math.max(GetEntityMaxHealth(PlayerPedId()), 200)
    if options.adminRevive then
        if not LocalPlayer.state.wounded then
            options.forceRespawn = false
        end
    end
    if options.forceRespawn == nil then
        options.forceRespawn = true
    end

    setPlayerAsWounded(false, options)
end)


-- Stabilisation
local function stabilisationComplete()
    Base.Notification("EMS have stabilised you")

    -- Revive the player if they were knocked out or have minor injuries
    if LocalPlayer.state.woundedSeverity == "knockedout" or LocalPlayer.state.woundedSeverity == "minor" then
        TriggerEvent("base_player:revive", { respawnHealth = 120 })
    end
end

AddStateBagChangeHandler("stabilised", "", function(bagName, key, value)
    if GetPlayerFromStateBagName(bagName) == PlayerId() and value then
        stabilisationComplete()
    end
end)

-- Hospitalisation
hospitaliseTime = 0

RegisterNetEvent("hospitalisePatient", function(stretcherNet)
    Base.Notification("You have been hospitalised")
    LocalPlayer.state:set("hospitalised", true, true)

    hospitaliseTime = HOSPITALISE_TIME

    while hospitaliseTime > 0 and not LocalPlayer.state.dead do
        Wait(1000)
        hospitaliseTime = hospitaliseTime - 1
    end

    -- Cleanup hospitalisation
    LocalPlayer.state:set("hospitalised", nil, true)
    hospitaliseTime = 0
    --local stretcherEnt = NetworkGetEntityFromNetworkId(stretcherNet)
    --NetworkRequestControlOfEntity(stretcherEnt)
    Wait(100)
    --Entity(stretcherEnt).state:set("blockStretcherMove", false, true)

    -- Revive the player if they were not killed during hospitalisation
    if LocalPlayer.state.dead then
        Base.Notification("You died in hospital from your wounds")
        return
    end
    setPlayerAsWounded(false, { respawnHealth = math.max(GetPedMaxHealth(PlayerPedId()), 200) })
end)


-- Logging in while wounded
local severities = {
    ["knockedout"] = "OVRD_KO",
    ["minor"] = "OVRD_MINOR",
    ["severe"] = "OVRD_SEVERE",
    ["dead"] = "OVRD_DEAD",
}
RegisterNetEvent("player:setWoundedSeverity", function(severity)
    Wait(1000) -- this prevents issues where players are set to 0,0,0
    TriggerEvent("playerDamage", {
        fatal = true,
        weaponLabel = "Unknown",
        isMelee = false,
        boneLabel = "Unknown",
        weaponType = severities[severity],
        damage = 100,
    })
end)
