--- ===================================== QUI Alt Menu ========================================== ---

AddVehicleInteractionAction(
    function()
	    return Base.Vehicles.GetClosestVehicle(playerPed, 4.0, true)
    end,

    function() end,

    { text = "Windows", icon = "&#xF05AF;", subMenu = {
        { text = "Windows Up", icon = "&#xF05B1;", action = "window_up" },
        { text = "Windows Down", icon = "&#xF05AE;", action = "window_down" },
    }},
    
    {requires = {Vehicle = true}}
)

--- ============================================================================================= ---


WindowUp = function() setAllWindows(GetVehiclePedIsIn(GetPlayerPed(-1)), true) end
WindowDown = function() setAllWindows(GetVehiclePedIsIn(GetPlayerPed(-1)), false) end

onBaseReady(function()
    QUI.registerMenuAction("window_up", WindowUp)
    QUI.registerMenuAction("window_down", WindowDown)
end)


-- windows
local VEHICLE_WINDOW_INDEX = {
    FRONT_RIGHT = 0,
    FRONT_LEFT  = 1,
    BACK_RIGHT  = 2,
    BACK_LEFT   = 3,
}

function toggleWindow(veh, index)

end

function getWindow(veh, index)

end 

function setWindow(veh, index, toggle)
    if (toggle) then
        RollUpWindow(veh, VEHICLE_WINDOW_INDEX[index])
    else 
        RollDownWindow(veh, VEHICLE_WINDOW_INDEX[index])
    end
end

function setAllWindows(veh, toggle)
    if (toggle) then
        for index, i in pairs(VEHICLE_WINDOW_INDEX) do
            RollUpWindow(veh, i)
        end
    else
        for index, i in pairs(VEHICLE_WINDOW_INDEX) do
            RollDownWindow(veh, i)
        end
    end
end

-- doors
local VEHICLE_DOOR_INDEX = {
	FRONT_LEFT  = 0,
	FRONT_RIGHT = 1,
	BACK_LEFT   = 2,
	BACK_RIGHT  = 3, 
	HOOD        = 4,
	TRUNK       = 5,
}

function toggleDoor(veh, index)
    local open = getDoor(veh, index)
    setDoor(veh, index, not open)
end

function getDoor(veh, index)
    return (GetVehicleDoorAngleRatio(veh, VEHICLE_DOOR_INDEX[index]) > 0.0)
end 

function setDoor(veh, index, toggle)
    if (toggle) then
        SetVehicleDoorOpen(veh, VEHICLE_DOOR_INDEX[index], false, false)
    else
        SetVehicleDoorShut(veh, VEHICLE_DOOR_INDEX[index], false)
    end
end

function toggleAllDoors(veh)
    local toggle = getDoor(veh, 0)
    for index, i in pairs(VEHICLE_DOOR_INDEX) do
        setDoor(veh, index, not toggle)
    end
end