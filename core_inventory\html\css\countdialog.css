@import url('https://fonts.googleapis.com/css2?family=Teko:wght@300&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* Generic */
.dialog-container {
  display: none;
  position: absolute;
  width: 20vh;
  top: 85%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

.dialog-container header {
  font-family: 'Inter', sans-serif;
  font-size: 12;
  color: white;
  background-color: rgba(10, 10, 10, 0.8);
  text-align: center;
  height: 3vh;
  line-height: 3vh;
  margin-bottom: 0.1vh;
}

.dialog-container input {
  font-family: 'Inter', sans-serif;
  width: 100%;
  color: white;
  text-align: center;
  background-color: rgba(10, 10, 10, 0.5);
  border: 0px;
  height: 4vh;
  line-height: 4vh;
  border-bottom: 0.2vh solid rgba(10, 10, 10, 0.9);
}

.dialog-container input:focus {
  outline: none;
}

.dialogButton {
  font-family: 'Inter', sans-serif;
  width: 50%;
  height: 30px;
  background-color: rgba(10, 10, 10, 0.8);
  color: white;
  border: 0px;
}

.dialogButton:hover {
  background-color: rgba(30, 30, 30, 0.8);
}

.dialogButton:focus {
  outline: none;
}

#dialogCancel,
#tagDialogCancel {
  border-left: 1.5px solid rgba(10, 10, 10, 0.9);
  float: right;
}

#dialogExc {
  display: none;
  position: absolute;
  right: 15px;
  top: 38px;
  font-size: 24;
}
