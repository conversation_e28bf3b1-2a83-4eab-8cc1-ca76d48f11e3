{"Weapons": {"Thrown": [{"menuName": "Baseball", "weaponName": "WEAPON_BALL", "data": {"sub": 10}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Fire Extinguisher", "weaponName": "WEAPON_FIREEXTINGUISHER", "data": {"sub": 6}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Flare", "weaponName": "WEAPON_FLARE", "data": {"sub": 9}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Grenade", "weaponName": "WEAPON_GRENADE", "data": {"sub": 0}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "<PERSON>", "weaponName": "WEAPON_PETROLCAN", "data": {"sub": 7}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "<PERSON><PERSON><PERSON>", "weaponName": "WEAPON_MOLOTOV", "data": {"sub": 5}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Pipebomb", "weaponName": "WEAPON_PIPEBOMB", "data": {"sub": 11}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Proximity Mine", "weaponName": "WEAPON_PROXMINE", "data": {"sub": 2}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Smoke Grenade", "weaponName": "WEAPON_SMOKEGRENADE", "data": {"sub": 4}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Snowball", "weaponName": "WEAPON_SNOWBALL", "data": {"sub": 8}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Sticky Bomb", "weaponName": "WEAPON_STICKYBOMB", "data": {"sub": 1}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Tear Gas", "weaponName": "WEAPON_BZGAS", "data": {"sub": 3}, "ammo": true, "weapon": true, "submenu": []}], "Heavy": [{"menuName": "Compact Grenade Launcher", "weaponName": "WEAPON_COMPACTLAUNCHER", "data": {"sub": 8}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Firework Launcher", "weaponName": "WEAPON_FIREWORK", "data": {"sub": 5}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Grenade Launcher", "weaponName": "WEAPON_GRENADELAUNCHER", "data": {"sub": 0}, "ammo": true, "weapon": true, "submenu": [{"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_SMALL"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP"}}]}, {"menuName": "Grenade Launcher (Smoke)", "weaponName": "WEAPON_GRENADELAUNCHER_SMOKE", "data": {"sub": 1}, "ammo": true, "weapon": true, "submenu": [{"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_SMALL"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP"}}]}, {"menuName": "Homing Launcher", "weaponName": "WEAPON_HOMINGLAUNCHER", "data": {"sub": 7}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Minigun", "weaponName": "WEAPON_MINIGUN", "data": {"sub": 4}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Railgun", "weaponName": "WEAPON_RAILGUN", "data": {"sub": 6}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "RPG", "weaponName": "WEAPON_RPG", "data": {"sub": 2}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "<PERSON><PERSON>", "weaponName": "WEAPON_STINGER", "data": {"sub": 3}, "ammo": true, "weapon": true, "submenu": []}], "Snipers": [{"menuName": "Heavy Sniper", "weaponName": "WEAPON_HEAVYSNIPER", "data": {"sub": 1}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Normal Scope", "data": {"action": "COMPONENT_AT_SCOPE_LARGE"}}]}, {"menuName": "Heavy Sniper MK2", "weaponName": "WEAPON_HEAVYSNIPER_MK2", "data": {"sub": 3}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Default Magazine", "data": {"action": "COMPONENT_HEAVYSNIPER_MK2_CLIP_01"}}, {"menuName": "Extended Magazine", "data": {"action": "COMPONENT_HEAVYSNIPER_MK2_CLIP_02"}}, {"menuName": "Incendiary Rounds", "data": {"action": "COMPONENT_HEAVYSNIPER_MK2_CLIP_INCENDIARY"}}, {"menuName": "Armor Piercing Rounds", "data": {"action": "COMPONENT_HEAVYSNIPER_MK2_CLIP_ARMORPIERCING"}}, {"menuName": "Full Metal Jacket Rounds", "data": {"action": "COMPONENT_HEAVYSNIPER_MK2_CLIP_FMJ"}}, {"menuName": "Explosive Rounds", "data": {"action": "COMPONENT_HEAVYSNIPER_MK2_CLIP_EXPLOSIVE"}}, {"menuName": "Normal Scope", "data": {"action": "COMPONENT_AT_SCOPE_LARGE_MK2"}}, {"menuName": "Advanced Scope", "data": {"action": "COMPONENT_AT_SCOPE_MAX"}}, {"menuName": "Night Vision Scope", "data": {"action": "COMPONENT_AT_SCOPE_NV"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_THERMAL"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_SR_SUPP_03"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SR_BARREL_01"}}, {"menuName": "Heavy Barrel", "data": {"action": "COMPONENT_AT_SR_BARREL_02"}}, {"menuName": "Squared Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_08"}}, {"menuName": "Bell-End Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_09"}}]}, {"menuName": "Marksman Rifle", "weaponName": "WEAPON_MARKSMANRIFLE", "data": {"sub": 2}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_MARKSMANR<PERSON>LE_CLIP_02"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP"}}, {"menuName": "<PERSON> Finish", "data": {"action": "COMPONENT_MAR<PERSON>MANR<PERSON>LE_VARMOD_LUXE"}}]}, {"menuName": "Sniper Rifle", "weaponName": "WEAPON_SNIPERRIFLE", "data": {"sub": 0}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Advanced Scope", "data": {"action": "COMPONENT_AT_SCOPE_MAX"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP_02"}}, {"menuName": "<PERSON><PERSON>rip <PERSON>ish", "data": {"action": "COMPONENT_SNIPERRIFLE_VARMOD_LUXE"}}]}], "Shotguns": [{"menuName": "Assault Shotgun", "weaponName": "WEAPON_ASSAULTSHOTGUN", "data": {"sub": 3}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_ASSAULTSHOTGUN_CLIP_02"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}]}, {"menuName": "Bullpup Shotgun", "weaponName": "WEAPON_BULLPUPSHOTGUN", "data": {"sub": 2}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_BULLPUPRIFLE_CLIP_02"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_SMALL"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP"}}, {"menuName": "Gilded Gun Metal Finish", "data": {"action": "COMPONENT_BULLPUPRIFLE_VARMOD_LOW"}}]}, {"menuName": "Double Barrel Shotgun", "weaponName": "WEAPON_DBSHOTGUN", "data": {"sub": 6}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Heavy Shotgun", "weaponName": "WEAPON_HEAVYSHOTGUN", "data": {"sub": 5}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_HEAVYSHOTGUN_CLIP_02"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP_02"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP"}}]}, {"menuName": "Musket", "weaponName": "WEAPON_MUSKET", "data": {"sub": 4}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Pump Shotgun", "weaponName": "WEAPON_PUMPSHOTGUN", "data": {"sub": 0}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_SR_SUPP"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "<PERSON> Finish", "data": {"action": "COMPONENT_PUMPSHOTGUN_VARMOD_LOWRIDER"}}]}, {"menuName": "<PERSON><PERSON><PERSON> Shotgun", "weaponName": "WEAPON_SAWNOFFSHOTGUN", "data": {"sub": 1}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Gilded Gun Metal Finish", "data": {"action": "COMPONENT_SAWNOFFSHOTGUN_VARMOD_LUXE"}}]}, {"menuName": "Sweeper Shotgun", "weaponName": "WEAPON_AUTOSHOTGUN", "data": {"sub": 7}, "ammo": true, "weapon": true, "submenu": []}], "Assault": [{"menuName": "Advanced Rifle", "weaponName": "WEAPON_ADVANCEDRIFLE", "data": {"sub": 2}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_ADVANCEDRIFLE_CLIP_02"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_SMALL"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "Gilded Gun Metal Finish", "data": {"action": "COMPONENT_ADVANCEDRIFLE_VARMOD_LUXE"}}]}, {"menuName": "Assault Rifle", "weaponName": "WEAPON_ASSAULTRIFLE", "data": {"sub": 0}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_ASSAULTRIFLE_CLIP_02"}}, {"menuName": "Drum Magazine", "data": {"action": "COMPONENT_ASSAULTRIFLE_CLIP_03"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_MACRO"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP_02"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "<PERSON> Finish", "data": {"action": "COMPONENT_ASSAULTRIFLE_VARMOD_LUXE"}}]}, {"menuName": "Assault Rifle MK2", "weaponName": "WEAPON_ASSAULTRIFLE_MK2", "data": {"sub": 6}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Default Magazine", "data": {"action": "COMPONENT_ASSAULTRIFLE_MK2_CLIP_01"}}, {"menuName": "Extended Magazine", "data": {"action": "COMPONENT_ASSAULTRIFLE_MK2_CLIP_02"}}, {"menuName": "Tracer Rounds", "data": {"action": "COMPONENT_ASSAULTRIFLE_MK2_CLIP_TRACER"}}, {"menuName": "Incendiary Rounds", "data": {"action": "COMPONENT_ASSAULTRIFLE_MK2_CLIP_INCENDIARY"}}, {"menuName": "Armor Piercing Rounds", "data": {"action": "COMPONENT_ASSAULTRIFLE_MK2_CLIP_ARMORPIERCING"}}, {"menuName": "Full Metal Jacket Rounds", "data": {"action": "COMPONENT_ASSAULTRIFLE_MK2_CLIP_FMJ"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP_02"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "Holographic Sight", "data": {"action": "COMPONENT_AT_SIGHTS"}}, {"menuName": "<PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_MACRO_MK2"}}, {"menuName": "<PERSON> Scope", "data": {"action": "COMPONENT_AT_SCOPE_MEDIUM_MK2"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP_02"}}, {"menuName": "Flat Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_01"}}, {"menuName": "Tactical Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_02"}}, {"menuName": "Fat-End Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_03"}}, {"menuName": "Precision Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_04"}}, {"menuName": "Heavy Duty Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_05"}}, {"menuName": "Slanted Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_06"}}, {"menuName": "Split-End Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_07"}}, {"menuName": "Standard Barrel", "data": {"action": "COMPONENT_AT_AR_BARREL_01"}}, {"menuName": "Heavy Barrel", "data": {"action": "COMPONENT_AT_AR_BARREL_02"}}]}, {"menuName": "Bullpup Rifle", "weaponName": "WEAPON_BULLPUPRIFLE", "data": {"sub": 4}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP_02"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP"}}]}, {"menuName": "Carbine Rifle", "weaponName": "WEAPON_CARBINERIFLE", "data": {"sub": 1}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_CARBINERIFLE_CLIP_02"}}, {"menuName": "Box Magazine", "data": {"action": "COMPONENT_CARBINERIFLE_CLIP_03"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_MEDIUM"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "Rail Cover", "data": {"action": "COMPONENT_AT_RAILCOVER_01"}}, {"menuName": "<PERSON> Finish", "data": {"action": "COMPONENT_CARBINERIFLE_VARMOD_LUXE"}}]}, {"menuName": "Carbine Rifle MK2", "weaponName": "WEAPON_CARBINERIFLE_MK2", "data": {"sub": 7}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Default Magazine", "data": {"action": "COMPONENT_CARBINERIFLE_MK2_CLIP_01"}}, {"menuName": "Extended Magazine", "data": {"action": "COMPONENT_CARBINERIFLE_MK2_CLIP_02"}}, {"menuName": "Tracer Rounds", "data": {"action": "COMPONENT_CARBINERIFLE_MK2_CLIP_TRACER"}}, {"menuName": "Incendiary Rounds", "data": {"action": "COMPONENT_CARBINERIFLE_MK2_CLIP_INCENDIARY"}}, {"menuName": "Armor Piercing Rounds", "data": {"action": "COMPONENT_CARBINERIFLE_MK2_CLIP_ARMORPIERCING"}}, {"menuName": "Full Metal Jacket Rounds", "data": {"action": "COMPONENT_CARBINERIFLE_MK2_CLIP_FMJ"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP_02"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "Holographic Sight", "data": {"action": "COMPONENT_AT_SIGHTS"}}, {"menuName": "<PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_MACRO_MK2"}}, {"menuName": "<PERSON> Scope", "data": {"action": "COMPONENT_AT_SCOPE_MEDIUM_MK2"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP"}}, {"menuName": "Flat Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_01"}}, {"menuName": "Tactical Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_02"}}, {"menuName": "Fat-End Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_03"}}, {"menuName": "Precision Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_04"}}, {"menuName": "Heavy Duty Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_05"}}, {"menuName": "Slanted Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_06"}}, {"menuName": "Split-End Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_07"}}, {"menuName": "Normal Barrel", "data": {"action": "COMPONENT_AT_CR_BARREL_01"}}, {"menuName": "Heavy Barrel", "data": {"action": "COMPONENT_AT_CR_BARREL_02"}}]}, {"menuName": "Compact Rifle", "weaponName": "WEAPON_COMPACTRIFLE", "data": {"sub": 5}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_COMPACTRIFLE_CLIP_02"}}, {"menuName": "Drum Magazine", "data": {"action": "COMPONENT_COMPACTRIFLE_CLIP_03"}}]}, {"menuName": "Special Carbine", "weaponName": "WEAPON_SPECIALCARBINE", "data": {"sub": 3}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_SPECIALCARBINE_CLIP_02"}}, {"menuName": "Beta C-Magazine", "data": {"action": "COMPONENT_SPECIALCARBINE_CLIP_03"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_MEDIUM"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP_02"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP"}}]}, {"menuName": "Special Carbine MK2", "weaponName": "WEAPON_SPECIALCARBINE_MK2", "data": {"sub": 7}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Default Magazine", "data": {"action": "COMPONENT_SPECIALCARBINE_MK2_CLIP_01"}}, {"menuName": "Extended Magazine", "data": {"action": "COMPONENT_SPECIALCARBINE_MK2_CLIP_02"}}, {"menuName": "Tracer Rounds", "data": {"action": "COMPONENT_SPECIALCARBINE_MK2_CLIP_TRACER"}}, {"menuName": "Incendiary Rounds", "data": {"action": "COMPONENT_SPECIALCARBINE_MK2_CLIP_INCENDIARY"}}, {"menuName": "Armor Piercing Rounds", "data": {"action": "COMPONENT_SPECIALCARBINE_MK2_CLIP_ARMORPIERCING"}}, {"menuName": "Full Metal Jacket Rounds", "data": {"action": "COMPONENT_SPECIALCARBINE_MK2_CLIP_FMJ"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP_02"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "Holographic Sight", "data": {"action": "COMPONENT_AT_SIGHTS"}}, {"menuName": "<PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_MACRO_MK2"}}, {"menuName": "<PERSON> Scope", "data": {"action": "COMPONENT_AT_SCOPE_MEDIUM_MK2"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP_02"}}, {"menuName": "Flat Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_01"}}, {"menuName": "Tactical Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_02"}}, {"menuName": "Fat-End Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_03"}}, {"menuName": "Precision Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_04"}}, {"menuName": "Heavy Duty Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_05"}}, {"menuName": "Slanted Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_06"}}, {"menuName": "Split-End Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_07"}}, {"menuName": "Normal Barrel", "data": {"action": "COMPONENT_AT_CR_BARREL_01"}}, {"menuName": "Heavy Barrel", "data": {"action": "COMPONENT_AT_CR_BARREL_02"}}]}], "Submachine": [{"menuName": "Assault SMG", "weaponName": "WEAPON_ASSAULTSMG", "data": {"sub": 2}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_ASSAULTSMG_CLIP_02"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_MACRO"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP_02"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "<PERSON> Finish", "data": {"action": "COMPONENT_ASSAULTSMG_VARMOD_LOWRIDER"}}]}, {"menuName": "Combat MG", "weaponName": "WEAPON_COMBATMG", "data": {"sub": 4}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_COMBATMG_CLIP_02"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_MEDIUM"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP"}}]}, {"menuName": "Combat PDW", "weaponName": "WEAPON_COMBATPDW", "data": {"sub": 5}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_COMBATPDW_CLIP_02"}}, {"menuName": "Drum Magazine", "data": {"action": "COMPONENT_COMBATPDW_CLIP_03"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_SMALL"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_AR_AFGRIP"}}]}, {"menuName": "<PERSON><PERSON> Sweeper", "weaponName": "WEAPON_GUSENBERG", "data": {"sub": 6}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_GUSENBERG_CLIP_02"}}]}, {"menuName": "Machine Pistol", "weaponName": "WEAPON_MACHINEPISTOL", "data": {"sub": 7}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_MACHINEPISTOL_CLIP_02"}}, {"menuName": "Drum Magazine", "data": {"action": "COMPONENT_MACHINEPISTOL_CLIP_03"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_PI_SUPP"}}]}, {"menuName": "MG", "weaponName": "WEAPON_MG", "data": {"sub": 3}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_MG_CLIP_02"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_SMALL_02"}}]}, {"menuName": "Micro SMG", "weaponName": "WEAPON_MICROSMG", "data": {"sub": 0}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_MICROSMG_CLIP_02"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_MACRO"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP_02"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_PI_FLSH"}}, {"menuName": "<PERSON> Finish", "data": {"action": "COMPONENT_MICROSMG_VARMOD_LUXE"}}]}, {"menuName": "Mini SMG", "weaponName": "WEAPON_MINISMG", "data": {"sub": 8}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_MINISMG_CLIP_02"}}]}, {"menuName": "PP91", "weaponName": "WEAPON_PP91", "data": {"sub": 1}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_PP91_CLIP_02"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_PI_SUPP"}}]}, {"menuName": "SMG", "weaponName": "WEAPON_SMG", "data": {"sub": 1}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_SMG_CLIP_02"}}, {"menuName": "Drum Magazine", "data": {"action": "COMPONENT_SMG_CLIP_03"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_MACRO_02"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_PI_SUPP"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "<PERSON> Finish", "data": {"action": "COMPONENT_SMG_VARMOD_LUXE"}}]}, {"menuName": "SMG MK2", "weaponName": "WEAPON_SMG_MK2", "data": {"sub": 9}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Default Magazine", "data": {"action": "COMPONENT_SMG_MK2_CLIP_01"}}, {"menuName": "Extended Magazine", "data": {"action": "COMPONENT_SMG_MK2_CLIP_02"}}, {"menuName": "Tracer Rounds", "data": {"action": "COMPONENT_SMG_MK2_CLIP_TRACER"}}, {"menuName": "Incendiary Rounds", "data": {"action": "COMPONENT_SMG_MK2_CLIP_INCENDIARY"}}, {"menuName": "Hollow Point Rounds", "data": {"action": "COMPONENT_SMG_MK2_CLIP_HOLLOWPOINT"}}, {"menuName": "Full Metal Jacket Rounds", "data": {"action": "COMPONENT_SMG_MK2_CLIP_FMJ"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_AR_FLSH"}}, {"menuName": "Holographic Sight", "data": {"action": "COMPONENT_AT_SIGHTS_SMG"}}, {"menuName": "<PERSON>", "data": {"action": "COMPONENT_AT_SCOPE_MACRO_02_SMG_MK2"}}, {"menuName": "Medium Scope", "data": {"action": "COMPONENT_AT_SCOPE_SMALL_SMG_MK2"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_PI_SUPP"}}, {"menuName": "Flat Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_01"}}, {"menuName": "Tactical Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_02"}}, {"menuName": "Fat-End Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_03"}}, {"menuName": "Precision Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_04"}}, {"menuName": "Heavy Duty Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_05"}}, {"menuName": "Slanted Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_06"}}, {"menuName": "Split-End Muzzle Brake", "data": {"action": "COMPONENT_AT_MUZZLE_07"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "COMPONENT_AT_SB_BARREL_01"}}, {"menuName": "Heavy Barrel", "data": {"action": "COMPONENT_AT_SB_BARREL_02"}}]}], "Handguns": [{"menuName": "AP Pistol", "weaponName": "WEAPON_APPISTOL", "data": {"sub": 2}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_APPISTOL_CLIP_02"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_PI_SUPP"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_PI_FLSH"}}, {"menuName": "Gilded Gun Metal Finish", "data": {"action": "COMPONENT_APPISTOL_VARMOD_LUXE"}}]}, {"menuName": "Combat Pistol", "weaponName": "WEAPON_COMBATPISTOL", "data": {"sub": 1}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_COMBATPISTOL_CLIP_02"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_PI_SUPP"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_PI_FLSH"}}, {"menuName": "<PERSON> Finish", "data": {"action": "COMPONENT_COMBATPISTOL_VARMOD_LOWRIDER"}}]}, {"menuName": "Flare Gun", "weaponName": "WEAPON_FLAREGUN", "data": {"sub": 8}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Heavy Pistol", "weaponName": "WEAPON_HEAVYPISTOL", "data": {"sub": 5}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_HEAVYPISTOL_CLIP_02"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_PI_FLSH"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_PI_SUPP"}}, {"menuName": "<PERSON><PERSON>rip <PERSON>ish", "data": {"action": "COMPONENT_HEAVYPISTOL_VARMOD_LUXE"}}]}, {"menuName": "Heavy Revolver", "weaponName": "WEAPON_REVOLVER", "data": {"sub": 10}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Marks<PERSON>", "weaponName": "WEAPON_MARKSMANPISTOL", "data": {"sub": 9}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Variation 1", "data": {"action": "COMPONENT_REVOLVER_VARMOD_BOSS"}}, {"menuName": "Variation 2", "data": {"action": "COMPONENT_REVOLVER_VARMOD_GOON"}}]}, {"menuName": "Pistol", "weaponName": "WEAPON_PISTOL", "data": {"sub": 0}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_PISTOL_CLIP_02"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_PI_SUPP_02"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_PI_FLSH"}}, {"menuName": "<PERSON> Finish", "data": {"action": "COMPONENT_PISTOL_VARMOD_LUXE"}}]}, {"menuName": "Pistol MK2", "weaponName": "WEAPON_PISTOL_MK2", "data": {"sub": 10}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Default Magazine", "data": {"action": "COMPONENT_PISTOL_MK2_CLIP_01"}}, {"menuName": "Extended Magazine", "data": {"action": "COMPONENT_PISTOL_MK2_CLIP_02"}}, {"menuName": "Tracer Rounds", "data": {"action": "COMPONENT_PISTOL_MK2_CLIP_TRACER"}}, {"menuName": "Incendiary Rounds", "data": {"action": "COMPONENT_PISTOL_MK2_CLIP_INCENDIARY"}}, {"menuName": "Hollow Point Rounds", "data": {"action": "COMPONENT_PISTOL_MK2_CLIP_HOLLOWPOINT"}}, {"menuName": "Full Metal Jacket Rounds", "data": {"action": "COMPONENT_PISTOL_MK2_CLIP_FMJ"}}, {"menuName": "Red Dot Sight", "data": {"action": "COMPONENT_AT_PI_RAIL"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_PI_FLSH_02"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_PI_SUPP_02"}}, {"menuName": "Compensator", "data": {"action": "COMPONENT_AT_PI_COMP"}}]}, {"menuName": "Pistol .50", "weaponName": "WEAPON_PISTOL50", "data": {"sub": 3}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_PISTOL50_CLIP_02"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_AR_SUPP_02"}}, {"menuName": "Flashlight", "data": {"action": "COMPONENT_AT_PI_FLSH"}}, {"menuName": "Platinum Pearl Deluxe Finish", "data": {"action": "COMPONENT_PISTOL50_VARMOD_LUXE"}}]}, {"menuName": "SNS Pistol", "weaponName": "WEAPON_SNSPISTOL", "data": {"sub": 4}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_SNSPISTOL_CLIP_02"}}, {"menuName": "<PERSON><PERSON>rip <PERSON>ish", "data": {"action": "COMPONENT_SNSPISTOL_VARMOD_LOWRIDER"}}]}, {"menuName": "Stun Gun", "weaponName": "WEAPON_STUNGUN", "data": {"sub": 7}, "ammo": true, "weapon": true, "submenu": []}, {"menuName": "Vintage Pistol", "weaponName": "WEAPON_VINTAGEPISTOL", "data": {"sub": 6}, "ammo": true, "weapon": true, "submenu": [{"menuName": "Extended Magazine", "data": {"action": "COMPONENT_VINTAGEPISTOL_CLIP_02"}}, {"menuName": "Suppressor", "data": {"action": "COMPONENT_AT_PI_SUPP"}}]}], "Melee": [{"menuName": "<PERSON><PERSON>", "weaponName": "WEAPON_DAGGER", "data": {"sub": 8}, "weapon": true, "submenu": []}, {"menuName": "Baseball Bat", "weaponName": "WEAPON_BAT", "data": {"sub": 4}, "weapon": true, "submenu": []}, {"menuName": "Battle Axe", "weaponName": "WEAPON_BATTLEAXE", "data": {"sub": 15}, "weapon": true, "submenu": []}, {"menuName": "<PERSON><PERSON>", "weaponName": "WEAPON_BOTTLE", "data": {"sub": 7}, "weapon": true, "submenu": []}, {"menuName": "<PERSON> Knuckles", "weaponName": "WEAPON_KNUCKLE", "data": {"sub": 1}, "weapon": true, "submenu": [{"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "COMPONENT_KNUCKLE_VARMOD_BASE"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COMPONENT_KNUCKLE_VARMOD_PIMP"}}, {"menuName": "Ballas", "data": {"action": "COMPONENT_KNUCKLE_VARMOD_BALLAS"}}, {"menuName": "Dollars", "data": {"action": "COMPONENT_KNUCKLE_VARMOD_DOLLAR"}}, {"menuName": "Diamond", "data": {"action": "COMPONENT_KNUCKLE_VARMOD_DIAMOND"}}, {"menuName": "Hate", "data": {"action": "COMPONENT_KNUCKLE_VARMOD_HATE"}}, {"menuName": "Love", "data": {"action": "COMPONENT_KNUCKLE_VARMOD_LOVE"}}, {"menuName": "Player", "data": {"action": "COMPONENT_KNUC<PERSON><PERSON>_VARMOD_PLAYER"}}, {"menuName": "King", "data": {"action": "COMPONENT_KNUCKLE_VARMOD_KING"}}, {"menuName": "Vagos", "data": {"action": "COMPONENT_KNUCKLE_VARMOD_VAGOS"}}]}, {"menuName": "Crowbar", "weaponName": "WEAPON_CROWBAR", "data": {"sub": 6}, "weapon": true, "submenu": []}, {"menuName": "Flashlight", "weaponName": "WEAPON_FLASHLIGHT", "data": {"sub": 11}, "weapon": true, "submenu": []}, {"menuName": "Golf Club", "weaponName": "WEAPON_GOLFCLUB", "data": {"sub": 5}, "weapon": true, "submenu": []}, {"menuName": "Hammer", "weaponName": "WEAPON_HAMMER", "data": {"sub": 3}, "weapon": true, "submenu": []}, {"menuName": "Hatchet", "weaponName": "WEAPON_HATCHET", "data": {"sub": 9}, "weapon": true, "submenu": []}, {"menuName": "Knife", "weaponName": "WEAPON_KNIFE", "data": {"sub": 0}, "weapon": true, "submenu": []}, {"menuName": "Machete", "weaponName": "WEAPON_MACHETE", "data": {"sub": 10}, "weapon": true, "submenu": []}, {"menuName": "Nightstick", "weaponName": "WEAPON_NIGHTSTICK", "data": {"sub": 2}, "weapon": true, "submenu": []}, {"menuName": "<PERSON><PERSON>", "weaponName": "WEAPON_WRENCH", "data": {"sub": 14}, "weapon": true, "submenu": []}, {"menuName": "Pool Cue", "weaponName": "WEAPON_POOLCUE", "data": {"sub": 13}, "weapon": true, "submenu": []}, {"menuName": "Switchblade", "weaponName": "WEAPON_SWITCHBLADE", "data": {"sub": 12}, "weapon": true, "submenu": [{"menuName": "Variation 1", "data": {"action": "COMPONENT_SWITCHBLADE_VARMOD_VAR1"}}, {"menuName": "Variation 2", "data": {"action": "COMPONENT_SWITCHBLADE_VARMOD_VAR2"}}]}]}, "Vehicle RGBs": {"Neon Colors": [{"menuName": "White", "data": {"action": "222 222 255"}}, {"menuName": "Cream", "data": {"action": "207 192 165"}}, {"menuName": "Red", "data": {"action": "255 1 1"}}, {"menuName": "Lava Red", "data": {"action": "105 0 0"}}, {"menuName": "<PERSON>", "data": {"action": "74 10 10"}}, {"menuName": "Garnet Red", "data": {"action": "71 14 14"}}, {"menuName": "Wine Red", "data": {"action": "8 0 0"}}, {"menuName": "Pony Pink", "data": {"action": "255 50 100"}}, {"menuName": "Fluorescent Pink", "data": {"action": "255 5 190"}}, {"menuName": "Light Pink", "data": {"action": "38 3 11"}}, {"menuName": "Hot Pink", "data": {"action": "99 0 18"}}, {"menuName": "Pink", "data": {"action": "176 18 89"}}, {"menuName": "Salmon Pink", "data": {"action": "143 47 85"}}, {"menuName": "Orange", "data": {"action": "138 11 0"}}, {"menuName": "Light Orange", "data": {"action": "107 11 0"}}, {"menuName": "Gold", "data": {"action": "255 62 0"}}, {"menuName": "Light Gold", "data": {"action": "194 102 16"}}, {"menuName": "Golden Shower", "data": {"action": "255 150 5"}}, {"menuName": "Bronze", "data": {"action": "74 52 27"}}, {"menuName": "Yellow", "data": {"action": "245 137 15"}}, {"menuName": "Flur Yellow", "data": {"action": "162 168 39"}}, {"menuName": "Flurorescent Yellow", "data": {"action": "255 255 0"}}, {"menuName": "Mint Green", "data": {"action": "0 255 140"}}, {"menuName": "Fluorescent Green", "data": {"action": "94 255 1"}}, {"menuName": "Dark Green", "data": {"action": "0 18 7"}}, {"menuName": "Sea Green", "data": {"action": "0 33 30"}}, {"menuName": "Bright Green", "data": {"action": "0 56 5"}}, {"menuName": "Petrol Green", "data": {"action": "11 65 69"}}, {"menuName": "Electric Blue", "data": {"action": "3 83 255"}}, {"menuName": "Midnight Blue", "data": {"action": "0 1 8"}}, {"menuName": "Galaxy Blue", "data": {"action": "0 13 20"}}, {"menuName": "Dark Blue", "data": {"action": "0 16 41"}}, {"menuName": "Blue", "data": {"action": "0 27 87"}}, {"menuName": "Racing Blue", "data": {"action": "14 49 109"}}, {"menuName": "Purple", "data": {"action": "35 1 255"}}, {"menuName": "Spin Purple", "data": {"action": "26 24 46"}}, {"menuName": "<PERSON>", "data": {"action": "5 0 8"}}, {"menuName": "Bright <PERSON>", "data": {"action": "50 6 66"}}, {"menuName": "Blacklight", "data": {"action": "15 3 255"}}], "Smoke Colors": [{"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "18 17 16"}}, {"menuName": "Black", "data": {"action": "8 8 8"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "15 15 15"}}, {"menuName": "Anthracite Black", "data": {"action": "18 17 16"}}, {"menuName": "Black Steel", "data": {"action": "28 30 33"}}, {"menuName": "Dark Silver", "data": {"action": "41 44 46"}}, {"menuName": "Silver", "data": {"action": "90 94 102"}}, {"menuName": "Blue Silver", "data": {"action": "119 124 135"}}, {"menuName": "Rolled Steel", "data": {"action": "81 84 89"}}, {"menuName": "Shadow Silver", "data": {"action": "50 59 71"}}, {"menuName": "Stone Silver", "data": {"action": "51 51 51"}}, {"menuName": "Midnight Silver", "data": {"action": "31 34 38"}}, {"menuName": "Cast Iron Silver", "data": {"action": "35 41 46"}}, {"menuName": "Red", "data": {"action": "105 0 0"}}, {"menuName": "Torino Red", "data": {"action": "138 11 0"}}, {"menuName": "Formula Red", "data": {"action": "107 0 0"}}, {"menuName": "Lava Red", "data": {"action": "107 11 0"}}, {"menuName": "Blaze Red", "data": {"action": "97 16 9"}}, {"menuName": "<PERSON>", "data": {"action": "74 10 10"}}, {"menuName": "Garnet Red", "data": {"action": "71 14 14"}}, {"menuName": "Sunset Red", "data": {"action": "56 12 0"}}, {"menuName": "Cabernet Red", "data": {"action": "38 3 11"}}, {"menuName": "Wine Red", "data": {"action": "8 0 0"}}, {"menuName": "Candy Red", "data": {"action": "99 0 18"}}, {"menuName": "Hot Pink", "data": {"action": "176 18 89"}}, {"menuName": "Pink", "data": {"action": "143 47 85"}}, {"menuName": "Salmon Pink", "data": {"action": "246 151 153"}}, {"menuName": "Sunrise Orange", "data": {"action": "128 40 0"}}, {"menuName": "Bright Orange", "data": {"action": "194 102 16"}}, {"menuName": "Gold", "data": {"action": "94 83 67"}}, {"menuName": "Bronze", "data": {"action": "74 52 27"}}, {"menuName": "Yellow", "data": {"action": "245 137 15"}}, {"menuName": "Flur Yellow", "data": {"action": "162 168 39"}}, {"menuName": "Dark Green", "data": {"action": "0 18 7"}}, {"menuName": "Sea Green", "data": {"action": "0 33 30"}}, {"menuName": "<PERSON>", "data": {"action": "31 38 30"}}, {"menuName": "Bright Green", "data": {"action": "0 56 5"}}, {"menuName": "Petrol Green", "data": {"action": "11 65 69"}}, {"menuName": "Lime Green", "data": {"action": "86 143 0"}}, {"menuName": "Midnight Blue", "data": {"action": "0 1 8"}}, {"menuName": "Galaxy Blue", "data": {"action": "0 13 20"}}, {"menuName": "Dark Blue", "data": {"action": "0 16 41"}}, {"menuName": "Saxon Blue", "data": {"action": "28 47 79"}}, {"menuName": "Blue", "data": {"action": "0 27 87"}}, {"menuName": "Mariner Blue", "data": {"action": "59 78 120"}}, {"menuName": "Harbor Blue", "data": {"action": "39 45 59"}}, {"menuName": "Diamond Blue", "data": {"action": "149 178 219"}}, {"menuName": "Surf Blue", "data": {"action": "62 98 122"}}, {"menuName": "Nautical Blue", "data": {"action": "28 49 64"}}, {"menuName": "Racing Blue", "data": {"action": "14 49 109"}}, {"menuName": "Light Blue", "data": {"action": "57 90 131"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "34 25 24"}}, {"menuName": "Creek Brown", "data": {"action": "38 33 23"}}, {"menuName": "<PERSON><PERSON> Brown", "data": {"action": "41 27 6"}}, {"menuName": "<PERSON> Brown", "data": {"action": "51 33 17"}}, {"menuName": "Bee<PERSON><PERSON> Brown", "data": {"action": "36 19 9"}}, {"menuName": "<PERSON>", "data": {"action": "59 23 0"}}, {"menuName": "Sad<PERSON>", "data": {"action": "61 48 35"}}, {"menuName": "<PERSON>", "data": {"action": "55 56 43"}}, {"menuName": "Wood<PERSON><PERSON> Brown", "data": {"action": "87 80 54"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "94 83 67"}}, {"menuName": "<PERSON>", "data": {"action": "110 98 70"}}, {"menuName": "<PERSON>lee<PERSON>", "data": {"action": "153 141 115"}}, {"menuName": "Purple", "data": {"action": "26 24 46"}}, {"menuName": "Spin Purple", "data": {"action": "22 22 41"}}, {"menuName": "<PERSON>", "data": {"action": "5 0 8"}}, {"menuName": "Bright <PERSON>", "data": {"action": "50 6 66"}}, {"menuName": "Cream", "data": {"action": "207 192 165"}}, {"menuName": "<PERSON>", "data": {"action": "179 185 201"}}]}, "Vehicle Paints": {"Wheels Colors": [{"menuName": "Black", "data": {"action": 0}}, {"menuName": "Carbon Black", "data": {"action": 147}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": 1}}, {"menuName": "Anhracite Black", "data": {"action": 11}}, {"menuName": "Black Steel", "data": {"action": 2}}, {"menuName": "Dark Steel", "data": {"action": 3}}, {"menuName": "Silver", "data": {"action": 4}}, {"menuName": "Bluish Silver", "data": {"action": 5}}, {"menuName": "Rolled Steel", "data": {"action": 6}}, {"menuName": "Shadow Silver", "data": {"action": 7}}, {"menuName": "Stone Silver", "data": {"action": 8}}, {"menuName": "Midnight Silver", "data": {"action": 9}}, {"menuName": "Cast Iron Silver", "data": {"action": 10}}, {"menuName": "Red", "data": {"action": 27}}, {"menuName": "Torino Red", "data": {"action": 28}}, {"menuName": "Formula Red", "data": {"action": 29}}, {"menuName": "Lava Red", "data": {"action": 150}}, {"menuName": "Blaze Red", "data": {"action": 30}}, {"menuName": "<PERSON>", "data": {"action": 31}}, {"menuName": "Garnet Red", "data": {"action": 32}}, {"menuName": "Sunset Red", "data": {"action": 33}}, {"menuName": "Cabernet Red", "data": {"action": 34}}, {"menuName": "Wine Red", "data": {"action": 143}}, {"menuName": "Candy Red", "data": {"action": 35}}, {"menuName": "Hot Pink", "data": {"action": 135}}, {"menuName": "Pfsiter Pink", "data": {"action": 137}}, {"menuName": "Salmon Pink", "data": {"action": 136}}, {"menuName": "Sunrise Orange", "data": {"action": 36}}, {"menuName": "Orange", "data": {"action": 38}}, {"menuName": "Bright Orange", "data": {"action": 138}}, {"menuName": "Gold", "data": {"action": 99}}, {"menuName": "Bronze", "data": {"action": 90}}, {"menuName": "Yellow", "data": {"action": 88}}, {"menuName": "Race Yellow", "data": {"action": 89}}, {"menuName": "Dew Yellow", "data": {"action": 91}}, {"menuName": "Dark Green", "data": {"action": 49}}, {"menuName": "Racing Green", "data": {"action": 50}}, {"menuName": "Sea Green", "data": {"action": 51}}, {"menuName": "<PERSON>", "data": {"action": 52}}, {"menuName": "Bright Green", "data": {"action": 53}}, {"menuName": "Gasoline Green", "data": {"action": 54}}, {"menuName": "Lime Green", "data": {"action": 92}}, {"menuName": "Midnight Blue", "data": {"action": 141}}, {"menuName": "Galaxy Blue", "data": {"action": 61}}, {"menuName": "Dark Blue", "data": {"action": 62}}, {"menuName": "Saxon Blue", "data": {"action": 63}}, {"menuName": "Blue", "data": {"action": 64}}, {"menuName": "Mariner Blue", "data": {"action": 65}}, {"menuName": "Harbor Blue", "data": {"action": 66}}, {"menuName": "Diamond Blue", "data": {"action": 67}}, {"menuName": "Surf Blue", "data": {"action": 68}}, {"menuName": "Nautical Blue", "data": {"action": 69}}, {"menuName": "Racing Blue", "data": {"action": 73}}, {"menuName": "Ultra Blue", "data": {"action": 70}}, {"menuName": "Light Blue", "data": {"action": 74}}, {"menuName": "Chocolate Brown", "data": {"action": 96}}, {"menuName": "<PERSON><PERSON>", "data": {"action": 101}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 95}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 94}}, {"menuName": "<PERSON> Brown", "data": {"action": 97}}, {"menuName": "Bee<PERSON><PERSON> Brown", "data": {"action": 103}}, {"menuName": "<PERSON>", "data": {"action": 104}}, {"menuName": "Sad<PERSON>", "data": {"action": 98}}, {"menuName": "<PERSON>", "data": {"action": 100}}, {"menuName": "Wood<PERSON><PERSON> Brown", "data": {"action": 102}}, {"menuName": "<PERSON><PERSON>", "data": {"action": 99}}, {"menuName": "<PERSON>", "data": {"action": 105}}, {"menuName": "Bleached Brown", "data": {"action": 106}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 71}}, {"menuName": "Spinnaker Purple", "data": {"action": 72}}, {"menuName": "Midnight Purple", "data": {"action": 142}}, {"menuName": "Bright <PERSON>", "data": {"action": 145}}, {"menuName": "Cream", "data": {"action": 107}}, {"menuName": "Ice White", "data": {"action": 111}}, {"menuName": "<PERSON>", "data": {"action": 112}}], "Secondary Chrome": [{"menuName": "Chrome", "data": {"action": 120}}], "Primary Chrome": [{"menuName": "Chrome", "data": {"action": 120}}], "Secondary Classic": [{"menuName": "Black", "data": {"action": 0}}, {"menuName": "Carbon Black", "data": {"action": 147}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": 1}}, {"menuName": "Anhracite Black", "data": {"action": 11}}, {"menuName": "Black Steel", "data": {"action": 2}}, {"menuName": "Dark Steel", "data": {"action": 3}}, {"menuName": "Silver", "data": {"action": 4}}, {"menuName": "Bluish Silver", "data": {"action": 5}}, {"menuName": "Rolled Steel", "data": {"action": 6}}, {"menuName": "Shadow Silver", "data": {"action": 7}}, {"menuName": "Stone Silver", "data": {"action": 8}}, {"menuName": "Midnight Silver", "data": {"action": 9}}, {"menuName": "Cast Iron Silver", "data": {"action": 10}}, {"menuName": "Red", "data": {"action": 27}}, {"menuName": "Torino Red", "data": {"action": 28}}, {"menuName": "Formula Red", "data": {"action": 29}}, {"menuName": "Lava Red", "data": {"action": 150}}, {"menuName": "Blaze Red", "data": {"action": 30}}, {"menuName": "<PERSON>", "data": {"action": 31}}, {"menuName": "Garnet Red", "data": {"action": 32}}, {"menuName": "Sunset Red", "data": {"action": 33}}, {"menuName": "Cabernet Red", "data": {"action": 34}}, {"menuName": "Wine Red", "data": {"action": 143}}, {"menuName": "Candy Red", "data": {"action": 35}}, {"menuName": "Hot Pink", "data": {"action": 135}}, {"menuName": "Pfsiter Pink", "data": {"action": 137}}, {"menuName": "Salmon Pink", "data": {"action": 136}}, {"menuName": "Sunrise Orange", "data": {"action": 36}}, {"menuName": "Orange", "data": {"action": 38}}, {"menuName": "Bright Orange", "data": {"action": 138}}, {"menuName": "Gold", "data": {"action": 99}}, {"menuName": "Bronze", "data": {"action": 90}}, {"menuName": "Yellow", "data": {"action": 88}}, {"menuName": "Race Yellow", "data": {"action": 89}}, {"menuName": "Dew Yellow", "data": {"action": 91}}, {"menuName": "Dark Green", "data": {"action": 49}}, {"menuName": "Racing Green", "data": {"action": 50}}, {"menuName": "Sea Green", "data": {"action": 51}}, {"menuName": "<PERSON>", "data": {"action": 52}}, {"menuName": "Bright Green", "data": {"action": 53}}, {"menuName": "Gasoline Green", "data": {"action": 54}}, {"menuName": "Lime Green", "data": {"action": 92}}, {"menuName": "Midnight Blue", "data": {"action": 141}}, {"menuName": "Galaxy Blue", "data": {"action": 61}}, {"menuName": "Dark Blue", "data": {"action": 62}}, {"menuName": "Saxon Blue", "data": {"action": 63}}, {"menuName": "Blue", "data": {"action": 64}}, {"menuName": "Mariner Blue", "data": {"action": 65}}, {"menuName": "Harbor Blue", "data": {"action": 66}}, {"menuName": "Diamond Blue", "data": {"action": 67}}, {"menuName": "Surf Blue", "data": {"action": 68}}, {"menuName": "Nautical Blue", "data": {"action": 69}}, {"menuName": "Racing Blue", "data": {"action": 73}}, {"menuName": "Ultra Blue", "data": {"action": 70}}, {"menuName": "Light Blue", "data": {"action": 74}}, {"menuName": "Chocolate Brown", "data": {"action": 96}}, {"menuName": "<PERSON><PERSON>", "data": {"action": 101}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 95}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 94}}, {"menuName": "<PERSON> Brown", "data": {"action": 97}}, {"menuName": "Bee<PERSON><PERSON> Brown", "data": {"action": 103}}, {"menuName": "<PERSON>", "data": {"action": 104}}, {"menuName": "Sad<PERSON>", "data": {"action": 98}}, {"menuName": "<PERSON>", "data": {"action": 100}}, {"menuName": "Wood<PERSON><PERSON> Brown", "data": {"action": 102}}, {"menuName": "<PERSON><PERSON>", "data": {"action": 99}}, {"menuName": "<PERSON>", "data": {"action": 105}}, {"menuName": "Bleached Brown", "data": {"action": 106}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 71}}, {"menuName": "Spinnaker Purple", "data": {"action": 72}}, {"menuName": "Midnight Purple", "data": {"action": 142}}, {"menuName": "Bright <PERSON>", "data": {"action": 145}}, {"menuName": "Cream", "data": {"action": 107}}, {"menuName": "Ice White", "data": {"action": 111}}, {"menuName": "<PERSON>", "data": {"action": 112}}], "Primary Classic": [{"menuName": "Black", "data": {"action": 0}}, {"menuName": "Carbon Black", "data": {"action": 147}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": 1}}, {"menuName": "Anhracite Black", "data": {"action": 11}}, {"menuName": "Black Steel", "data": {"action": 2}}, {"menuName": "Dark Steel", "data": {"action": 3}}, {"menuName": "Silver", "data": {"action": 4}}, {"menuName": "Bluish Silver", "data": {"action": 5}}, {"menuName": "Rolled Steel", "data": {"action": 6}}, {"menuName": "Shadow Silver", "data": {"action": 7}}, {"menuName": "Stone Silver", "data": {"action": 8}}, {"menuName": "Midnight Silver", "data": {"action": 9}}, {"menuName": "Cast Iron Silver", "data": {"action": 10}}, {"menuName": "Red", "data": {"action": 27}}, {"menuName": "Torino Red", "data": {"action": 28}}, {"menuName": "Formula Red", "data": {"action": 29}}, {"menuName": "Lava Red", "data": {"action": 150}}, {"menuName": "Blaze Red", "data": {"action": 30}}, {"menuName": "<PERSON>", "data": {"action": 31}}, {"menuName": "Garnet Red", "data": {"action": 32}}, {"menuName": "Sunset Red", "data": {"action": 33}}, {"menuName": "Cabernet Red", "data": {"action": 34}}, {"menuName": "Wine Red", "data": {"action": 143}}, {"menuName": "Candy Red", "data": {"action": 35}}, {"menuName": "Hot Pink", "data": {"action": 135}}, {"menuName": "Pfsiter Pink", "data": {"action": 137}}, {"menuName": "Salmon Pink", "data": {"action": 136}}, {"menuName": "Sunrise Orange", "data": {"action": 36}}, {"menuName": "Orange", "data": {"action": 38}}, {"menuName": "Bright Orange", "data": {"action": 138}}, {"menuName": "Gold", "data": {"action": 99}}, {"menuName": "Bronze", "data": {"action": 90}}, {"menuName": "Yellow", "data": {"action": 88}}, {"menuName": "Race Yellow", "data": {"action": 89}}, {"menuName": "Dew Yellow", "data": {"action": 91}}, {"menuName": "Dark Green", "data": {"action": 49}}, {"menuName": "Racing Green", "data": {"action": 50}}, {"menuName": "Sea Green", "data": {"action": 51}}, {"menuName": "<PERSON>", "data": {"action": 52}}, {"menuName": "Bright Green", "data": {"action": 53}}, {"menuName": "Gasoline Green", "data": {"action": 54}}, {"menuName": "Lime Green", "data": {"action": 92}}, {"menuName": "Midnight Blue", "data": {"action": 141}}, {"menuName": "Galaxy Blue", "data": {"action": 61}}, {"menuName": "Dark Blue", "data": {"action": 62}}, {"menuName": "Saxon Blue", "data": {"action": 63}}, {"menuName": "Blue", "data": {"action": 64}}, {"menuName": "Mariner Blue", "data": {"action": 65}}, {"menuName": "Harbor Blue", "data": {"action": 66}}, {"menuName": "Diamond Blue", "data": {"action": 67}}, {"menuName": "Surf Blue", "data": {"action": 68}}, {"menuName": "Nautical Blue", "data": {"action": 69}}, {"menuName": "Racing Blue", "data": {"action": 73}}, {"menuName": "Ultra Blue", "data": {"action": 70}}, {"menuName": "Light Blue", "data": {"action": 74}}, {"menuName": "Chocolate Brown", "data": {"action": 96}}, {"menuName": "<PERSON><PERSON>", "data": {"action": 101}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 95}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 94}}, {"menuName": "<PERSON> Brown", "data": {"action": 97}}, {"menuName": "Bee<PERSON><PERSON> Brown", "data": {"action": 103}}, {"menuName": "<PERSON>", "data": {"action": 104}}, {"menuName": "Sad<PERSON>", "data": {"action": 98}}, {"menuName": "<PERSON>", "data": {"action": 100}}, {"menuName": "Wood<PERSON><PERSON> Brown", "data": {"action": 102}}, {"menuName": "<PERSON><PERSON>", "data": {"action": 99}}, {"menuName": "<PERSON>", "data": {"action": 105}}, {"menuName": "Bleached Brown", "data": {"action": 106}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 71}}, {"menuName": "Spinnaker Purple", "data": {"action": 72}}, {"menuName": "Midnight Purple", "data": {"action": 142}}, {"menuName": "Bright <PERSON>", "data": {"action": 145}}, {"menuName": "Cream", "data": {"action": 107}}, {"menuName": "Ice White", "data": {"action": 111}}, {"menuName": "<PERSON>", "data": {"action": 112}}], "Secondary Metal": [{"menuName": "Brushed Steel", "data": {"action": 117}}, {"menuName": "Brushed Black Steel", "data": {"action": 118}}, {"menuName": "Brushed Aluminum", "data": {"action": 119}}, {"menuName": "Pure Gold", "data": {"action": 158}}, {"menuName": "Brushed Gold", "data": {"action": 159}}], "Primary Metal": [{"menuName": "Brushed Steel", "data": {"action": 117}}, {"menuName": "Brushed Black Steel", "data": {"action": 118}}, {"menuName": "Brushed Aluminum", "data": {"action": 119}}, {"menuName": "Pure Gold", "data": {"action": 158}}, {"menuName": "Brushed Gold", "data": {"action": 159}}], "Secondary Matte": [{"menuName": "Black", "data": {"action": 12}}, {"menuName": "<PERSON>", "data": {"action": 13}}, {"menuName": "Light Gray", "data": {"action": 14}}, {"menuName": "Ice White", "data": {"action": 131}}, {"menuName": "Blue", "data": {"action": 83}}, {"menuName": "Dark Blue", "data": {"action": 82}}, {"menuName": "Midnight Blue", "data": {"action": 84}}, {"menuName": "Midnight Purple", "data": {"action": 149}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 148}}, {"menuName": "Red", "data": {"action": 39}}, {"menuName": "Dark Red", "data": {"action": 40}}, {"menuName": "Orange", "data": {"action": 41}}, {"menuName": "Yellow", "data": {"action": 42}}, {"menuName": "Lime Green", "data": {"action": 55}}, {"menuName": "Green", "data": {"action": 128}}, {"menuName": "<PERSON>", "data": {"action": 151}}, {"menuName": "Foliage Green", "data": {"action": 155}}, {"menuName": "<PERSON>", "data": {"action": 152}}, {"menuName": "Dark Earth", "data": {"action": 153}}, {"menuName": "Desert Tan", "data": {"action": 154}}], "Primary Matte": [{"menuName": "Black", "data": {"action": 12}}, {"menuName": "<PERSON>", "data": {"action": 13}}, {"menuName": "Light Gray", "data": {"action": 14}}, {"menuName": "Ice White", "data": {"action": 131}}, {"menuName": "Blue", "data": {"action": 83}}, {"menuName": "Dark Blue", "data": {"action": 82}}, {"menuName": "Midnight Blue", "data": {"action": 84}}, {"menuName": "Midnight Purple", "data": {"action": 149}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 148}}, {"menuName": "Red", "data": {"action": 39}}, {"menuName": "Dark Red", "data": {"action": 40}}, {"menuName": "Orange", "data": {"action": 41}}, {"menuName": "Yellow", "data": {"action": 42}}, {"menuName": "Lime Green", "data": {"action": 55}}, {"menuName": "Green", "data": {"action": 128}}, {"menuName": "<PERSON>", "data": {"action": 151}}, {"menuName": "Foliage Green", "data": {"action": 155}}, {"menuName": "<PERSON>", "data": {"action": 152}}, {"menuName": "Dark Earth", "data": {"action": 153}}, {"menuName": "Desert Tan", "data": {"action": 154}}], "Secondary Metallic": [{"menuName": "Black", "data": {"action": 0}}, {"menuName": "Carbon Black", "data": {"action": 147}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": 1}}, {"menuName": "Anhracite Black", "data": {"action": 11}}, {"menuName": "Black Steel", "data": {"action": 2}}, {"menuName": "Dark Steel", "data": {"action": 3}}, {"menuName": "Silver", "data": {"action": 4}}, {"menuName": "Bluish Silver", "data": {"action": 5}}, {"menuName": "Rolled Steel", "data": {"action": 6}}, {"menuName": "Shadow Silver", "data": {"action": 7}}, {"menuName": "Stone Silver", "data": {"action": 8}}, {"menuName": "Midnight Silver", "data": {"action": 9}}, {"menuName": "Cast Iron Silver", "data": {"action": 10}}, {"menuName": "Red", "data": {"action": 27}}, {"menuName": "Torino Red", "data": {"action": 28}}, {"menuName": "Formula Red", "data": {"action": 29}}, {"menuName": "Lava Red", "data": {"action": 150}}, {"menuName": "Blaze Red", "data": {"action": 30}}, {"menuName": "<PERSON>", "data": {"action": 31}}, {"menuName": "Garnet Red", "data": {"action": 32}}, {"menuName": "Sunset Red", "data": {"action": 33}}, {"menuName": "Cabernet Red", "data": {"action": 34}}, {"menuName": "Wine Red", "data": {"action": 143}}, {"menuName": "Candy Red", "data": {"action": 35}}, {"menuName": "Hot Pink", "data": {"action": 135}}, {"menuName": "Pfsiter Pink", "data": {"action": 137}}, {"menuName": "Salmon Pink", "data": {"action": 136}}, {"menuName": "Sunrise Orange", "data": {"action": 36}}, {"menuName": "Orange", "data": {"action": 38}}, {"menuName": "Bright Orange", "data": {"action": 138}}, {"menuName": "Gold", "data": {"action": 99}}, {"menuName": "Bronze", "data": {"action": 90}}, {"menuName": "Yellow", "data": {"action": 88}}, {"menuName": "Race Yellow", "data": {"action": 89}}, {"menuName": "Dew Yellow", "data": {"action": 91}}, {"menuName": "Dark Green", "data": {"action": 49}}, {"menuName": "Racing Green", "data": {"action": 50}}, {"menuName": "Sea Green", "data": {"action": 51}}, {"menuName": "<PERSON>", "data": {"action": 52}}, {"menuName": "Bright Green", "data": {"action": 53}}, {"menuName": "Gasoline Green", "data": {"action": 54}}, {"menuName": "Lime Green", "data": {"action": 92}}, {"menuName": "Midnight Blue", "data": {"action": 141}}, {"menuName": "Galaxy Blue", "data": {"action": 61}}, {"menuName": "Dark Blue", "data": {"action": 62}}, {"menuName": "Saxon Blue", "data": {"action": 63}}, {"menuName": "Blue", "data": {"action": 64}}, {"menuName": "Mariner Blue", "data": {"action": 65}}, {"menuName": "Harbor Blue", "data": {"action": 66}}, {"menuName": "Diamond Blue", "data": {"action": 67}}, {"menuName": "Surf Blue", "data": {"action": 68}}, {"menuName": "Nautical Blue", "data": {"action": 69}}, {"menuName": "Racing Blue", "data": {"action": 73}}, {"menuName": "Ultra Blue", "data": {"action": 70}}, {"menuName": "Light Blue", "data": {"action": 74}}, {"menuName": "Chocolate Brown", "data": {"action": 96}}, {"menuName": "<PERSON><PERSON>", "data": {"action": 101}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 95}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 94}}, {"menuName": "<PERSON> Brown", "data": {"action": 97}}, {"menuName": "Bee<PERSON><PERSON> Brown", "data": {"action": 103}}, {"menuName": "<PERSON>", "data": {"action": 104}}, {"menuName": "Sad<PERSON>", "data": {"action": 98}}, {"menuName": "<PERSON>", "data": {"action": 100}}, {"menuName": "Wood<PERSON><PERSON> Brown", "data": {"action": 102}}, {"menuName": "<PERSON><PERSON>", "data": {"action": 99}}, {"menuName": "<PERSON>", "data": {"action": 105}}, {"menuName": "Bleached Brown", "data": {"action": 106}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 71}}, {"menuName": "Spinnaker Purple", "data": {"action": 72}}, {"menuName": "Midnight Purple", "data": {"action": 142}}, {"menuName": "Bright <PERSON>", "data": {"action": 145}}, {"menuName": "Cream", "data": {"action": 107}}, {"menuName": "Ice White", "data": {"action": 111}}, {"menuName": "<PERSON>", "data": {"action": 112}}], "Primary Metallic": [{"menuName": "Black", "data": {"action": 0}}, {"menuName": "Carbon Black", "data": {"action": 147}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": 1}}, {"menuName": "Anhracite Black", "data": {"action": 11}}, {"menuName": "Black Steel", "data": {"action": 2}}, {"menuName": "Dark Steel", "data": {"action": 3}}, {"menuName": "Silver", "data": {"action": 4}}, {"menuName": "Bluish Silver", "data": {"action": 5}}, {"menuName": "Rolled Steel", "data": {"action": 6}}, {"menuName": "Shadow Silver", "data": {"action": 7}}, {"menuName": "Stone Silver", "data": {"action": 8}}, {"menuName": "Midnight Silver", "data": {"action": 9}}, {"menuName": "Cast Iron Silver", "data": {"action": 10}}, {"menuName": "Red", "data": {"action": 27}}, {"menuName": "Torino Red", "data": {"action": 28}}, {"menuName": "Formula Red", "data": {"action": 29}}, {"menuName": "Lava Red", "data": {"action": 150}}, {"menuName": "Blaze Red", "data": {"action": 30}}, {"menuName": "<PERSON>", "data": {"action": 31}}, {"menuName": "Garnet Red", "data": {"action": 32}}, {"menuName": "Sunset Red", "data": {"action": 33}}, {"menuName": "Cabernet Red", "data": {"action": 34}}, {"menuName": "Wine Red", "data": {"action": 143}}, {"menuName": "Candy Red", "data": {"action": 35}}, {"menuName": "Hot Pink", "data": {"action": 135}}, {"menuName": "Pfsiter Pink", "data": {"action": 137}}, {"menuName": "Salmon Pink", "data": {"action": 136}}, {"menuName": "Sunrise Orange", "data": {"action": 36}}, {"menuName": "Orange", "data": {"action": 38}}, {"menuName": "Bright Orange", "data": {"action": 138}}, {"menuName": "Gold", "data": {"action": 99}}, {"menuName": "Bronze", "data": {"action": 90}}, {"menuName": "Yellow", "data": {"action": 88}}, {"menuName": "Race Yellow", "data": {"action": 89}}, {"menuName": "Dew Yellow", "data": {"action": 91}}, {"menuName": "Dark Green", "data": {"action": 49}}, {"menuName": "Racing Green", "data": {"action": 50}}, {"menuName": "Sea Green", "data": {"action": 51}}, {"menuName": "<PERSON>", "data": {"action": 52}}, {"menuName": "Bright Green", "data": {"action": 53}}, {"menuName": "Gasoline Green", "data": {"action": 54}}, {"menuName": "Lime Green", "data": {"action": 92}}, {"menuName": "Midnight Blue", "data": {"action": 141}}, {"menuName": "Galaxy Blue", "data": {"action": 61}}, {"menuName": "Dark Blue", "data": {"action": 62}}, {"menuName": "Saxon Blue", "data": {"action": 63}}, {"menuName": "Blue", "data": {"action": 64}}, {"menuName": "Mariner Blue", "data": {"action": 65}}, {"menuName": "Harbor Blue", "data": {"action": 66}}, {"menuName": "Diamond Blue", "data": {"action": 67}}, {"menuName": "Surf Blue", "data": {"action": 68}}, {"menuName": "Nautical Blue", "data": {"action": 69}}, {"menuName": "Racing Blue", "data": {"action": 73}}, {"menuName": "Ultra Blue", "data": {"action": 70}}, {"menuName": "Light Blue", "data": {"action": 74}}, {"menuName": "Chocolate Brown", "data": {"action": 96}}, {"menuName": "<PERSON><PERSON>", "data": {"action": 101}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 95}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 94}}, {"menuName": "<PERSON> Brown", "data": {"action": 97}}, {"menuName": "Bee<PERSON><PERSON> Brown", "data": {"action": 103}}, {"menuName": "<PERSON>", "data": {"action": 104}}, {"menuName": "Sad<PERSON>", "data": {"action": 98}}, {"menuName": "<PERSON>", "data": {"action": 100}}, {"menuName": "Wood<PERSON><PERSON> Brown", "data": {"action": 102}}, {"menuName": "<PERSON><PERSON>", "data": {"action": 99}}, {"menuName": "<PERSON>", "data": {"action": 105}}, {"menuName": "Bleached Brown", "data": {"action": 106}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": 71}}, {"menuName": "Spinnaker Purple", "data": {"action": 72}}, {"menuName": "Midnight Purple", "data": {"action": 142}}, {"menuName": "Bright <PERSON>", "data": {"action": 145}}, {"menuName": "Cream", "data": {"action": 107}}, {"menuName": "Ice White", "data": {"action": 111}}, {"menuName": "<PERSON>", "data": {"action": 112}}]}, "Vehicle Mods": {"Wheels Benny": [{"menuName": "Stock", "data": {"action": "24 -1"}}, {"menuName": "OG Hu<PERSON>ts", "data": {"action": "24 0"}}, {"menuName": "O<PERSON> (Chrome Lip)", "data": {"action": "24 1"}}], "Wheels Back": [{"menuName": "Stock", "data": {"action": "24 -1"}}, {"menuName": "Speedway", "data": {"action": "24 0"}}, {"menuName": "Streetspecial", "data": {"action": "24 1"}}, {"menuName": "Racer", "data": {"action": "24 2"}}, {"menuName": "Trackstar", "data": {"action": "24 3"}}, {"menuName": "Overlord", "data": {"action": "24 4"}}, {"menuName": "Trident", "data": {"action": "24 5"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "24 6"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "24 7"}}, {"menuName": "<PERSON>s", "data": {"action": "24 8"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "24 9"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "24 10"}}, {"menuName": "Iceshield", "data": {"action": "24 11"}}, {"menuName": "Loops", "data": {"action": "24 12"}}], "Wheels Front": [{"menuName": "Stock", "data": {"action": "23 -1"}}, {"menuName": "Speedway", "data": {"action": "23 0"}}, {"menuName": "Streetspecial", "data": {"action": "23 1"}}, {"menuName": "Racer", "data": {"action": "23 2"}}, {"menuName": "Trackstar", "data": {"action": "23 3"}}, {"menuName": "Overlord", "data": {"action": "23 4"}}, {"menuName": "Trident", "data": {"action": "23 5"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "23 6"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "23 7"}}, {"menuName": "<PERSON>s", "data": {"action": "23 8"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "23 9"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "23 10"}}, {"menuName": "Iceshield", "data": {"action": "23 11"}}, {"menuName": "Loops", "data": {"action": "23 12"}}], "Wheels Muscle": [{"menuName": "Stock", "data": {"action": "23 -1"}}, {"menuName": "Classicfive", "data": {"action": "23 0"}}, {"menuName": "Dukes", "data": {"action": "23 1"}}, {"menuName": "Musclefreak", "data": {"action": "23 2"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "23 3"}}, {"menuName": "Azrea", "data": {"action": "23 4"}}, {"menuName": "Mecha", "data": {"action": "23 5"}}, {"menuName": "Blacktop", "data": {"action": "23 6"}}, {"menuName": "Dragspl", "data": {"action": "23 7"}}, {"menuName": "Revolver", "data": {"action": "23 8"}}, {"menuName": "Classicrod", "data": {"action": "23 9"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "23 10"}}, {"menuName": "Fivestar", "data": {"action": "23 11"}}, {"menuName": "Oldschool", "data": {"action": "23 12"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "23 13"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "23 14"}}, {"menuName": "Sixgun", "data": {"action": "23 15"}}, {"menuName": "Mercenary", "data": {"action": "23 16"}}], "Wheels Lowrider": [{"menuName": "Stock", "data": {"action": "23 -1"}}, {"menuName": "Flare", "data": {"action": "23 0"}}, {"menuName": "Wired", "data": {"action": "23 1"}}, {"menuName": "Triplegolds", "data": {"action": "23 2"}}, {"menuName": "Bigworm", "data": {"action": "23 3"}}, {"menuName": "Sevenfives", "data": {"action": "23 4"}}, {"menuName": "Splitsix", "data": {"action": "23 5"}}, {"menuName": "Freshmesh", "data": {"action": "23 6"}}, {"menuName": "Leadsled", "data": {"action": "23 7"}}, {"menuName": "Turbine", "data": {"action": "23 8"}}, {"menuName": "Superfin", "data": {"action": "23 9"}}, {"menuName": "Classicrod", "data": {"action": "23 10"}}, {"menuName": "Dollar", "data": {"action": "23 11"}}, {"menuName": "Dukes", "data": {"action": "23 12"}}, {"menuName": "Lowfive", "data": {"action": "23 13"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "23 14"}}], "Wheels Highend": [{"menuName": "Stock", "data": {"action": "23 -1"}}, {"menuName": "Shadow", "data": {"action": "23 0"}}, {"menuName": "Hyper", "data": {"action": "23 1"}}, {"menuName": "Blade", "data": {"action": "23 2"}}, {"menuName": "Diamond", "data": {"action": "23 3"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "23 4"}}, {"menuName": "Chromaticz", "data": {"action": "23 5"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data": {"action": "23 6"}}, {"menuName": "Obeyrs", "data": {"action": "23 7"}}, {"menuName": "Gtchrome", "data": {"action": "23 8"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "23 9"}}, {"menuName": "Solar", "data": {"action": "23 10"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "23 11"}}, {"menuName": "Dashvip", "data": {"action": "23 12"}}, {"menuName": "Lozspeedten", "data": {"action": "23 13"}}, {"menuName": "Carboninferno", "data": {"action": "23 14"}}, {"menuName": "Carbonshadow", "data": {"action": "23 15"}}, {"menuName": "Carbonz", "data": {"action": "23 16"}}, {"menuName": "Carbonsolar", "data": {"action": "23 17"}}, {"menuName": "Carboncheetahr", "data": {"action": "23 18"}}, {"menuName": "Carbonsracer", "data": {"action": "23 19"}}], "Wheels Tuner": [{"menuName": "Stock", "data": {"action": "23 -1"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "23 0"}}, {"menuName": "Supermesh", "data": {"action": "23 1"}}, {"menuName": "Outsider", "data": {"action": "23 2"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "23 3"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "23 4"}}, {"menuName": "<PERSON>licer", "data": {"action": "23 5"}}, {"menuName": "Elquatro", "data": {"action": "23 6"}}, {"menuName": "Dubbed", "data": {"action": "23 7"}}, {"menuName": "Fivestar", "data": {"action": "23 8"}}, {"menuName": "Slideways", "data": {"action": "23 9"}}, {"menuName": "Apex", "data": {"action": "23 10"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "23 11"}}, {"menuName": "Countersteer", "data": {"action": "23 12"}}, {"menuName": "Endov1", "data": {"action": "23 13"}}, {"menuName": "Endov2dish", "data": {"action": "23 14"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "23 15"}}, {"menuName": "Chokadori", "data": {"action": "23 16"}}, {"menuName": "Chicane", "data": {"action": "23 17"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "23 18"}}, {"menuName": "Dishedeight", "data": {"action": "23 19"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "23 20"}}, {"menuName": "Zokusha", "data": {"action": "23 21"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "23 22"}}, {"menuName": "Rallymaster", "data": {"action": "23 23"}}], "Wheels Offroad": [{"menuName": "Stock", "data": {"action": "23 -1"}}, {"menuName": "Raider", "data": {"action": "23 0"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "23 1"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "23 2"}}, {"menuName": "Cairngorm", "data": {"action": "23 3"}}, {"menuName": "Amazon", "data": {"action": "23 4"}}, {"menuName": "Challenger", "data": {"action": "23 5"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "23 6"}}, {"menuName": "Fivestar", "data": {"action": "23 7"}}, {"menuName": "Rockcrawler", "data": {"action": "23 8"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data": {"action": "23 9"}}], "Wheels SUV": [{"menuName": "Stock", "data": {"action": "23 -1"}}, {"menuName": "Vip", "data": {"action": "23 0"}}, {"menuName": "Benefactor", "data": {"action": "23 1"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "23 2"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "23 3"}}, {"menuName": "Royalsix", "data": {"action": "23 4"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "23 5"}}, {"menuName": "Deluxe", "data": {"action": "23 6"}}, {"menuName": "Icedout", "data": {"action": "23 7"}}, {"menuName": "Cognscenti", "data": {"action": "23 8"}}, {"menuName": "Lozspeedten", "data": {"action": "23 9"}}, {"menuName": "Supernova", "data": {"action": "23 10"}}, {"menuName": "Obeyrs", "data": {"action": "23 11"}}, {"menuName": "Lozspeedballer", "data": {"action": "23 12"}}, {"menuName": "Extra vaganzo", "data": {"action": "23 13"}}, {"menuName": "Splitsix", "data": {"action": "23 14"}}, {"menuName": "Empowered", "data": {"action": "23 15"}}, {"menuName": "Sunrise", "data": {"action": "23 16"}}, {"menuName": "Dashvip", "data": {"action": "23 17"}}, {"menuName": "Cutter", "data": {"action": "23 18"}}], "Wheels Sport": [{"menuName": "Stock", "data": {"action": "23 -1"}}, {"menuName": "Inferno", "data": {"action": "23 0"}}, {"menuName": "Deepfive", "data": {"action": "23 1"}}, {"menuName": "Lozspeed", "data": {"action": "23 2"}}, {"menuName": "Diamondcut", "data": {"action": "23 3"}}, {"menuName": "Chrono", "data": {"action": "23 4"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "23 5"}}, {"menuName": "Fiftynine", "data": {"action": "23 6"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "23 7"}}, {"menuName": "Syntheticz", "data": {"action": "23 8"}}, {"menuName": "Organictyped", "data": {"action": "23 9"}}, {"menuName": "Endov1", "data": {"action": "23 10"}}, {"menuName": "Duper7", "data": {"action": "23 11"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "23 12"}}, {"menuName": "Groundride", "data": {"action": "23 13"}}, {"menuName": "Spacer", "data": {"action": "23 14"}}, {"menuName": "Venum", "data": {"action": "23 15"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "23 16"}}, {"menuName": "Dashvip", "data": {"action": "23 17"}}, {"menuName": "Icekid", "data": {"action": "23 18"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "23 19"}}, {"menuName": "<PERSON>en<PERSON>", "data": {"action": "23 20"}}, {"menuName": "Superfive", "data": {"action": "23 21"}}, {"menuName": "Endov2", "data": {"action": "23 22"}}, {"menuName": "Slitsix", "data": {"action": "23 23"}}], "Horns": [{"menuName": "<PERSON>", "data": {"action": "14 -1"}}, {"menuName": "Truck Horn", "data": {"action": "14 0"}}, {"menuName": "Police Horn", "data": {"action": "14 1"}}, {"menuName": "Clown Horn", "data": {"action": "14 2"}}, {"menuName": "Musical Horn 1", "data": {"action": "14 3"}}, {"menuName": "Musical Horn 2", "data": {"action": "14 4"}}, {"menuName": "Musical Horn 3", "data": {"action": "14 5"}}, {"menuName": "Musical Horn 4", "data": {"action": "14 6"}}, {"menuName": "Musical Horn 5", "data": {"action": "14 7"}}, {"menuName": "<PERSON>", "data": {"action": "14 8"}}, {"menuName": "Classical Horn 1", "data": {"action": "14 9"}}, {"menuName": "Classical Horn 2", "data": {"action": "14 10"}}, {"menuName": "Classical Horn 3", "data": {"action": "14 11"}}, {"menuName": "Classical Horn 4", "data": {"action": "14 12"}}, {"menuName": "Classical Horn 5", "data": {"action": "14 13"}}, {"menuName": "Classical Horn 6", "data": {"action": "14 14"}}, {"menuName": "Classical Horn 7", "data": {"action": "14 15"}}, {"menuName": "Scale - Do", "data": {"action": "14 16"}}, {"menuName": "Scale - Re", "data": {"action": "14 17"}}, {"menuName": "Scale - Mi", "data": {"action": "14 18"}}, {"menuName": "Scale - Fa", "data": {"action": "14 19"}}, {"menuName": "Scale - Sol", "data": {"action": "14 20"}}, {"menuName": "Scale - La", "data": {"action": "14 21"}}, {"menuName": "Scale - Ti", "data": {"action": "14 22"}}, {"menuName": "Scale - Do - High", "data": {"action": "14 23"}}, {"menuName": "Jazz Horn 1", "data": {"action": "14 25"}}, {"menuName": "Jazz Horn 2", "data": {"action": "14 26"}}, {"menuName": "Jazz Horn 3", "data": {"action": "14 27"}}, {"menuName": "<PERSON> Looped", "data": {"action": "14 28"}}, {"menuName": "Star Spangled Banner 1", "data": {"action": "14 29"}}, {"menuName": "Star Spangled Banner 2", "data": {"action": "14 30"}}, {"menuName": "Star Spangled Banner 3", "data": {"action": "14 31"}}, {"menuName": "Star Spangled Banner 4", "data": {"action": "14 32"}}, {"menuName": "Classical Horn Looped 1", "data": {"action": "14 33"}}, {"menuName": "Classical Horn 8", "data": {"action": "14 34"}}, {"menuName": "Classical Horn 2 Looped", "data": {"action": "14 35"}}]}, "Vehicles": {"Bicycles": [{"menuName": "BMX", "data": {"action": "BMX"}}, {"menuName": "Cruiser", "data": {"action": "CRUISER"}}, {"menuName": "Endurex Race", "data": {"action": "TRIBIKE2"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "FIXTER"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "SCORCHER"}}, {"menuName": "Tri-Cycles Race", "data": {"action": "TRIBIKE3"}}, {"menuName": "Whippet Race", "data": {"action": "TRIBIKE"}}], "Boats": [{"menuName": "<PERSON>g", "data": {"action": "TUG"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "MARQUIS"}}, {"menuName": "Lampadati Toro", "data": {"action": "TORO"}}, {"menuName": "Nagasaki Dinghy (2 Seater)", "data": {"action": "DINGHY2"}}, {"menuName": "Nagasaki Dinghy (4 Seater Black)", "data": {"action": "DINGHY3"}}, {"menuName": "Nagasaki Dinghy (4 Seater Red)", "data": {"action": "DINGHY"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "SPEEDER"}}, {"menuName": "Shitzu Jetmax", "data": {"action": "SUNTRAP"}}, {"menuName": "<PERSON><PERSON> (Police)", "data": {"action": "PREDATOR"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "SQUALO"}}, {"menuName": "Shitzu Suntrap", "data": {"action": "SUNTRAP"}}, {"menuName": "Shitzu Tropic", "data": {"action": "TROPIC"}}, {"menuName": "Speedophile <PERSON>", "data": {"action": "SEASHARK"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON> (Lifeguard)", "data": {"action": "SEASHARK2"}}, {"menuName": "Submersible", "data": {"action": "SUBMERSIBLE"}}, {"menuName": "Submersible (Kraken)", "data": {"action": "SUBMERSIBLE2"}}], "Helicopters": [{"menuName": "<PERSON><PERSON><PERSON> (Atomic)", "data": {"action": "BLIMP"}}, {"menuName": "Blimp (Xero Gas)", "data": {"action": "BLIMP2"}}, {"menuName": "<PERSON> Savage", "data": {"action": "SAVAGE"}}, {"menuName": "Buckingham SuperVolito", "data": {"action": "SUPERVOLITO"}}, {"menuName": "Buckingham SuperVolito Carbon", "data": {"action": "SUPERVOLITO2"}}, {"menuName": "Buckingham Swift", "data": {"action": "SWIFT"}}, {"menuName": "Buckingham Swift Deluxe", "data": {"action": "SWIFT2"}}, {"menuName": "Buckingham Valkyrie", "data": {"action": "VALKYRIE"}}, {"menuName": "Buckingham Volatus", "data": {"action": "VOLATUS"}}, {"menuName": "HVT Skylift", "data": {"action": "SKYLIFT"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "FROGGER"}}, {"menuName": "<PERSON><PERSON><PERSON> (TPE/FIB)", "data": {"action": "FROGGER2"}}, {"menuName": "<PERSON><PERSON><PERSON> (Attack Chopper)", "data": {"action": "BUZZARD2"}}, {"menuName": "<PERSON><PERSON><PERSON> (Unarmed)", "data": {"action": "BUZZARD"}}, {"menuName": "Western Annihilator", "data": {"action": "ANNIHILATOR"}}, {"menuName": "Western Cargobob (Desert Camo)", "data": {"action": "CARGOBOB"}}, {"menuName": "Western Cargobob (Jetsam)", "data": {"action": "CARGOBOB2"}}, {"menuName": "Western Cargobob (TPE)", "data": {"action": "CARGOBOB3"}}, {"menuName": "Western Mavrick", "data": {"action": "MAVERICK"}}, {"menuName": "<PERSON> (Police)", "data": {"action": "POLMAV"}}], "Planes": [{"menuName": "Buckingham Cargo Plane (An-225)", "data": {"action": "CARGOPLANE"}}, {"menuName": "Buckingham Jet (B747)", "data": {"action": "JET"}}, {"menuName": "<PERSON> Luxor", "data": {"action": "LUXOR"}}, {"menuName": "Buckingham Luxor Deluxe", "data": {"action": "LUXOR2"}}, {"menuName": "Buckingham Miljet", "data": {"action": "MILJET"}}, {"menuName": "Buckingham Nimbus", "data": {"action": "NIMBUS"}}, {"menuName": "<PERSON> Shamal", "data": {"action": "SHAMAL"}}, {"menuName": "Buckingham Vestra", "data": {"action": "VESTRA"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "MAMMATUS"}}, {"menuName": "Jobuilt P-996 Lazer", "data": {"action": "LAZER"}}, {"menuName": "Job<PERSON> Velum (4 Seater)", "data": {"action": "VELUM"}}, {"menuName": "Job<PERSON> Velum (5 Seater)", "data": {"action": "VELUM2"}}, {"menuName": "Mammoth Dodo", "data": {"action": "DODO"}}, {"menuName": "Mammoth Hydra", "data": {"action": "HYDRA"}}, {"menuName": "Mammoth Titan", "data": {"action": "TITAN"}}, {"menuName": "Western Besra", "data": {"action": "BESRA"}}, {"menuName": "Western Cuban 800", "data": {"action": "CUBAN800"}}, {"menuName": "Western Duster", "data": {"action": "DUSTER"}}, {"menuName": "Western Mallard Stunt Plane", "data": {"action": "STUNT"}}], "Motorcycles": [{"menuName": "<PERSON><PERSON>", "data": {"action": "AKUMA"}}, {"menuName": "Dinka Double-T", "data": {"action": "DOUBLE"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "ENDURO"}}, {"menuName": "Dinka Thrust", "data": {"action": "THRUST"}}, {"menuName": "Dinka Vindicator", "data": {"action": "VINDICATOR"}}, {"menuName": "LLC Avarus", "data": {"action": "AVARUS"}}, {"menuName": "LLC Hexer", "data": {"action": "HEXER"}}, {"menuName": "LLC Innovation", "data": {"action": "INNOVATION"}}, {"menuName": "LLC Sanctus", "data": {"action": "SANCTUS"}}, {"menuName": "Maibatsu Carbon RS", "data": {"action": "CARBONRS"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "CHIMERA"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "MANCHEZ"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "SANCHEZ"}}, {"menuName": "<PERSON><PERSON><PERSON> (Graphics)", "data": {"action": "SANCHEZ2"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "SHOTARO"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "BATI"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>(Race)", "data": {"action": "BATI2"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "ESSKEY"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "FAGGIO"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "FAGGIO3"}}, {"menuName": "Pegassi Faggio Sport", "data": {"action": "FAGGIO2"}}, {"menuName": "Pegassi FCR 1000", "data": {"action": "FCR"}}, {"menuName": "Pegassi FCR 1000 Custom", "data": {"action": "FCR2"}}, {"menuName": "Pegassi Oppressor", "data": {"action": "OPPRESSOR"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "RUFFIAN"}}, {"menuName": "Pegassi Vortex", "data": {"action": "VORTEX"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "DIABOLUS"}}, {"menuName": "<PERSON>pe Di<PERSON>olus Custom", "data": {"action": "DIABOLUS2"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "LECTRO"}}, {"menuName": "Principe Nemesis", "data": {"action": "NEMESIS"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "DEFILER"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "HAKUCHOU"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "HAKUCHOU2"}}, {"menuName": "Shitzu PCJ 600", "data": {"action": "PCJ"}}, {"menuName": "<PERSON><PERSON> Vader", "data": {"action": "VADER"}}, {"menuName": "Western Bagger", "data": {"action": "BAGGER"}}, {"menuName": "Western Cliffhanger", "data": {"action": "CLIFFHANGER"}}, {"menuName": "Western Daemon", "data": {"action": "DAEMON2"}}, {"menuName": "Western Daemon Custom", "data": {"action": "DAEMON"}}, {"menuName": "Western Gargoyle", "data": {"action": "GARGOYLE"}}, {"menuName": "Western Nightblade", "data": {"action": "NIGHTBLADE"}}, {"menuName": "Western Rat Bike", "data": {"action": "RATBIKE"}}, {"menuName": "Western Sovereign", "data": {"action": "SOVEREIGN"}}, {"menuName": "Western Wolfsbane", "data": {"action": "WOLFSBANE"}}, {"menuName": "Western Zombie Bobber", "data": {"action": "ZOMBIEA"}}, {"menuName": "Western Zombie Chopper", "data": {"action": "ZOMBIEB"}}], "Emergency": [{"menuName": "Albany Police Roadcruiser (Snow)", "data": {"action": "POLICEOLD2"}}, {"menuName": "Ambulance", "data": {"action": "AMBULANCE"}}, {"menuName": "Army Barracks Truck", "data": {"action": "BARRACKS"}}, {"menuName": "Army Truck Cab", "data": {"action": "BARRACKS2"}}, {"menuName": "Bravado Buffalo (FIB)", "data": {"action": "FBI"}}, {"menuName": "Bravado Half-track", "data": {"action": "HALFTRACK"}}, {"menuName": "Brute Police Riot Van", "data": {"action": "RIOT"}}, {"menuName": "<PERSON><PERSON> (Army Mesa)", "data": {"action": "CRUSADER"}}, {"menuName": "<PERSON><PERSON><PERSON> (FIB)", "data": {"action": "FBI2"}}, {"menuName": "Declasse Lifeguard", "data": {"action": "LGUARD"}}, {"menuName": "Declasse Park Ranger", "data": {"action": "PRANGER"}}, {"menuName": "Declasse Police Rancher (Snow)", "data": {"action": "POLICEOLD1"}}, {"menuName": "Declasse Police Transporter", "data": {"action": "POLICET"}}, {"menuName": "Declasse Sheriff SUV", "data": {"action": "SHERIFF2"}}, {"menuName": "Firetruck", "data": {"action": "FIRETRUK"}}, {"menuName": "HVY APC", "data": {"action": "APC"}}, {"menuName": "Prison Bus", "data": {"action": "PBUS"}}, {"menuName": "Rhino Tank", "data": {"action": "RHINO"}}, {"menuName": "Vapid Police Buffalo", "data": {"action": "POLICE2"}}, {"menuName": "Vapid Police Cruiser", "data": {"action": "POLICE"}}, {"menuName": "Vapid Police Interceptor", "data": {"action": "POLICE3"}}, {"menuName": "Vapid Sheriff Cruiser", "data": {"action": "SHERIFF"}}, {"menuName": "Vapid Unmarked Police Cruiser", "data": {"action": "POLICE4"}}, {"menuName": "Western Police Bike", "data": {"action": "POLICEB"}}], "Trains": [{"menuName": "Container Car 1", "data": {"action": "FREIGHTCONT1"}}, {"menuName": "Container Car 2", "data": {"action": "FREIGHTCONT2"}}, {"menuName": "Flatbed Car", "data": {"action": "FREIGHTCAR"}}, {"menuName": "Freight Train Cab", "data": {"action": "FREIGHT"}}, {"menuName": "Grain Car", "data": {"action": "FREIGHTGRAIN"}}, {"menuName": "Metro Train (Half)", "data": {"action": "METROTRAIN"}}, {"menuName": "Oil Tanker Car", "data": {"action": "TANKERCAR"}}], "Trailers": [{"menuName": "Army Flatbed Trailer", "data": {"action": "ARMYTRAILER"}}, {"menuName": "Army Flatbed Trailer (with <PERSON><PERSON>)", "data": {"action": "ARMYTRAILER2"}}, {"menuName": "Army Fuel Tanker", "data": {"action": "ARMYTANKER"}}, {"menuName": "Boat trailer (small)", "data": {"action": "BOATTRAILER"}}, {"menuName": "Boat Trailer (With Boat)", "data": {"action": "TR3"}}, {"menuName": "Car Tranport Trailer", "data": {"action": "TR4"}}, {"menuName": "Car Tranport Trailer (Empty)", "data": {"action": "TR2"}}, {"menuName": "Commercial Trailer (Graphics 1)", "data": {"action": "TRAILERS2"}}, {"menuName": "Commercial Trailer (Graphics 2)", "data": {"action": "TRAILERS3"}}, {"menuName": "Container Trailer", "data": {"action": "DOCKTRAILER"}}, {"menuName": "Container Trailer (Gunrunning)", "data": {"action": "TRAILERS4"}}, {"menuName": "Fame or Shame Trailer", "data": {"action": "TVTRAILER"}}, {"menuName": "Flatbed Trailer", "data": {"action": "TRFLAT"}}, {"menuName": "Flatbed Trailer", "data": {"action": "FREIGHTTRAILER"}}, {"menuName": "<PERSON><PERSON> Trailer", "data": {"action": "GRAINTRAILER"}}, {"menuName": "<PERSON> Trailer", "data": {"action": "BALETRAILER"}}, {"menuName": "Logging Trailer", "data": {"action": "TRAILERLOGS"}}, {"menuName": "Meth Lab Trailer", "data": {"action": "PROPTRAILER"}}, {"menuName": "Mobile Operations Center (Non-Functional)", "data": {"action": "TRAILERLARGE"}}, {"menuName": "<PERSON><PERSON> Tanker Trailer (Plain)", "data": {"action": "TANKER2"}}, {"menuName": "<PERSON><PERSON> (Ron)", "data": {"action": "TANKER"}}, {"menuName": "Plain Trailer", "data": {"action": "TRAILERS"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "RAKETRAILER"}}, {"menuName": "Small Trailer", "data": {"action": "TRAILERSMALL"}}, {"menuName": "Vom Feuer Anti-Aircraft Trailer", "data": {"action": "TRAILERSMALL2"}}], "Service": [{"menuName": "Airtug", "data": {"action": "AIRTUG"}}, {"menuName": "Brute Airport Bus", "data": {"action": "AIRBUS"}}, {"menuName": "Brute Bus (City Bus)", "data": {"action": "BUS"}}, {"menuName": "Brute Rental Shuttle Bus", "data": {"action": "RENTALBUS"}}, {"menuName": "Brute Tourbus", "data": {"action": "TOURBUS"}}, {"menuName": "Cable Car (Mt. Chilliad)", "data": {"action": "CABLECAR"}}, {"menuName": "Dashound Coach Bus", "data": {"action": "COACH"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "BULLDOZER"}}, {"menuName": "Forklift", "data": {"action": "FORKLIFT"}}, {"menuName": "HVY Brickade", "data": {"action": "BRICKADE"}}, {"menuName": "Jobuilt Trashmaster", "data": {"action": "TRASH"}}, {"menuName": "<PERSON><PERSON> Trashmaster (<PERSON>)", "data": {"action": "TRASH2"}}, {"menuName": "MTL Dune", "data": {"action": "DUNE"}}, {"menuName": "Nagasaki Caddy", "data": {"action": "CADDY"}}, {"menuName": "<PERSON><PERSON><PERSON> (Bunker)", "data": {"action": "CADDY3"}}, {"menuName": "<PERSON><PERSON><PERSON> (Golf)", "data": {"action": "CADDY2"}}, {"menuName": "<PERSON><PERSON><PERSON> (Airpot Tug)", "data": {"action": "RIPLEY"}}, {"menuName": "Stanley Fieldmaster Tractor", "data": {"action": "TRACTOR2"}}, {"menuName": "<PERSON>master <PERSON> (Snow)", "data": {"action": "TRACTOR3"}}, {"menuName": "Stanley Lawn Mower", "data": {"action": "MOWER"}}, {"menuName": "<PERSON> (Rusty)", "data": {"action": "tractor"}}, {"menuName": "Vapid Pickup Utility", "data": {"action": "UTILLITRUCK"}}, {"menuName": "Vapid Plumbing Utility", "data": {"action": "UTILLITRUCK2"}}, {"menuName": "Vapid Telephone Utlity", "data": {"action": "UTILLITRUCK3"}}], "Trucks": [{"menuName": "Brute Armored Boxville", "data": {"action": "BOXVILLE5"}}, {"menuName": "<PERSON><PERSON><PERSON> (Go Postal)", "data": {"action": "BOXVILLE2"}}, {"menuName": "<PERSON><PERSON><PERSON> (Humane Labs)", "data": {"action": "BOXVILLE3"}}, {"menuName": "<PERSON><PERSON><PERSON> (Post OP)", "data": {"action": "BOXVILLE4"}}, {"menuName": "<PERSON><PERSON><PERSON> (Water & Power)", "data": {"action": "BOXVILLE"}}, {"menuName": "Brute <PERSON>ade", "data": {"action": "STOCKADE"}}, {"menuName": "<PERSON><PERSON><PERSON> (Snow)", "data": {"action": "STOCKADE3"}}, {"menuName": "<PERSON><PERSON><PERSON> (2 Axle)", "data": {"action": "TIPTRUCK"}}, {"menuName": "<PERSON><PERSON><PERSON> (3 Axle)", "data": {"action": "TIPTRUCK2"}}, {"menuName": "Cutter", "data": {"action": "CUTTER"}}, {"menuName": "Dock Handler", "data": {"action": "HANDLER"}}, {"menuName": "Dock Tug", "data": {"action": "DOCKTUG"}}, {"menuName": "Dump Truck", "data": {"action": "DUMP"}}, {"menuName": "HVY Biff", "data": {"action": "BIFF"}}, {"menuName": "<PERSON><PERSON>r", "data": {"action": "HAULER"}}, {"menuName": "Jobuilt Hauler Custom", "data": {"action": "HAULER2"}}, {"menuName": "Jobuilt Phantom", "data": {"action": "PHANTOM"}}, {"menuName": "Jobuilt Phantom Custom", "data": {"action": "PHANTOM3"}}, {"menuName": "Jobuilt Phantom Wedge", "data": {"action": "PHANTOM2"}}, {"menuName": "Jobuilt Rubble", "data": {"action": "RUBBLE"}}, {"menuName": "<PERSON><PERSON><PERSON> (Graphics 1)", "data": {"action": "MULE"}}, {"menuName": "<PERSON><PERSON><PERSON> (Graphics 2)", "data": {"action": "MULE2"}}, {"menuName": "<PERSON><PERSON><PERSON> (Plain)", "data": {"action": "MULE3"}}, {"menuName": "Mixer", "data": {"action": "MIXER"}}, {"menuName": "Mixer (Support Wheel)", "data": {"action": "MIXER2"}}, {"menuName": "MTL Flatbed Truck", "data": {"action": "FLATBED"}}, {"menuName": "MTL Packer", "data": {"action": "PACKER"}}, {"menuName": "MTL Pounder", "data": {"action": "POUNDER"}}, {"menuName": "MTL Wastelander", "data": {"action": "WASTELANDER"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "BENSON"}}, {"menuName": "Vapid Scrap Truck", "data": {"action": "SCRAP"}}, {"menuName": "Vapid Tow Truck", "data": {"action": "TOWTRUCK"}}, {"menuName": "Vapid Tow Truck (Old)", "data": {"action": "TOWTRUCK2"}}], "Vans": [{"menuName": "BF Surfer", "data": {"action": "SURFER"}}, {"menuName": "<PERSON><PERSON> (Rusty)", "data": {"action": "SURFER2"}}, {"menuName": "Bravado Paradise", "data": {"action": "PARADISE"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON> (Deludamol)", "data": {"action": "RUMPO2"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON> (Weazel News)", "data": {"action": "RUMPO"}}, {"menuName": "Bravado Rumpo Custom", "data": {"action": "RUMPO3"}}, {"menuName": "Brava<PERSON>", "data": {"action": "YOUGA"}}, {"menuName": "Bravado Youga Classic", "data": {"action": "YOUGA2"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "CAMPER"}}, {"menuName": "<PERSON><PERSON><PERSON> (Business)", "data": {"action": "PONY"}}, {"menuName": "Brute Pony (Cannibus Shop)", "data": {"action": "PONY2"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "TACO"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "BURRITO3"}}, {"menuName": "<PERSON><PERSON><PERSON> (Bug Stars)", "data": {"action": "BURRITO2"}}, {"menuName": "<PERSON><PERSON><PERSON> (Construction)", "data": {"action": "BURRITO4"}}, {"menuName": "<PERSON><PERSON><PERSON> (Gang)", "data": {"action": "GBURRITO2"}}, {"menuName": "<PERSON><PERSON><PERSON> (Graphics)", "data": {"action": "BURRITO"}}, {"menuName": "<PERSON><PERSON><PERSON> (Snow)", "data": {"action": "BURRITO5"}}, {"menuName": "<PERSON><PERSON><PERSON> (The Lost)", "data": {"action": "GBURRITO"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "MOONBEAM"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "MINIVAN"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "SPEEDO"}}, {"menuName": "<PERSON><PERSON><PERSON> (Clown)", "data": {"action": "SPEEDO2"}}, {"menuName": "Zirconium Journey", "data": {"action": "JOURNEY"}}], "Pickups": [{"menuName": "Bravado <PERSON>", "data": {"action": "BISON"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON> (Backrack)", "data": {"action": "BISON2"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON> (Construction)", "data": {"action": "BISON3"}}, {"menuName": "Bravado Ratloader", "data": {"action": "RATLOADER2"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON> (Rusty)", "data": {"action": "RATLOADER"}}, {"menuName": "Vapid Bobcat", "data": {"action": "BOBCATXL"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "SADLER"}}, {"menuName": "<PERSON><PERSON><PERSON> (Snow)", "data": {"action": "SADLER2"}}], "VIP": [{"menuName": "Benefactor Schafter LWB", "data": {"action": "SCHAFTER4"}}, {"menuName": "Benefactor <PERSON><PERSON>er LWB (Armored)", "data": {"action": "SCHAFTER6"}}, {"menuName": "Benefactor Schafter V12", "data": {"action": "SCHAFTER3"}}, {"menuName": "Benefactor Schafter V12 (Armored)", "data": {"action": "SCHAFTER5"}}, {"menuName": "Benefactor <PERSON><PERSON><PERSON>", "data": {"action": "LIMO2"}}, {"menuName": "Benefactor XLS", "data": {"action": "XLS"}}, {"menuName": "Benefactor <PERSON><PERSON> (Armored)", "data": {"action": "XLS2"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "COGNOSCENTI"}}, {"menuName": "<PERSON><PERSON> (Armored)", "data": {"action": "COGNOSCENTI2"}}, {"menuName": "<PERSON><PERSON> 55", "data": {"action": "COG552"}}, {"menuName": "Enus Co<PERSON> 55 (Armored)", "data": {"action": "COG552"}}, {"menuName": "Gallivanter Baller LE", "data": {"action": "BALLER3"}}, {"menuName": "Gallivanter Baller LE (Armored)", "data": {"action": "BALLER5"}}, {"menuName": "Gallivanter Baller LE LWB", "data": {"action": "BALLER4"}}, {"menuName": "Gallivanter Baller LE LWB (Armored)", "data": {"action": "BALLER6"}}], "Offroad": [{"menuName": "Benefactor Dubsta 6x6", "data": {"action": "DUBSTA3"}}, {"menuName": "BF Bifta", "data": {"action": "BIFTA"}}, {"menuName": "BF Dune FAV", "data": {"action": "DUNE3"}}, {"menuName": "BF Injection", "data": {"action": "BFINJECTION"}}, {"menuName": "BF Ramp Buggy", "data": {"action": "DUNE5"}}, {"menuName": "<PERSON><PERSON> <PERSON><PERSON> (Spoiler)", "data": {"action": "DUNE4"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "DUNE"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "DLOADER"}}, {"menuName": "Bravado Space Docker", "data": {"action": "DUNE2"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "BODHI2"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "KALAHARI"}}, {"menuName": "<PERSON>is <PERSON> (Off-Road)", "data": {"action": "MESA3"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "MARSHALL"}}, {"menuName": "Coil Brawler", "data": {"action": "BRAWLER"}}, {"menuName": "Declasse Rancher XL", "data": {"action": "RANCHERXL"}}, {"menuName": "Declasse Rancher <PERSON> (Snow)", "data": {"action": "RANCHERXL2"}}, {"menuName": "HVY Insurgent Pick-Up Custom", "data": {"action": "INSURGENT3"}}, {"menuName": "HVY Nightshark", "data": {"action": "NIGHTSHARK"}}, {"menuName": "Insurgent", "data": {"action": "INSURGENT"}}, {"menuName": "Insurgent (Gun Mount)", "data": {"action": "INSURGENT2"}}, {"menuName": "<PERSON>", "data": {"action": "REBEL"}}, {"menuName": "<PERSON> (Rusty)", "data": {"action": "REBEL2"}}, {"menuName": "Karin <PERSON>", "data": {"action": "TECHNICAL"}}, {"menuName": "Karin Technical Aqua", "data": {"action": "TECHNICAL2"}}, {"menuName": "Karin Technical Custom", "data": {"action": "TECHNICAL3"}}, {"menuName": "Nagasaki Blazer", "data": {"action": "BLAZER"}}, {"menuName": "<PERSON><PERSON><PERSON> (Hot Rod)", "data": {"action": "BLAZER3"}}, {"menuName": "<PERSON><PERSON><PERSON> Blazer (Lifeguard)", "data": {"action": "BLAZER2"}}, {"menuName": "Nagasaki Blazer (Street)", "data": {"action": "BLAZER4"}}, {"menuName": "Nagasaki Blazer Aqua", "data": {"action": "BLAZER5"}}, {"menuName": "Vapid Desert Raid", "data": {"action": "TROPHYTRUCK2"}}, {"menuName": "Vapid Guardian", "data": {"action": "GUARDIAN"}}, {"menuName": "Vapid Liberator", "data": {"action": "MONSTER"}}, {"menuName": "Vapid Sandking", "data": {"action": "SANDKING2"}}, {"menuName": "Vapid Sandking XL", "data": {"action": "SANDKING"}}, {"menuName": "Vapid Trophy Truck", "data": {"action": "TROPHYTRUCK"}}], "SUVs": [{"menuName": "Albany Cavalcade", "data": {"action": "CAVALCADE"}}, {"menuName": "Albany Cavalcade Mk2", "data": {"action": "CAVALCADE2"}}, {"menuName": "Benefactor <PERSON><PERSON>", "data": {"action": "DUBSTA"}}, {"menuName": "Benefactor <PERSON><PERSON> (Flat Black)", "data": {"action": "DUBSTA2"}}, {"menuName": "Benefactor <PERSON><PERSON>", "data": {"action": "SERRANO"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "GRESLEY"}}, {"menuName": "Canis <PERSON>", "data": {"action": "MESA"}}, {"menuName": "<PERSON><PERSON> (Snow)", "data": {"action": "MESA2"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "SEMINOLE"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "GRANGER"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "LANDSTALKER"}}, {"menuName": "Emperor <PERSON><PERSON><PERSON>", "data": {"action": "HABANERO"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "HUNTLEY"}}, {"menuName": "Fathom FQ 2", "data": {"action": "FQ2"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON> (Large)", "data": {"action": "BALLER"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON> (Small)", "data": {"action": "BALLER2"}}, {"menuName": "<PERSON>L", "data": {"action": "BJXL"}}, {"menuName": "Mammoth Patriot", "data": {"action": "PATRIOT"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "ROCOTO"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "CONTENDER"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "RADI"}}], "Compacts": [{"menuName": "Benefactor <PERSON><PERSON>", "data": {"action": "PANTO"}}, {"menuName": "Bollokan Prairie", "data": {"action": "PRAIRIE"}}, {"menuName": "Declasse Rhapsody", "data": {"action": "RHAPSODY"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "BLISTA"}}, {"menuName": "Grotti Brioso R/A", "data": {"action": "BRIOSO"}}, {"menuName": "<PERSON>", "data": {"action": "DILETTANTE"}}, {"menuName": "<PERSON> (FlyUS)", "data": {"action": "DILETTANTE2"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "ISSI2"}}], "Sedans": [{"menuName": "Albany Emperor", "data": {"action": "EMPEROR"}}, {"menuName": "<PERSON> (<PERSON>)", "data": {"action": "EMPEROR2"}}, {"menuName": "Albany Emperor (Snow)", "data": {"action": "EMPEROR3"}}, {"menuName": "Albany Primo", "data": {"action": "PRIMO"}}, {"menuName": "Albany Washington", "data": {"action": "WASHINGTON"}}, {"menuName": "Benefactor <PERSON><PERSON>", "data": {"action": "GLENDALE"}}, {"menuName": "Benefactor <PERSON><PERSON><PERSON>", "data": {"action": "SCHAFTER2"}}, {"menuName": "Chariot <PERSON>", "data": {"action": "ROMERO"}}, {"menuName": "Cheval Fugitive", "data": {"action": "FUGITIVE"}}, {"menuName": "Cheval <PERSON>", "data": {"action": "SURGE"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "ASEA"}}, {"menuName": "<PERSON><PERSON><PERSON> (snow)", "data": {"action": "ASEA2"}}, {"menuName": "Declasse Premier", "data": {"action": "PREMIER"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "REGINA"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "STRETCH"}}, {"menuName": "Enus Super Diamond", "data": {"action": "SUPERD"}}, {"menuName": "<PERSON>", "data": {"action": "ASTEROPE"}}, {"menuName": "Karin <PERSON>", "data": {"action": "INTRUDER"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "TAILGATER"}}, {"menuName": "Ubermacht Oracle", "data": {"action": "ORACLE"}}, {"menuName": "Ubermacht Oracle Mk2", "data": {"action": "ORACLE2"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "STANIER"}}, {"menuName": "<PERSON><PERSON><PERSON> (Taxi)", "data": {"action": "TAXI"}}, {"menuName": "Vulcan Ingot", "data": {"action": "INGOT"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "WARRENER"}}, {"menuName": "Zirconium Stratum", "data": {"action": "STRATUM"}}], "Coupes": [{"menuName": "Dewbauchee Exemplar", "data": {"action": "EXEMPLAR"}}, {"menuName": "<PERSON><PERSON> Cabrio", "data": {"action": "COGCABRIO"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "WINDSOR"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "WINDSOR2"}}, {"menuName": "Lampadati Felon", "data": {"action": "FELON"}}, {"menuName": "Lampadati Felon GT", "data": {"action": "FELON2"}}, {"menuName": "Ocelot F620", "data": {"action": "F620"}}, {"menuName": "<PERSON><PERSON><PERSON> Jack<PERSON>", "data": {"action": "JACKAL"}}, {"menuName": "Ubermacht Sentinel", "data": {"action": "SENTINEL"}}, {"menuName": "Ubermacht Sentinel XS", "data": {"action": "SENTINEL2"}}, {"menuName": "Ubermacht Zion", "data": {"action": "ORACLE"}}, {"menuName": "Ubermacht Zion Cabrio", "data": {"action": "ORACLE2"}}], "Lowriders": [{"menuName": "Albany Buccaneer", "data": {"action": "BUCCANEER"}}, {"menuName": "Albany Primo", "data": {"action": "PRIMO"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "MOONBEAM"}}, {"menuName": "Declasse Sabre Turbo", "data": {"action": "SABREGT"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "TORNADO"}}, {"menuName": "Declasse Voodoo", "data": {"action": "VOODOO2"}}, {"menuName": "Dundreary Virgo Classic", "data": {"action": "VIRGO3"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "CHINO2"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "MINIVAN"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "SLAMVAN"}}, {"menuName": "Willard Faction", "data": {"action": "FACTION2"}}, {"menuName": "Willard Faction Donk", "data": {"action": "FACTION3"}}], "Muscle": [{"menuName": "Albany Buccaneer", "data": {"action": "BUCCANEER"}}, {"menuName": "Albany <PERSON> Strange", "data": {"action": "BTYPE2"}}, {"menuName": "Albany Lurcher", "data": {"action": "LURCHER"}}, {"menuName": "Albany Virgo", "data": {"action": "VIRGO"}}, {"menuName": "Bravado Gauntlet", "data": {"action": "GAUNTLET"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON> (Race)", "data": {"action": "GAUNTLET2"}}, {"menuName": "Cheval Picador", "data": {"action": "PICADOR"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "MAMBA"}}, {"menuName": "Declasse Sabre Turbo", "data": {"action": "SABREGT"}}, {"menuName": "<PERSON><PERSON><PERSON> (Race)", "data": {"action": "STALION2"}}, {"menuName": "Declasse Stallion", "data": {"action": "STALION"}}, {"menuName": "Declasse Tampa", "data": {"action": "TAMPA"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "VIGERO"}}, {"menuName": "Declasse Voodoo", "data": {"action": "VOODOO2"}}, {"menuName": "Declasse Weaponized Tampa", "data": {"action": "TAMPA3"}}, {"menuName": "Dundreary Virgo Classic", "data": {"action": "VIRGO"}}, {"menuName": "<PERSON><PERSON>nte Duke <PERSON>", "data": {"action": "DUKES2"}}, {"menuName": "Imponte Dukes", "data": {"action": "DUKES"}}, {"menuName": "Imponte Nightshade", "data": {"action": "NIGHTSHADE"}}, {"menuName": "Imponte Pheonix", "data": {"action": "PHOENIX"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "RUINER"}}, {"menuName": "Imponte Ruiner 2000", "data": {"action": "RUINER2"}}, {"menuName": "Invetero Coquette BlackFin", "data": {"action": "COQUETTE3"}}, {"menuName": "Vapid Blade", "data": {"action": "BLADE"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "CHINO"}}, {"menuName": "Vapid Dominator", "data": {"action": "DOMINATOR"}}, {"menuName": "<PERSON><PERSON><PERSON> (Race)", "data": {"action": "DOMINATOR2"}}, {"menuName": "Vapid Hotknife", "data": {"action": "HOTKNIFE"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "SLAMVAN"}}, {"menuName": "<PERSON><PERSON><PERSON> (Lost MC)", "data": {"action": "SLAMVAN2"}}, {"menuName": "Willard Faction", "data": {"action": "FACTION"}}], "Sports Classics": [{"menuName": "Albany Manana", "data": {"action": "MANANA"}}, {"menuName": "Albany Roosevelt 1", "data": {"action": "BTYPE"}}, {"menuName": "Albany Roosevelt 2", "data": {"action": "BTYPE3"}}, {"menuName": "Benefactor Stirling GT", "data": {"action": "FELTZER3"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "TORNADO"}}, {"menuName": "<PERSON><PERSON><PERSON> (Rusty)", "data": {"action": "TORNADO2"}}, {"menuName": "Declasse <PERSON>", "data": {"action": "TORNADO3"}}, {"menuName": "<PERSON><PERSON><PERSON> (Rusty)", "data": {"action": "TORNADO4"}}, {"menuName": "Declasse <PERSON>", "data": {"action": "TORNADO6"}}, {"menuName": "Dewbauchee JB 700", "data": {"action": "JB700"}}, {"menuName": "Grotti Cheetah Classic", "data": {"action": "CHEETAH2"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "STINGER"}}, {"menuName": "<PERSON><PERSON><PERSON> Stinger GT", "data": {"action": "STINGERGT"}}, {"menuName": "Grotti Turismo Classic", "data": {"action": "TURISMO2"}}, {"menuName": "Invetero Coquette Classic", "data": {"action": "COQUETTE2"}}, {"menuName": "Lampadati Casco", "data": {"action": "CASCO"}}, {"menuName": "Lampadati Pigalle", "data": {"action": "PIGALLE"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "ARDENT"}}, {"menuName": "<PERSON>eg<PERSON>i <PERSON>", "data": {"action": "INFERNUS2"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "MONROE"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "TORERO"}}, {"menuName": "Truffade Z-Type", "data": {"action": "ZTYPE"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "PEYOTE"}}], "Sports": [{"menuName": "Albany Alpha", "data": {"action": "ALPHA"}}, {"menuName": "Annis Elegy Retro Custom", "data": {"action": "ELEGY"}}, {"menuName": "Annis Elegy RH8", "data": {"action": "ELEGY2"}}, {"menuName": "Benefactor <PERSON><PERSON><PERSON>", "data": {"action": "FELTZER2"}}, {"menuName": "Benefactor <PERSON><PERSON><PERSON><PERSON>", "data": {"action": "SCHWARZER"}}, {"menuName": "Benefactor <PERSON><PERSON>", "data": {"action": "SURANO"}}, {"menuName": "BF Raptor", "data": {"action": "RAPTOR"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "BANSHEE"}}, {"menuName": "Bravado Buffalo", "data": {"action": "BUFFALO"}}, {"menuName": "Bravado Buffalo S", "data": {"action": "BUFFALO2"}}, {"menuName": "Bravado <PERSON> S (Race)", "data": {"action": "BUFFALO3"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "VERLIERER2"}}, {"menuName": "Declasse Drift Tampa", "data": {"action": "TAMPA2"}}, {"menuName": "Dewbauchee Massacro", "data": {"action": "MASSACRO"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Race)", "data": {"action": "MASSACRO2"}}, {"menuName": "Dewbauchee Rapid GT", "data": {"action": "RAPIDGT"}}, {"menuName": "Dewbauchee Rapid GT Cabrio", "data": {"action": "RAPIDGT2"}}, {"menuName": "Dewbauchee Seven-70", "data": {"action": "SEVEN70"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON><PERSON> Specter", "data": {"action": "SPECTER"}}, {"menuName": "Dewbauchee Specter Custom", "data": {"action": "SPECTER2"}}, {"menuName": "Dinka Blista Compact", "data": {"action": "BLISTA2"}}, {"menuName": "<PERSON><PERSON> (Race)", "data": {"action": "BLISTA3"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "JESTER"}}, {"menuName": "<PERSON><PERSON> (Race)", "data": {"action": "JESTER2"}}, {"menuName": "Grotti Bestia GTS", "data": {"action": "BESTIAGTS"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "CARBONIZZARE"}}, {"menuName": "<PERSON><PERSON>lion", "data": {"action": "KHAMELION"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "RUSTON"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "COQUETTE"}}, {"menuName": "<PERSON>", "data": {"action": "FUTO"}}, {"menuName": "<PERSON>", "data": {"action": "KURUMA"}}, {"menuName": "<PERSON> (Armoured)", "data": {"action": "KURUMA2"}}, {"menuName": "<PERSON>", "data": {"action": "SULTAN"}}, {"menuName": "Lampadati Furore GT", "data": {"action": "FUROREGT"}}, {"menuName": "Lampadati Tropos Rallye", "data": {"action": "TROPOS"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "PENUMBRA"}}, {"menuName": "Obey 9F", "data": {"action": "NINEF"}}, {"menuName": "Obey 9F Cabrio", "data": {"action": "NINEF2"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "OMNIS"}}, {"menuName": "Ocelot Lynx", "data": {"action": "LYNX"}}, {"menuName": "Pfister Comet Retro Custom", "data": {"action": "COMET3"}}, {"menuName": "<PERSON><PERSON> Comet", "data": {"action": "COMET2"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "FUSILADE"}}], "Supercars": [{"menuName": "Annis RE-7B", "data": {"action": "LE7B"}}, {"menuName": "Bravado Banshee 900R", "data": {"action": "BANSHEE2"}}, {"menuName": "Coil Rocket Voltic", "data": {"action": "VOLTIC2"}}, {"menuName": "Coil Voltic", "data": {"action": "VOLTIC"}}, {"menuName": "Dewbauchee Vagner", "data": {"action": "VAGNER"}}, {"menuName": "Emperor ETR1", "data": {"action": "SHEAVA"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "CHEETAH"}}, {"menuName": "Grotti Turismo R", "data": {"action": "TURISMOR"}}, {"menuName": "Grotti X80 Proto", "data": {"action": "PROTOTIPO"}}, {"menuName": "<PERSON>", "data": {"action": "SULTANRS"}}, {"menuName": "Ocelot Penetrator", "data": {"action": "PENETRATOR"}}, {"menuName": "Ocelot XA-21", "data": {"action": "XA21"}}, {"menuName": "Overflod Entity XF", "data": {"action": "ENTITYXF"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "INFERNUS"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "OSIRIS"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "REAPER"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "TEMPESTA"}}, {"menuName": "<PERSON>eg<PERSON><PERSON>", "data": {"action": "VACCA"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "ZENTORNO"}}, {"menuName": "Pfister 811", "data": {"action": "PFISTER811"}}, {"menuName": "Progen GP1", "data": {"action": "GP1"}}, {"menuName": "Progen Itali GTB", "data": {"action": "ITALIGTB"}}, {"menuName": "Progen Itali GTB Custom", "data": {"action": "ITALIGTB2"}}, {"menuName": "Progen T20", "data": {"action": "T20"}}, {"menuName": "Progen Tyrus", "data": {"action": "TYRUS"}}, {"menuName": "Truffade Adder", "data": {"action": "ADDER"}}, {"menuName": "Truff<PERSON> Nero", "data": {"action": "NERO"}}, {"menuName": "Truffade Nero Custom", "data": {"action": "NERO2"}}, {"menuName": "Vapid Bullet", "data": {"action": "BULLET"}}, {"menuName": "Vapid FMJ", "data": {"action": "FMJ"}}]}, "Skins": {"NPC List": [{"menuName": "<PERSON>", "data": {"action": "ig_abigail"}}, {"menuName": "<PERSON>", "data": {"action": "csb_abigail"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "u_m_y_abner"}}, {"menuName": "African American Male", "data": {"action": "a_m_m_afriamer_01"}}, {"menuName": "Air Hostess", "data": {"action": "s_f_y_airhostess_01"}}, {"menuName": "Air Worker", "data": {"action": "s_m_y_airworker"}}, {"menuName": "Al Di Napoli", "data": {"action": "u_m_m_aldinapoli"}}, {"menuName": "Alien", "data": {"action": "s_m_m_movalien_01"}}, {"menuName": "Altru<PERSON> <PERSON><PERSON> Male", "data": {"action": "a_m_o_acult_01"}}, {"menuName": "Altruist Cult Old Male 2", "data": {"action": "a_m_o_acult_02"}}, {"menuName": "Alt<PERSON><PERSON> <PERSON><PERSON>", "data": {"action": "a_m_y_acult_01"}}, {"menuName": "Altruist Cult Young Male 2", "data": {"action": "a_m_y_acult_02"}}, {"menuName": "Altruist cult Mid-Age Male", "data": {"action": "a_m_m_acult_01"}}, {"menuName": "<PERSON> - In-game", "data": {"action": "ig_amanda<PERSON>ley"}}, {"menuName": "<PERSON> - Cutscene -BROKEN", "data": {"action": "cs_amanda<PERSON>ley"}}, {"menuName": "Ammu-Nation Clerk - City", "data": {"action": "s_m_y_ammucity_01"}}, {"menuName": "Ammu-Nation Clerk - Country", "data": {"action": "s_m_m_ammucountry"}}, {"menuName": "Andreas - In-game", "data": {"action": "ig_andreas"}}, {"menuName": "Andreas - Cutscene - BROKEN", "data": {"action": "cs_andreas"}}, {"menuName": "<PERSON>", "data": {"action": "csb_anita"}}, {"menuName": "<PERSON> - Cutscene", "data": {"action": "csb_anton"}}, {"menuName": "<PERSON> - In-Game", "data": {"action": "u_m_y_antonb"}}, {"menuName": "Armenian Mob Boss", "data": {"action": "g_m_m_armboss_01"}}, {"menuName": "Armenian Mob Goon 1", "data": {"action": "g_m_m_armgoon_01"}}, {"menuName": "Armenian Mob Goon 2", "data": {"action": "g_m_y_armgoon_02"}}, {"menuName": "Armenian Mob lieutenant 1", "data": {"action": "g_m_m_armlieut_01"}}, {"menuName": "Security - Armoured - MP", "data": {"action": "mp_s_m_armoured_01"}}, {"menuName": "Security - Armoured - SP 1", "data": {"action": "s_m_m_armoured_01"}}, {"menuName": "Security - Armoured - SP 2", "data": {"action": "s_m_m_armoured_02"}}, {"menuName": "Army Mechanic", "data": {"action": "s_m_y_armymech_01"}}, {"menuName": "<PERSON> - In-game", "data": {"action": "ig_ashley"}}, {"menuName": "<PERSON> - Cutscene - BROKEN", "data": {"action": "cs_ashley"}}, {"menuName": "Autopsy", "data": {"action": "s_m_y_autopsy_01"}}, {"menuName": "Autoshop", "data": {"action": "s_m_m_autoshop_01"}}, {"menuName": "Autoshop", "data": {"action": "s_m_m_autoshop_02"}}, {"menuName": "Azteca Gangster 1", "data": {"action": "g_m_y_azteca_01"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "u_m_y_babyd"}}, {"menuName": "Balla East Territory 1", "data": {"action": "g_m_y_ballaeast_01"}}, {"menuName": "Balla Original Territory 1", "data": {"action": "g_m_y_ballaorig_01"}}, {"menuName": "Ballas", "data": {"action": "g_f_y_ballas_01"}}, {"menuName": "Ballas OG - Cutscene", "data": {"action": "csb_ballasog"}}, {"menuName": "Ballas OG - In-game", "data": {"action": "ig_ballasog"}}, {"menuName": "Balla South Territory 1", "data": {"action": "g_m_y_ballasout_01"}}, {"menuName": "Bankman", "data": {"action": "ig_bankman"}}, {"menuName": "Bankman", "data": {"action": "u_m_m_bankman"}}, {"menuName": "Bankman-BROKEN", "data": {"action": "cs_bankman"}}, {"menuName": "Barman", "data": {"action": "s_m_y_barman_01"}}, {"menuName": "<PERSON>", "data": {"action": "ig_barry"}}, {"menuName": "Barry-BROKEN", "data": {"action": "cs_barry"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "ig_barry_p"}}, {"menuName": "<PERSON>_P-<PERSON><PERSON><PERSON>", "data": {"action": "cs_barry_p"}}, {"menuName": "Bartender", "data": {"action": "s_f_y_bartender_01"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "u_m_y_baygor"}}, {"menuName": "Baywatch", "data": {"action": "s_f_y_baywatch_01"}}, {"menuName": "Baywatch", "data": {"action": "s_m_y_baywatch_01"}}, {"menuName": "Beach Female", "data": {"action": "a_f_m_beach_01"}}, {"menuName": "Beach Male", "data": {"action": "a_m_m_beach_01"}}, {"menuName": "Beach Male 2", "data": {"action": "a_m_m_beach_02"}}, {"menuName": "Beach Muscle Male", "data": {"action": "a_m_y_musclbeac_01"}}, {"menuName": "Beach Muscle Male 2", "data": {"action": "a_m_y_musclbeac_02"}}, {"menuName": "Beach Old Male", "data": {"action": "a_m_o_beach_01"}}, {"menuName": "Beach Tramp Female", "data": {"action": "a_f_m_trampbeac_01"}}, {"menuName": "Beach Tramp Male", "data": {"action": "a_m_m_trampbeac_01"}}, {"menuName": "Beach Young Female", "data": {"action": "a_f_y_beach_01"}}, {"menuName": "Beach Young Male", "data": {"action": "a_m_y_beach_01"}}, {"menuName": "Beach Young Male 2", "data": {"action": "a_m_y_beach_02"}}, {"menuName": "Beach Young Male 3", "data": {"action": "a_m_y_beach_03"}}, {"menuName": "Bestmen", "data": {"action": "ig_bestmen"}}, {"menuName": "Beverly", "data": {"action": "ig_beverly"}}, {"menuName": "Beverly Hills Female", "data": {"action": "a_f_m_bevhills_01"}}, {"menuName": "Beverly Hills Female 2", "data": {"action": "a_f_m_bevhills_02"}}, {"menuName": "Beverly Hills Male", "data": {"action": "a_m_m_bevhills_01"}}, {"menuName": "Beverly Hills Male 2", "data": {"action": "a_m_m_bevhills_02"}}, {"menuName": "Beverly Hills Young Female", "data": {"action": "a_f_y_bevhills_01"}}, {"menuName": "Beverly Hills Young Female 2", "data": {"action": "a_f_y_bevhills_02"}}, {"menuName": "Beverly Hills Young Female 3", "data": {"action": "a_f_y_bevhills_03"}}, {"menuName": "Beverly Hills Young Female 4", "data": {"action": "a_f_y_bevhills_04"}}, {"menuName": "Beverly Hills Young Male", "data": {"action": "a_m_y_bevhills_01"}}, {"menuName": "Beverly Hills Young Male 2", "data": {"action": "a_m_y_bevhills_02"}}, {"menuName": "Beverly-BROKEN", "data": {"action": "cs_beverly"}}, {"menuName": "<PERSON>_<PERSON>", "data": {"action": "ig_beverly_p"}}, {"menuName": "Beverly_P-<PERSON><PERSON><PERSON>", "data": {"action": "cs_beverly_p"}}, {"menuName": "Bikehire", "data": {"action": "u_m_m_bikehire_01"}}, {"menuName": "Bikerch<PERSON>", "data": {"action": "u_f_y_bikerchic"}}, {"menuName": "Black Street Male 1", "data": {"action": "a_m_y_stbla_01"}}, {"menuName": "Black Street Male 2", "data": {"action": "a_m_y_stbla_02"}}, {"menuName": "Black Ops Soldier 1", "data": {"action": "s_m_y_blackops_01"}}, {"menuName": "Black Ops Soldier 2", "data": {"action": "s_m_y_blackops_02"}}, {"menuName": "Bodybuilder Female", "data": {"action": "a_f_m_bodybuild_01"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "s_m_m_bouncer_01"}}, {"menuName": "<PERSON>", "data": {"action": "ig_brad"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data": {"action": "cs_brad"}}, {"menuName": "Bradcadaver-BROKEN", "data": {"action": "cs_bradcadaver"}}, {"menuName": "Breakdancer Male", "data": {"action": "a_m_y_breakdance_01"}}, {"menuName": "Bride", "data": {"action": "ig_bride"}}, {"menuName": "Bride-<PERSON><PERSON><PERSON>", "data": {"action": "csb_bride"}}, {"menuName": "Burger Drug Worker", "data": {"action": "csb_burgerdrug"}}, {"menuName": "Burgerdrug", "data": {"action": "u_m_y_burgerdrug_01"}}, {"menuName": "Busboy", "data": {"action": "s_m_y_busboy_01"}}, {"menuName": "Business Casual", "data": {"action": "a_m_y_busicas_01"}}, {"menuName": "Business Female 2", "data": {"action": "a_f_m_business_02"}}, {"menuName": "Business Male", "data": {"action": "a_m_m_business_01"}}, {"menuName": "Business Young Female", "data": {"action": "a_f_y_business_01"}}, {"menuName": "Business Young Female 2", "data": {"action": "a_f_y_business_02"}}, {"menuName": "Business Young Female 3", "data": {"action": "a_f_y_business_03"}}, {"menuName": "Business Young Female 4", "data": {"action": "a_f_y_business_04"}}, {"menuName": "Business Young Male", "data": {"action": "a_m_y_business_01"}}, {"menuName": "Business Young Male 2", "data": {"action": "a_m_y_business_02"}}, {"menuName": "Business Young Male 3", "data": {"action": "a_m_y_business_03"}}, {"menuName": "Busker", "data": {"action": "s_m_o_busker_01"}}, {"menuName": "Car 3 Guy 1", "data": {"action": "csb_car3guy1"}}, {"menuName": "Car 3 Guy 2", "data": {"action": "csb_car3guy2"}}, {"menuName": "Car3Guy1", "data": {"action": "ig_car3guy1"}}, {"menuName": "Car3Guy2", "data": {"action": "ig_car3guy2"}}, {"menuName": "Carbuyer-BROKEN", "data": {"action": "cs_carbuyer"}}, {"menuName": "<PERSON>", "data": {"action": "ig_casey"}}, {"menuName": "<PERSON>-<PERSON><PERSON><PERSON>", "data": {"action": "cs_casey"}}, {"menuName": "Chef", "data": {"action": "ig_chef"}}, {"menuName": "Chef", "data": {"action": "s_m_y_chef_01"}}, {"menuName": "Chemsec", "data": {"action": "s_m_m_chemsec_01"}}, {"menuName": "Chemwork", "data": {"action": "g_m_m_chemwork_01"}}, {"menuName": "Chemwork_P", "data": {"action": "g_m_m_chemwork_01_p"}}, {"menuName": "Chengsr", "data": {"action": "ig_chengsr"}}, {"menuName": "Chengsr-BROKEN", "data": {"action": "cs_chengsr"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "g_m_m_chiboss_01"}}, {"menuName": "Chiboss_P", "data": {"action": "g_m_m_chiboss_01_p"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "g_m_m_chicold_01"}}, {"menuName": "<PERSON><PERSON>_<PERSON>", "data": {"action": "g_m_m_chicold_01_p"}}, {"menuName": "Chigoon", "data": {"action": "g_m_m_chigoon_01"}}, {"menuName": "Chigoon", "data": {"action": "g_m_m_chigoon_02"}}, {"menuName": "Chigoon_P", "data": {"action": "g_m_m_chigoon_01_p"}}, {"menuName": "Chinese Goon", "data": {"action": "csb_chin_goon"}}, {"menuName": "Chip", "data": {"action": "u_m_y_chip"}}, {"menuName": "Chrisformage", "data": {"action": "ig_chrisformage"}}, {"menuName": "Chrisformage-BROKEN", "data": {"action": "cs_chrisformage"}}, {"menuName": "Ciasec", "data": {"action": "s_m_m_ciasec_01"}}, {"menuName": "<PERSON>", "data": {"action": "mp_m_claude_01"}}, {"menuName": "<PERSON>", "data": {"action": "ig_clay"}}, {"menuName": "Clay-BROKEN", "data": {"action": "cs_clay"}}, {"menuName": "Claypain", "data": {"action": "ig_claypain"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "csb_cletus"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "ig_cletus"}}, {"menuName": "Clown", "data": {"action": "s_m_y_clown_01"}}, {"menuName": "Cntrybar", "data": {"action": "s_m_m_cntrybar_01"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "u_f_y_comjane"}}, {"menuName": "Construct", "data": {"action": "s_m_y_construct_01"}}, {"menuName": "Construct", "data": {"action": "s_m_y_construct_02"}}, {"menuName": "Cooker", "data": {"action": "csb_chef"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "csb_cop"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "s_f_y_cop_01"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "s_m_y_cop_01"}}, {"menuName": "Corpse", "data": {"action": "u_f_m_corpse_01"}}, {"menuName": "Corpse", "data": {"action": "u_f_y_corpse_01"}}, {"menuName": "Corpse", "data": {"action": "u_f_y_corpse_02"}}, {"menuName": "Customer", "data": {"action": "csb_customer"}}, {"menuName": "Cyclist", "data": {"action": "u_m_y_cyclist_01"}}, {"menuName": "Cyclist Male", "data": {"action": "a_m_y_cyclist_01"}}, {"menuName": "Cyclist Sport", "data": {"action": "a_m_y_roadcyc_01"}}, {"menuName": "<PERSON>", "data": {"action": "ig_dale"}}, {"menuName": "<PERSON><PERSON>BROK<PERSON>", "data": {"action": "cs_dale"}}, {"menuName": "<PERSON>", "data": {"action": "ig_da<PERSON><PERSON><PERSON>"}}, {"menuName": "<PERSON> - Cutscene - BROK<PERSON>", "data": {"action": "cs_da<PERSON><PERSON>on"}}, {"menuName": "Deadhooker", "data": {"action": "mp_f_deadhooker"}}, {"menuName": "Dealer", "data": {"action": "s_m_y_dealer_01"}}, {"menuName": "Debra - Cutscene - BROKEN", "data": {"action": "cs_debra"}}, {"menuName": "<PERSON>", "data": {"action": "ig_denise"}}, {"menuName": "<PERSON>'s Friend", "data": {"action": "csb_denise_friend"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data": {"action": "cs_denise"}}, {"menuName": "<PERSON> - Ingame", "data": {"action": "ig_devin"}}, {"menuName": "<PERSON> - Cutscene - BROK<PERSON>", "data": {"action": "cs_devin"}}, {"menuName": "Devin's Security", "data": {"action": "s_m_y_devinsec_01"}}, {"menuName": "Dockwork", "data": {"action": "s_m_m_dockwork_01"}}, {"menuName": "Dockwork", "data": {"action": "s_m_y_dockwork_01"}}, {"menuName": "Doctor", "data": {"action": "s_m_m_doctor_01"}}, {"menuName": "Dom", "data": {"action": "ig_dom"}}, {"menuName": "Dom-BROKEN", "data": {"action": "cs_dom"}}, {"menuName": "<PERSON>man", "data": {"action": "s_m_y_doorman_01"}}, {"menuName": "Downtown Female", "data": {"action": "a_f_m_downtown_01"}}, {"menuName": "Downtown Male", "data": {"action": "a_m_y_downtown_01"}}, {"menuName": "<PERSON> - In-game", "data": {"action": "ig_dreyfuss"}}, {"menuName": "<PERSON> - Cutscene - BROKEN", "data": {"action": "cs_dreyfuss"}}, {"menuName": "Dr. <PERSON><PERSON> - In-game", "data": {"action": "ig_drf<PERSON>lander"}}, {"menuName": "Dr. <PERSON><PERSON> - Cutscene - BROKEN", "data": {"action": "cs_drfriedlander"}}, {"menuName": "Dwservice", "data": {"action": "s_m_y_dwservice_01"}}, {"menuName": "Dwservice", "data": {"action": "s_m_y_dwservice_02"}}, {"menuName": "East SA Male", "data": {"action": "a_m_m_eastsa_01"}}, {"menuName": "East SA Male", "data": {"action": "a_m_m_eastsa_02"}}, {"menuName": "East SA Young Female", "data": {"action": "a_f_y_eastsa_01"}}, {"menuName": "East SA Young Female 2", "data": {"action": "a_f_y_eastsa_02"}}, {"menuName": "East SA Young Female 3", "data": {"action": "a_f_y_eastsa_03"}}, {"menuName": "East SA Young Male", "data": {"action": "a_m_y_eastsa_01"}}, {"menuName": "East SA Young Male", "data": {"action": "a_m_y_eastsa_02"}}, {"menuName": "Eastsa SA Female", "data": {"action": "a_f_m_eastsa_01"}}, {"menuName": "Eastsa SA Female 2", "data": {"action": "a_f_m_eastsa_02"}}, {"menuName": "Epsilon Female", "data": {"action": "a_f_y_epsilon_01"}}, {"menuName": "Epsilon Male", "data": {"action": "a_m_y_epsilon_01"}}, {"menuName": "Epsilon Male 2", "data": {"action": "a_m_y_epsilon_02"}}, {"menuName": "Exarmy", "data": {"action": "mp_m_exarmy_01"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "ig_fabien"}}, {"menuName": "Fabien-BROKEN", "data": {"action": "cs_fabien"}}, {"menuName": "Factory", "data": {"action": "s_f_y_factory_01"}}, {"menuName": "Factory", "data": {"action": "s_m_y_factory_01"}}, {"menuName": "Famca", "data": {"action": "g_m_y_famca_01"}}, {"menuName": "Famdd", "data": {"action": "mp_m_famdd_01"}}, {"menuName": "Famdnf", "data": {"action": "g_m_y_famdnf_01"}}, {"menuName": "<PERSON>amfor", "data": {"action": "g_m_y_famfor_01"}}, {"menuName": "Families", "data": {"action": "g_f_y_families_01"}}, {"menuName": "Families Gang Member", "data": {"action": "csb_ramp_gang"}}, {"menuName": "<PERSON>", "data": {"action": "a_m_m_farmer_01"}}, {"menuName": "Fat Black Female", "data": {"action": "a_f_m_fatbla_01"}}, {"menuName": "Fat Cult Female", "data": {"action": "a_f_m_fatcult_01"}}, {"menuName": "Fat Latino Male", "data": {"action": "a_m_m_fatlatin_01"}}, {"menuName": "Fat white Female", "data": {"action": "a_f_m_fatwhite_01"}}, {"menuName": "Fbisuit", "data": {"action": "ig_fbisuit_01"}}, {"menuName": "Fbisuit-BROKEN", "data": {"action": "cs_fbisuit_01"}}, {"menuName": "Fembarber", "data": {"action": "s_f_m_fembarber"}}, {"menuName": "Fibarchitect", "data": {"action": "u_m_m_fibarchitect"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "u_m_y_fibmugger_01"}}, {"menuName": "Fiboffice", "data": {"action": "s_m_m_fiboffice_01"}}, {"menuName": "Fiboffice", "data": {"action": "s_m_m_fiboffice_02"}}, {"menuName": "Fibsec", "data": {"action": "mp_m_fibsec_01"}}, {"menuName": "Filmdirector", "data": {"action": "u_m_m_filmdirector"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "u_m_o_finguru_01"}}, {"menuName": "Fireman", "data": {"action": "s_m_y_fireman_01"}}, {"menuName": "Fitness Female", "data": {"action": "a_f_y_fitness_01"}}, {"menuName": "Fitness Female 2", "data": {"action": "a_f_y_fitness_02"}}, {"menuName": "<PERSON>", "data": {"action": "ig_floyd"}}, {"menuName": "Floyd-BROK<PERSON>", "data": {"action": "cs_floyd"}}, {"menuName": "Fos Rep", "data": {"action": "csb_fos_rep"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "s_m_m_gaffer_01"}}, {"menuName": "Gang", "data": {"action": "ig_ramp_gang"}}, {"menuName": "Garbage", "data": {"action": "s_m_y_garbage"}}, {"menuName": "Gardener", "data": {"action": "s_m_m_gardener_01"}}, {"menuName": "Gay Male", "data": {"action": "a_m_y_gay_01"}}, {"menuName": "Gay Male 2", "data": {"action": "a_m_y_gay_02"}}, {"menuName": "General Fat Male", "data": {"action": "a_m_m_genfat_01"}}, {"menuName": "General Fat Male 2", "data": {"action": "a_m_m_genfat_02"}}, {"menuName": "General Hot Young Female", "data": {"action": "a_f_y_genhot_01"}}, {"menuName": "General Street Old Female", "data": {"action": "a_f_o_genstreet_01"}}, {"menuName": "General Street Old Male", "data": {"action": "a_m_o_genstreet_01"}}, {"menuName": "General Street Young Male", "data": {"action": "a_m_y_genstreet_01"}}, {"menuName": "General Street Young Male 2", "data": {"action": "a_m_y_genstreet_02"}}, {"menuName": "Gentransport", "data": {"action": "s_m_m_gentransport"}}, {"menuName": "<PERSON>", "data": {"action": "csb_g"}}, {"menuName": "Glenstank", "data": {"action": "u_m_m_glenstank_01"}}, {"menuName": "Golfer Male", "data": {"action": "a_m_m_golfer_01"}}, {"menuName": "Golfer Young Female", "data": {"action": "a_f_y_golfer_01"}}, {"menuName": "Golfer <PERSON>", "data": {"action": "a_m_y_golfer_01"}}, {"menuName": "Griff", "data": {"action": "u_m_m_griff_01"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "s_m_y_grip_01"}}, {"menuName": "Groom", "data": {"action": "ig_groom"}}, {"menuName": "Groom-BROKEN", "data": {"action": "csb_groom"}}, {"menuName": "Grove Street Dealer", "data": {"action": "csb_grove_str_dlr"}}, {"menuName": "Guadalope-BROKEN", "data": {"action": "cs_guadalope"}}, {"menuName": "<PERSON>", "data": {"action": "u_m_y_guido_01"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "u_m_y_gunvend_01"}}, {"menuName": "Gurk-BROKEN", "data": {"action": "cs_gurk"}}, {"menuName": "Hairdress", "data": {"action": "s_m_m_hairdress_01"}}, {"menuName": "<PERSON>o", "data": {"action": "csb_hao"}}, {"menuName": "<PERSON>o", "data": {"action": "ig_hao"}}, {"menuName": "Hasidic Jew Male", "data": {"action": "a_m_m_hasjew_01"}}, {"menuName": "Hasidic Jew Young Male", "data": {"action": "a_m_y_hasjew_01"}}, {"menuName": "Hc_<PERSON>", "data": {"action": "hc_driver"}}, {"menuName": "<PERSON><PERSON>_<PERSON><PERSON>", "data": {"action": "hc_gunman"}}, {"menuName": "<PERSON><PERSON>_Hacker", "data": {"action": "hc_hacker"}}, {"menuName": "Hic", "data": {"action": "ig_ramp_hic"}}, {"menuName": "<PERSON>ck", "data": {"action": "csb_ramp_hic"}}, {"menuName": "Highsec", "data": {"action": "s_m_m_highsec_01"}}, {"menuName": "Highsec", "data": {"action": "s_m_m_highsec_02"}}, {"menuName": "Hiker Female", "data": {"action": "a_f_y_hiker_01"}}, {"menuName": "Hiker Male", "data": {"action": "a_m_y_hiker_01"}}, {"menuName": "Hillbilly Male", "data": {"action": "a_m_m_hillbilly_01"}}, {"menuName": "Hillbilly Male 2", "data": {"action": "a_m_m_hillbilly_02"}}, {"menuName": "Hippie", "data": {"action": "u_m_y_hippie_01"}}, {"menuName": "Hippie <PERSON>", "data": {"action": "a_f_y_hippie_01"}}, {"menuName": "Hippie Male", "data": {"action": "a_m_y_hippy_01"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "csb_ramp_hipster"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "ig_ramp_hipster"}}, {"menuName": "Hipster Female", "data": {"action": "a_f_y_hipster_01"}}, {"menuName": "Hipster Female 2", "data": {"action": "a_f_y_hipster_02"}}, {"menuName": "Hipster Female 3", "data": {"action": "a_f_y_hipster_03"}}, {"menuName": "Hipster Female 4", "data": {"action": "a_f_y_hipster_04"}}, {"menuName": "Hipster Male", "data": {"action": "a_m_y_hipster_01"}}, {"menuName": "Hipster Male 2", "data": {"action": "a_m_y_hipster_02"}}, {"menuName": "Hipster Male 3", "data": {"action": "a_m_y_hipster_03"}}, {"menuName": "<PERSON>", "data": {"action": "s_f_y_hooker_01"}}, {"menuName": "<PERSON>", "data": {"action": "s_f_y_hooker_02"}}, {"menuName": "<PERSON>", "data": {"action": "s_f_y_hooker_03"}}, {"menuName": "Hotposh", "data": {"action": "u_f_y_hotposh_01"}}, {"menuName": "<PERSON>", "data": {"action": "csb_hugh"}}, {"menuName": "<PERSON>", "data": {"action": "ig_hunter"}}, {"menuName": "Hunter-BROKEN", "data": {"action": "cs_hunter"}}, {"menuName": "Hwaycop", "data": {"action": "s_m_y_hwaycop_01"}}, {"menuName": "Imporage", "data": {"action": "u_m_y_imporage"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "csb_imran"}}, {"menuName": "Indian Male", "data": {"action": "a_m_m_indian_01"}}, {"menuName": "Indian Old Female", "data": {"action": "a_f_o_indian_01"}}, {"menuName": "Indian Young Female", "data": {"action": "a_f_y_indian_01"}}, {"menuName": "Indian Young Male", "data": {"action": "a_m_y_indian_01"}}, {"menuName": "<PERSON>", "data": {"action": "ig_janet"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data": {"action": "cs_janet"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "csb_janitor"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "s_m_m_janitor"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "ig_jay_norris"}}, {"menuName": "<PERSON>", "data": {"action": "u_m_m_jesus_01"}}, {"menuName": "Jetskier", "data": {"action": "a_m_y_jetski_01"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "ig_jewelass"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "u_f_y_jewelass_01"}}, {"menuName": "Jewelass-BROK<PERSON>", "data": {"action": "cs_jewelass"}}, {"menuName": "Jewelsec", "data": {"action": "u_m_m_jewelsec_01"}}, {"menuName": "Jewelthief", "data": {"action": "u_m_m_jewelthief"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "ig_jimmy<PERSON><PERSON>"}}, {"menuName": "Jimmyboston-BROKEN", "data": {"action": "cs_jimmy<PERSON><PERSON>"}}, {"menuName": "Jimmydisanto", "data": {"action": "ig_jimmy<PERSON><PERSON>o"}}, {"menuName": "Jimmydisanto-BROKEN", "data": {"action": "cs_jimmy<PERSON><PERSON>o"}}, {"menuName": "Joe<PERSON>uteman", "data": {"action": "ig_joeminuteman"}}, {"menuName": "Joeminuteman-BROKEN", "data": {"action": "cs_joe<PERSON>uteman"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "a_f_y_runner_01"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "a_m_y_runner_01"}}, {"menuName": "<PERSON><PERSON> 2", "data": {"action": "a_m_y_runner_02"}}, {"menuName": "<PERSON> - In-game", "data": {"action": "ig_johnny<PERSON><PERSON><PERSON>"}}, {"menuName": "<PERSON> - Cutscene - BROKEN", "data": {"action": "cs_johnny<PERSON><PERSON><PERSON>"}}, {"menuName": "<PERSON>", "data": {"action": "ig_josef"}}, {"menuName": "Josef-BROKEN", "data": {"action": "cs_josef"}}, {"menuName": "<PERSON>", "data": {"action": "ig_josh"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data": {"action": "cs_josh"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "a_f_y_juggalo_01"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "a_m_y_juggalo_01"}}, {"menuName": "<PERSON>", "data": {"action": "u_m_y_justin"}}, {"menuName": "Kerrymcintosh", "data": {"action": "ig_kerrymcintosh"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "g_m_m_korboss_01"}}, {"menuName": "Korean", "data": {"action": "g_m_y_korean_01"}}, {"menuName": "Korean", "data": {"action": "g_m_y_korean_02"}}, {"menuName": "Korean Female", "data": {"action": "a_f_m_ktown_01"}}, {"menuName": "Korean Female 2", "data": {"action": "a_f_m_ktown_02"}}, {"menuName": "Korean Male", "data": {"action": "a_m_m_ktown_01"}}, {"menuName": "Korean Old Female", "data": {"action": "a_f_o_ktown_01"}}, {"menuName": "Korean Old Male", "data": {"action": "a_m_o_ktown_01"}}, {"menuName": "Korean Young Male 3", "data": {"action": "a_m_y_ktown_01"}}, {"menuName": "Korean Young Male 4", "data": {"action": "a_m_y_ktown_02"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "g_m_y_korlieut_01"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "ig_<PERSON><PERSON><PERSON><PERSON>"}}, {"menuName": "Lamardavis-BROKEN", "data": {"action": "cs_la<PERSON><PERSON><PERSON>"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "s_m_m_lathandy_01"}}, {"menuName": "Latino Street Male 2", "data": {"action": "a_m_m_stlat_02"}}, {"menuName": "Latino Street Young Male", "data": {"action": "a_m_y_stlat_01"}}, {"menuName": "Latino Young Male", "data": {"action": "a_m_y_latino_01"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "ig_lazlow"}}, {"menuName": "Lazlow-BROKEN", "data": {"action": "cs_lazlow"}}, {"menuName": "Lestercrest", "data": {"action": "ig_lester<PERSON>st"}}, {"menuName": "Lestercrest-BROKEN", "data": {"action": "cs_lestercrest"}}, {"menuName": "Lifeinvad", "data": {"action": "ig_lifeinvad_01"}}, {"menuName": "Lifeinvad", "data": {"action": "ig_lifeinvad_02"}}, {"menuName": "Lifeinvad", "data": {"action": "s_m_m_lifeinvad_01"}}, {"menuName": "Lifeinvad-BROKEN", "data": {"action": "cs_lifeinvad_01"}}, {"menuName": "Linecook", "data": {"action": "s_m_m_linecook"}}, {"menuName": "Lost", "data": {"action": "g_f_y_lost_01"}}, {"menuName": "Lost", "data": {"action": "g_m_y_lost_01"}}, {"menuName": "Lost", "data": {"action": "g_m_y_lost_02"}}, {"menuName": "Lost", "data": {"action": "g_m_y_lost_03"}}, {"menuName": "Lsmetro", "data": {"action": "s_m_m_lsmetro_01"}}, {"menuName": "Ma<PERSON><PERSON>", "data": {"action": "ig_magenta"}}, {"menuName": "Magenta-BROKEN", "data": {"action": "cs_magenta"}}, {"menuName": "Maid", "data": {"action": "s_f_m_maid_01"}}, {"menuName": "Malibu Male", "data": {"action": "a_m_m_malibu_01"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "u_m_y_mani"}}, {"menuName": "<PERSON>", "data": {"action": "ig_manuel"}}, {"menuName": "Manuel-BROKEN", "data": {"action": "cs_manuel"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "s_m_m_mariachi_01"}}, {"menuName": "Marine", "data": {"action": "csb_ramp_marine"}}, {"menuName": "Marine", "data": {"action": "s_m_m_marine_01"}}, {"menuName": "Marine", "data": {"action": "s_m_m_marine_02"}}, {"menuName": "Marine", "data": {"action": "s_m_y_marine_01"}}, {"menuName": "Marine", "data": {"action": "s_m_y_marine_02"}}, {"menuName": "Marine", "data": {"action": "s_m_y_marine_03"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "u_m_m_markfost"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "ig_marnie"}}, {"menuName": "Marnie-BROKEN", "data": {"action": "cs_marnie"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "mp_m_marston_01"}}, {"menuName": "Martinmadrazo-BROKEN", "data": {"action": "cs_mart<PERSON><PERSON><PERSON><PERSON>"}}, {"menuName": "<PERSON><PERSON>-BROK<PERSON>", "data": {"action": "cs_maryann"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "ig_maryann"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "csb_maude"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "ig_maude"}}, {"menuName": "Merryweather Merc", "data": {"action": "csb_mweather"}}, {"menuName": "<PERSON><PERSON>dict", "data": {"action": "a_m_y_methhead_01"}}, {"menuName": "Mex", "data": {"action": "ig_ramp_mex"}}, {"menuName": "Mexboss", "data": {"action": "g_m_m_mexboss_01"}}, {"menuName": "Mexboss", "data": {"action": "g_m_m_mexboss_02"}}, {"menuName": "Mexgang", "data": {"action": "g_m_y_mexgang_01"}}, {"menuName": "Mexgoon", "data": {"action": "g_m_y_mexgoon_01"}}, {"menuName": "Mexgoon", "data": {"action": "g_m_y_mexgoon_02"}}, {"menuName": "Mexgoon", "data": {"action": "g_m_y_mexgoon_03"}}, {"menuName": "Mexgoon_P", "data": {"action": "g_m_y_mexgoon_03_p"}}, {"menuName": "Mexican", "data": {"action": "csb_ramp_mex"}}, {"menuName": "Mexican Rural", "data": {"action": "a_m_m_mexcntry_01"}}, {"menuName": "Mexican Thug", "data": {"action": "a_m_y_mexthug_01"}}, {"menuName": "Mexican labourer", "data": {"action": "a_m_m_mexlabor_01"}}, {"menuName": "<PERSON>", "data": {"action": "ig_michelle"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data": {"action": "cs_michelle"}}, {"menuName": "Migrant", "data": {"action": "s_f_y_migrant_01"}}, {"menuName": "Migrant", "data": {"action": "s_m_m_migrant_01"}}, {"menuName": "Militarybum", "data": {"action": "u_m_y_militarybum"}}, {"menuName": "<PERSON>", "data": {"action": "ig_milton"}}, {"menuName": "Milton-BROKEN", "data": {"action": "cs_milton"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "s_m_y_mime"}}, {"menuName": "<PERSON>", "data": {"action": "u_f_m_miranda"}}, {"menuName": "Mistress", "data": {"action": "u_f_y_mistress"}}, {"menuName": "<PERSON>", "data": {"action": "mp_f_misty_01"}}, {"menuName": "<PERSON>", "data": {"action": "ig_molly"}}, {"menuName": "Molly-<PERSON><PERSON><PERSON>", "data": {"action": "cs_molly"}}, {"menuName": "Motorcross Biker", "data": {"action": "a_m_y_motox_01"}}, {"menuName": "Motorcross Biker 2x", "data": {"action": "a_m_y_motox_02"}}, {"menuName": "Mountainbiker", "data": {"action": "a_m_y_dhill_01"}}, {"menuName": "Moviestar", "data": {"action": "u_f_o_moviestar"}}, {"menuName": "Movprem", "data": {"action": "s_f_y_movprem_01"}}, {"menuName": "Movprem", "data": {"action": "s_m_m_movprem_01"}}, {"menuName": "Movpremf-BROKEN", "data": {"action": "cs_movpremf_01"}}, {"menuName": "Movpremmale-BROKEN", "data": {"action": "cs_movpremmale"}}, {"menuName": "Movspace", "data": {"action": "s_m_m_movspace_01"}}, {"menuName": "Mp_Headtargets", "data": {"action": "mp_headtargets"}}, {"menuName": "Mrk", "data": {"action": "ig_mrk"}}, {"menuName": "Mrk-BROKEN", "data": {"action": "cs_mrk"}}, {"menuName": "Mrs_<PERSON><PERSON>", "data": {"action": "ig_mrs_thornhill"}}, {"menuName": "<PERSON>_<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "data": {"action": "cs_mrs_thornhill"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "ig_mrs<PERSON><PERSON>ps"}}, {"menuName": "Mrsphillips-BROKEN", "data": {"action": "cs_mrsphillips"}}, {"menuName": "Natalia", "data": {"action": "ig_natalia"}}, {"menuName": "Natalia-BROKEN", "data": {"action": "cs_natalia"}}, {"menuName": "Nervousron", "data": {"action": "ig_nervousron"}}, {"menuName": "Nervousron-BROKEN", "data": {"action": "cs_nervousron"}}, {"menuName": "<PERSON>", "data": {"action": "ig_nigel"}}, {"menuName": "Nigel<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "cs_nigel"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "mp_m_niko_01"}}, {"menuName": "OG Boss", "data": {"action": "a_m_m_og_boss_01"}}, {"menuName": "Old_Man1A", "data": {"action": "ig_old_man1a"}}, {"menuName": "Old_Man1A-BROKEN", "data": {"action": "cs_old_man1a"}}, {"menuName": "Old_Man2", "data": {"action": "ig_old_man2"}}, {"menuName": "Old_Man2-<PERSON><PERSON><PERSON>", "data": {"action": "cs_old_man2"}}, {"menuName": "Omega", "data": {"action": "ig_omega"}}, {"menuName": "Omega-BROKEN", "data": {"action": "cs_omega"}}, {"menuName": "Oneil", "data": {"action": "ig_oneil"}}, {"menuName": "Orleans", "data": {"action": "ig_orleans"}}, {"menuName": "Orleans-BROKEN", "data": {"action": "cs_orleans"}}, {"menuName": "Ortega", "data": {"action": "csb_ortega"}}, {"menuName": "Ortega", "data": {"action": "ig_ortega"}}, {"menuName": "<PERSON>", "data": {"action": "csb_oscar"}}, {"menuName": "Paparazzi", "data": {"action": "u_m_y_paparazzi"}}, {"menuName": "Paparazzi Male", "data": {"action": "a_m_m_paparazzi_01"}}, {"menuName": "Paper", "data": {"action": "ig_paper"}}, {"menuName": "Paper-BROKEN", "data": {"action": "cs_paper"}}, {"menuName": "Paper_P-BROKEN", "data": {"action": "cs_paper_p"}}, {"menuName": "Paramedic", "data": {"action": "s_m_m_paramedic_01"}}, {"menuName": "Party", "data": {"action": "u_m_y_party_01"}}, {"menuName": "Partytarget", "data": {"action": "u_m_m_partytarget"}}, {"menuName": "<PERSON>", "data": {"action": "ig_patricia"}}, {"menuName": "<PERSON>-BROK<PERSON>", "data": {"action": "cs_patricia"}}, {"menuName": "Pestcont", "data": {"action": "s_m_y_pestcont_01"}}, {"menuName": "Pilot", "data": {"action": "s_m_m_pilot_01"}}, {"menuName": "Pilot", "data": {"action": "s_m_m_pilot_02"}}, {"menuName": "Pilot", "data": {"action": "s_m_y_pilot_01"}}, {"menuName": "Pogo", "data": {"action": "u_m_y_pogo_01"}}, {"menuName": "Pologoon", "data": {"action": "g_m_y_pologoon_01"}}, {"menuName": "Pologoon", "data": {"action": "g_m_y_pologoon_02"}}, {"menuName": "Pologoon_P", "data": {"action": "g_m_y_pologoon_01_p"}}, {"menuName": "Pologoon_P", "data": {"action": "g_m_y_pologoon_02_p"}}, {"menuName": "Polynesian", "data": {"action": "a_m_m_polynesian_01"}}, {"menuName": "Polynesian Young", "data": {"action": "a_m_y_polynesian_01"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "u_f_y_poppymich"}}, {"menuName": "Porn Dude", "data": {"action": "csb_porndudes"}}, {"menuName": "Porndudes_P-BROKEN", "data": {"action": "csb_porndudes_p"}}, {"menuName": "Postal", "data": {"action": "s_m_m_postal_01"}}, {"menuName": "Postal", "data": {"action": "s_m_m_postal_02"}}, {"menuName": "Priest", "data": {"action": "ig_priest"}}, {"menuName": "Priest-BROK<PERSON>", "data": {"action": "cs_priest"}}, {"menuName": "Princess", "data": {"action": "u_f_y_princess"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "s_m_m_prisguard_01"}}, {"menuName": "Prismuscl", "data": {"action": "s_m_y_prismuscl_01"}}, {"menuName": "Prisoner", "data": {"action": "s_m_y_prisoner_01"}}, {"menuName": "Prisoner", "data": {"action": "u_m_y_prisoner_01"}}, {"menuName": "Prolhost", "data": {"action": "u_f_o_prolhost_01"}}, {"menuName": "Prologue Driver", "data": {"action": "u_m_y_proldriver_01"}}, {"menuName": "Prologue Driver", "data": {"action": "csb_prologuedriver"}}, {"menuName": "Prologue Host Female", "data": {"action": "a_f_m_prolhost_01"}}, {"menuName": "Prologue Host Male", "data": {"action": "a_m_m_prolhost_01"}}, {"menuName": "Prologue Security", "data": {"action": "csb_prolsec"}}, {"menuName": "Prolsec", "data": {"action": "ig_prolsec_02"}}, {"menuName": "Prolsec", "data": {"action": "u_m_m_prolsec_01"}}, {"menuName": "Prolsec-BROKEN", "data": {"action": "cs_prolsec_02"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "u_f_m_promourn_01"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "u_m_m_promourn_01"}}, {"menuName": "Pros", "data": {"action": "mp_g_m_pros_01"}}, {"menuName": "<PERSON>", "data": {"action": "s_f_y_ranger_01"}}, {"menuName": "<PERSON>", "data": {"action": "s_m_y_ranger_01"}}, {"menuName": "Reporter", "data": {"action": "csb_reporter"}}, {"menuName": "Republican Space Ranger", "data": {"action": "u_m_y_rsranger_01"}}, {"menuName": "Rivalpap", "data": {"action": "u_m_m_rivalpap"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "s_m_y_robber_01"}}, {"menuName": "<PERSON><PERSON> - Cutscene", "data": {"action": "csb_r<PERSON><PERSON>i"}}, {"menuName": "<PERSON><PERSON> - In-Game", "data": {"action": "ig_r<PERSON><PERSON><PERSON><PERSON>"}}, {"menuName": "Rural Meth Addict Female", "data": {"action": "a_f_y_rurmeth_01"}}, {"menuName": "Rural meth Addict Male", "data": {"action": "a_m_m_rurmeth_01"}}, {"menuName": "Russian Drunk - In-game", "data": {"action": "ig_russiandrunk"}}, {"menuName": "Russian Drunk - Cutscene - BROK<PERSON>", "data": {"action": "cs_russiandrunk"}}, {"menuName": "Salton Female", "data": {"action": "a_f_m_salton_01"}}, {"menuName": "Salton Male", "data": {"action": "a_m_m_salton_01"}}, {"menuName": "Salton Male 2", "data": {"action": "a_m_m_salton_02"}}, {"menuName": "Salton Male 3", "data": {"action": "a_m_m_salton_03"}}, {"menuName": "Salton Male 4", "data": {"action": "a_m_m_salton_04"}}, {"menuName": "Salton Old Female", "data": {"action": "a_f_o_salton_01"}}, {"menuName": "Salton Old Male", "data": {"action": "a_m_o_salton_01"}}, {"menuName": "Salton Young Male", "data": {"action": "a_m_y_salton_01"}}, {"menuName": "Salvaboss", "data": {"action": "g_m_y_salvaboss_01"}}, {"menuName": "Salvagoon", "data": {"action": "g_m_y_salvagoon_01"}}, {"menuName": "Salvagoon", "data": {"action": "g_m_y_salvagoon_02"}}, {"menuName": "Salvagoon", "data": {"action": "g_m_y_salvagoon_03"}}, {"menuName": "Salvagoon_P", "data": {"action": "g_m_y_salvagoon_03_p"}}, {"menuName": "Scientist", "data": {"action": "s_m_m_scientist_01"}}, {"menuName": "Screen_Writer", "data": {"action": "ig_screen_writer"}}, {"menuName": "Screenwriter", "data": {"action": "csb_screen_writer"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "s_f_y_scrubs_01"}}, {"menuName": "Security", "data": {"action": "s_m_m_security_01"}}, {"menuName": "Sheriff", "data": {"action": "s_f_y_sheriff_01"}}, {"menuName": "Sheriff", "data": {"action": "s_m_y_sheriff_01"}}, {"menuName": "Shop_High", "data": {"action": "s_f_m_shop_high"}}, {"menuName": "Shop_Low", "data": {"action": "s_f_y_shop_low"}}, {"menuName": "Shop_Mask", "data": {"action": "s_m_y_shop_mask"}}, {"menuName": "Shop_Mid", "data": {"action": "s_f_y_shop_mid"}}, {"menuName": "Shopkeep", "data": {"action": "mp_m_shopkeep_01"}}, {"menuName": "<PERSON><PERSON><PERSON>arian - In-game", "data": {"action": "ig_siemonyetarian"}}, {"menuName": "<PERSON><PERSON><PERSON> - Cutscene - BROKEN", "data": {"action": "cs_siemonyetarian"}}, {"menuName": "Skater Female", "data": {"action": "a_f_y_skater_01"}}, {"menuName": "Skater Male", "data": {"action": "a_m_m_skater_01"}}, {"menuName": "Skater Young Male", "data": {"action": "a_m_y_skater_01"}}, {"menuName": "Skater Young Male 2", "data": {"action": "a_m_y_skater_02"}}, {"menuName": "Skid Row Female", "data": {"action": "a_f_m_skidrow_01"}}, {"menuName": "Skid Row Male", "data": {"action": "a_m_m_skidrow_01"}}, {"menuName": "Yankton Cop", "data": {"action": "s_m_m_snowcop_01"}}, {"menuName": "Solomon", "data": {"action": "ig_solomon"}}, {"menuName": "Solomon-BROKEN", "data": {"action": "cs_solomon"}}, {"menuName": "South Central Female", "data": {"action": "a_f_m_soucent_01"}}, {"menuName": "South Central Female 2", "data": {"action": "a_f_m_soucent_02"}}, {"menuName": "South Central Female Dressy", "data": {"action": "a_f_y_scdressy_01"}}, {"menuName": "South Central Latino Male", "data": {"action": "a_m_m_socenlat_01"}}, {"menuName": "South Central MC Female", "data": {"action": "a_f_m_soucentmc_01"}}, {"menuName": "South Central Male", "data": {"action": "a_m_m_soucent_01"}}, {"menuName": "South Central Male 2", "data": {"action": "a_m_m_soucent_02"}}, {"menuName": "South Central Male 3", "data": {"action": "a_m_m_soucent_03"}}, {"menuName": "South Central Male 4", "data": {"action": "a_m_m_soucent_04"}}, {"menuName": "South Central Old Female", "data": {"action": "a_f_o_soucent_01"}}, {"menuName": "South Central Old Female 2", "data": {"action": "a_f_o_soucent_02"}}, {"menuName": "South Central Old Male", "data": {"action": "a_m_o_soucent_01"}}, {"menuName": "South Central Old Male 2", "data": {"action": "a_m_o_soucent_02"}}, {"menuName": "South Central Old Male 3", "data": {"action": "a_m_o_soucent_03"}}, {"menuName": "South Central Young Female", "data": {"action": "a_f_y_soucent_01"}}, {"menuName": "South Central Young Female 2", "data": {"action": "a_f_y_soucent_02"}}, {"menuName": "South Central Young Female 3", "data": {"action": "a_f_y_soucent_03"}}, {"menuName": "South Central Young Male", "data": {"action": "a_m_y_soucent_01"}}, {"menuName": "South Central Young Male 2", "data": {"action": "a_m_y_soucent_02"}}, {"menuName": "South Central Young Male 3", "data": {"action": "a_m_y_soucent_03"}}, {"menuName": "South Central Young Male 4", "data": {"action": "a_m_y_soucent_04"}}, {"menuName": "Sports Biker", "data": {"action": "u_m_y_sbike"}}, {"menuName": "Spy Actor", "data": {"action": "u_m_m_spyactor"}}, {"menuName": "Spy Actress", "data": {"action": "u_f_y_spyactress"}}, {"menuName": "Stag Party Groom", "data": {"action": "u_m_y_staggrm_01"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "ig_steve<PERSON>ns"}}, {"menuName": "<PERSON><PERSON><PERSON>-BROKEN", "data": {"action": "cs_steve<PERSON>ns"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "ig_stretch"}}, {"menuName": "Stretch-BROKEN", "data": {"action": "cs_stretch"}}, {"menuName": "<PERSON>per", "data": {"action": "csb_stripper_01"}}, {"menuName": "<PERSON>per", "data": {"action": "s_f_y_stripper_01"}}, {"menuName": "<PERSON>per", "data": {"action": "s_f_y_stripper_02"}}, {"menuName": "Stripper 2", "data": {"action": "csb_stripper_02"}}, {"menuName": "Stripperlite", "data": {"action": "mp_f_stripperlite"}}, {"menuName": "Stripperlite", "data": {"action": "s_f_y_stripperlite"}}, {"menuName": "<PERSON><PERSON><PERSON><PERSON>", "data": {"action": "s_m_m_strperf_01"}}, {"menuName": "Strpreach", "data": {"action": "s_m_m_strpreach_01"}}, {"menuName": "Strpunk", "data": {"action": "g_m_y_strpunk_01"}}, {"menuName": "Strpunk", "data": {"action": "g_m_y_strpunk_02"}}, {"menuName": "Strvend", "data": {"action": "s_m_m_strvend_01"}}, {"menuName": "Strvend", "data": {"action": "s_m_y_strvend_01"}}, {"menuName": "Sunbather Male", "data": {"action": "a_m_y_sunbathe_01"}}, {"menuName": "Surfer", "data": {"action": "a_m_y_surfer_01"}}, {"menuName": "Swat", "data": {"action": "s_m_y_swat_01"}}, {"menuName": "Sweatshop", "data": {"action": "s_f_m_sweatshop_01"}}, {"menuName": "Sweatshop", "data": {"action": "s_f_y_sweatshop_01"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "ig_talina"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "ig_tanisha"}}, {"menuName": "Tanisha-BROKEN", "data": {"action": "cs_tanisha"}}, {"menuName": "Taocheng", "data": {"action": "ig_taocheng"}}, {"menuName": "Taocheng-BROKEN", "data": {"action": "cs_taocheng"}}, {"menuName": "Taostranslator", "data": {"action": "ig_taostranslator"}}, {"menuName": "Taostranslator-BROKEN", "data": {"action": "cs_taostranslator"}}, {"menuName": "Taostranslator_P", "data": {"action": "ig_taostranslator_p"}}, {"menuName": "Taphillbilly", "data": {"action": "u_m_o_taphillbilly"}}, {"menuName": "Tattoo Artist", "data": {"action": "u_m_y_tattoo_01"}}, {"menuName": "Tennis Player Female", "data": {"action": "a_f_y_tennis_01"}}, {"menuName": "Tennis Player Male", "data": {"action": "a_m_m_tennis_01"}}, {"menuName": "Tenniscoach", "data": {"action": "ig_tenniscoach"}}, {"menuName": "Tenniscoach-BROKEN", "data": {"action": "cs_tenniscoach"}}, {"menuName": "<PERSON>", "data": {"action": "ig_terry"}}, {"menuName": "<PERSON><PERSON>BROK<PERSON>", "data": {"action": "cs_terry"}}, {"menuName": "Tom-BROK<PERSON>", "data": {"action": "cs_tom"}}, {"menuName": "Tomepsilon", "data": {"action": "ig_tomepsilon"}}, {"menuName": "Tomepsilon-BROKEN", "data": {"action": "cs_tomepsilon"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "csb_tonya"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "ig_tonya"}}, {"menuName": "Topless", "data": {"action": "a_f_y_topless_01"}}, {"menuName": "Tourist Female", "data": {"action": "a_f_m_tourist_01"}}, {"menuName": "Tourist Male", "data": {"action": "a_m_m_tourist_01"}}, {"menuName": "Tourist Young Female", "data": {"action": "a_f_y_tourist_01"}}, {"menuName": "Tourist Young Female 2", "data": {"action": "a_f_y_tourist_02"}}, {"menuName": "Tracydisanto", "data": {"action": "ig_tracydisanto"}}, {"menuName": "Tracydisanto-BROKEN", "data": {"action": "cs_tracydisanto"}}, {"menuName": "Traffic Warden", "data": {"action": "csb_trafficwarden"}}, {"menuName": "Trafficwarden", "data": {"action": "ig_trafficwarden"}}, {"menuName": "Tramp", "data": {"action": "u_m_o_tramp_01"}}, {"menuName": "Tramp Female", "data": {"action": "a_f_m_tramp_01"}}, {"menuName": "Tramp Male", "data": {"action": "a_m_m_tramp_01"}}, {"menuName": "Tramp Old Male", "data": {"action": "a_m_o_tramp_01"}}, {"menuName": "Tranvestite Male", "data": {"action": "a_m_m_tranvest_01"}}, {"menuName": "Tranvestite Male 2", "data": {"action": "a_m_m_tranvest_02"}}, {"menuName": "Trucker", "data": {"action": "s_m_m_trucker_01"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "ig_tylerdix"}}, {"menuName": "Ups", "data": {"action": "s_m_m_ups_01"}}, {"menuName": "Ups", "data": {"action": "s_m_m_ups_02"}}, {"menuName": "Uscg", "data": {"action": "s_m_y_uscg_01"}}, {"menuName": "Vagos", "data": {"action": "g_f_y_vagos_01"}}, {"menuName": "Valet", "data": {"action": "s_m_y_valet_01"}}, {"menuName": "Vespucci Beach Male", "data": {"action": "a_m_y_beachvesp_01"}}, {"menuName": "Vespucci Beach Male", "data": {"action": "a_m_y_beachvesp_02"}}, {"menuName": "Vinewood Douche", "data": {"action": "a_m_y_vindouche_01"}}, {"menuName": "Vinewood Female", "data": {"action": "a_f_y_vinewood_01"}}, {"menuName": "Vinewood Female 2", "data": {"action": "a_f_y_vinewood_02"}}, {"menuName": "Vinewood Female 3", "data": {"action": "a_f_y_vinewood_03"}}, {"menuName": "Vinewood Female 4", "data": {"action": "a_f_y_vinewood_04"}}, {"menuName": "Vinewood Male", "data": {"action": "a_m_y_vinewood_01"}}, {"menuName": "Vinewood Male 2", "data": {"action": "a_m_y_vinewood_02"}}, {"menuName": "Vinewood Male 3", "data": {"action": "a_m_y_vinewood_03"}}, {"menuName": "Vinewood Male 4", "data": {"action": "a_m_y_vinewood_04"}}, {"menuName": "<PERSON>", "data": {"action": "ig_wade"}}, {"menuName": "Wade-BROK<PERSON>", "data": {"action": "cs_wade"}}, {"menuName": "Waiter", "data": {"action": "s_m_y_waiter_01"}}, {"menuName": "White Street Male", "data": {"action": "a_m_y_stwhi_01"}}, {"menuName": "White Street Male", "data": {"action": "a_m_y_stwhi_02"}}, {"menuName": "Willyfist", "data": {"action": "u_m_m_willyfist"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "s_m_y_winclean_01"}}, {"menuName": "Xmech", "data": {"action": "s_m_y_xmech_01"}}, {"menuName": "Xmech", "data": {"action": "s_m_y_xmech_02"}}, {"menuName": "Yoga Female", "data": {"action": "a_f_y_yoga_01"}}, {"menuName": "Yoga Male", "data": {"action": "a_m_y_yoga_01"}}, {"menuName": "Zimbor", "data": {"action": "ig_zimbor"}}, {"menuName": "Zimbor-BROKEN", "data": {"action": "cs_zimbor"}}, {"menuName": "Zombie", "data": {"action": "u_m_y_zombie_01"}}], "Animal List": [{"menuName": "<PERSON><PERSON>", "data": {"action": "a_c_boar"}}, {"menuName": "Cat", "data": {"action": "a_c_cat_01"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "a_c_chimp"}}, {"menuName": "<PERSON>p", "data": {"action": "a_c_chop"}}, {"menuName": "Cormorant", "data": {"action": "a_c_cormorant"}}, {"menuName": "Cow", "data": {"action": "a_c_cow"}}, {"menuName": "Coyote", "data": {"action": "a_c_coyote"}}, {"menuName": "Crow", "data": {"action": "a_c_crow"}}, {"menuName": "Deer", "data": {"action": "a_c_deer"}}, {"menuName": "Dolphin", "data": {"action": "a_c_dolphin"}}, {"menuName": "Fish", "data": {"action": "a_c_fish"}}, {"menuName": "Hawk", "data": {"action": "a_c_chickenhawk"}}, {"menuName": "Hen", "data": {"action": "a_c_hen"}}, {"menuName": "<PERSON><PERSON><PERSON>", "data": {"action": "a_c_humpback"}}, {"menuName": "<PERSON><PERSON>", "data": {"action": "a_c_husky"}}, {"menuName": "Mountain Lion", "data": {"action": "a_c_mtlion"}}, {"menuName": "Pig", "data": {"action": "a_c_pig"}}, {"menuName": "<PERSON>eon", "data": {"action": "a_c_pigeon"}}, {"menuName": "Rat", "data": {"action": "a_c_rat"}}, {"menuName": "Golden Retriever", "data": {"action": "a_c_retriever"}}, {"menuName": "Rhesus Monkey", "data": {"action": "a_c_rhesus"}}, {"menuName": "Seagull", "data": {"action": "a_c_seagull"}}, {"menuName": "Hammerhead Shark", "data": {"action": "a_c_sharkhammer"}}, {"menuName": "Tiger Shark", "data": {"action": "a_c_sharktiger"}}, {"menuName": "German Shepherd", "data": {"action": "a_c_shepherd"}}, {"menuName": "<PERSON>", "data": {"action": "a_c_stingray"}}, {"menuName": "Whale", "data": {"action": "a_c_killerwhale"}}], "Player List": [{"menuName": "<PERSON>", "data": {"action": "player_zero"}}, {"menuName": "<PERSON>", "data": {"action": "player_one"}}, {"menuName": "<PERSON>", "data": {"action": "player_two"}}, {"menuName": "MP Male", "data": {"action": "mp_m_freemode_01"}}, {"menuName": "MP Female", "data": {"action": "mp_f_freemode_01"}}]}}