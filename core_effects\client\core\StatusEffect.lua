---@class EffectHandlers
---@field onStart function Function which runs when the effect starts
---@field onTick function Function which runs every effect tick (default is every game tick)
---@field onStop function Function  which runs when the effect stops

---@class EffectOptions
---@field alert boolean Whether the affect should have a red highlight signifying it is critical
---@field desc string Text description displayed to the user when viewing the effect
---@field subdesc string Secondary description, used for listing the actual effects
---@field tickRate integer Specifies how often the onTick event should run , default is 0

---Status effect class, created using the StatusEffect function

---The core of each effect. Handles UI control and the effects it has on the player
---@type function
---@param name string Identifier of the effect
---@param label string User-friendly label of the effect
---@param handlers EffectHandlers
---@param options EffectOptions
---@return table
function StatusEffect(name, label, handlers, options)
    local self = {}

    if not handlers then handlers = {} end
    if not options then options = {} end

    self.name = name
    self.label = label
    self.alert = options.alert or false
    self.desc = options.desc or ""
    self.subdesc = options.subdesc or ""
    self.data = {}

    self.tickRate = options.tickRate or 0
    self.onTick = handlers.onTick or false
    self.onStart = handlers.onStart or false
    self.onStop = handlers.onStop or false

    self.active = false

    self.start = function(uiData)
        if self.onStart then self.onStart(self) end

        self.active = true

        if not uiData then uiData = {} end
        uiData.name = self.name
        uiData.label = self.label
        uiData.alert = self.alert
        uiData.desc = self.desc
        uiData.subdesc = self.subdesc

        QUI.TriggerEvent("addEffect", uiData)

        Citizen.CreateThread(function()
            while self.active do
                Wait(self.tickRate)
                self.tick()
            end
        end)
    end

    self.stop = function()
        if self.onStop then self.onStop(self) end

        QUI.TriggerEvent("removeEffect", self.name)

        self.active = false
    end

    self.tick = function()
        if self.onTick then self.onTick(self) end
    end

    return self
end
