@font-face {
  font-family: 'Circular';
  src: url("fonts/Circular-Bold.woff2") format("woff2"), url("fonts/Circular-Bold.woff") format("woff"), url("fonts/Circular-Bold.ttf") format("truetype");
  font-weight: bold;
  font-style: normal; }

@font-face {
  font-family: 'Circular';
  src: url("fonts/Circular-Book.woff2") format("woff2"), url("fonts/Circular-Book.woff") format("woff"), url("fonts/Circular-Book.ttf") format("truetype");
  font-weight: normal;
  font-style: normal; }

@font-face {
    font-family: "Inter";
    src: url("https://db.onlinewebfonts.com/t/1130e1ae4e4a76c7118c0b35c4b9d019.eot");
    src: url("https://db.onlinewebfonts.com/t/1130e1ae4e4a76c7118c0b35c4b9d019.eot?#iefix")format("embedded-opentype"),
    url("https://db.onlinewebfonts.com/t/1130e1ae4e4a76c7118c0b35c4b9d019.woff2")format("woff2"),
    url("https://db.onlinewebfonts.com/t/1130e1ae4e4a76c7118c0b35c4b9d019.woff")format("woff"),
    url("https://db.onlinewebfonts.com/t/1130e1ae4e4a76c7118c0b35c4b9d019.ttf")format("truetype"),
    url("https://db.onlinewebfonts.com/t/1130e1ae4e4a76c7118c0b35c4b9d019.svg#Inter")format("svg");
}

* {
  position: relative;
  margin: 0;
  padding: 0;
  outline: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

body {
  font-weight: normal;
  overflow: hidden;
  height: 100%; }

a {
  display: block;
  text-decoration: none; }

.crosshair,
.menu-car,
.menu-user {
  opacity: 0; }

#cursorPointer {
  position: absolute;
  z-index: 999999;
  display: none; }

.crosshair {
  text-align: center;
  width: 8px;
  height: 8px;
  border: 1px solid #FFFFFF;
  border-radius: 100%;
  position: absolute;
  top: calc(50% - 4px);
  left: calc(50% - 4px);
  -webkit-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  z-index: 200; }

.crosshair.fadeIn {
  width: 8px;
  height: 8px;
  top: calc(50% - 4px);
  left: calc(50% - 4px);
  z-index: 200; }

.crosshair.active {
  width: 16px;
  height: 16px;
  top: calc(50% - 8px);
  left: calc(50% - 8px);
  background: #FFFFFF url("assets/close.png") no-repeat 4px 4px;
  background-size: 45%; }

.menu {
	top: 50%;
	left: 55%;
  max-height: 70%;
  max-width: 40%;
	z-index: 0;
	opacity: 0;
	transform: translateY(-50%);
	position: relative;
  display: inline-flex;
  flex-direction: column;
  flex-wrap: wrap;
  justify-content: start;
  align-items: start;
  justify-items: start;
}

.menu a {
  line-height: 20px;
  font-family: 'Inter', sans-serif;
	font-size: 12px;
	display: inline-block;
	color: white;
	background: rgba(10,10,10,0.7);
	border-radius: 5px;
	min-width: 200px;
	text-align: center;
	padding: 10px 20px 10px 18px;
	margin: 2px 0;
  margin-right: 16px;
	will-change: transform;
}

.menu a:hover {
	-webkit-transform: scale(1.1);
	-ms-transform: scale(1.1);
  background: linear-gradient(to top, rgba(79, 119, 156, 0.678), rgba(10, 10, 10, 0.805));
	transform: scale(1.1);
}

.menu a .emoji {
	font-size: 22px;
	margin-right: 5px;
	display: inline-block;
	vertical-align: middle;
}

.fadeIn {
  opacity: 1;
  z-index: 10; }

#searchSprite {
  display: none;
  transform: translate(-50%, -50%);
}