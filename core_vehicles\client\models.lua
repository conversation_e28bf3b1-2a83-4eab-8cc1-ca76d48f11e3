------------------------------------
-- Civilian Interaction Functions --
------------------------------------

AddVehicleInteractionAction(
    function() -- Condition
	    return GetEntityModel(GetVehiclePedIsIn(GetPlayerPed(-1))) == GetHashKey("modelx") or GetEntityModel(GetVehiclePedIsIn(GetPlayerPed(-1))) == GetHashKey("pdmodelx")
    end,

    function() -- Action
        TriggerEvent("main_ptk:tpa")
    end,

    { text = "Toggle Autopilot", icon = "&#xF000A;", action = "tesla_autopilot"},  -- Menu Item

    {requires = {Vehicle = true}}  -- Requires
)




------------------------------------
-- Police Interaction Functions --
------------------------------------


-----
-- PD530D RBT Sign

AddVehicleInteractionAction(
    function() -- Condition
	    return GetEntityModel(GetVehiclePedIsIn(GetPlayerPed(-1))) == GetHashKey("pd530d")
    end,

    function() -- Action
        local veh = GetVehiclePedIsIn(GetPlayerPed(-1))
        SetVehicleAutoRepairDisabled(veh, true)
        if IsVehicleExtraTurnedOn(veh, 2) then
            SetVehicleExtra(veh, 2, 1)
        else
            SetVehicleExtra(veh, 2, 0)
        end
    end,

    { text = "RBT Sign", icon = "&#xF000A;", action = "rbt_sign"},  -- Menu Item

    {requires = {Vehicle = true}}  -- Requires
)


-----
-- Command Support Unit (CSU)

AddVehicleInteractionAction(
    function() -- Condition
	    return GetEntityModel(GetVehiclePedIsIn(GetPlayerPed(-1))) == GetHashKey("csu")
    end,

    function() end, -- Action

    { text = "Control CSU", icon = "&#xF0B69;", subMenu = {
        { text = "Operate Camera", icon = "&#xF0104;", action = "csu_camera" },
        { text = "Deploy Setup", icon = "&#xF12A9;", action = "csu_setup" },
        { text = "", icon = "&#xF12A9;", action = "placeholder" },
    }},  -- Menu Item

    {requires = {Vehicle = true}}  -- Requires
)


CSUSetup = function()
    local veh = GetVehiclePedIsIn(GetPlayerPed(-1))

    if IsVehicleExtraTurnedOn(veh, 12) then
        SetVehicleExtra(veh, 11, 0)
        SetVehicleExtra(veh, 12, 1)
    else
        SetVehicleExtra(veh, 12, 0)
        SetVehicleExtra(veh, 11, 1)
        ToggleDoor3()
    end
end

onBaseReady(function()
    QUI.registerMenuAction("csu_camera", function() TriggerEvent("main_roleplay:operateCSUCamera") end)
    QUI.registerMenuAction("csu_setup", CSUSetup)
end)


-----
-- Dot Speedo Van Light Board

AddVehicleInteractionAction(
    function() -- Condition
	    return GetEntityModel(GetVehiclePedIsIn(GetPlayerPed(-1))) == GetHashKey("dotspeedo") and IsVehicleExtraTurnedOn(GetVehiclePedIsIn(GetPlayerPed(-1)), 4)
    end,

    function() end, -- Action

    { text = "Operate LED Board", icon = "&#xF0B69;", subMenu = {
        { text = "Open / Close", icon = "&#xF0104;", action = "dotspeedo_openclose" },
        { text = "Reset Sign", icon = "&#xF12A9;", action = "dotspeedo_reset" },
        { text = "Signs", icon = "&#xF0B69;", subMenu = {
            { text = "Merge Left", icon = "&#xF0104;", action = "dotspeedo_mergeleft" },
            { text = "Merge Right", icon = "&#xF12A9;", action = "dotspeedo_mergeright" },
            { text = "Road Work Ahead", icon = "&#xF12A9;", action = "dotspeedo_roadwork" },
            { text = "Road Closed", icon = "&#xF12A9;", action = "dotspeedo_roadclosed" },
            { text = "40KM/H", icon = "&#xF0104;", action = "dotspeedo_40" },
            { text = "Slow Down", icon = "&#xF12A9;", action = "dotspeedo_slowdown" },
            { text = "Vehicle Accident Ahead", icon = "&#xF12A9;", action = "dotspeedo_mva" },
        }}
    }},  -- Menu Item

    {requires = {Vehicle = true}}  -- Requires
)

AddVehicleInteractionAction(
    function() -- Condition
	    return GetEntityModel(GetVehiclePedIsIn(GetPlayerPed(-1))) == GetHashKey("dotspeedo") and IsVehicleExtraTurnedOn(GetVehiclePedIsIn(GetPlayerPed(-1)), 3)
    end,

    function() end, -- Action

    { text = "Operate LED Board", icon = "&#xF0B69;", subMenu = {
        { text = "Open / Close", icon = "&#xF0104;", action = "dotspeedo_openclose" },
        { text = "Reset Sign", icon = "&#xF12A9;", action = "dotspeedo_reset" },
        { text = "Signs", icon = "&#xF0B69;", subMenu = {
            { text = "Merge Left", icon = "&#xF0104;", action = "dotspeedo_arrowleft" },
            { text = "Merge Right", icon = "&#xF12A9;", action = "dotspeedo_arrowright" },
            { text = "Reset Sign", icon = "&#xF12A9;", action = "dotspeedo_reset" },
        }}
    }},  -- Menu Item

    {requires = {Vehicle = true}}  -- Requires
)

dotSpeedo_openClose = function()
    local veh = GetVehiclePedIsIn(GetPlayerPed(-1))
    local open = AreBombBayDoorsOpen(veh)

    SetVehicleAutoRepairDisabled(veh, true)

    if open then
        SetVehicleDoorShut(GetVehiclePedIsIn(GetPlayerPed(-1), false), 5, false)
        CloseBombBayDoors(veh)
        Base.Notification("DOT Board Closed")
    else
        SetVehicleDoorOpen(GetVehiclePedIsIn(GetPlayerPed(-1), false), 5, false, false)
        OpenBombBayDoors(veh)
        Base.Notification("DOT Board Opened")

    end
end

dotSpeedo_reset = function()
    local veh = GetVehiclePedIsIn(GetPlayerPed(-1))

    SetVehicleAutoRepairDisabled(veh, true)

    SetVehicleExtra(veh, 1, 1)
    SetVehicleExtra(veh, 2, 1)
    SetVehicleExtra(veh, 5, 1)
    SetVehicleExtra(veh, 6, 1)
    SetVehicleExtra(veh, 7, 1)
    SetVehicleExtra(veh, 8, 1)
    SetVehicleExtra(veh, 9, 1)
    SetVehicleExtra(veh, 10, 1)
    SetVehicleExtra(veh, 11, 1)
end

dotSpeedo_board = function(extra)
    local veh = GetVehiclePedIsIn(GetPlayerPed(-1))
    SetVehicleAutoRepairDisabled(veh, true)

    dotSpeedo_reset()

    SetVehicleExtra(veh, extra, 0)
end

dotSpeedo_board1 = function() dotSpeedo_board(1) end
dotSpeedo_board2 = function() dotSpeedo_board(2) end
dotSpeedo_board5 = function() dotSpeedo_board(5) end
dotSpeedo_board6 = function() dotSpeedo_board(6) end
dotSpeedo_board7 = function() dotSpeedo_board(7) end
dotSpeedo_board8 = function() dotSpeedo_board(8) end
dotSpeedo_board9 = function() dotSpeedo_board(9) end
dotSpeedo_board10 = function() dotSpeedo_board(10) end
dotSpeedo_board11 = function() dotSpeedo_board(11) end

onBaseReady(function()
    QUI.registerMenuAction("dotspeedo_openclose", dotSpeedo_openClose)
    QUI.registerMenuAction("dotspeedo_reset", dotSpeedo_reset)

    QUI.registerMenuAction("dotspeedo_arrowleft", dotSpeedo_board2)
    QUI.registerMenuAction("dotspeedo_arrowright", dotSpeedo_board1)

    QUI.registerMenuAction("dotspeedo_mergeleft", dotSpeedo_board5)
    QUI.registerMenuAction("dotspeedo_mergeright", dotSpeedo_board8)
    QUI.registerMenuAction("dotspeedo_roadwork", dotSpeedo_board6)
    QUI.registerMenuAction("dotspeedo_roadclosed", dotSpeedo_board7)
    QUI.registerMenuAction("dotspeedo_40", dotSpeedo_board9)
    QUI.registerMenuAction("dotspeedo_slowdown", dotSpeedo_board10)
    QUI.registerMenuAction("dotspeedo_mva", dotSpeedo_board11)

end)