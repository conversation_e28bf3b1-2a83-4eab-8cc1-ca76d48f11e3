refreshPlayerData = function()
	Wait(200)
	local playerData = Base.GetPlayerData()
	if (playerData and playerData.accounts) then
		local bankBalance = 0

		for i, account in ipairs(playerData.accounts) do
			if account.name == "bank" then
				bankBalance = account.money
			end
		end

		-- TODO_JOB_OPT: change inventory to not display jobs
		SendNUIMessage({
			charInfo = {
				name = playerData.icname,
				job = "unimplemented",
				jobName = "Check orgs app",
				jobGradeName = "on your phone",
				subjob = "unimplemented",
				subjobName = "Jobs will no",
				subjobGradeName = "longer show here",
				bank = bankBalance,
			}
		})
	end
end

onBaseReady(function()
	Wait(500)

	PlayerData = Base.GetPlayerData()
	refreshPlayerData()
end)

RegisterNetEvent("base:characterLoaded", function(data)
	Wait(500)
	refreshPlayerData()
end)

RegisterNetEvent("updatedPlayerOrgs", refreshPlayerData)

RegisterNetEvent("esx:setAccountMoney")
AddEventHandler('esx:setAccountMoney', function(account)
	refreshPlayerData()
end)

local days = {
	[0] = "Sunday",
	[1] = "Monday",
	[2] = "Tuesday",
	[3] = "Wednesday",
	[4] = "Thursday",
	[5] = "Friday",
	[6] = "Saturday"
}
local months = {
	[0] = "January",
	[1] = "February",
	[2] = "March",
	[3] = "April",
	[4] = "May",
	[5] = "June",
	[6] = "July",
	[7] = "August",
	[8] = "September",
	[9] = "October",
	[10] = "November",
	[11] = "December"
}

function CalculateTimeToDisplay()
	local hour = GetClockHours()
	local minute = GetClockMinutes()
	if hour <= 9 then
		hour = "0" .. hour
	end
	if minute <= 9 then
		minute = "0" .. minute
	end
	return hour .. ":" .. minute
end

Citizen.CreateThread(function()
	while true do
		Wait(1000)
		SendNUIMessage({
			time = CalculateTimeToDisplay(),
			date = days[GetClockDayOfWeek()] .. " | " .. months[GetClockMonth()]
		})
	end
end)

RegisterNetEvent("gcPhone:myPhoneNumber")
AddEventHandler("gcPhone:myPhoneNumber", function(phone)
	Base.SetPlayerData("phone", phone)
	SendNUIMessage({ charInfo = { phone = phone } })
end)
