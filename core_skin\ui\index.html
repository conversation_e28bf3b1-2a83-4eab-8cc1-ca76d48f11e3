<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        
        <script src="https://code.jquery.com/jquery-3.3.1.min.js" integrity="sha256-FgpCb/KJQlLNfOu91ta32o/NMZxltwRo8QtmkMRdAu8=" crossorigin="anonymous"></script>
        <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>        
        <script src="app.js"></script>
        <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet">
        <link href="styles.css" rel="stylesheet">
    </head>
    <body>
        <div id="container">
            <ul id="skin-tablist">
                <li><a tab="skin-basic" selected="selected">Basic</a></li>
                <li><a tab="skin-adv">Advanced</a></li>
                <li><a tab="skin-cos">Cosmetics</a></li>
                <li><a tab="skin-clothing">Clothing</a></li>
            </ul>
            <div id="content-container">
                <table class="skin-container" tab="skin-charCreate" cellspacing="20">
                    <tr>
                        <td><h3>Gender</h3></td>
                    </tr>
                    <tr>
                        <td><button class="modelButton" model="mp_m_freemode_01">Male</button></td>
                    </tr>
                    <tr>
                        <td><button class="modelButton" model="mp_f_freemode_01">Female</button></td>
                    </tr>
                </table>
                <table class="skin-container" tab="skin-basic" cellspacing="20">
                    <tr>
                        <td><h3>Heritage</h3></td>
                    </tr>
                    <tr>
                        <td><p>Mum</p><input type="range" min="0" max="45" value="0" category="skin" feature="mother"></td>
                        <td><p>Dad</p><input type="range" min="0" max="45" value="0" category="skin" feature="father"></td>
                    </tr>
                    <tr>
                        <td colspan="2"><p>Shape Mix</p><input type="range" min="0" max="100" value="50" category="skin" feature="shapemix"></td>
                    </tr>
                    <tr>
                        <td colspan="2"><p>Skin Colour</p><input type="range" min="0" max="45" value="0" category="skin" feature="colour"></td>
                    </tr>
                    <tr>
                        <td><h3>Facial Features</h3></td>
                    </tr>
                    <tr>
                        <td><p>Blemishes</p><input type="range" min="0" max="23" value="0" category="skin" feature="blemish" key="texture"></td>
                        <td><p>Severity</p><input type="range" min="0" max="100" value="0" category="skin" feature="blemish" key="opacity"></td>
                    </tr>
                    <tr>
                        <td><p>Ageing</p><input type="range" min="0" max="14" value="0" category="skin" feature="age" key="texture"></td>
                        <td><p>Severity</p><input type="range" min="0" max="100" value="0" category="skin" feature="age" key="opacity"></td>
                    </tr>
                    <tr>
                        <td><p>Freckles</p><input type="range" min="0" max="17" value="0" category="skin" feature="freckles" key="texture"></td>
                        <td><p>Severity</p><input type="range" min="0" max="100" value="0" category="skin" feature="freckles" key="opacity"></td>
                    </tr>
                    <tr>
                        <td><p>Complexion</p><input type="range" min="0" max="11" value="0" category="skin" feature="complexion" key="texture"></td>
                        <td><p>Severity</p><input type="range" min="0" max="100" value="0" category="skin" feature="complexion" key="opacity"></td>
                    </tr>
                    <tr>
                        <td><p>Sun Damage</p><input type="range" min="0" max="10" value="0" category="skin" feature="damage" key="texture"></td>
                        <td><p>Severity</p><input type="range" min="0" max="100" value="0" category="skin" feature="damage" key="opacity"></td>
                    </tr>
                </table>
                <table class="skin-container" tab="skin-adv" cellspacing="20">
                    <tr>
                        <td><h3>Nose</h3></td>
                        <td><h3>Eyes</h3></td>                    
                    </tr>
                    <tr>
                        <td><p>Width</p><input type="range" min="-100" max="100" value="0" category="skin" feature="nose" key="width"></td>
                        <td><p>Size</p><input type="range" min="-100" max="100" value="0" category="skin" feature="eyes" key="size"></td>
                    </tr>
                    <tr>
                        <td><p>Height</p><input type="range" min="-100" max="100" value="0" category="skin" feature="nose" key="height"></td>
                        <td><p>Colour</p><input type="range" min="-100" max="100" value="0" category="skin" feature="eyes" key="colour"></td>
                    </tr>
                    <tr> 
                        <td><p>Twist</p><input type="range" min="-100" max="100" value="0" category="skin" feature="nose" key="twist"></td>
                        <td><p>Brow Height</p><input type="range" min="-100" max="100" value="0" category="skin" feature="eyes" key="brow_height"></td>
                    </tr>
                    <tr>
                        <td><p>Peak Height</p><input type="range" min="-100" max="100" value="0" category="skin" feature="nose" key="peak_height"></td>
                        <td><p>Brow Forward</p><input type="range" min="-100" max="100" value="0" category="skin" feature="eyes" key="brow_forward"></td>
                    </tr>
                    <tr> 
                        <td><p>Peak Length</p><input type="range" min="-100" max="100" value="0" category="skin" feature="nose" key="peak_length"></td>
                    </tr>
                    <tr> 
                        <td><p>Peak Lowering</p><input type="range" min="-100" max="100" value="0" category="skin" feature="nose" key="peak_lowering"></td>
                    </tr>
                    <tr>                    
                        <td><h3>Cheeks</h3></td>
                        <td><h3>Jaw</h3></td>
                    </tr>
                    <tr>        
                        <td><p>Width</p><input type="range" min="-100" max="100" value="0" category="skin" feature="cheeks" key="width"></td>
                        <td><p>Width</p><input type="range" min="-100" max="100" value="0" category="skin" feature="jaw" key="width"></td>
                    </tr>
                    <tr>
                        <td><p>Height</p><input type="range" min="-100" max="100" value="0" category="skin" feature="cheeks" key="height"></td>
                        <td><p>Length</p><input type="range" min="-100" max="100" value="0" category="skin" feature="jaw" key="length"></td>
                    </tr>
                    <tr>
                        <td><p>Chub</p><input type="range" min="-100" max="100" value="0" category="skin" feature="cheeks" key="chub"></td>
                    </tr>

                    <tr>
                        <td><h3>Chin</h3></td>
                        <td><h3>Other</h3></td>
                    </tr>
                    <tr>
                        <td><p>Lower</p><input type="range" min="-100" max="100" value="0" category="skin" feature="chin" key="lower"></td>
                        <td><p>Lips</p><input type="range" min="-100" max="100" value="0" category="skin" feature="lip_thickness"></td>
                    </tr>
                    <tr>
                        <td><p>Length</p><input type="range" min="-100" max="100" value="0" category="skin" feature="chin" key="length"></td>
                        <td><p>Neck</p><input type="range" min="-100" max="100" value="0" category="skin" feature="neck_thickness"></td>
                    </tr>
                    <tr>
                        <td><p>Width</p><input type="range" min="-100" max="100" value="0" category="skin" feature="chin" key="width"></td>

                    </tr>
                    <tr>
                        <td><p>Tip</p><input type="range" min="-100" max="100" value="0" category="skin" feature="chin" key="tip"></td>
                    </tr>

                </table>
                <table class="skin-container" tab="skin-cos" cellspacing="20">
                    <tr>
                        <td><h3>Hair</h3></td>
                        <td><h3>Beard</h3></td>                    
                    </tr>
                    <tr>
                        <td><p>Style</p>
                            <table class="arrowSelector" cellspacing="0"><tr>
                                <td><button class="lower"><</button></td>
                                <td><input type="text" category="cosmetics" feature="head" key="style" value="0" min="0" max="150"></td>
                                <td><button class="raise">></button></td>
                            </tr></table>
                        </td>
                        <td><p>Style</p>
                            <table class="arrowSelector" cellspacing="0"><tr>
                                <td><button class="lower"><</button></td>
                                <td><input type="text" category="cosmetics" feature="beard" key="style" value="0" min="0" max="150"></td>
                                <td><button class="raise">></button></td>
                            </tr></table>
                        </td>
                    </tr>
                    <tr>
                        <td><p>Colour</p><input class="char-slider" type="range" min="0" max="63" value="0" category="cosmetics" feature="head" key="colour"></td>
                        <td><p>Thickness</p><input class="char-slider" type="range" min="0" max="100" value="0" category="cosmetics" feature="beard" key="thickness"></td>
                    </tr>
                    <tr>                        
                        <td><p>Highlights</p><input class="char-slider" type="range" min="0" max="63" value="0" category="cosmetics" feature="head" key="highlights"></td>
                        <td><p>Colour</p><input class="char-slider" type="range" min="0" max="63" value="0" category="cosmetics" feature="beard" key="colour"></td>
                    </tr>
                    <tr>
                        <td><p>Fade</p><input class="char-slider" type="range" min="0" max="67" value="0" category="cosmetics" feature="head" key="fade"></td>
                        <td></td>
                    </tr>

                    <tr>
                        <td><h3>Chest</h3></td>
                        <td><h3>Eyebrows</h3></td>                    
                    </tr>
                    <tr>
                        <td><p>Style</p><input class="char-slider" type="range" min="0" max="16" value="0" category="cosmetics" feature="chest" key="style"></td>
                        <td><p>Style</p><input class="char-slider" type="range" min="0" max="33" value="0" category="cosmetics" feature="eyebrows" key="style"></td>
                    </tr>
                    <tr>
                        <td><p>Thickness</p><input class="char-slider" type="range" min="0" max="100" value="0" category="cosmetics" feature="chest" key="thickness"></td>
                        <td><p>Thickness</p><input class="char-slider" type="range" min="0" max="100" value="0" category="cosmetics" feature="eyebrows" key="thickness"></td>
                    </tr>
                    <tr> 
                        <td><p>Colour</p><input class="char-slider" type="range" min="0" max="63" value="0" category="cosmetics" feature="chest" key="colour"></td>
                        <td><p>Colour</p><input class="char-slider" type="range" min="0" max="63" value="0" category="cosmetics" feature="eyebrows" key="colour"></td>
                    </tr>
                    <tr>
                        <td><h3>Lipstick</h3></td>
                        <td><h3>Makeup</h3></td>                    
                    </tr>
                    <tr>
                        <td><p>Style</p><input class="char-slider" type="range" min="0" max="9" value="0" category="cosmetics" feature="lipstick" key="style"></td>
                        <td><p>Style</p><input class="char-slider" type="range" min="0" max="74" value="0" category="cosmetics" feature="makeup" key="style"></td>
                    </tr>
                    <tr>
                        <td><p>Thickness</p><input class="char-slider" type="range" min="0" max="100" value="0" category="cosmetics" feature="lipstick" key="thickness"></td>
                        <td><p>Thickness</p><input class="char-slider" type="range" min="0" max="100" value="0" category="cosmetics" feature="makeup" key="thickness"></td>
                    </tr>
                    <tr> 
                        <td><p>Colour</p><input class="char-slider" type="range" min="0" max="63" value="0" category="cosmetics" feature="lipstick" key="colour"></td>
                        <td><p>Colour</p><input class="char-slider" type="range" min="0" max="63" value="0" category="cosmetics" feature="makeup" key="colour"></td>
                    </tr>
                    <tr>
                        <td><h3>Blush</h3></td>                   
                    </tr>
                    <tr>
                        <td><p>Style</p><input class="char-slider" type="range" min="0" max="6" value="0" category="cosmetics" feature="blush" key="style"></td>
                    </tr>
                    <tr>
                        <td><p>Thickness</p><input class="char-slider" type="range" min="0" max="100" value="0" category="cosmetics" feature="blush" key="thickness"></td>
                    </tr>
                    <tr> 
                        <td><p>Colour</p><input class="char-slider" type="range" min="0" max="63" value="0" category="cosmetics" feature="blush" key="colour"></td>
                    </tr>
                </table>
                <table class="skin-container" tab="skin-clothing" cellspacing="2">
                    <tr><td><h3>Head</h3></td><td style="font-size: 11;">Hint: You can type in a number too!</td></tr>
                    <tr>
                        <td><p>Hat</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="helmet" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Hat Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="helmet" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>                      
                    </tr>
                    <tr>
                        <td><p>Glasses</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="glasses" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Glasses Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="glasses" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Mask</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="mask" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Mask Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="mask" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Ear</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="ears" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Ear Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="ears" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr><td><h3>Chest</h3></td></tr>
                    <tr>
                        <td><p>Torso</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="torso" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Torso Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="torso" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Undershirt</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="tshirt" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Undershirt Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="tshirt" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>                    
                    <tr>
                        <td><p>Arms</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="arms" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Arms Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="arms" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Chain</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="chain" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Chain Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="chain" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Watch</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="watch" key="model" value="-1" min="-1" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Watch Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="watch" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Bracelet</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="bracelet" key="model" value="-1" min="-1" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Bracelet Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="bracelet" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Bag</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="bag" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Bag Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="bag" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Decals</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="decals" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Decals Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="decals" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr><td><h3>Legs</h3></td></tr>
                    <tr>
                        <td><p>Pants</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="pants" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Pants Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="pants" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Shoes</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="shoes" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Shoes Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="shoes" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                </table>
                <table class="skin-container" tab="skin-clothing-restricted" cellspacing="2">
                    <tr><td><h3>Head</h3></td><td style="font-size: 11;">Hint: You can type in a number too!</td></tr>
                    <tr>
                        <td><p>Hat</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="helmet" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Hat Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="helmet" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>                      
                    </tr>
                    <tr>
                        <td><p>Glasses</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="glasses" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Glasses Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="glasses" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Mask</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="mask" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Mask Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="mask" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Ear</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="ears" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Ear Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="ears" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr><td><h3>Chest</h3></td></tr>
                    <tr>
                        <td><p>Torso</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="torso" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Torso Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="torso" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Undershirt</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="tshirt" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Undershirt Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="tshirt" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Arms</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="arms" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Arms Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="arms" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Chain</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="chain" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Chain Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="chain" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Watch</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="watch" key="model" value="0" min="-1" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Watch Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="watch" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Bracelet</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="bracelet" key="model" value="0" min="-1" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Bracelet Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="bracelet" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Vest</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="bproof" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Vest Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="bproof" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Bag</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="bag" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Bag Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="bag" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Decals</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="decals" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Decals Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="decals" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr><td><h3>Legs</h3></td></tr>
                    <tr>
                        <td><p>Pants</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="pants" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Pants Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="pants" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Shoes</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="shoes" key="model" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                    <tr>
                        <td><p>Shoes Style</p></td>
                        <td><table class="arrowSelector" cellspacing="0"><tr>
                            <td><button class="lower"><</button></td>
                            <td><input type="text" category="clothing" feature="shoes" key="texture" value="0" min="0" max="100"></td>
                            <td><button class="raise">></button></td>
                        </tr></table></td>
                    </tr>
                </table>
            </div>            
            <div id="buttonpanel">
                <button id="resetSkin" class="skinChangeButton">Reset</button>
                <button id="saveSkin" class="skinChangeButton">Save</button>
            </div>
        </div>
        
        <div id="cameraBar">
            <button item="helmet" id="helmet" onclick="submitButtonStyle(this)">Hat</button>
            <button item="shirt" id="shirt" onclick="submitButtonStyle(this)">Shirt</button>
            <button item="decals" id="decals" onclick="submitButtonStyle(this)">Decals</button>
            <button item="bproof" id="bproof" onclick="submitButtonStyle(this)">Vest</button>
            <button item="pants" id="pants" onclick="submitButtonStyle(this)">Pants</button>
            <button item="shoes" id="shoes" onclick="submitButtonStyle(this)">Shoes</button>
            <button item="chain" id="chain" onclick="submitButtonStyle(this)">Chain</button>
            <button item="arms" id="arms" onclick="submitButtonStyle(this)">Gloves</button>
            <button item="bag" id="bag" onclick="submitButtonStyle(this)">Bag</button>
            <button item="mask" id="mask" onclick="submitButtonStyle(this)">Mask</button>
            <button item="glasses" id="glasses" onclick="submitButtonStyle(this)">Glasses</button>
        </div>

        <div id="cameraViewBar">
            <div style="margin-left: 10px; float: center; text-align: center;">
                <label id="rotationBarLabel">Rotation<input type="range" id="rotationBar" min="-180" max="180" value="0"></label> 
                <button id="handsUp" item="hands">Hands Up</button>
            </div>

            <div style="margin-top: 20px; margin-left: 10px; text-align: center;">
                Camera Options
            </div>

            <div style=" margin-top: 10px; text-align: center;">
                <button id="camera" item="head">Head</button>
                <button id="camera" item="bproof">Chest</button>
                <button id="camera" item="decalTexture">Decals</button>
                <button id="camera" item="pants">Pants</button>
                <button id="camera" item="shoes">Shoes</button>
                <button id="camera" item="whole_torso">Whole Body</button>
                <button id="camera" item="lower_torso">Lower Torso</button>
                <button id="camera" item="upper_torso">Upper Torso</button>
            </div>
            
        </div>
        
    </body>
</html>