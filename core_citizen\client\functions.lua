---
---	PD Functions
---
local actionComplete = false

local isZiptied = false
local isCuffed = false
local isDragged = false
local isDragging = false
local preCuffVariation = GetPedDrawableVariation(playerPed, 7)
local cuffCheck = false

function startCuffCheck()
	if (cuffCheck) then return end
	cuffCheck = true
	Citizen.CreateThread(function()
		while (isPlayerCuffed()) do
			Wait(0)
			local ped = PlayerPedId()
			if (not IsPedCuffed(ped) and not IsEntityInWater(ped)) then
				SetEnableHandcuffs(ped, true)
			elseif (IsEntityInWater(ped)) then
				SetEnableHandcuffs(ped, false)
				SetPedDiesInWater(ped, true)
			else
				SetPedDiesInWater(ped, false)
			end
		end
		SetPedDiesInWater(PlayerPedId(), true)
		SetEnableHandcuffs(PlayerPedId(), false)

		cuffCheck = false
	end)
end

function cuff(player)
	-- Cuff Checks
	local playerPed = GetPlayerPed(-1)
	local targetPed = GetPlayerPed(player)
	local state = Player(GetPlayerServerId(player)).state

	if (exports["base_scripting"]:inForceAnimation()) then return end

	if DoesEntityExist(targetPed) and player ~= PlayerId() and targetPed ~= playerPed then
		-- Neither player can be sprinting
		if GetEntitySpeed(targetPed) > 2.0 or GetEntitySpeed(playerPed) > 2.0 then
			return
		end

		Base.Sound.PlayOnEntity(PlayerPedId(), 6.0, "cuff" .. math.random(1, 4), 0.5)

		if state.cuff then
			Base.Animation.StartForcedAnim(targetPed, "mp_arresting", "idle", true, false, true)
			Base.Player.attachPlayerToEntity(targetPed, playerPed, 11816, 0.1, 0.65, 0.0, 0.0, 0.0, 0.0)
		else
			Base.Animation.StartForcedAnim(targetPed, "mp_arresting", "arrested_spin_r_0", false, false, true)
			Base.Player.attachPlayerToEntity(targetPed, playerPed, 11816, 0.1, 0.65, 0.0, 0.0, 0.0, 0.0)
		end

		Base.Animation.ActionAnimation("mp_arresting", "a_uncuff", true, true, true, 5,
			function()
				print("Cuffing player", targetPed)
				TriggerEvent("fdg_ui:SendNotification", "Player handcuffed")
				TriggerServerEvent("core_citizen:affectCitizen", GetPlayerServerId(player), "cuff")
			end,

			function()
				print("Cuffing player", targetPed)
				Base.Animation.StopForcedAnim(targetPed)
				TriggerServerEvent("core_citizen:affectCitizen", GetPlayerServerId(player), "uncuff")
			end
		)
	end
end

function isPlayerCuffed()
	return isCuffed
end

function isPlayerDragging()
	return isDragging
end

function addCuffs()
	local playerPed = GetPlayerPed(-1)

	TriggerEvent("inventory:removeWeapon")

	isCuffed = true
	TriggerEvent("playerCuffed", true)
	SetPedCanPlayGestureAnims(playerPed, false)
	LocalPlayer.state:set("cuff", true, true)

	-- Remember the old variation
	preCuffVariation = GetPedDrawableVariation(playerPed, 7)
	local gender = Base.Skin.getCurrentSkin().sex
	if gender == 0 then
		SetPedComponentVariation(playerPed, 7, 41, 0, 0)
	else
		SetPedComponentVariation(playerPed, 7, 25, 0, 0)
	end

	-- cuffing
	startCuffCheck()

	Citizen.CreateThread(function()
		while isCuffed do
			Wait(0)
			Base.Player.blockActions(false)
		end
	end)

	Citizen.CreateThread(function()
		if not isDead and not IsPedInAnyVehicle(PlayerPedId()) then
			Base.Animation.StartForcedAnim(PlayerPedId(), "mp_arresting", "idle", true, false, true)
		else
			while isDead or IsPedInAnyVehicle(PlayerPedId()) do
				Wait(50)
				if not isDead and not IsPedInAnyVehicle(PlayerPedId()) then
					Base.Animation.StartForcedAnim(PlayerPedId(), "mp_arresting", "idle", true, false, true)
					break
				end
			end
		end
	end)
end

function removeCuffs()
	local playerPed = GetPlayerPed(-1)
	isCuffed = false
	isZiptied = false
	SetPedCanPlayGestureAnims(playerPed, true)
	LocalPlayer.state:set("cuff", false, true)

	-- Bring back the old character variation
	SetPedComponentVariation(playerPed, 7, preCuffVariation, 0, 0)

	Base.Animation.StopForcedAnim(playerPed)
	TriggerEvent("playerCuffed", false)
end

RegisterNetEvent("core_citizen:uncuff")
AddEventHandler("core_citizen:uncuff", function(data)
	if isCuffed then
		removeCuffs()
	end
	Wait(500)
	DetachEntity(GetPlayerPed(-1), true, false)
end)

RegisterNetEvent("core_citizen:cuff")
AddEventHandler("core_citizen:cuff", function(data)
	if not isCuffed then
		if GetCurrentPedWeapon(GetPlayerPed(-1), 1) ~= false then -- Checks if player has weapon equiped, returns false if no weapon out
			local gun, _ = exports["core_inventory"]:getSelectedWeapon()
			RemoveWeaponFromPed(GetPlayerPed(-1), gun)
			Wait(5)
		end
		addCuffs()
	else
		removeCuffs()
	end

	Wait(500)
	DetachEntity(GetPlayerPed(-1), true, false)
end)

function ziptie(player)
	local ped = GetPlayerPed(player)

	if DoesEntityExist(ped) then
		Base.Animation.ActionAnimation("mp_arresting", "a_uncuff", true, true, true, 2,
			function()
				if IsEntityPlayingAnim(ped, "mp_arresting", "idle", 3) then
					TriggerEvent("fdg_ui:SendNotification", "Removed Zipties")
					TriggerServerEvent("core_citizen:affectCitizen", GetPlayerServerId(player), "ziptie")
				else
					local item = exports["core_inventory"]:getItem("ziptie")
					if not item or item.count < 1 then
						return Base.Notification("You do not have any zipties.")
					end
					TriggerEvent("fdg_ui:SendNotification", "Applied Zipties")
					TriggerServerEvent("core_citizen:affectCitizen", GetPlayerServerId(player), "ziptie")
					TriggerServerEvent("core_citizen:useZipties")
				end
			end,
			function()

			end
		)
	end
end

function addZiptie()
	local playerPed = GetPlayerPed(-1)

	winCount = 0
	isCuffed = true
	isZiptied = true
	SetPedCanPlayGestureAnims(playerPed, false)
	Base.Sound.PlayOnEntity(playerPed, 6.0, "Ziptie_On", 0.15)

	Base.Decors.set("PlayerZiptied", true, 3)
	Base.Animation.StartForcedAnim(playerPed, "mp_arresting", "idle", true, false, true)
	TriggerEvent("playerCuffed", true)
end

function removeZiptie()
	local playerPed = GetPlayerPed(-1)

	isZiptied = false
	isCuffed = false
	SetPedCanPlayGestureAnims(playerPed, true)
	Base.Sound.PlayOnEntity(playerPed, 6.0, "Ziptie_Cut", 0.3)

	Base.Decors.set("PlayerZiptied", false, 3)
	Base.Animation.StopForcedAnim(playerPed)
	TriggerEvent("playerCuffed", false)
end

RegisterNetEvent("core_citizen:ziptie")
AddEventHandler("core_citizen:ziptie", function(data)
	if not isZiptied and not isCuffed then
		if GetCurrentPedWeapon(GetPlayerPed(-1), 1) ~= false then -- Checks if player has weapon equiped, returns false if no weapon out
			local gun, _ = exports["core_inventory"]:getSelectedWeapon()
			RemoveWeaponFromPed(GetPlayerPed(-1), gun)
			Wait(5)
		end
		addZiptie()
	elseif isZiptied then
		removeZiptie()
	end

	Wait(500)
	DetachEntity(GetPlayerPed(-1), true, false)
end)

local winCount = 0

Citizen.CreateThread(function()
	while true do
		Wait(1000)
		if isZiptied and not isDead then
			local won = StartMinigame(3, 0.05 * winCount);
			if won then
				Base.Sound.PlayOnEntity(PlayerPedId(), 6.0, "russle", 1.0)
				winCount = winCount + 1
				TriggerEvent("fdg_ui:SendNotification",
					(winCount == 3 and "You breakout of your zipties." or "Your zipties loosen."))
			end

			if winCount == 3 then
				playerPed = PlayerPedId()
				isZiptied = false
				isCuffed = false
				SetPedCanPlayGestureAnims(playerPed, true)
				Base.Decors.set("PlayerZiptied", isZiptied, 3)
				TriggerEvent("playerCuffed", false)
				winCount = 0
				Player(GetPlayerServerId(PlayerId())).state:set('ziptie', false, true)
				TriggerServerEvent("core_citizen:log_ziptie", GetPlayerServerId(PlayerId()))

				if not exports["main_roleplay"]:IsPlayerInTrunk() then
					Base.Animation.StopForcedAnim(playerPed)
				end
			end

			Wait(20000)
		end
	end
end)

function StartMinigame(dif, inc) -- minigame, possibly add variables that can be set for different cars?
	local started      = true
	local won          = false

	local Config       = {
		Right = true,
		Inc = inc,
		WinOp = 0.0,
		Pins = 0,
		MaxPin = dif,
		Square = { CurPos = 0.575, MaxLef = 0.5, MaxRig = 0.64 },
		Possible = { Square = { 0.51, 0.52, 0.53, 0.54, 0.55, 0.56, 0.57, 0.58, 0.59, 0.60, 0.61, 0.62, 0.63 }, Speed = { 0.001, 0.0015, 0.002, 0.0025 } }
	}

	local CurrentPos   = math.random(1, #Config.Possible.Square);
	local CurrentSpeed = 0.135
	while started and isZiptied and not isDead do
		if Config.WinOp > 0.0 then
			Config.WinOp = Config.WinOp - 2;
		end

		-- qwick maphs
		if Config.Square.CurPos >= Config.Square.MaxRig and Config.Right then
			Config.Right = false;
		elseif Config.Square.CurPos <= Config.Square.MaxLef and not Config.Right then
			Config.Right = true;
		end

		if Config.Right then -- makes it move // multiplying the value by the distance between frames will mean regardless of your fps it won't go too quick
			Config.Square.CurPos = Config.Square.CurPos + CurrentSpeed * GetFrameTime()
		else
			Config.Square.CurPos = Config.Square.CurPos - CurrentSpeed * GetFrameTime()
		end

		-- square shit
		drawRct(0.5, 0.8, 0.15, 0.015, 0, 0, 0, 200)                             -- main square
		drawRct(0.5, 0.8, 0.15, 0.015, 0, 200, 0, Config.WinOp)                  -- green shit
		drawRct(Config.Possible.Square[CurrentPos], 0.8, 0.010, 0.015, 150, 0, 0, 150) -- red squares in the background
		drawRct(Config.Square.CurPos, 0.8, 0.010, 0.015, 200, 200, 0, 180)       -- yellow square

		if (IsControlJustPressed(0, 18)) then
			local dif = Config.Square.CurPos - Config.Possible.Square[CurrentPos];

			if ((dif > -0.015) and (dif < 0.015)) then
				Config.WinOp = 200; Config.Pins = Config.Pins + 1;
				if (Config.Pins >= Config.MaxPin) then
					started = false;
					won = true;
					return true;
				end
			else
				started = false;
				won = false;
				return false;
			end
			CurrentPos   = math.random(1, #Config.Possible.Square);
			CurrentSpeed = CurrentSpeed + Config.Inc;
		end
		Wait(0)
	end

	return false
end

function drawRct(x, y, width, height, r, g, b, a)
	DrawRect(x + width / 2, y + height / 2, width, height, r, g, b, a)
end

function drag(player)
	local playerPed = GetPlayerPed(-1)

	if isDragging == GetPlayerPed(player) then
		isDragging = false
	else
		isDragging = GetPlayerPed(player)
	end

	TriggerServerEvent("core_citizen:affectCitizen", GetPlayerServerId(player), "drag", GetPlayerServerId(PlayerId()))
end

AddEventHandler("interact", function()
	if isDragging then
		TriggerServerEvent("core_citizen:affectCitizen", Base.Player.getSourceFromPed(isDragging), "drag",
			GetPlayerServerId(PlayerId()))
		isDragging = false
	end
end)

RegisterNetEvent("core_citizen:drag")
AddEventHandler("core_citizen:drag", function(target)
	local playerPed = GetPlayerPed(-1)
	local targetPed = GetPlayerPed(GetPlayerFromServerId(target))

	if isCuffed then
		if isDragged then
			isDragged = false
			DetachEntity(GetPlayerPed(-1), true, false)
		else
			isDragged = true
			Base.Player.attachPlayerToEntity(playerPed, targetPed, 11816, 0.1, 0.65, 0.0, 0.0, 0.0, 0.0)
		end
	end
end)

local prioritySeats = { --Prioritise putting players in these seats first
	[`bcat`] = { 4, 3, 2, 1 },
	[`porslandy`] = { 2, 1, 0 },
}

function putInVehicle(player)
	local playerPed = GetPlayerPed(-1)

	local playerPed = GetPlayerPed(-1)
	local coords    = GetEntityCoords(playerPed)

	local vehicle   = Base.Vehicles.GetClosestVehicle(GetPlayerPed(-1), 10.0, true)

	if isDragging == GetPlayerPed(player) then
		isDragging = false
	end

	TriggerEvent("carry:stop")
	Wait(500)

	if DoesEntityExist(vehicle) then
		local maxSeats = GetVehicleMaxNumberOfPassengers(vehicle)
		local freeSeat = nil

		if prioritySeats[GetEntityModel(vehicle)] then
			for k, v in ipairs(prioritySeats[GetEntityModel(vehicle)]) do
				if IsVehicleSeatFree(vehicle, v) then
					freeSeat = v
					break
				end
			end
		end

		if freeSeat == nil then
			for i = maxSeats - 1, 0, -1 do
				if IsVehicleSeatFree(vehicle, i) then
					freeSeat = i
					break
				end
			end
		end

		if freeSeat ~= nil then
			TriggerServerEvent("core_citizen:affectCitizen", GetPlayerServerId(player), "putInVehicle",
				GetPlayerServerId(PlayerId()), NetworkGetNetworkIdFromEntity(vehicle), freeSeat)
		end
	end
end

RegisterNetEvent("core_citizen:putInVehicle")
AddEventHandler("core_citizen:putInVehicle", function(source, vehicle, seat)
	local playerPed = GetPlayerPed(-1)
	local targetPed = GetPlayerPed(source)

	if isDragged then
		isDragged = false
		Base.Player.detachPlayer(targetPed)
	end

	if LocalPlayer.state.unconscious then
		local hasGun, currentGun = GetCurrentPedWeapon(playerPed)
		if hasGun ~= false then
			RemoveWeaponFromPed(playerPed, currentGun)
		end
	end

	TriggerEvent("carry:stop")
	Wait(500)

	local wasCuffed = isCuffed
	local wasZiptied = isZiptied
	if wasCuffed then
		removeCuffs(player)
		Wait(200)
	end
	SetPedIntoVehicle(playerPed, NetworkGetEntityFromNetworkId(vehicle), seat)
	Wait(500)
	if wasCuffed and not wasZiptied then
		addCuffs()
	elseif wasZiptied then
		addZiptie()
	end

	exports["core_vehicles"]:setSeatbelt(true)
end)

local seatLabels = {
	[-1] = "Drivers Seat",
	[0] = "Passenger Seat",
	[1] = "Back Left Seat",
	[2] = "Back Right Seat",
	[3] = "Further Back Left Seat",
	[4] = "Further Back Right Seat",
}

function pullFromVehicle(vehicle)
	if DoesEntityExist(vehicle) then
		if GetVehicleDoorLockStatus(vehicle) == 2 then
			Base.Notification("Vehicle is locked")
			return
		end

		local seats = GetVehicleModelNumberOfSeats(GetEntityModel(vehicle))
		local elements = {}

		for seat = -1, seats do
			if DoesEntityExist(GetPedInVehicleSeat(vehicle, seat)) then
				local label = seatLabels[seat] or ("Seat " .. (seat + 2))
				table.insert(elements, { label = label, seat = seat })
			end
		end

		Base.UI.Menu.Open('default', GetCurrentResourceName(), 'seat_selector',
			{
				title    = "Choose Seat",
				align    = 'bottom-right',
				elements = elements,
			},

			function(data, menu)
				menu.close()

				local seat = data.current.seat
				local door = seat + 1
				local ped = GetPedInVehicleSeat(vehicle, seat)

				-- Check distance between ped and yourself
				local crds = GetEntityCoords(PlayerPedId())
				local targetCoords = GetEntityCoords(ped)
				local distance = GetDistanceBetweenCoords(crds.x, crds.y, crds.z, targetCoords.x, targetCoords.y,
					targetCoords.z, true)

				if distance > 3.0 then
					TriggerEvent("fdg_ui:SendNotification", "You are too far away")
					return
				end

				if DoesEntityExist(ped) and IsPedAPlayer(ped) and ped ~= PlayerId() then
					local target = GetPlayerServerId(NetworkGetPlayerIndexFromPed(ped))
					local state = Player(target).state

					TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(vehicle),
						"setDoorState", door, true)
					Wait(1000)
					TriggerServerEvent("core_citizen:affectCitizen", target, "pullFromVehicle",
						GetPlayerServerId(PlayerId()))

					if state.unconscious then
						Wait(500)
						TriggerEvent("carry:start", target)
					end

					Wait(1000)
					TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(vehicle),
						"setDoorState", door, false)
				end
			end,

			function(data, menu)
				menu.close()
			end
		)
	end
end

RegisterNetEvent("core_citizen:pullFromVehicle")
AddEventHandler("core_citizen:pullFromVehicle", function(source)
	local sourcePed = GetPlayerPed(GetPlayerFromServerId(source))
	local playerPed = PlayerPedId()

	local crds = GetEntityCoords(sourcePed)
	local head = GetEntityHeading(sourcePed)
	local x, y, z = table.unpack(crds + GetEntityForwardVector(sourcePed) * 0.4)

	SetEntityCoords(playerPed, x, y, crds.z)
	SetEntityHeading(playerPed, head)
end)

local playerSearching

function search(player, searchType)
	if not IsEntityVisible(GetPlayerPed(player)) then
		return Base.Notification("Unable to find player to search.")
	end

	TriggerServerEvent("fdg:sendTextToPlayer", GetPlayerServerId(player), "~r~Searching~w~ you", 6000)
	actionComplete = false
	Citizen.CreateThread(function()
		while not actionComplete do
			Wait(0)
			local coords = GetEntityCoords(GetPlayerPed(player))
			local state = Player(GetPlayerServerId(player)).state
			playerSearching = GetPlayerServerId(player)
			Base.DrawText(coords.x, coords.y, coords.z + 1.0, "Searching")

			while playerSearching do
				Wait(1000)
				local targetCoords = GetEntityCoords(GetPlayerPed(player))
				local playerCoords = GetEntityCoords(GetPlayerPed(-1))

				if not IsEntityVisible(GetPlayerPed(player)) then
					TriggerServerEvent("citizen:stopSearchPlayer", playerSearching)
					Base.Notification("Unable to find player to search!")
					exports["core_inventory"]:hideInventory()
					actionComplete = true
					break
				end

				local dist = GetDistanceBetweenCoords(playerCoords.x, playerCoords.y, playerCoords.z, targetCoords.x,
					targetCoords.y, targetCoords.z)
				if dist > 3 then
					TriggerServerEvent("citizen:stopSearchPlayer", playerSearching)
					Base.Notification("The person you were searching moved too far away!")
					exports["core_inventory"]:hideInventory()
					actionComplete = true
					break
				end

				if not state.ziptie and not state.cuff then
					TriggerServerEvent("citizen:stopSearchPlayer", playerSearching)
					Base.Notification("The person you were searching is no longer restrained!")
					exports["core_inventory"]:hideInventory()
					actionComplete = true
					break
				end
			end
		end
	end)

	Base.Animation.ActionAnimation("mp_common", "givetake1_a", true, true, true, 6, function()
		if actionComplete then return end
		playerSearching = GetPlayerServerId(player)

		if playerSearching ~= -1 then
			TriggerServerEvent("fdg:sendTextToPlayer", playerSearching, "~r~Searched~w~ you", 3000)
			TriggerServerEvent("citizen:searchPlayer", playerSearching)
		end

		Wait(2000)
		actionComplete = true
	end, function()
		actionComplete = true
		TriggerServerEvent("fdg:sendTextToPlayer", GetPlayerServerId(player), "~r~Stopped searching~w~ you", 3000)
	end)
end

RegisterNetEvent("inventory:clientSync")
AddEventHandler('inventory:clientSync', function(name, inv, money, dirty, capacity)
	if string.find(name, "playerinventory") and name ~= "playerinventory-" .. GetPlayerServerId(PlayerId()) then
		local _inventoryPlayer = string.gsub(name, "playerinventory%-", "")
		if not playerSearching then playerSearching = tonumber(_inventoryPlayer) end

		exports["core_inventory"]:loadExternalInventory("Searching Citizen", inv, money, dirty, capacity,
			function(itemIndex, count)
				TriggerServerEvent('core_citizen:confiscatePlayerItem', GetPlayerServerId(PlayerId()), itemIndex, count,
					playerSearching)
			end,

			function(itemIndex, count)
				TriggerServerEvent('core_citizen:confiscatePlayerItem', playerSearching, itemIndex, count)
			end,

			function()
				TriggerServerEvent('citizen:stopSearchPlayer', playerSearching)
				playerSearching = false
			end
		)
	end
end)

local function openLicenseRevokeMenu(_source)
	TriggerServerCallback("main_roleplay:GetLicenses", function(licenses, number)
		local licensestable = {}
		for k, v in pairs(licenses) do
			table.insert(licensestable, { label = v.label, value = v.type })
		end
		Base.UI.Menu.Open('default', GetCurrentResourceName(), "revoke_id_menu",
			{
				title    = "Revoke License Menu",
				align    = "bottom-right",
				elements = licensestable
			},
			function(revoke_data, revoke_menu)
				revoke_menu.close()
				Base.Notification(revoke_data.current.label .. " revoked.", { timeout = 5000, layout = "bottom-right" })
				TriggerServerEvent("main_roleplay:RevokeLicense", _source, revoke_data.current.value,
					revoke_data.current.label)
			end,
			function(revoke_data, revoke_menu)
				revoke_menu.close()
			end
		)
		-- type, label
	end, _source)
end

-- Interaction Options
local function canInteract()
	return not LocalPlayer.state.cuffed and not LocalPlayer.state.ziptied and not LocalPlayer.state.wounded
end

RegisterInteraction("player", "cuff", { label = "Cuff", emoji = "🔒", order = 1 },
	function(player)
		cuff(player)
	end, function(player)
		if not canInteract() then return false end
		if not DoesPlayerHaveOrgPermission("handcuff") then return false end

		return not Player(GetPlayerServerId(player)).state.cuff
	end
)

RegisterInteraction("player", "uncuff", { label = "Uncuff", emoji = "🔓", order = 1 },
	function(player)
		cuff(player)
	end, function(player)
		if not canInteract() then return false end
		if not DoesPlayerHaveOrgPermission("handcuff") then return false end

		return Player(GetPlayerServerId(player)).state.cuff
	end
)

-- Search
RegisterInteraction("player", "search", { label = "Search", emoji = "🔍", order = 3 },
	function(player)
		search(player, "full")
	end, function(player)
		if not canInteract() then return false end
		if (DoesPlayerHaveOrgPermission("search.full") and (Player(GetPlayerServerId(player)).state.cuff or Player(GetPlayerServerId(player)).state.ziptie)) or (Player(GetPlayerServerId(player)).state.ziptie and not Player(GetPlayerServerId(player)).state.wounded) then return true end
		return false
	end
)

-- Drag
RegisterInteraction("player", "drag", { label = "Escort", emoji = "🚶‍♂️", order = 2 },
	function(player)
		drag(player)
	end, function(player)
		if not canInteract() then return false end
		if not DoesPlayerHaveOrgPermission("handcuff") then return false end
		return Player(GetPlayerServerId(player)).state.cuff
	end
)

-- Put in Vehicle
RegisterInteraction("player", "putInVehicle", { label = "Put in Vehicle", emoji = "📥", order = 5 },
	function(player)
		putInVehicle(player)
	end, function(player)
		if not canInteract() then return false end
		if not DoesPlayerHaveOrgPermission("putinvehicle") then return false end
		return true
	end
)

-- Pull from Vehicle
RegisterInteraction("vehicle", "pullFromVehicle", { label = "Pull from Vehicle", emoji = "📤", order = 6 },
	function(vehicle)
		pullFromVehicle(vehicle)
	end, function(vehicle)
		if not canInteract() then return false end
		if not DoesPlayerHaveOrgPermission("pullfromvehicle") then return false end
		return true
	end
)

-- GSR test
RegisterInteraction("player", "gsr", { label = "GSR Test", emoji = "🔫", order = 5 },
	function(player)
		Base.Notification("Testing Sample", { layout = "bottom-right", theme = "progress", timeout = 3000 })
		local hasGsr = AwaitServerCallback("main_roleplay:GSRTest", GetPlayerServerId(player))
		Wait(3000)
		if (hasGsr) then
			Base.Notification("Test indicates traces of Gunshot Residue on their clothing.",
				{ timeout = 5000, layout = "bottom-right" })
		else
			Base.Notification("Test indicates no traces of anything.", { timeout = 5000, layout = "bottom-right" })
		end
	end, function(player)
		if not canInteract() then return false end
		if not DoesPlayerHaveOrgPermission("gsr") then return false end
		return true
	end
)

-- Revoke License
RegisterInteraction("player", "revokelicense", { label = "Revoke License", emoji = "🚗" },
	function(player)
		openLicenseRevokeMenu(GetPlayerServerId(player))
	end, function(player)
		if not canInteract() then return false end
		if not DoesPlayerHaveOrgPermission("revokelicense") then return false end
		return true
	end
)

-- Ziptie
RegisterInteraction("player", "ziptie", { label = "Ziptie", emoji = "🔒" },
	function(player)
		ziptie(player)
	end, function(player)
		if not canInteract() then return false end

		-- Require the ped to be surrendering
		local targetPed = GetPlayerPed(player)
		local targetState = Player(GetPlayerServerId(player)).state
		if not IsEntityPlayingAnim(targetPed, "random@arrests@busted", "idle_a", 3) and not IsEntityPlayingAnim(targetPed, "mp_arresting", "idle", 3) and not targetState.sedated then
			return false
		end

		local serverId = GetPlayerServerId(player)
		return Player(serverId).state.ziptie or exports["core_inventory"]:getItem(string.lower("ziptie"))
	end
)
