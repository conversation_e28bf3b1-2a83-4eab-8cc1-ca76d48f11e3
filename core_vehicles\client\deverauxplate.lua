local PLATE_COORDS = vector3(-928.40, -2040.1, 9.4)

local function openMenu()
    TriggerServerCallback("veh:devPlateOptions", function(vehElements)
        Base.UI.Menu.CloseAll()
        Base.UI.Menu.Open("default", GetCurrentResourceName(), "dev_plate_changer",
            {
                title = "Select vehicle to apply Deveraux plates",
                align = "bottom-right",
                elements = vehElements,
            },
            function(d)
                TriggerServerEvent("veh:toggleDevPlate", d.current.value)
                Base.UI.Menu.CloseAll()
            end,
            function()
                Base.UI.Menu.CloseAll()
            end
        )
    end)
end

local function pdDevPlate()
    TriggerServerCallback("veh:pdApplyDevPlate", function(pass)
        print(pass)
        if pass then
            local vehicle = GetVehiclePedIsIn(PlayerPedId())
            SetVehicleNumberPlateTextIndex(vehicle, 4)
        end
    end)
end

-- World marker interface
AddEventHandler("org:markerInteraction", function(type, marker)
    if type ~= "cop_dev_plate" then return end
    pdDevPlate()
end)

onBaseReady(function()
    Base.Markers.Create("dev_plate_changer", { x = PLATE_COORDS.x, y = PLATE_COORDS.y, z = PLATE_COORDS.z, size = 1.5 }, { r = 143, g = 45, b = 173 }, "Open plate shop", {}, openMenu, false, false, false)
end)
