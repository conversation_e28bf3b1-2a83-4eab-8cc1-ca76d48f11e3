<html>

    <head>
        <link href="styles.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.0.0/animate.min.css"/>
        
        <script src="script.js" defer></script>
        <script src="https://code.jquery.com/jquery-3.3.1.min.js" integrity="sha256-FgpCb/KJQlLNfOu91ta32o/NMZxltwRo8QtmkMRdAu8=" crossorigin="anonymous"></script>
        <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>        

        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    </head>

    <body>
        <object data="/ui/poker/index.html" width="100%" height="100%" type="text/html"></object>
        <div id="viewport-container" style="position: absolute";>

            <div id="kb-container" class="content-container">

                <header class="blue">

                    
                    <img src="img/tahnee-new.png" id="header-logo">
                    

                    <div class="header-element" id="last-right-element">
                        <label>NOW PLAYING</label>
                        <span id="now-playing"></span>
                    </div>

                    <div class="header-element">
                        <label>NOW SELLING</label>
                        <span id="now-selling"></span>
                    </div>

                    <div class="header-element">
                        <label>NEXT GAME</label>
                        <span id="next-timer"></span>
                    </div>

                    <div id="close-terminal">Exit Terminal</div>

                </header>

                <main>

                    <div id="numbers-betting" class="main-element">
                       
                        <div id="betting-header-container">
                            <label class="main-element-title">Select Your Numbers</label>
                            <label class="descriptor">Select up to ten numbers. The more numbers drawn that you pick, the bigger the win.</label>
                        </div>
                        
                        <div id="numbers-grid-container"></div>    
                        
                        <div id="betting-footer-container">
                            <label class="main-element-title" id="sides-descriptor">Select Your Side</label>

                            <label class="descriptor">Twenty numbers are drawn each game. Select the side that will have the most numbers drawn.</label> 
                            <div id="sides-container">

                                <div class="side-option heads">
                                    <label>Heads</label>
                                    <span>A chance to DOUBLE your money.</span>
                                </div>

                                <div class="side-option draw">
                                    <label>Draw</label>
                                    <span>A chance to QUADRUPLE your money.</span>
                                </div>

                                <div class="side-option tails">
                                    <label>Tails</label>
                                    <span>A chance to DOUBLE your money.</span>
                                </div>

                            </div>
                        </div>

                    </div>

                    

                    <div id="confirm-betting" class="main-element">

                        <label class="main-element-title">Bet Slip</label>

                        <div id="disclaimer">ALL BETS ARE NON-REFUNDABLE. MALFUNCTION VOIDS ALL PLAYS AND PAYS.</div>
                        
                        <div id="bet-slip-contents">

                            <div class="bs-item" id="bs-item-numbers">

                                <div class="bs-item-header">
                                    <label class="bs-title">Numbers Bet</label>
                                    <button class="new-clear-button bs-main-numbers" id="clear-bet-numbers">CLEAR</button>
                                </div>

                                <div class="bs-splash splash-numbers">Select numbers to begin.</div>

                                <div class="bs-picks bs-main-numbers">
                                    <label id="bs-numbers-title">Selected Numbers:</label>
                                    <span id="bs-numbers"></span>
                                    <label id="bs-amount-title"></label>
                                </div>
                                
                                <div class="bs-input bs-main-numbers">
                                    <div class="bs-amount-input">
                                        <label>$</label>
                                        <input type="number" id="bet-amount-numbers" name="bet-amount-numbers" required="required" placeholder="Enter Bet Amount">
                                    </div>
                                    <div class="bs-potential-container">
                                        <span class="bs-mpw-title">Max Potential Winnings</span>
                                        <span class="bs-mpw-calc calc-potwin-numbers">$100000</span>
                                    </div>
                                </div>

                                <button class="new-place-button bs-main-numbers input-open" id="place-bet-numbers">Place Bet</button>

                                <div class="bs-errors-container bs-main-numbers" id="errors-numbers"></div>
                        
                            </div>

                            <div class="bs-item" id="bs-item-sides">

                                <div class="bs-item-header">
                                    <label class="bs-title">Sides Bet</label>
                                    <button class="new-clear-button bs-main-sides" id="clear-bet-sides">CLEAR</button>
                                </div>

                                <div class="bs-splash splash-sides">Select a side to begin.</div>
                                
                                <div class="bs-picks bs-main-sides">
                                    <label id="bs-sides-title">Selected Side:</label>
                                    <span id="bs-sides">HEADS</span>
                                </div>
                                
                                <div class="bs-input bs-main-sides">
                                    <div class="bs-amount-input">
                                        <label>$</label>
                                        <input type="number" id="bet-amount-sides" name="bet-amount-sides" required="required" placeholder="Enter Bet Amount">
                                    </div>
                                    <div class="bs-potential-container">
                                        <span class="bs-mpw-title">Max Potential Winnings</span>
                                        <span class="bs-mpw-calc calc-potwin-sides">$100000</span>
                                    </div>
                                </div>

                                <button class="new-place-button bs-main-sides input-open" id="place-bet-sides">Place Bet</button>

                                <div class="bs-errors-container bs-main-sides" id="errors-sides"></div>
                        
                            </div>

                        </div>
                    </div>

                </main>

            <div>

        </div>
    </body>

</html>