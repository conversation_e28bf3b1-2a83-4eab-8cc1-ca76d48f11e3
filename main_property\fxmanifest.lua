fx_version 'adamant'
game 'gta5'


server_scripts {
	"@oxmysql/lib/MySQL.lua",
	"@base_scripting/reference/sinit.lua",
	"@base/server/utility.lua",
	"server/duckestate/*.lua",
	"server/classes/*.lua",
	"server/*.lua"
}

client_scripts {
	'@polyzone/client.lua',
	'@polyzone/BoxZone.lua',
	'@polyzone/EntityZone.lua',
	'@polyzone/CircleZone.lua',
	'@polyzone/ComboZone.lua',
	"@base_scripting/reference/cinit.lua",
	"@base/client/utility.lua",
	"@base/client/eventbridge.lua",
	"client/*.lua",
	"client/classes/*.lua"
}

dependencies {
	"base_scripting"
}

files {
    'frontend/dist/index.html',
    'frontend/dist/**/*.js',
    'frontend/dist/**/*.css',
    'frontend/dist/**/*.png',
    'frontend/dist/**/*.svg',
    'frontend/dist/**/*.jpg',
	'frontend/dist/**/*.jpeg',
    'frontend/dist/**/*.gif',
}

ui_page 'frontend/dist/index.html'
