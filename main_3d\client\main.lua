local htmlElements = {}

local STATIC_ELEMENTS = {

    {
        coords = vector3(-364.04736328125, 7415.3793945312, 6.0103798866272),
        heading = 160.7,
        rotate = true,
        image = "racing/td-background.png",
        scale = 2.5
    },

    -- Debate Stage
    -- {
    --     coords = vector3(689.69940185547, 587.3740234375, 130.85589599609),
    --     heading = 160.2,
    --     rotate = false,
    --     image = "election/debate2.jpg",
    --     scale = 6.5,
    --     offsetZ = 3.0
    -- },
    -- {
    --     coords = vector3(685.61, 576.03, 130.46),
    --     heading = 161.5,
    --     rotate = false,
    --     image = "election/chad.png",
    --     scale = 2.5,
    --     offsetZ = 0
    -- },

    -- {
    --     coords = vector3(681.44, 576.72, 130.46),
    --     heading = 190.2,
    --     rotate = false,
    --     image = "election/trump.png",
    --     scale = 2.5,
    --     offsetZ = 0
    -- },
    -- {
    --     coords = vector3(689.24, 574.08, 130.46),
    --     heading = 133.7,
    --     rotate = false,
    --     image = "election/desmond.png",
    --     scale = 2.5,
    --     offsetZ = 0
    -- },
    -- {
    --     coords = vector3(677.96, 576.85, 130.46),
    --     heading = 200.2,
    --     rotate = false,
    --     image = "election/pinky.png",
    --     scale = 2.5,
    --     offsetZ = 0
    -- },
    -- {
    --     coords = vector3(691.99, 571.91, 130.46),
    --     heading = 130.1,
    --     rotate = false,
    --     image = "election/aladeen.png",
    --     scale = 2.5,
    --     offsetZ = 0
    -- },

    -- -- Election 2
    -- {
    --     coords = vector3(-526.80340576172, -235.69409179688, 37.140174865723),
    --     heading = 208.1,
    --     rotate = false,
    --     image = "election/election.png",
    --     scale = 9.0,
    --     offsetZ = 3.0
    -- },

    -- {
    --     coords = vector3(-512.79693603516, -227.26593017578, 36.359069824219),
    --     heading = 208.1,
    --     rotate = false,
    --     image = "election/aladeen.png",
    --     scale = 4.5,
    --     offsetZ = 1.0
    -- },

    -- {
    --     coords = vector3(-497.92709350586, -227.78930664062, 36.409427642822),
    --     heading = 208.1,
    --     rotate = false,
    --     image = "election/chad.png",
    --     scale = 4.5,
    --     offsetZ = 1.0
    -- },

    -- {
    --     coords = vector3(-542.20123291016, -243.83012390137, 36.447296142578),
    --     heading = 208.1,
    --     rotate = false,
    --     image = "election/pinky.png",
    --     scale = 4.5,
    --     offsetZ = 1.0
    -- },

    -- {
    --     coords = vector3(-552.49816894531, -257.74566650391, 36.290203094482),
    --     heading = 208.1,
    --     rotate = false,
    --     image = "election/desmond.png",
    --     scale = 4.5,
    --     offsetZ = 1.0
    -- },

    -- {
    --     coords = vector3(-483.24359130859, -231.50593566895, 36.244293212891),
    --     heading = 208.1,
    --     rotate = false,
    --     image = "election/trump.png",
    --     scale = 4.5,
    --     offsetZ = 1.0
    -- },

    -- Voting
    -- {
    --     coords = vector3(-534.98, -221.67, 37.65),
    --     heading = 210.2,
    --     rotate = true,
    --     image = "election/vote_here.png",
    --     scale = 4.5,
    --     offsetZ = 2.0
    -- },
    -- {
    --     coords = vector3(683.39, 569.26, 130.46),
    --     heading = 210.2,
    --     rotate = true,
    --     image = "election/vote_here.png",
    --     scale = 4.5,
    --     offsetZ = 2.5
    -- },
    -- {
    --     coords = vector3(-895.6, 6692.42, 3.42),
    --     heading = 210.2,
    --     rotate = true,
    --     image = "election/vote_here.png",
    --     scale = 4.5,
    --     offsetZ = 2.5
    -- },

    -- Maze Bank Area Debate
    -- {
    --     coords = vector3(162.***********, -988.***********, 30.************),
    --     heading = 337.7,
    --     image = "streamteam/trannyparent2.png",
    --     scale = 2.5,
    --     rotate = true,
    --     offsetZ = 2.0
    -- },
    -- {
    --     coords = vector3(-338.***********, -1970.**********, 22.***********),
    --     heading = 302.6,
    --     image = "election/debate.png",
    --     scale = 8.1,
    --     offsetZ = 3.6
    -- },
    -- Candidates
    -- {
    --     coords = vector3(-331.***********, -1977.**********, 23.************),
    --     heading = 321.8,
    --     image = "streamteam/DevStreamIGposter.png",
    --     scale = 3.5
    -- },
    -- {
    --     coords = vector3(-329.***********, -1978.**********, 22.************),
    --     heading = 321.8,
    --     image = "election/2.png",
    --     scale = 3.5
    -- },
    -- {
    --     coords = vector3(-327.***********, -1980.**********, 22.************),
    --     heading = 321.8,
    --     image = "election/3.png",
    --     scale = 3.5
    -- },
    -- {
    --     coords = vector3(-333.87191772461, -1975.4371337891, 22.991930007935),
    --     heading = 321.8,
    --     image = "election/4.png",
    --     scale = 3.5
    -- },
    -- {
    --     coords = vector3(-336.18029785156, -1973.3250732422, 22.991931915283),
    --     heading = 321.8,
    --     image = "election/5.png",
    --     scale = 3.5
    -- },
}

local function refreshStaticElements(elements)
    CreateThread(function()
        Wait(100)

        -- delete elements
        for k, v in pairs(htmlElements) do
            RemoveHTML3D(v.txn)
            htmlElements[k] = nil
        end

        Wait(100)

        -- create elements
        for key, element in pairs(elements) do
            local txn, dui = DrawHTML3D("nui://" .. GetCurrentResourceName() .. "/dui/index.html", {
                textureSizeX = 1280,
                textureSizeY = 1500,
                offsetZ = element.offsetZ or 1.0,
                heading = element.heading,
                coords = element.coords,
                maxDist = CONFIG.MAX_RENDER_DISTANCE,
                scale = element.scale or 1.5,
                rotate = element.rotate or false,
                persistent = false,
                duiMessage = { type = element.type or "image", image = element.image }
            })
            htmlElements[txn] = { txn = txn, dui = dui }
            Wait(250)
        end
    end)
end

RegisterNetEvent("main_3d:create_static_elements", function(serverSigns)
    local elements = table.clone(STATIC_ELEMENTS)
    elements = table.merge(elements, serverSigns)

    print(json.encode(serverSigns))

    refreshStaticElements(elements)
end)
