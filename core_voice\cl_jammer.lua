-- jamming event
RegisterNetEvent("being_jammed")
AddEventHandler("being_jammed", function()

    -- Send NUI event to trigger jam
    Send<PERSON>IEvent("setJamming", true)
    local exit = false
    local previous_radio_state = Voice.Radio.blocked

    --thread that runs for five seconds
    Citizen.CreateThread(function()
        --if 5 seconds passed break
        while not exit do
            Voice.Radio.blocked = true
            Citizen.Wait(1000)
        end
    end)

    Citizen.Wait(30000)
    exit = true
    Voice.Radio.blocked = previous_radio_state
    --end the jam
    SendNUIEvent("setJamming", false)
end)