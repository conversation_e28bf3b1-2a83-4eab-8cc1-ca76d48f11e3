@import url('https://fonts.googleapis.com/css2?family=Passion+One:wght@900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Archivo&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Teko:wght@300&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

.inventory {
  table-layout: fixed;
}

.inventory tbody {
  display: block;
  overflow-y: scroll;
  max-height: 44vh !important;
}

.inventory thead tr {
  display: block;
}

#inventory-window {
  display: none;
  width: 100%;
  height: 100%;
}

#inventory-container {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: white;
  height: 50vh;
}

#inventory-container-center {
  width: 5%;
}

.inventoryslot {
  position: relative;
  min-width: 85px;
  width: 8vh;
  height: 8vh;
  background-color: rgba(10, 10, 10, 0.6);
  text-align: center;
  vertical-align: top;
}

.inventoryslot:hover {
  background: linear-gradient(to top, rgba(79, 119, 156, 0.678), rgba(10, 10, 10, 0.805));
}

.inventoryitem {
  z-index: 900;
  width: 8vh;
  height: 8vh;
}

.inventoryitem img {
  position: relative;
  left: 40%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
}

.countLabel {
  font-family: 'Inter', sans-serif;
  color: white;
  position: absolute;
  vertical-align: text-bottom;
  right: 3px;
  bottom: 3px;
  font-size: 1.0vh;
  font-weight: 300;
}

.itemLabel {
  font-family: 'Inter', sans-serif;
  position: relative;
  top: 40%;
  height: auto;
  text-align: center;
  color: white;
}

.inventory-header {
  width: 100%;
  display: block;
  background-color: rgba(15, 15, 15, 0.8);
  column-span: all;
  height: 30px;
  line-height: 30px;
  color: white;
  border-radius: 5px
}

.inventory-label {
  font-family: 'Inter', sans-serif;
  font-size: 12;
  font-weight: 500;
  padding-left: 20px;
  float: left;
}

.inventory-weight {
  font-family: 'Inter', sans-serif;
  font-size: 12;
  text-align: right;
  float: right;
}

.inventory-capacity {
  font-family: 'Inter', sans-serif;
  font-size: 12;
  padding-right: 10px;
  margin-left: 4px;
  text-align: right;
  float: right;
}

.copy-inventory {
  height: 100%;
  padding-right: 10px;
  float: right;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
