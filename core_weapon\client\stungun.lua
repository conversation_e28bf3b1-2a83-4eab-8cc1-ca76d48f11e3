Citizen.CreateThread(function()
    while true do 
        Wait(0)
        SetPedMinGroundTimeForStungun(GetPlayerPed(-1), 10000)
        if IsPedBeingStunned(GetPlayerPed(-1)) then
            stunGun()
        else
            Wait(750)
        end
    end
end)

function stunGun()
    local playerPed = GetPlayerPed(-1)
    RequestAnimSet("move_m@drunk@verydrunk")
    while not HasAnimSetLoaded("move_m@drunk@verydrunk") do
        Citizen.Wait(0)
    end
    --DoScreenFadeOut(500)
    SetPedMinGroundTimeForStungun(GetPlayerPed(-1), 10000)
    SetPedMovementClipset(playerPed, "move_m@drunk@verydrunk", true)
    --SetTimecycleModifier("spectator5")
    SetPedIsDrunk(playerPed, true)
    SetPedMotionBlur(playerPed, true)
    Wait(500)
    --DoScreenFadeIn(500)
    Wait(60000)
    --DoScreenFadeOut(500)
    Wait(500)
    --ClearTimecycleModifier()
    ResetScenarioTypesEnabled()
    ResetPedMovementClipset(playerPed, 0)
    SetPedIsDrunk(playerPed, false)
    SetPedMotionBlur(playerPed, false)
    --DoScreenFadeIn(500)
end