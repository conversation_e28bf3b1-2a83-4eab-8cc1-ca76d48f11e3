local inCasino              = false
local videoWallRenderTarget = nil
local showBigWin            = false

--
-- Teleports
--

local points                = {
    {
        id = "entry-main",
        label = "Enter Casino",
        entryCoords = vector3(935.64, 46.58, 81.09),
        exitCoords = vector3(1089.65, 206.06, -48.99),
        heading = 358.47,
        type = "entry"
    },
    {
        id = "exit-casino",
        label = "Exit Casino",
        entryCoords = vector3(1089.65, 205.96, -48.99),
        exitCoords = vector3(935.64, 46.58, 81.09),
        heading = 100.0,
        type = "exit"
    },

    --------

    -- {
    --     id = "entry-terace",
    --     label = "Go to Terace",
    --     entryCoords = vector3(1085.7, 214.71, -49.20),
    --     exitCoords = vector3(964.95, 58.40, 112.55),
    --     type = "exit"
    -- },
    -- {
    --     id = "exit-terace",
    --     label = "Return to Casino",
    --     entryCoords = vector3(964.95, 58.40, 112.55),
    --     exitCoords = vector3(1085.7, 214.71, -49.20),
    --     type = "entry"
    -- },

    --------

    -- { id = "entry-helipad",
    --     label = "To Helipad",
    --     entryCoords = vector3(1119.36, 266.92, -51.04),
    --     exitCoords = vector3(959.30, 31.75, 120.22),
    --     type = "exit"
    -- },
    -- { id = "exit-helipad",
    --     label = "Return to Casino",
    --     entryCoords = vector3(959.30, 31.75, 120.22),
    --     exitCoords = vector3(1119.36, 266.92, -51.04),
    --     type = "entry"
    -- },
}

onBaseReady(function()
    createCasinoTeleports()
end)

function createCasinoTeleports()
    for k, marker in pairs(points) do
        Base.Markers.Delete(marker.id)
        Base.Markers.Create(marker.id, { x = marker.entryCoords.x, y = marker.entryCoords.y, z = marker.entryCoords.z, marker = 20, size = -0.5, bobbing = true, noLower = true }, { r = 0, g = 150, b = 200 }, marker.label, marker,
            function(v)
                teleportAtPoint(v)
            end)
    end
end

function teleportAtPoint(point)
    DoScreenFadeOut(500)
    while (not IsScreenFadedOut()) do Wait(0) end
    NetworkFadeOutEntity(PlayerPedId(), false, true)

    Wait(100)

    local entity = PlayerPedId()
    Base.Player.setEntityVisible(false)
    SetEntityCoords(entity, point.exitCoords.x, point.exitCoords.y, point.exitCoords.z - 0.95, 0.0, 0.0, 0.0, false)
    FreezeEntityPosition(entity, true)
    SetEntityHeading(entity, point.heading)

    if point.type == "entry" then
        inCasino = true
        startCasinoThreads()
        TriggerEvent("fdg_ui:SendNotification", "Gamble responsibly, see the front counter for blacklisting")
        -- createWheels()
    elseif point.type == "exit" then
        inCasino = false
        -- DeleteEntity(mainWheel)
        -- DeleteEntity(baseWheel)
    end
    -- create the exit marker

    FreezeEntityPosition(entity, false)
    Base.Player.setEntityVisible(true)
    SetGameplayCamRelativeHeading(0.0)
    SetGameplayCamRelativePitch(0.0, 0.0)
    NetworkFadeInEntity(PlayerPedId(), true)

    Wait(100)
    DoScreenFadeIn(500)
end

--
-- Threads
--

function startCasinoThreads()
    local interior = GetInteriorAtCoords(GetEntityCoords(GetPlayerPed(-1)))
    while not IsInteriorReady(interior) do Citizen.Wait(10) end
    RequestStreamedTextureDict('Prop_Screen_Vinewood')

    while not HasStreamedTextureDictLoaded('Prop_Screen_Vinewood') do
        Citizen.Wait(100)
    end

    RegisterNamedRendertarget('casinoscreen_01')

    LinkNamedRendertarget(`vw_vwint01_video_overlay`)

    videoWallRenderTarget = GetNamedRendertargetRenderId('casinoscreen_01')

    Citizen.CreateThread(function()
        local lastUpdatedTvChannel = 0

        while true do
            Citizen.Wait(0)

            if not inCasino then
                ReleaseNamedRendertarget('casinoscreen_01')

                videoWallRenderTarget = nil
                showBigWin            = false

                break
            end

            if videoWallRenderTarget then
                local currentTime = GetGameTimer()

                if showBigWin then
                    setVideoWallTvChannelWin()

                    lastUpdatedTvChannel = GetGameTimer() - 33666
                    showBigWin           = false
                else
                    if (currentTime - lastUpdatedTvChannel) >= 42666 then
                        setVideoWallTvChannel()

                        lastUpdatedTvChannel = currentTime
                    end
                end

                SetTextRenderId(videoWallRenderTarget)
                SetScriptGfxDrawOrder(4)
                SetScriptGfxDrawBehindPausemenu(true)
                DrawInteractiveSprite('Prop_Screen_Vinewood', 'BG_Wall_Colour_4x4', 0.25, 0.5, 0.5, 1.0, 0.0, 255, 255, 255, 255)
                DrawTvChannel(0.5, 0.5, 1.0, 1.0, 0.0, 255, 255, 255, 255)
                SetTextRenderId(GetDefaultScriptRendertargetRenderId())
            end
        end
    end)
end

--
-- Functions
--

function setVideoWallTvChannel()
    SetTvChannelPlaylist(0, "CASINO_DIA_PL", true)
    SetTvAudioFrontend(true)
    SetTvVolume(-100.0)
    SetTvChannel(0)
end

function setVideoWallTvChannelWin()
    SetTvChannelPlaylist(0, 'CASINO_WIN_PL', true)
    SetTvAudioFrontend(true)
    SetTvVolume(-100.0)
    SetTvChannel(-1)
    SetTvChannel(0)
end

function Draw3DText(x, y, z, text)
    -- Check if coords are visible and get 2D screen coords
    local onScreen, _x, _y = World3dToScreen2d(x, y, z)
    if onScreen then
        -- Calculate text scale to use
        local dist = GetDistanceBetweenCoords(GetGameplayCamCoords(), x, y, z, 1)
        local scale = 1.8 * (1 / dist) * (1 / GetGameplayCamFov()) * 100

        -- Draw text on screen
        SetTextScale(scale, scale)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextColour(255, 255, 255, 255)
        SetTextDropShadow(0, 0, 0, 0, 255)
        SetTextDropShadow()
        SetTextEdge(4, 0, 0, 0, 255)
        SetTextOutline()
        SetTextEntry("STRING")
        SetTextCentre(1)
        AddTextComponentString(text)
        DrawText(_x, _y)
    end
end
