local DAMAGE_INTERVAL = 10
local RAGE_INTERVAL = 90

local effect = StatusEffect("rabies", "Rabies",
    {
        onStart = function(effect)
            effect.data.ticks = 0
            SetPlayerMeleeWeaponDamageModifier(PlayerId(), 4.0)
            AddPlayerSpeedBuff("rabies", 0.2)
        end,

        onTick = function(effect)
            effect.data.ticks = (effect.data.ticks or 0) + 1

            if effect.data.ticks % DAMAGE_INTERVAL == 0 then
                SetEntityHealth(PlayerPedId(), GetEntityHealth(PlayerPedId()) - 1)

                if GetEntityHealth(PlayerPedId()) <= 0 then
                    Wait(3000)

                    TriggerEvent("playerDamage", {
                        weaponLabel = "Rabies",
                        weaponType = "OVRD_SEVERE",
                        damageOverTime = false,
                        fatal = true,
                        isMelee = false,
                        boneLabel = "Unknown",
                        damage = 100,
                    })
                    effect.stop()
                end
            end

            if effect.data.ticks % RAGE_INTERVAL == 0 then
                local player, closestDistance = Base.Player.getClosestPlayer(GetEntityCoords(PlayerPedId()), 10.0)
                if player ~= -1 and closestDistance < 5.0 then
                    SetPlayerControl(PlayerId(), false, 256)
                    exports.main_progressbar:start(4000, "Panicking...")
                    TaskReactAndFleePed(PlayerPedId(), GetPlayerPed(player))
                    Wait(4000)
                    SetPlayerControl(PlayerId(), true, 256)
                    ClearPedTasks(PlayerPedId())
                end
            end
        end,

        onStop = function()
            SetPlayerMeleeWeaponDamageModifier(PlayerId(), 4.0)
            RemoveSpeedBuff("rabies")
        end,
    },
    {
        alert = false,
        desc = "You're infected with rabies",
        subdesc = "Strength, speed, and aggression are increased.",
        tickRate = 1000,
    }
)

RegisterInteraction("player", "inject_rabies", { label = "Inject Rabies" },
    function(player)
        exports.main_roleplay:startDatabaseAnim("inject")
        TriggerServerEvent("effect:injectRabies", GetPlayerServerId(player))
    end,
    function(player)
        return exports["core_inventory"]:getItem("rabiessyringe")
    end
)

RegisterNetEvent("effects:startRabies", function()
    effect.start()
end)
