fx_version "adamant"
game "gta5"

description "QUI Controller Resource"

client_scripts {
    "@base_scripting/reference/cinit.lua",
    "@base/client/utility.lua",
    "@base/client/eventbridge.lua",
    "client/ui.lua",
    "client/menus.lua",
    "client/targetcrosshair.lua"
}

exports {
    "registerMenuAction",
    "setRadialMenuItems",
    "openRadialMenu",
    "closeRadialMenu",
    "registerQUICallback",
    "sendQUIEvent",
    "toggleQUIState",
}

files {
    "dist/index.html",
    "dist/**/*.js",
    "dist/**/*.css",
    "dist/**/*.png",
    "dist/**/*.eot",
    "dist/**/*.ttf",
    "dist/**/*.woff",
    "dist/**/*.woff2"
}

ui_page "dist/index.html"
