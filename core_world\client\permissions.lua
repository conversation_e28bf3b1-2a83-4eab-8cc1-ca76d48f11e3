-- TODO: Refactor this whole file
function CheckVeh(allowed) -- to stop repeat code
	if (allowed == 1) then
		return true
	else
		if (IsPedInAnyVehicle(PlayerPedId(), true)) then
			return false
		else
			return true
		end
	end
end

-- properties

local ownedProperties = {}

RegisterNetEvent("fdg_properties:syncProperties", function(properties, houses)
	ownedProperties = {}
	for k, v in pairs(properties) do
		ownedProperties[v.name] = true
	end
	refreshTeleports()
end)

function hasKeyToProperty(property)
	if (property == nil) then
		return false
	end
	return ownedProperties[property] ~= nil
end

-- licenses

local currentLicenses = {}

RegisterNetEvent("base:characterLoaded", function(data)
	local ped = PlayerPedId()
	SetPedHelmet(ped, false)
	Wait(1000)
	TriggerServerCallback('main_roleplay:GetLicenses', function(licences, number)
		currentLicenses = licences
		refreshTeleports()
	end)
end)

RegisterNetEvent("base:updateLicenses", function(licenses)
	currentLicenses = licenses
	refreshTeleports()
end)

function hasLicense(licenses)
	if (licenses == nil) then
		return false
	end

	licenses = type(licenses) == "string" and json.decode(licenses) or licenses

	for _, v in pairs(currentLicenses) do
		for _, i in pairs(licenses) do
			if (v.type == i) then
				return true
			end
		end
	end

	return false
end
