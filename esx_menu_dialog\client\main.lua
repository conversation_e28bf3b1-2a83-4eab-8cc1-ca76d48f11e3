local Keys = {
	["ESC"] = 322, ["F1"] = 288, ["F2"] = 289, ["F3"] = 170, ["F5"] = 166, ["F6"] = 167, ["F7"] = 168, ["F8"] = 169, ["F9"] = 56, ["F10"] = 57,
	["~"] = 243, ["1"] = 157, ["2"] = 158, ["3"] = 160, ["4"] = 164, ["5"] = 165, ["6"] = 159, ["7"] = 161, ["8"] = 162, ["9"] = 163, ["-"] = 84, ["="] = 83, ["BACKSPACE"] = 177,
	["TAB"] = 37, ["Q"] = 44, ["W"] = 32, ["E"] = 38, ["R"] = 45, ["T"] = 245, ["Y"] = 246, ["U"] = 303, ["P"] = 199, ["["] = 39, ["]"] = 40, ["ENTER"] = 18,
	["CAPS"] = 137, ["A"] = 34, ["S"] = 8, ["D"] = 9, ["F"] = 23, ["G"] = 47, ["H"] = 74, ["K"] = 311, ["L"] = 182,
	["LEFTSHIFT"] = 21, ["Z"] = 20, ["X"] = 73, ["C"] = 26, ["V"] = 0, ["B"] = 29, ["N"] = 249, ["M"] = 244, [","] = 82, ["."] = 81,
	["LEFTCTRL"] = 36, ["LEFTALT"] = 19, ["SPACE"] = 22, ["RIGHTCTRL"] = 70,
	["HOME"] = 213, ["PAGEUP"] = 10, ["PAGEDOWN"] = 11, ["DELETE"] = 178,
	["LEFT"] = 174, ["RIGHT"] = 175, ["TOP"] = 27, ["DOWN"] = 173,
	["NENTER"] = 201, ["N4"] = 108, ["N5"] = 60, ["N6"] = 107, ["N+"] = 96, ["N-"] = 97, ["N7"] = 117, ["N8"] = 61, ["N9"] = 118
}

local MenuType    = 'dialog'
local OpenedMenus = {}

local openMenu = function(namespace, name, data)
	OpenedMenus[namespace .. '_' .. name] = true

	SendNUIMessage({
		action    = 'openMenu',
		namespace = namespace,
		name      = name,
		data      = data,
	})

	Wait(200)

	SetNuiFocus(true, true)
end

local closeMenu = function(namespace, name)

	OpenedMenus[namespace .. '_' .. name] = nil
	local OpenedMenuCount                 = 0

	SendNUIMessage({
		action    = 'closeMenu',
		namespace = namespace,
		name      = name,
		data      = value,
	})

	for k,v in pairs(OpenedMenus) do
		if v == true then
			OpenedMenuCount = OpenedMenuCount + 1
		end
	end

	if OpenedMenuCount == 0 then
		SetNuiFocus(false)
	end
end



RegisterNUICallback('menu_submit', function(data, cb)

	local menu = Base.UI.Menu.GetOpened(MenuType, data._namespace, data._name)

	if menu.submit ~= nil then
		menu.submit(data, menu)
	end

	cb('OK')
end)

RegisterNUICallback('menu_cancel', function(data, cb)

	local menu = Base.UI.Menu.GetOpened(MenuType, data._namespace, data._name)

	if menu.cancel ~= nil then
		menu.cancel(data, menu)
	end

	cb('OK')
end)

RegisterNUICallback('menu_change', function(data, cb)

	local menu = Base.UI.Menu.GetOpened(MenuType, data._namespace, data._name)

	if menu.change ~= nil then
		menu.change(data, menu)
	end

	cb('OK')
end)

onBaseReady(function()
	Base.UI.Menu.RegisterType(MenuType, openMenu, closeMenu)
end)

local lastCount = 0
Citizen.CreateThread(function()
	while true do
	  	Wait(0)
	  	local OpenedMenuCount = 0

	  	for k,v in pairs(OpenedMenus) do
	  		if v == true then
	  			OpenedMenuCount = OpenedMenuCount + 1
	  			break
	  		end
	  	end

	  	if OpenedMenuCount > 0 then
			DisableControlAction(0, 1,   true) -- LookLeftRight
			DisableControlAction(0, 2,   true) -- LookUpDown
			DisableControlAction(0, 142, true) -- MeleeAttackAlternate
			DisableControlAction(0, 106, true) -- VehicleMouseControlOverride
			DisableControlAction(0, 12, true) -- WeaponWheelUpDown
			DisableControlAction(0, 14, true) -- WeaponWheelNext
			DisableControlAction(0, 15, true) -- WeaponWheelPrev
			DisableControlAction(0, 16, true) -- SelectNextWeapon
			DisableControlAction(0, 17, true) -- SelectPrevWeapon
		end

		if (OpenedMenuCount > 0 and lastCount ~= OpenedMenuCount) then
			Base.Controls.disableAll(true)
		elseif (OpenedMenuCount == 0 and lastCount ~= OpenedMenuCount) then
			Base.Controls.disableAll(false)
		end

		lastCount = OpenedMenuCount
	end
end)