fx_version "adamant"
games { "gta5" }
description "Scripts which relate to the world and environment. Examples include weather, door locks and static teleports"
version "1.0"

client_scripts {
    "@base_scripting/reference/cinit.lua",
    "@polyzone/client.lua",
    "@base/client/utility.lua",
    "@base/client/player.lua",
    "@base/client/markers.lua",
    "@base/client/sounds.lua",
    "client/*.lua",
}

exports {
    "getWeather",
}

server_scripts {
    "@base_scripting/reference/sinit.lua",
    "@polyzone/client.lua",
    "@oxmysql/lib/MySQL.lua",
    "@base/server/utility.lua",
    "@base/server/instance.lua",
    "server/*.lua"
}

shared_scripts {
    "config.lua"
}
