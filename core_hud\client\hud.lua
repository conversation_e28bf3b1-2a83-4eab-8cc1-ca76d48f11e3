local raw = LoadResourceFile(GetCurrentResourceName(), GetResourceMetadata(GetCurrentResourceName(), 'postal_file'))
local postals = json.decode(raw) -- holds all postals in a json then converts to table

local hud = config.setDefaultHud()

local hudHidden = false
local adminPlayerBlips = false

Citizen.CreateThread(function()
    while true do
        local ped        = PlayerPedId()
        local pos        = GetEntityCoords(ped)

        -- street labels
        local str1, str2 = GetStreetNameAtCoord(pos.x, pos.y, pos.z)
        local street     = GetStreetNameFromHashKey(str1)
        local suburb     = config.zones[GetNameOfZone(pos.x, pos.y, pos.z)] or lastSuburb
        local time       = "00:00"
        lastSuburb       = suburb

        if (str2 ~= 0) then street = street .. " & " .. GetStreetNameFromHashKey(str2) end

        -- clock time
        if (GetClockHours() < 10) then
            time = "0" .. GetClockHours()
        else
            time = GetClockHours()
        end

        if (GetClockMinutes() < 10) then
            time = time .. ":0" .. GetClockMinutes()
        else
            time = time .. ":" .. GetClockMinutes()
        end

        -- postals
        local ndm = -1                                       -- nearest distance magnitude
        local ni = -1                                        -- nearest index
        for i, p in ipairs(postals) do
            local dm = (pos.x - p.x) ^ 2 + (pos.y - p.y) ^ 2 -- distance magnitude
            if ndm == -1 or dm < ndm then
                ni = i
                ndm = dm
            end
        end

        local direction = math.floor((GetEntityHeading(ped)))

        -- sends in raw heading in a float
        hud.location.heading = calculateHeading(direction)
        -- top bar with street names
        hud.location.street = street
        -- bottom bar with area | postcode | time
        hud.location.suburb = suburb
        hud.location.postcode = postals[ni].code
        hud.location.time = time

        Wait(1000)
    end
end)

-- Health HUD Updating
onBaseReady(function()
    while true do
        local ped = PlayerPedId()
        local pos = GetEntityCoords(ped)

        hud.id = "HUD_REFRESH"

        -- true/false if the hud should be hidden
        hud.hide = hudHidden

        -- setting of health and armour
        hud.health = math.floor(GetEntityHealth(ped) - 100)
        hud.armour = math.floor(GetPedArmour(ped))

        -- if the player is speaking then this will return true
        hud.voice.active = NetworkIsPlayerTalking(PlayerId())

        -- our script handles death differently, if player is dead in our script then health is set to 0
        if (isDead or hud.health > 500) then hud.health = 0 end

        if (IsPauseMenuActive()) then
            hud.hide = true
        end

        if IsPedSwimming(ped) then
            hud.underwaterTime = GetPlayerUnderwaterTimeRemaining(PlayerId()) * 10

            if hud.underwaterTime <= 0 then
                hud.underwaterTime = 0
            end
        else
            hud.underwaterTime = nil
        end

        -- vehicle stuff / only set if they are in a vehicle to save performance
        hud.vehicle.inVehicle = IsPedInAnyVehicle(ped, false)
        if (hud.vehicle.inVehicle) then
            local veh = GetVehiclePedIsIn(ped, false)
            hud.vehicle.fuel = math.floor(getVehicleFuel(veh))
            hud.vehicle.speed = math.floor(GetEntitySpeed(veh) * 3.6)
            hud.vehicle.type = "car"

            if (hud.vehicle.fuel < 0) then
                hud.vehicle.fuel = 0.0
            end

            local class = GetVehicleClass(veh)   -- gets class
            if (class == 15 or class == 16) then -- 15 is Heli, 16 is plane
                hud.vehicle.type = "air"
            end

            if hud.vehicle.type == "air" then
                hud.vehicle.speed = math.floor(GetEntitySpeed(GetVehiclePedIsIn(GetPlayerPed(-1), false)) * 0.539957)
            end
        end

        -- Sends NUI message to the JS file
        SendNUIMessage(hud)

        -- hides minmap
        if not isDead and gpsEnabled then
            DisplayRadar(true)
        elseif (hudHidden) then
            DisplayRadar(false)
        else
            DisplayRadar(hud.vehicle.inVehicle or adminPlayerBlips)
        end

        Wait(250)
    end
end)

-- thread just handles hiding of native gta 5 hud elements and the hide hud button
Citizen.CreateThread(function()
    while true do
        if (IsControlPressed(1, 19)) then
            if (IsControlJustPressed(1, 74)) then
                TriggerEvent("core_hud:toggle", hudHidden)
            end
        end

        HideHudComponentThisFrame(3)
        HideHudComponentThisFrame(4)
        HideHudComponentThisFrame(6)
        HideHudComponentThisFrame(7)
        HideHudComponentThisFrame(8)
        HideHudComponentThisFrame(9)

        Wait(1)
    end
end)

-- events sending data
RegisterNetEvent("setRange")
AddEventHandler("setRange", function(level)
    hud.voice.level = level
end)

RegisterNetEvent("core_hud:belt")
AddEventHandler("core_hud:belt", function(seatbelt)
    hud.vehicle.seatbelt = seatbelt
end)

RegisterNetEvent('fdg_ui:toggleAdmin')
AddEventHandler('fdg_ui:toggleAdmin', function(toggle)
    adminPlayerBlips = toggle
end)

RegisterNetEvent("core_hud:toggle")
AddEventHandler("core_hud:toggle", function(toggle)
    hudHidden = not toggle
end)

function calculateHeading(direction)
    if (direction < 22.5) then
        return "N";
    elseif (direction < 67.5) then
        return "NW";
    elseif (direction < 112.5) then
        return "W";
    elseif (direction < 157.5) then
        return "SW";
    elseif (direction < 202.5) then
        return "S";
    elseif (direction < 247.5) then
        return "SE";
    elseif (direction < 292.5) then
        return "E";
    elseif (direction < 337.5) then
        return "NE";
    else
        return "N";
    end
end
