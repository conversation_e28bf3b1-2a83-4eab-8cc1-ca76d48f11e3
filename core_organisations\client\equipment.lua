local currentOrg = nil

-- Triggers the equipment inventory to open
function OpenEquipmentMenu(org, animation)
    if LocalPlayer.state.cuffed or LocalPlayer.state.wounded then
        return Base.Notification("You cannot do this while cuffed or wounded.")
    end

    if animation then
        Base.Animation.StartForcedAnim(playerPed, "amb@prop_human_bum_bin@idle_a", "idle_a", true, true, true)
    end

    TriggerServerEvent("orgs:openEquipmentInventory", org)
    currentOrg = org
end

-- Equipment inventory
RegisterNetEvent("inventory:clientSync", function(name, inv)
    if name == "equipment-" .. GetPlayerServerId(PlayerId()) then
        exports["core_inventory"]:loadExternalInventory("Job Equipment", inv, false, false, 0,
            function(itemIndex, count)
                TriggerServerEvent("orgs:storeEquipment", currentOrg, itemIndex, count)
            end,

            function(itemIndex, count)
                TriggerServerEvent("orgs:getEquipment", currentOrg, itemIndex, count)
            end,

            function()
                Base.Animation.StopForcedAnim(PlayerPedId())
                TriggerServerEvent("orgs:closeEquipment")
                currentOrg = nil
            end
        )
    end
end)

-- World marker interface
AddEventHandler("org:markerInteraction", function(type, marker)
    if type ~= "equipment" then return end
    print("Open Equipment Menu", marker.organisation, marker.identifier)
    OpenEquipmentMenu(marker.organisation, true)
end)

-- E menu interface
RegisterInteraction("vehicle", "equip", { label = "Job Equipment", emoji = "💼" },
    function(vehicle)
        if not DoesEntityExist(vehicle) then return end

        local org = Entity(vehicle).state.job
        if org == nil then return end

        OpenEquipmentMenu(org, true)
        Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
    end, function(entity)
        local state = Entity(entity).state

        if not state.job then return false end
        -- if not state.equipment then return false end

        return DoesPlayerHaveOrg(state.job)
    end
)
