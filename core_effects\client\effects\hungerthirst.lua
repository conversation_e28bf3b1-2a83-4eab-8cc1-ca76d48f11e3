local starvingEffect = StatusEffect("starving", "Starving",
    {
        onStart = function(effect)
            AddClipset("starving", "move_m@depressed@a", 50)
        end,

        onTick = function(effect)
            DisableControlAction(1, 21)
        end,

        onStop = function(effect)
            RemoveClipset("starving")
        end,
    },
    {
        alert = true,
        desc = "You feel weak, and need food now!",
        subdesc = "Unable to run or jump"
    }
)

local hungryEffect = StatusEffect("hungry", "Hungry",
    {
        onStart = function(effect)
            BlockInfiniteStamina("hunger", true)
        end,

        onStop = function(effect)
            BlockInfiniteStamina("hunger", false)
        end,


    },
    {
        alert = false,
        desc = "You are feeling tired, and need to eat soon",
        subdesc = "Your stamina is reduced"
    }
)

local peckishEffect = StatusEffect("peckish", "Peckish",
    {
        onStart = function(effect)

        end,

        onTick = function(effect)
            -- print(effect.name, effect.hello)
        end,

        onStop = function(effect)

        end,
    },
    {
        alert = false,
        desc = "You should consider looking for food"
    }
)

local dehydratedEffect = StatusEffect("dehydrated", "Dehydrated",
    {
        onStart = function(effect)
            AddClipset("dehydrated", "move_m@depressed@a", 50)
            Base.Notification("You are dehyrated. Find water fast!")
        end,

        onTick = function(effect)
            DisableControlAction(1, 21)
            DisableControlAction(1, 22)
        end,

        onStop = function(effect)
            RemoveClipset("dehydrated")
        end,
    },
    {
        alert = true,
        desc = "You feel weak, and need a drink now!",
        subdesc = "Unable to run or jump"
    }
)

local parchedEffect = StatusEffect("parched", "Parched",
    {
        onStart = function(effect)
            Base.Notification("You are parched, find something to drink!")
            BlockInfiniteStamina("thirst", true)
        end,

        onTick = function(effect)
            -- print(effect.name, effect.hello)
        end,

        onStop = function(effect)
            BlockInfiniteStamina("thirst", false)
        end,
    },
    {
        alert = false,
        desc = "You need something to drink",
        subdesc = "Your stamina is reduced"
    }
)

local thirstyEffect = StatusEffect("thirsty", "Thirsty",
    {
        onStart = function(effect)
            Base.Notification("You're thirsty, consider finding something to drink")
        end,

        onTick = function(effect)
            -- print(effect.name, effect.hello)
        end,

        onStop = function(effect)

        end,
    },
    {
        alert = false,
        desc = "Consider finding something to drink"
    }
)

-- LinearStatusEffectManager("thirst",
--     {
--         startValue = 100,
--         tickRate = 55000,
--         min = 0,
--         max = 100,
--         databaseSave = true
--     },
--     {
--         LinearStatusEffect(dehydratedEffect, 0, 10),
--         LinearStatusEffect(parchedEffect, 11, 30),
--         LinearStatusEffect(thirstyEffect, 31, 60),
--     }
-- )


-- LinearStatusEffectManager("hunger",
--     {
--         startValue = 100,
--         tickRate = 100000,
--         min = 0,
--         max = 100,
--         databaseSave = true
--     },
--     {
--         LinearStatusEffect(starvingEffect, 0, 10),
--         LinearStatusEffect(hungryEffect, 11, 30),
--         LinearStatusEffect(peckishEffect, 31, 50),
--     }
-- )
