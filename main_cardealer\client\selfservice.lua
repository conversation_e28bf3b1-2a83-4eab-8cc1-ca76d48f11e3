-- Script State
local currentShowroom
local currentModel
local dealerVehicleEntity

local function playerEnterShowroom(showroom)
    exports["core_inventory"]:toggleKeys(false)
    LocalPlayer.state.inVehicleShop = true
    currentShowroom = showroom
    DoScreenFadeOut(500)
    FreezeEntityPosition(playerPed, true)
    Wait(500)

    local visible = showroom.visibleShowroom
    Base.Player.setEntityVisible(visible or false)

    SetEntityCoords(PlayerPedId(), showroom.previewSpawn.coords)

    CreateThread(function()
        Wait(1000)
        DoScreenFadeIn(500)
    end)

    CreateThread(function()
        while currentShowroom do
            Wait(0)

            -- Disable exiting the vehicle
            DisableControlAction(0, 75, true)
        end
    end)
end

local function playerExitShowroom()
    exports["core_inventory"]:toggleKeys(true)
    LocalPlayer.state.inVehicleShop = false
    if not currentShowroom then
        currentShowroom = SHOWROOM_BLIPS[1]
    end

    DoScreenFadeOut(500)
    Wait(500)
    Base.Player.setEntityVisible(true)
    SetEntityCoords(PlayerPedId(), currentShowroom.menu.coords.x, currentShowroom.menu.coords.y, currentShowroom.menu.coords.z - 1.0)

    CreateThread(function()
        Wait(1000)
        DoScreenFadeIn(500)
        FreezeEntityPosition(playerPed, false)
        currentShowroom = nil
    end)
end

local function generateMenuOptions(vehicleData)
    local menuOptions = {}
    local categorisedVehicles = {}

    -- Loop through the vehicle data and categorize accordingly
    for i, vehicle in ipairs(vehicleData) do
        local categoryLabel
        if vehicle.cardealer_type == 'car_racing' then
            categoryLabel = "Racing Class: " .. (vehicle.racing_class or "Unknown Racing Class")
        else
            categoryLabel =  (vehicle.class or "Unknown Class")
        end

        if not categorisedVehicles[categoryLabel] then
            categorisedVehicles[categoryLabel] = {}
        end

        table.insert(categorisedVehicles[categoryLabel], vehicle)
    end

    for categoryLabel, vehicles in pairs(categorisedVehicles) do
        local options = {}
        for i, vehicle in ipairs(vehicles) do
            table.insert(options, vehicle.name .. " - $" .. vehicle.price)
        end

        table.insert(menuOptions, {
            name    = categoryLabel,
            label   = categoryLabel,
            value   = 0,
            type    = 'slider',
            options = options
        })
    end

    return menuOptions, categorisedVehicles
end

local function openConfirmPurchaseMenu(vehicleData, assignPlayer)
    local props = Base.Vehicles.Props.Get(dealerVehicleEntity)

    local label = 'Buy ' .. vehicleData.name .. ' for ' .. vehicleData.price .. ' ?'
    if assignID then
        label = 'Assign ' .. vehicleData.name .. ' to ' .. assignPlayer.label .. ' ?'
    end

    local assignTo = nil
    if assignPlayer then
        assignTo = assignPlayer.value
    end

    OpenConfirmMenu(label, function(status)
        closeShowroom()
        if status then
            print("Purchasing vehicle", vehicleData.id, props)
            TriggerServerEvent("cardealer:purchaseVehicle", vehicleData.id, currentShowroom.id, props, assignTo)
        end
    end)
end

local function openVehiclePurchaseMenu(vehicleData)
    if currentShowroom.assignable then
        TriggerServerCallback('esx:getIcNames', function(players)
            local elements = {}

            for _, v in ipairs(players) do
                local player = GetPlayerFromServerId(v.source)
                local ped = GetPlayerPed(player)
                if ped ~= GetPlayerPed(-1) then
                    local dist = #(GetEntityCoords(ped) - currentShowroom.menu.coords) 
                    -- print(player, ped, dist)
                    -- print(v.source, v.icname, player, ped, #(GetEntityCoords(ped) - currentShowroom.menu.coords))
                    if dist < 10.0 then
                        table.insert(elements, { label = v.icname, value = v.source, dist = dist })
                    end
                end
            end

            table.insert(elements, { label = PlayerData.icname, value = GetPlayerServerId(PlayerId()), dist = 0 })

            table.sort(elements, function(a, b)
                return a.dist < b.dist
            end)

            Base.UI.Menu.Open('default', GetCurrentResourceName(), 'cardealer_',
                {
                    title    = 'Sell ' .. vehicleData.name,
                    align    = 'bottom-right',
                    elements = elements
                },

                function(d, m)
                    openConfirmPurchaseMenu(vehicleData, d.current)
                end,

                function(d, m)
                    m.close()
                end
            )
        end)
    else
        openConfirmPurchaseMenu(vehicleData)
    end
end

local function spawnShowroomVehicle(vehicle)
    local model = GetHashKey(vehicle.model)
    currentModel = model

    -- Load while trying to spawn
    RequestModel(model)
    while not HasModelLoaded(model) and currentModel == model do
        Wait(1)
    end
    if currentModel ~= model then
        return print("Model changed, aborting spawn")
    end

    if DoesEntityExist(dealerVehicleEntity) then
        NetworkRequestControlOfEntity(dealerVehicleEntity)
        DeleteEntity(dealerVehicleEntity)
    end

    local spawnCoords = currentShowroom.previewSpawn.coords
    local spawnHeading = currentShowroom.previewSpawn.heading
    local network = currentShowroom.visibleShowroom

    dealerVehicleEntity = CreateVehicle(model, spawnCoords.x, spawnCoords.y, spawnCoords.z, spawnHeading, network, network)
    SetVehicleOnGroundProperly(dealerVehicleEntity)
    SetVehicleDirtLevel(dealerVehicleEntity, 0)
    TaskWarpPedIntoVehicle(PlayerPedId(), dealerVehicleEntity, -1)
    FreezeEntityPosition(dealerVehicleEntity, true)

    -- Apply mods
    print(json.encode(vehicle.props))
    Base.Vehicles.Props.Set(dealerVehicleEntity, vehicle.props)

    -- Set the plate variation to 0
    SetVehicleNumberPlateTextIndex(dealerVehicleEntity, 0)

    if not vehicle.props.color1 then
        local color = DEFAULT_COLORS[math.random(1, #DEFAULT_COLORS)]
        SetVehicleColours(dealerVehicleEntity, color.primary, color.secondary)
        local _, wheelColor = GetVehicleExtraColours(dealerVehicleEntity)
        SetVehicleExtraColours(dealerVehicleEntity, color.pearl, wheelColor)
    end
end

local function openShowroomMenu(menuOptions, vehData)
    Base.UI.Menu.CloseAll()

    Base.UI.Menu.Open('default', GetCurrentResourceName(), 'vehicle_shop',
        {
            title    = 'Purchase Vehicle',
            align    = 'bottom-right',
            elements = menuOptions,
        },
        function(data)
            local category = data.current.name
            local selectedIndex = data.current.value + 1
            local vehicleData = vehData[category][selectedIndex]
            openVehiclePurchaseMenu(vehicleData)
        end,
        closeShowroom,
        function(data)
            local category = data.current.name
            local selectedIndex = data.current.value + 1
            local vehicleData = vehData[category][selectedIndex]
            spawnShowroomVehicle(vehicleData)
        end
    )
end

function openShowroom(showroom)
    TriggerServerCallback("cardealer:getAvailableVehicles", function(vehicles)
        -- Fail if no vehicles
        if not vehicles then
            Base.Notification("No vehicles available at this showroom", "error")
            return print("No vehicles available")
        end

        -- Enter the showroom
        playerEnterShowroom(showroom)

        -- Generate the menu options
        local menuOptions, vehData = generateMenuOptions(vehicles)

        -- Spawn the first vehicle
        local firstCategory = menuOptions[1].name
        local firstVehicle = vehData[firstCategory][1]
        spawnShowroomVehicle(firstVehicle)

        -- Open the menu
        openShowroomMenu(menuOptions, vehData)
    end, showroom.type, showroom.restricted)
end

function closeShowroom()
    Base.UI.Menu.CloseAll()
    playerExitShowroom()

    if DoesEntityExist(dealerVehicleEntity) then
        NetworkRequestControlOfEntity(dealerVehicleEntity)
        DeleteEntity(dealerVehicleEntity)
    end
end

local function renderShowrooms()
    DoScreenFadeIn(0)
    Wait(1000)

    print("Rendering showrooms", job)

    for i, showroom in ipairs(SHOWROOMS) do
        showroom.id = i
        if (not showroom.restricted or DoesPlayerHaveOrgPermission("cardealer.purchase", showroom.restricted)) then
            local text = "View Showroom"

            if showroom.type == "usedcar" then
                text = "Sell Used Vehicle"
            elseif showroom.assignable then
                text = showroom.blipLabel
            end

            CreateInteractableMarker(showroom.menu.coords, function()
                openShowroom(showroom)
            end, { text = text })

            if showroom.blip then
                local blip = AddBlipForCoord(showroom.menu.coords)
                SetBlipSprite(blip, showroom.blip.sprite)
                SetBlipDisplay(blip, 4)
                SetBlipScale(blip, showroom.blip.scale)
                SetBlipColour(blip, showroom.blip.color)
                SetBlipAsShortRange(blip, true)
                BeginTextCommandSetBlipName("STRING")
                AddTextComponentString(showroom.label)
                EndTextCommandSetBlipName(blip)
            end
        end
    end
end

CreateThread(renderShowrooms)
RegisterNetEvent("base:characterLoaded", renderShowrooms)
RegisterNetEvent("updatedPlayerOrgs", renderShowrooms)

RegisterNetEvent("cardealer:purchaseComplete", function(vehId, currentShowroom)
    exports["base_vehicle"]:createFromId(vehId, currentShowroom.purchaseSpawn.coords, currentShowroom.purchaseSpawn.heading, function(vehicle)
        SetPedIntoVehicle(PlayerPedId(), vehicle, -1)
    end)
end)

RegisterCommand("vehtojson", function()
    local veh = GetVehiclePedIsIn(PlayerPedId(), false)
    print(json.encode(Base.Vehicles.Props.Get(veh)))
end)

-- When the resource stops, close the showroom
AddEventHandler("onResourceStop", function(resource)
    if resource == GetCurrentResourceName() then
        Base.UI.Menu.CloseAll()

        if currentShowroom then
            Base.Player.setEntityVisible(true)
            SetEntityCoords(PlayerPedId(), currentShowroom.menu.coords.x, currentShowroom.menu.coords.y, currentShowroom.menu.coords.z - 1.0)
            DoScreenFadeIn(0)
        end

        if DoesEntityExist(dealerVehicleEntity) then
            NetworkRequestControlOfEntity(dealerVehicleEntity)
            DeleteEntity(dealerVehicleEntity)
        end
    end
end)