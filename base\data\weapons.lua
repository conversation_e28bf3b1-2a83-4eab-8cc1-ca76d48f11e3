WeaponTickDamage = {
    [-868994466] = true,                -- water cannon
    [615608432] = true,                 -- fire
    [1198879012] = true,                -- fire
    [GetHash<PERSON>ey("FIRE")] = true,        -- fire
    [GetHash<PERSON>ey("WEAPON_FIRE")] = true, -- fire
    [3452007600] = true,                -- fall
    [-842959696] = true,                -- fall, but is also triggered for burns and drown??????
    [539292904] = "Explosion"           --Car exploding
}

WeaponCategories = {
    --painballs
    [GetHashKey("weapon_paintballgun")] = "WELT_PAINTBALL",  -- paintballgun
    [GetHashKey("weapon_bbshotgun")] = "WELT_BEANBAG",       -- Non-Lethal Shotgun
    [GetHashKey("weapon_paintballgun2")] = "WELT_PAINTBALL", -- paintballgun
    [GetHashKey("weapon_paintballgun3")] = "WELT_PAINTBALL", -- paintballgun
    [GetHash<PERSON><PERSON>("weapon_paintballgun4")] = "WELT_PAINTBALL", -- paintballgun
    [GetHashKey("weapon_paintballgun5")] = "WELT_PAINTBALL", -- paintballgun
    [GetHashKey("weapon_kl")] = "WELT_PAINTBALL",            -- SnowBall Gun
    [GetHashKey("weapon_pdtraining1")] = "WELT_PAINTBALL",   -- Special Carbine PDT (5.56x45mm)
    [GetHashKey("weapon_pdtraining2")] = "WELT_PAINTBALL",   -- MM4  PDT (5.56x45mm)
    [GetHashKey("weapon_pdtraining3")] = "WELT_PAINTBALL",   -- Heavy Pistol  PDT (.45 ACP)
    --GSW_LIGHT
    [453432689] = "GSW_LIGHT",                               --pistol
    [3219281620] = "GSW_LIGHT",                              --pistol mk2
    [-1075685676] = "GSW_LIGHT",                             --pistol mk2
    [-1076751822] = "GSW_LIGHT",                             --sns pistol
    [1593441988] = "GSW_LIGHT",                              --combat pistol
    [584646201] = "GSW_LIGHT",                               --ap pistol
    [137902532] = "GSW_LIGHT",                               --vintage pistol
    [-771403250] = "GSW_LIGHT",                              --heavy pistol
    [-619010992] = "GSW_LIGHT",                              --machine pistol
    [-598887786] = "GSW_LIGHT",                              -- marksman pistol
    [-1746263880] = "GSW_LIGHT",                             -- double action
    [324215364] = "GSW_LIGHT",                               -- micro smg
    [-1121678507] = "GSW_LIGHT",                             -- mini smg
    [736523883] = "GSW_LIGHT",                               -- smg
    [2024373456] = "GSW_LIGHT",                              -- smg mk2
    [-270015777] = "GSW_LIGHT",                              -- assualt smg
    [171789620] = "GSW_LIGHT",                               -- combat pdw
    [1627465347] = "GSW_LIGHT",                              -- gusenberg
    [1119849093] = "GSW_LIGHT",                              -- minigun
    [GetHashKey("weapon_pp91")] = "GSW_LIGHT",               -- pp-91
    [GetHashKey("weapon_crossbow")] = "GSW_LIGHT",           -- crossbow
    [GetHashKey("weapon_p90")] = "GSW_LIGHT",                -- P90
    [GetHashKey("weapon_vector")] = "GSW_LIGHT",             -- Vector
    [GetHashKey("weapon_mp7")] = "GSW_LIGHT",                -- Vector
    [GetHashKey("weapon_mp5")] = "GSW_LIGHT",                -- MP5
    [GetHashKey("weapon_mp9a")] = "GSW_LIGHT",               -- MP9A
    [GetHashKey("weapon_jericho")] = "GSW_LIGHT",            -- Jericho
    [GetHashKey("weapon_ceramicpistol")] = "GSW_LIGHT",
    [GetHashKey("weapon_m9a3")] = "GSW_LIGHT",
    [GetHashKey("weapon_snub")] = "GSW_LIGHT",
    [GetHashKey("weapon_ump45")] = "GSW_LIGHT",
    [GetHashKey("weapon_appistoltest")] = "GSW_LIGHT",
    [GetHashKey("weapon_akuraappistol")] = "GSW_LIGHT",
    [GetHashKey("weapon_celesteappistol")] = "GSW_LIGHT",
    [GetHashKey("weapon_dragonscaleappistol")] = "GSW_LIGHT",
    [GetHashKey("weapon_paddlepopappistol")] = "GSW_LIGHT",
    [GetHashKey("weapon_reaperappistol")] = "GSW_LIGHT",
    [GetHashKey("weapon_royaltyappistol")] = "GSW_LIGHT",
    [GetHashKey("weapon_impactglock")] = "GSW_IMPACT",
    [GetHashKey("weapon_rubberm4a1")] = "GSW_IMPACT",
    [GetHashKey("weapon_combatpistoltest")] = "GSW_LIGHT",
    [GetHashKey("weapon_p90test")] = "GSW_LIGHT",
    [GetHashKey("weapon_smgtest")] = "GSW_LIGHT",
    [GetHashKey("weapon_tecpistol")] = "GSW_LIGHT",
    --shotgun gsw
    [487013001] = "GSW_SHOTGUN",                            --pump shotgun
    [2017895192] = "GSW_SHOTGUN",                           -- sawnoff shotgun
    [-1654528753] = "GSW_SHOTGUN",                          --bullpup shotgun
    [-494615257] = "GSW_SHOTGUN",                           --assault shotgun
    [984333226] = "GSW_SHOTGUN",                            --heavy shotgun
    [-275439685] = "GSW_SHOTGUN",                           --double barrel shotgun
    [317205821] = "GSW_SHOTGUN",                            --auto shotgun
    [GetHashKey("weapon_pumpshotgun_mk2")] = "GSW_SHOTGUN", --pump shotgun mk2
    [GetHashKey("weapon_mossberg")] = "GSW_SHOTGUN",        --Mossberg 590
    [GetHashKey("weapon_combatshotgun")] = "GSW_SHOTGUN",   --Mossberg 590
    [GetHashKey("weapon_dp12")] = "GSW_SHOTGUN",            --DP12
    --GSW_MEDIUM
    [GetHashKey("weapon_carbinerifletest")] = "GSW_MEDIUM",
    [GetHashKey("weapon_famastest")] = "GSW_MEDIUM",
    [GetHashKey("weapon_heavypistoltest")] = "GSW_MEDIUM",
    [GetHashKey("weapon_specialcarbinetest_mk2")] = "GSW_MEDIUM",
    [-1716589765] = "GSW_MEDIUM",                                -- 50 cal pistol
    [GetHashKey("weapon_asiimovpistol50")] = "GSW_MEDIUM",       -- 50 cal pistol
    [GetHashKey("weapon_bangpistol50")] = "GSW_MEDIUM",          -- 50 cal pistol
    [GetHashKey("weapon_blackanimepistol50")] = "GSW_MEDIUM",    -- 50 cal pistol
    [GetHashKey("weapon_blackicepistol50")] = "GSW_MEDIUM",      -- 50 cal pistol
    [GetHashKey("weapon_blazeitpistol50")] = "GSW_MEDIUM",       -- 50 cal pistol
    [GetHashKey("weapon_blazepistol50")] = "GSW_MEDIUM",         -- 50 cal pistol
    [GetHashKey("weapon_bloodlinepistol50")] = "GSW_MEDIUM",     -- 50 cal pistol
    [GetHashKey("weapon_dragonpistol50")] = "GSW_MEDIUM",        -- 50 cal pistol
    [GetHashKey("weapon_fatduckpistol50")] = "GSW_MEDIUM",       -- 50 cal pistol
    [GetHashKey("weapon_fdgpistol50")] = "GSW_MEDIUM",           -- 50 cal pistol
    [GetHashKey("weapon_hotcoldpistol50")] = "GSW_MEDIUM",       -- 50 cal pistol
    [GetHashKey("weapon_orangepistol50")] = "GSW_MEDIUM",        -- 50 cal pistol
    [GetHashKey("weapon_pistol50_uwu")] = "GSW_MEDIUM",          -- 50 cal pistol
    [GetHashKey("weapon_pixeldpistol50")] = "GSW_MEDIUM",        -- 50 cal pistol
    [GetHashKey("weapon_printstreampistol50")] = "GSW_MEDIUM",   -- 50 cal pistol
    [GetHashKey("weapon_reddragonpistol50")] = "GSW_MEDIUM",     -- 50 cal pistol
    [GetHashKey("weapon_riptidepistol50")] = "GSW_MEDIUM",       -- 50 cal pistol
    [GetHashKey("weapon_shenlongpistol50")] = "GSW_MEDIUM",      -- 50 cal pistol
    [GetHashKey("weapon_specialforcespistol50")] = "GSW_MEDIUM", -- 50 cal pistol
    [GetHashKey("weapon_sultanpistol50")] = "GSW_MEDIUM",        -- 50 cal pistol
    [GetHashKey("weapon_waifupistol50")] = "GSW_MEDIUM",         -- 50 cal pistol
    [GetHashKey("weapon_pdpistol50")] = "GSW_MEDIUM",            -- 50 cal pistol
    [GetHashKey("weapon_xmas1pistol50")] = "GSW_MEDIUM",    -- 50 cal pistol
    [GetHashKey("weapon_xmas2pistol50")] = "GSW_MEDIUM",      -- 50 cal pistol
    [-**********] = "GSW_MEDIUM",                                -- assault rifle
    [GetHashKey("weapon_blackdragonak")] = "GSW_MEDIUM",         -- assault rifle
    [GetHashKey("weapon_royalgoldak")] = "GSW_MEDIUM",           -- assault rifle
    [GetHashKey("weapon_royaljadeak")] = "GSW_MEDIUM",           -- assault rifle
    [GetHashKey("weapon_royalcrimsonak")] = "GSW_MEDIUM",        -- assault rifle
    [961495388] = "GSW_MEDIUM",                                  -- assault rifle mk2
    [-**********] = "GSW_MEDIUM",                                -- carbine rifle
    [**********] = "GSW_MEDIUM",                                 -- carbine rifle mk2
    [-1357824103] = "GSW_MEDIUM",                                -- advanced rifle
    [2132975508] = "GSW_MEDIUM",                                 -- bullpup rifle
    [1649403952] = "GSW_MEDIUM",                                 -- compact rifle
    [-1660422300] = "GSW_MEDIUM",                                -- mg
    [2144741730] = "GSW_MEDIUM",                                 -- combat mg
    [3686625920] = "GSW_MEDIUM",                                 -- combat mg mk2
    [-1063057011] = "GSW_MEDIUM",                                -- special carbine
    [-1466123874] = "GSW_MEDIUM",                                -- musket
    [-1658906650] = "GSW_MEDIUM",                                -- Military Rifle
    [GetHashKey("weapon_tacticalrifle")] = "GSW_MEDIUM",         -- Tactical Rifle
    [GetHashKey("weapon_dd16")] = "GSW_MEDIUM",                  -- DD-16
    [GetHashKey("weapon_precisionrifle")] = "GSW_MEDIUM",        -- Precision Rifle
    [GetHashKey("weapon_specialcarbine_mk2")] = "GSW_MEDIUM",    -- Special Carbine MK2
    [GetHashKey("weapon_m1")] = "GSW_MEDIUM",                    -- M1 Grande
    [GetHashKey("weapon_m4a1")] = "GSW_MEDIUM",                  -- M4A1
    [GetHashKey("weapon_hk416")] = "GSW_MEDIUM",                 -- HK 416
    [GetHashKey("weapon_scar")] = "GSW_MEDIUM",                  -- SCAR-H
    [GetHashKey("weapon_ar15")] = "GSW_MEDIUM",                  -- AR-15
    [GetHashKey("weapon_huntingrifle")] = "GSW_MEDIUM",          -- Hunting rifle
    [GetHashKey("weapon_l85a2")] = "GSW_MEDIUM",                 -- L85A2
    [GetHashKey("weapon_sig516")] = "GSW_MEDIUM",                -- SIG516
    [GetHashKey("weapon_famas")] = "GSW_MEDIUM",                 -- Famas
    [GetHashKey("weapon_mk18b")] = "GSW_MEDIUM",                 -- Mk18B
    [GetHashKey("weapon_mm4")] = "GSW_MEDIUM",                   -- MM4
    [GetHashKey("weapon_m14")] = "GSW_MEDIUM",                   -- M14
    [GetHashKey("weapon_m19")] = "GSW_MEDIUM",                   -- M19
    [GetHashKey("weapon_glock")] = "GSW_LIGHT",                  -- Glock
    [GetHashKey("weapon_raptorglock")] = "GSW_LIGHT",            -- RAPTOR Glock
    [GetHashKey("weapon_mpx")] = "GSW_MEDIUM",                   -- MPX
    [GetHashKey("weapon_nsr9")] = "GSW_MEDIUM",                  -- NSR9
    [GetHashKey("weapon_mcxspear")] = "GSW_MEDIUM",              -- MCX Spear
    [GetHashKey("weapon_groza")] = "GSW_MEDIUM",                 -- Groza
    [GetHashKey("weapon_bullpuprifle_mk2")] = "GSW_MEDIUM",      -- bullpup rifle mk2
    [GetHashKey("weapon_ak971")] = "GSW_MEDIUM",
    [GetHashKey("weapon_m6ic")] = "GSW_MEDIUM",
    [GetHashKey("weapon_heavyrifle")] = "GSW_MEDIUM",
    [GetHashKey("weapon_mk47")] = "GSW_MEDIUM",
    [1328931688] = "GSW_MEDIUM",                             -- Catrifle
    [GetHashKey("weapon_battlerifle")] = "GSW_MEDIUM",       -- Battle Rifle
    --GSW_HEAVY
    [-1045183535] = "GSW_MEDIUM",                            -- revolver
    [2828843422] = "GSW_MEDIUM",                             -- musket
    [-952879014] = "GSW_MEDIUM",                             -- marksman rifle
    [GetHashKey("weapon_marksmanrifle_mk2")] = "GSW_MEDIUM", -- marksman rifle mk2
    [100416529] = "GSW_HEAVY",                               --sniper rifle
    [205991906] = "GSW_HEAVY",                               --heavy sniper
    [177293209] = "GSW_HEAVY",                               --heavy sniper mk2
    --MELEE_BLUNT
    [-1569615261] = "MELEE_BLUNT",                           --unarmed
    [1737195953] = "MELEE_BLUNT",                            --night stick
    [1317494643] = "MELEE_BLUNT",                            -- hammer
    [-1786099057] = "MELEE_BLUNT",                           --bat
    [-2067956739] = "MELEE_BLUNT",                           --crowbar
    [1141786504] = "MELEE_BLUNT",                            --golfclub
    [-656458692] = "MELEE_BLUNT",                            -- knuckleduster
    [GetHashKey("weapon_knuckles2")] = "MELEE_BLUNT",        -- diamond knuckleduster
    [GetHashKey("weapon_knuckles3")] = "MELEE_BLUNT",        -- lock knuckleduster
    [GetHashKey("weapon_knuckles4")] = "MELEE_BLUNT",        -- Shadow Dagger 1
    [GetHashKey("weapon_knuckles5")] = "MELEE_BLUNT",        -- Shadow Dagger 2
    [GetHashKey("weapon_knuckles6")] = "MELEE_BLUNT",        -- Shadow Dagger 3
    [GetHashKey("weapon_knuckles7")] = "MELEE_BLUNT",        -- Shadow Dagger 4
    [GetHashKey("weapon_knuckles8")] = "MELEE_BLUNT",        -- Shadow Dagger 5
    [GetHashKey("weapon_pknuckles")] = "MELEE_BLUNT",        -- Pineapple Dagger
    [GetHashKey("weapon_deverildo")] = "MELEE_BLUNT",
    [GetHashKey("weapon_capshield")] = "MELEE_BLUNT",
    [GetHashKey("weapon_chair")] = "MELEE_BLUNT",
    [GetHashKey("weapon_guitar")] = "MELEE_BLUNT",
    [-1951375401] = "MELEE_BLUNT",                       --flashlight
    [-1810795771] = "MELEE_BLUNT",                       --poolcue
    [419712736] = "MELEE_BLUNT",                         --wrench
    [GetHashKey("weapon_sledgehammer")] = "MELEE_BLUNT", -- sledge hammer
    [GetHashKey("weapon_lightsaberg")] = "MELEE_BLUNT",  -- lightsaber
    [GetHashKey("weapon_lightsaberp")] = "MELEE_BLUNT",  -- lightsaber
    [GetHashKey("weapon_lightsaberb")] = "MELEE_BLUNT",  -- lightsaber
    [GetHashKey("weapon_lightsaberr")] = "MELEE_BLUNT",  -- lightsaber
    [GetHashKey("weapon_lightsabergs")] = "MELEE_BLUNT", -- lightsaber
    [GetHashKey("weapon_bbshotgun")] = "MELEE_BLUNT",    -- Non-Lethal Shotgun
    [GetHashKey("weapon_bat2")] = "MELEE_BLUNT",         -- bat2
    [GetHashKey("weapon_christmasbat")] = "MELEE_BLUNT",
    [GetHashKey("weapon_dildobat")] = "MELEE_BLUNT",
    [GetHashKey("weapon_hotdog")] = "MELEE_BLUNT",
    [GetHashKey("weapon_stoppolice")] = "MELEE_BLUNT",
    [GetHashKey("weapon_twig")] = "MELEE_BLUNT",
    [GetHashKey("weapon_bat3")] = "MELEE_BLUNT",           -- hobby horse
    [GetHashKey("weapon_shovel")] = "MELEE_BLUNT",         -- shovel
    [-986084862] = "MELEE_BLUNT",                          -- breadstick
    [641083457] = "MELEE_BLUNT",                           -- dildo
    [-283872475] = "MELEE_BLUNT",                          -- Energy Sword
    --MELEE_STAB
    [-1716189206] = "MELEE_STAB",                          --knife
    [-102323637] = "MELEE_STAB",                           --bottle
    [-1834847097] = "MELEE_STAB",                          --dagger
    [-102973651] = "MELEE_STAB",                           -- hatchet
    [-581044007] = "MELEE_STAB",                           --machete
    [-538741184] = "MELEE_STAB",                           --switch blade
    [-853065399] = "MELEE_STAB",                           --battle axe
    [GetHashKey("weapon_stone_hatchet")] = "MELEE_STAB",   -- Steve's Stone Axe
    [GetHashKey("weapon_bonesaw")] = "MELEE_STAB",         -- Bonesaw
    [GetHashKey("weapon_vicaxe")] = "MELEE_STAB",          -- Viking Axe
    [GetHashKey("weapon_screwdriver")] = "MELEE_STAB",     -- Screwdriver
    [GetHashKey("weapon_katana")] = "MELEE_STAB",          -- Katana
    [GetHashKey("weapon_katana_thermal")] = "MELEE_STAB",  -- Thermnal Katana
    [GetHashKey("weapon_katana_thermalg")] = "MELEE_STAB", -- Green Thermnal Katana
    [GetHashKey("weapon_triblade")] = "MELEE_STAB",        -- Tri Blade
    [GetHashKey("weapon_machete2")] = "MELEE_STAB",        -- Reaper Machete
    [GetHashKey("weapon_chrisredfield")] = "MELEE_STAB",
    [GetHashKey("weapon_claws")] = "MELEE_STAB",
    [GetHashKey("weapon_cleaver")] = "MELEE_STAB",
    [GetHashKey("weapon_kitchenknife")] = "MELEE_STAB",
    [GetHashKey("weapon_needle")] = "MELEE_STAB",
    [GetHashKey("weapon_pickaxe")] = "MELEE_STAB",
    [GetHashKey("weapon_sickle")] = "MELEE_STAB",
    [GetHashKey("weapon_leonkennedy")] = "MELEE_STAB",
    [GetHashKey("weapon_fireaxe")] = "MELEE_STAB",    -- Fire Axe
    [GetHashKey("weapon_knuckles4")] = "MELEE_BLUNT", -- Shadow Dagger 1
    [GetHashKey("weapon_knuckles5")] = "MELEE_BLUNT", -- Shadow Dagger 2
    [GetHashKey("weapon_knuckles6")] = "MELEE_BLUNT", -- Shadow Dagger 3
    [GetHashKey("weapon_knuckles7")] = "MELEE_BLUNT", -- Shadow Dagger 4
    [GetHashKey("weapon_knuckles8")] = "MELEE_BLUNT", -- Shadow Dagger 5
    [GetHashKey("weapon_bknife_01")] = "MELEE_STAB",  -- Butterfly knife 1
    [GetHashKey("weapon_bknife_02")] = "MELEE_STAB",  -- Butterfly knife 2
    [GetHashKey("weapon_bknife_03")] = "MELEE_STAB",  -- Butterfly knife 3
    [GetHashKey("weapon_bknife_04")] = "MELEE_STAB",  -- Butterfly knife 4
    [GetHashKey("weapon_bknife_05")] = "MELEE_STAB",  -- Butterfly knife 5
    [GetHashKey("weapon_bknife_06")] = "MELEE_STAB",  -- Butterfly knife 6
    [GetHashKey("weapon_bknife_07")] = "MELEE_STAB",  -- Butterfly knife 7
    [GetHashKey("weapon_bknife_08")] = "MELEE_STAB",  -- Butterfly knife 8
    [GetHashKey("weapon_bknife_09")] = "MELEE_STAB",  -- Butterfly knife 9
    [GetHashKey("weapon_bknife_10")] = "MELEE_STAB",  -- Butterfly knife 10
    [GetHashKey("weapon_bknife_11")] = "MELEE_STAB",  -- Butterfly knife 11
    [GetHashKey("weapon_bknife_12")] = "MELEE_STAB",  -- Butterfly knife 12
    [GetHashKey("weapon_bknife_13")] = "MELEE_STAB",  -- Butterfly knife 13
    [GetHashKey("weapon_bknife_14")] = "MELEE_STAB",  -- Butterfly knife 14
    [GetHashKey("weapon_bknife_15")] = "MELEE_STAB",  -- Butterfly knife 15
    [GetHashKey("weapon_bknife_16")] = "MELEE_STAB",  -- Butterfly knife 16
    [GetHashKey("weapon_ctknife_01")] = "MELEE_STAB", -- CT knife 1
    [GetHashKey("weapon_ctknife_02")] = "MELEE_STAB", -- CT knife 2
    [GetHashKey("weapon_ctknife_03")] = "MELEE_STAB", -- CT knife 3
    [GetHashKey("weapon_tknife_01")] = "MELEE_STAB",  -- T knife 1
    [GetHashKey("weapon_tknife_02")] = "MELEE_STAB",  -- T knife 2
    [GetHashKey("weapon_tknife_03")] = "MELEE_STAB",  -- T knife 3
    [GetHashKey("weapon_tknife_04")] = "MELEE_STAB",  -- T knife 4
    [GetHashKey("weapon_fknife_01")] = "MELEE_STAB",  -- Flip knife 1
    [GetHashKey("weapon_fknife_01")] = "MELEE_STAB",  -- Flip knife 2
    [GetHashKey("weapon_fknife_01")] = "MELEE_STAB",  -- Flip knife 3
    [GetHashKey("weapon_gknife_01")] = "MELEE_STAB",  -- Gut knife 1
    [GetHashKey("weapon_gknife_02")] = "MELEE_STAB",  -- Gut knife 2
    [GetHashKey("weapon_gknife_03")] = "MELEE_STAB",  -- Gut knife 3
    [GetHashKey("weapon_gknife_04")] = "MELEE_STAB",  -- Gut knife 4
    [GetHashKey("weapon_kknife_01")] = "MELEE_STAB",  -- Karambit knife 1
    [GetHashKey("weapon_kknife_02")] = "MELEE_STAB",  -- Karambit knife 2
    [GetHashKey("weapon_kknife_03")] = "MELEE_STAB",  -- Karambit knife 3
    [GetHashKey("weapon_kknife_04")] = "MELEE_STAB",  -- Karambit knife 4
    [GetHashKey("weapon_kknife_05")] = "MELEE_STAB",  -- Karambit knife 5
    [GetHashKey("weapon_m9knife_1")] = "MELEE_STAB",  -- m9knife 1
    [GetHashKey("weapon_m9knife_2")] = "MELEE_STAB",  -- m9knife 2
    [GetHashKey("weapon_m9knife_3")] = "MELEE_STAB",  -- m9knife 3
    [GetHashKey("weapon_m9knife_4")] = "MELEE_STAB",  -- m9knife 4
    [GetHashKey("weapon_m9knife_5")] = "MELEE_STAB",  -- m9knife 5
    [GetHashKey("weapon_skyrim")] = "MELEE_STAB",     -- skyrim sword
    [GetHashKey("weapon_re5blade")] = "MELEE_STAB",   -- skyrim sword 2
    --ENV_EXPLOSION
    [539292904] = "CAR_EXPLOSION",                    --Car exploding
    [-1568386805] = "ENV_EXPLOSION",                  --grenadelauncher
    [-1312131151] = "ENV_EXPLOSION",                  --rpg
    [2138347493] = "ENV_EXPLOSION",                   --firework
    [1834241177] = "ENV_EXPLOSION",                   --railgun
    [1672152130] = "ENV_EXPLOSION",                   --homing launcher
    [125959754] = "ENV_EXPLOSION",                    --compact launcher
    [-1813897027] = "ENV_EXPLOSION",                  --grenade
    [741814745] = "ENV_EXPLOSION",                    -- stickybomb
    [-1420407917] = "ENV_EXPLOSION",                  --proximitymine
    [-1169823560] = "ENV_EXPLOSION",                  --pipebomb
    --chemical
    [-1600701090] = "ENV_GAS",                        --bz gas
    [350665120] = "ENV_GAS",                          --bz gas
    --burn
    [615608432] = "ENV_BURNS",                        -- molotov
    [1198879012] = "ENV_BURNS",                       -- flaregun
    [GetHashKey("FIRE")] = "ENV_BURNS",
    [GetHashKey("WEAPON_FIRE")] = "ENV_BURNS",
    --bite
    [4194021054] = "BITE",        --animal
    [148160082] = "BITE",         --cougar
    [-868994466] = "WATERCANNON", --water cannon
    --fall
    [3452007600] = "ENV_FALL",
    [-842959696] = "ENV_UNK",
    [1223143800] = "ENV_UNK", -- fence
    --vehicle
    [133987706] = "ENV_VEHICLE_INTERNAL",
    [2741846334] = "ENV_VEHICLE",
    [-1553120962] = "ENV_VEHICLE",
    -- drowning like noob
    [-10959621] = "ENV_DROWNING",
    [1936677264] = "ENV_DROWNING",
    -- Knocked the fuck out - mitigates damage and ragdolls player
    [600439132] = "MELEE_BLUNT",                      -- baseball (ball)
    [126349499] = "MELEE_BLUNT",                      -- snowball
    [GetHashKey("weapon_pineapple")] = "MELEE_BLUNT", -- snowball
    [GetHashKey("weapon_brick")] = "MELEE_BLUNT",     -- snowball
    [GetHashKey("weapon_book")] = "MELEE_BLUNT",
    [GetHashKey("weapon_shoe")] = "MELEE_BLUNT",
    [GetHashKey("weapon_stunrod")] = "MELEE_BLUNT",
    -- Misc
    [1548172417] = "MELEE_BLUNT", -- Umbrella Pistol Tazer
}

WeaponLabels = {
    --paintballs
    [GetHashKey("weapon_paintballgun")] = "Paintball Gun",      -- paintballgun
    [GetHashKey("weapon_paintballgun2")] = "Paintball Gun 2",   -- paintballgun
    [GetHashKey("weapon_paintballgun3")] = "Paintball Gun 3",   -- paintballgun
    [GetHashKey("weapon_paintballgun4")] = "Paintball Gun 4",   -- paintballgun
    [GetHashKey("weapon_paintballgun4")] = "Paintball Gun 5",   -- paintballgun
    [GetHashKey("weapon_kl")] = "Snowball Launcher",            -- SnowBall Gun
    [GetHashKey("weapon_pdtraining1")] = "PDT Special Carbine", -- Special Carbine PDT (5.56x45mm)
    [GetHashKey("weapon_pdtraining2")] = "PDT MM4",             -- MM4  PDT (5.56x45mm)
    [GetHashKey("weapon_pdtraining3")] = "PDT Heavy Pistol",    -- Heavy Pistol  PDT (.45 ACP)
    --GSW_LIGHT
    [453432689] = "Pistol",                                     --pistol
    [3219281620] = "Pistol MK2",                                --pistol mk2
    [-1075685676] = "Pistol MK2",                               --pistol mk2
    [-1076751822] = "sns Pistol",                               --sns pistol
    [1593441988] = "Combat Pistol",                             --combat pistol
    [584646201] = "AP Pistol",                                  --ap pistol
    [137902532] = "Vintage Pistol",                             --vintage pistol
    [-771403250] = "Heavy Pistol",                              --heavy pistol
    [-619010992] = "Machine Pistol",                            --machine pistol
    [-598887786] = "Marksman Pistol",                           -- marksman pistol
    [-1746263880] = "double action",                            -- double action
    [324215364] = "Micro SMG",                                  -- micro smg
    [-1121678507] = "Mini SMG",                                 -- mini smg
    [736523883] = "SMG",                                        -- smg
    [2024373456] = "SMG MK2",                                   -- smg mk2
    [-270015777] = "Assault SMG",                               -- assualt smg
    [171789620] = "Combat PDW",                                 -- combat pdw
    [1627465347] = "Gusenberg",                                 -- gusenberg
    [1119849093] = "Minigun",                                   -- minigun
    [GetHashKey("weapon_pp91")] = "PP-91",                      -- pp-91
    [GetHashKey("weapon_crossbow")] = "Crossbow",               -- crossbow
    [GetHashKey("weapon_p90")] = "P90",                         -- P90
    [GetHashKey("weapon_vector")] = "Vector",                   -- Vector
    [GetHashKey("weapon_mp7")] = "Vector",                      -- Vector
    [GetHashKey("weapon_mp5")] = "MP5",                         -- MP5
    [GetHashKey("weapon_mp9a")] = "MP9A",                       -- MP9A
    [GetHashKey("weapon_jericho")] = "Jericho",                 -- Jericho
    [GetHashKey("weapon_m9a3")] = "M9A3",
    [GetHashKey("weapon_snub")] = "Snub Revolver",
    [GetHashKey("weapon_ump45")] = "UMP45",
    [GetHashKey("weapon_appistoltest")] = "AP Pistol Test",
    [GetHashKey("weapon_akuraappistol")] = "AP Pistol",
    [GetHashKey("weapon_celesteappistol")] = "AP Pistol",
    [GetHashKey("weapon_dragonscaleappistol")] = "AP Pistol",
    [GetHashKey("weapon_paddlepopappistol")] = "AP Pistol",
    [GetHashKey("weapon_reaperappistol")] = "AP Pistol",
    [GetHashKey("weapon_royaltyappistol")] = "AP Pistol",
    [GetHashKey("weapon_impactglock")] = "Impact Glock",
    [GetHashKey("weapon_rubberm4a1")] = "Impact M4A1",
    [GetHashKey("weapon_combatpistoltest")] = "Combat Pistol Test",
    [GetHashKey("weapon_p90test")] = "P90 Test",
    [GetHashKey("weapon_smgtest")] = "SMG Test",
    [GetHashKey("weapon_tecpistol")] = "Tactical Pistol",
    --shotgun gsw
    [487013001] = "Pump Shotgun",                                --pump shotgun
    [2017895192] = "Sawnoff Shotgun",                            -- sawnoff shotgun
    [-1654528753] = "Bullpup Shotgun",                           --bullpup shotgun
    [-494615257] = "Assault Shotgun",                            --assault shotgun
    [984333226] = "Heavy Shotgun",                               --heavy shotgun
    [-275439685] = "Double Barrel Shotgun",                      --double barrel shotgun
    [317205821] = "Auto Shotgun",                                --auto shotgun
    [GetHashKey("weapon_pumpshotgun_mk2")] = "Pump Shotgun MK2", --pump shotgun mk2
    [GetHashKey("weapon_mossberg")] = "Mossberg 590",            --Mossberg 590
    [GetHashKey("weapon_combatshotgun")] = "Mossberg 590",       --Mossberg 590
    [GetHashKey("weapon_dp12")] = "DP12",                        --DP12
    --GSW_MEDIUM
    [GetHashKey("weapon_carbinerifletest")] = "Carbine Rifle Test",
    [GetHashKey("weapon_famastest")] = "Famas Test",
    [GetHashKey("weapon_heavypistoltest")] = "Heavy Pistol Test",
    [GetHashKey("weapon_specialcarbinetest_mk2")] = "Special Carbine Mk2 Test",
    [-1716589765] = "50 cal Pistol",                                   -- 50 cal pistol
    [GetHashKey("weapon_asiimovpistol50")] = "50 cal Pistol",          -- 50 cal pistol
    [GetHashKey("weapon_bangpistol50")] = "50 cal Pistol",             -- 50 cal pistol
    [GetHashKey("weapon_blackanimepistol50")] = "50 cal Pistol",       -- 50 cal pistol
    [GetHashKey("weapon_blackicepistol50")] = "50 cal Pistol",         -- 50 cal pistol
    [GetHashKey("weapon_blazeitpistol50")] = "50 cal Pistol",          -- 50 cal pistol
    [GetHashKey("weapon_blazepistol50")] = "50 cal Pistol",            -- 50 cal pistol
    [GetHashKey("weapon_bloodlinepistol50")] = "50 cal Pistol",        -- 50 cal pistol
    [GetHashKey("weapon_dragonpistol50")] = "50 cal Pistol",           -- 50 cal pistol
    [GetHashKey("weapon_fatduckpistol50")] = "50 cal Pistol",          -- 50 cal pistol
    [GetHashKey("weapon_fdgpistol50")] = "50 cal Pistol",              -- 50 cal pistol
    [GetHashKey("weapon_hotcoldpistol50")] = "50 cal Pistol",          -- 50 cal pistol
    [GetHashKey("weapon_orangepistol50")] = "50 cal Pistol",           -- 50 cal pistol
    [GetHashKey("weapon_pistol50_uwu")] = "50 cal Pistol",             -- 50 cal pistol
    [GetHashKey("weapon_pixeldpistol50")] = "50 cal Pistol",           -- 50 cal pistol
    [GetHashKey("weapon_printstreampistol50")] = "50 cal Pistol",      -- 50 cal pistol
    [GetHashKey("weapon_reddragonpistol50")] = "50 cal Pistol",        -- 50 cal pistol
    [GetHashKey("weapon_riptidepistol50")] = "50 cal Pistol",          -- 50 cal pistol
    [GetHashKey("weapon_shenlongpistol50")] = "50 cal Pistol",         -- 50 cal pistol
    [GetHashKey("weapon_specialforcespistol50")] = "50 cal Pistol",    -- 50 cal pistol
    [GetHashKey("weapon_sultanpistol50")] = "50 cal Pistol",           -- 50 cal pistol
    [GetHashKey("weapon_waifupistol50")] = "50 cal Pistol",            -- 50 cal pistol
    [GetHashKey("weapon_pdpistol50")] = "50 cal Pistol",               -- 50 cal pistol
    [GetHashKey("weapon_xmas1pistol50")] = "50 cal Pistol",       -- 50 cal pistol
    [GetHashKey("weapon_xmas2pistol50")] = "50 cal Pistol",         -- 50 cal pistol
    [-**********] = "Assault Rifle",                                   -- assault rifle
    [GetHashKey("weapon_blackdragonak")] = "Assault Rifle",            -- assault rifle
    [GetHashKey("weapon_royalgoldak")] = "Assault Rifle",              -- assault rifle
    [GetHashKey("weapon_royaljadeak")] = "Assault Rifle",              -- assault rifle
    [GetHashKey("weapon_royalcrimsonak")] = "Assault Rifle",           -- assault rifle
    [961495388] = "Assault Rifle MK2",                                 -- assault rifle mk2
    [-**********] = "Carbine Rifle",                                   -- carbine rifle
    [**********] = "Carbine Rifle MK2",                                -- carbine rifle mk2
    [-1357824103] = "Advanced Rifle",                                  -- advanced rifle
    [2132975508] = "Bullpup Rifle",                                    -- bullpup rifle
    [GetHashKey("weapon_bullpuprifle_mk2 ")] = "Bullpup Rifle MK2",    -- bullpup rifle mk2
    [1649403952] = "Compact Rifle",                                    -- compact rifle
    [-1660422300] = "MG",                                              -- mg
    [2144741730] = "Combat MG",                                        -- combat mg
    [3686625920] = "Combat MG MK2",                                    -- combat mg mk2
    [-1063057011] = "Special Carbine",                                 -- special carbine
    [-1466123874] = "Musket",                                          -- musket
    [-1658906650] = "Military Rifle",                                  --Military Rifle
    [GetHashKey("weapon_specialcarbine_mk2")] = "Special Carbine MK2", -- Special Carbine MK2
    [GetHashKey("weapon_m1")] = "M1 Grande",                           -- M1 Grande
    [GetHashKey("weapon_m4a1")] = "M4A1",                              -- M4A1
    [GetHashKey("weapon_hk416")] = "HK 416",                           -- HK 416
    [GetHashKey("weapon_scar")] = "SCAR-H",                            -- SCAR-H
    [GetHashKey("weapon_ar15")] = "AR-15",                             -- AR-15
    [GetHashKey("weapon_huntingrifle")] = "Hunting rifle",             -- Hunting rifle
    [GetHashKey("weapon_l85a2")] = "L85A2",                            -- L85A2
    [GetHashKey("weapon_sig516")] = "SIG516",                          -- SIG516
    [GetHashKey("weapon_famas")] = "Famas",                            -- Famas
    [GetHashKey("weapon_mk18b")] = "Mk18B",                            -- Mk18B
    [GetHashKey("weapon_mm4")] = "MM4",                                -- MM4
    [GetHashKey("weapon_m14")] = "M14",                                -- M14
    [GetHashKey("weapon_m19")] = "M19",                                -- M19
    [GetHashKey("weapon_glock")] = "Glock",                            -- Glock
    [GetHashKey("weapon_raptorglock")] = "RAPTOR Glock",               -- RAPTOR Glock
    [GetHashKey("weapon_mpx")] = "MPX",                                -- MPX
    [GetHashKey("weapon_nsr9")] = "NSR9",                              -- NSR9
    [GetHashKey("weapon_mcxspear")] = "MCX Spear",                     -- MCX Spear
    [GetHashKey("weapon_ak971")] = "AK971",                            -- AK971
    [GetHashKey("weapon_heavyrifle")] = "Heavy Rifle",                 -- Heavy Rifle
    [GetHashKey("weapon_mk47")] = "MK47",                              -- MK47
    [GetHashKey("weapon_tacticalrifle")] = "Tactical Rifle",           -- Tactical Rifle
    [GetHashKey("weapon_dd16")] = "DD16",                              -- DD-16
    [GetHashKey("weapon_precisionrifle")] = "Precision Rifle",         -- Precision Rifle
    [1328931688] = "Catrifle",                                         -- Catrifle
    [GetHashKey("weapon_m6ic")] = "M6IC",
    [GetHashKey("weapon_battlerifle")] = "Battle Rifle",
    [GetHashKey("weapon_groza")] = "Groza",
    [GetHashKey("weapon_bullpuprifle_mk2")] = "Bullpup Rifle MK2",
    --GSW_HEAVY
    [-1045183535] = "Revolver",                                      -- revolver
    [2828843422] = "Musket",                                         -- musket
    [-952879014] = "Marksman Rifle",                                 -- marksman rifle
    [GetHashKey("weapon_marksmanrifle_mk2")] = "Marksman Rifle MK2", -- marksman rifle mk2
    [100416529] = "Sniper Rifle",                                    --sniper rifle
    [205991906] = "Heavy Sniper",                                    --heavy sniper
    [177293209] = "Heavy Sniper MK2",                                --heavy sniper mk2
    --MELEE_BLUNT
    [-1569615261] = "Unarmed",                                       --unarmed
    [1737195953] = "Night Stick",                                    --night stick
    [1317494643] = "Hammer",                                         -- hammer
    [-1786099057] = "Bat",                                           --bat
    [-2067956739] = "Crowbar",                                       --crowbar
    [1141786504] = "Golfclub",                                       --golfclub
    [-656458692] = "Knuckleduster",                                  -- knuckleduster
    [GetHashKey("weapon_knuckles2")] = "Diamond Knuckleduster",      -- diamond knuckleduster
    [GetHashKey("weapon_knuckles3")] = "Lock Knuckleduster",         -- lock knuckleduster
    [GetHashKey("weapon_knuckles4")] = "Blunt Force",                -- Shadow Dagger 1
    [GetHashKey("weapon_knuckles5")] = "Blunt Force",                -- Shadow Dagger 2
    [GetHashKey("weapon_knuckles6")] = "Blunt Force",                -- Shadow Dagger 3
    [GetHashKey("weapon_knuckles7")] = "Blunt Force",                -- Shadow Dagger 4
    [GetHashKey("weapon_knuckles8")] = "Blunt Force",                -- Shadow Dagger 5
    [GetHashKey("weapon_deverildo")] = "Deverildo",
    [GetHashKey("weapon_capshield")] = "Cap Shield",
    [GetHashKey("weapon_chair")] = "Chair",
    [GetHashKey("weapon_guitar")] = "Guitar",
    [-1951375401] = "Flashlight",                            --flashlight
    [-1810795771] = "Poolcue",                               --poolcue
    [419712736] = "Wrench",                                  --wrench
    [GetHashKey("weapon_sledgehammer")] = "Sledge hammer",   -- sledge hammer
    [GetHashKey("weapon_lightsaberg")] = "Lightsaber",       -- lightsaber
    [GetHashKey("weapon_lightsaberp")] = "Lightsaber",       -- lightsaber
    [GetHashKey("weapon_lightsaberb")] = "Lightsaber",       -- lightsaber
    [GetHashKey("weapon_lightsaberr")] = "Lightsaber",       -- lightsaber
    [GetHashKey("weapon_lightsabergs")] = "Lightsaber",      -- lightsaber
    [GetHashKey("weapon_bbshotgun")] = "Non-Lethal Shotgun", -- Non-Lethal Shotgun
    [GetHashKey("weapon_bat2")] = "Bat2",                    -- bat2
    [GetHashKey("weapon_christmasbat")] = "Christmas Bat",
    [GetHashKey("weapon_dildobat")] = "Dildo Bat",
    [GetHashKey("weapon_hotdog")] = "Hotdog",
    [GetHashKey("weapon_bat3")] = "Hobby Horse",                      -- hobby horse
    [GetHashKey("weapon_shovel")] = "Shovel",                         -- shovel
    [-986084862] = "Breadstick",                                      -- breadstick
    [641083457] = "Dildo",                                            -- dildo
    [-283872475] = "Energy Sword",                                    -- Energy Sword
    --MELEE_STAB
    [-1716189206] = "Knife",                                          --knife
    [-102323637] = "Bottle",                                          --bottle
    [-1834847097] = "Dagger",                                         --dagger
    [-102973651] = "Hatchet",                                         -- hatchet
    [-581044007] = "Machete",                                         --machete
    [-538741184] = "Switch Blade",                                    --switch blade
    [-853065399] = "Battle Axe",                                      --battle axe
    [GetHashKey("weapon_stone_hatchet")] = "Steve's Stone Axe",       -- Steve's Stone Axe
    [GetHashKey("weapon_bonesaw")] = "Bonesaw",                       -- Bonesaw
    [GetHashKey("weapon_vicaxe")] = "Viking Axe",                     -- Viking Axe
    [GetHashKey("weapon_screwdriver")] = "Screwdriver",               -- Screwdriver
    [GetHashKey("weapon_katana")] = "Katana",                         -- Katana
    [GetHashKey("weapon_katana_thermal")] = "Thermnal Katana",        -- Thermnal Katana
    [GetHashKey("weapon_katana_thermalg")] = "Green Thermnal Katana", -- Green Thermnal Katana
    [GetHashKey("weapon_triblade")] = "Tri Blade",                    -- Tri Blade
    [GetHashKey("weapon_machete2")] = "Reaper Machete",               -- Reaper Machete
    [GetHashKey("weapon_chrisredfield")] = "Chrisredfield",
    [GetHashKey("weapon_claws")] = "Claws",
    [GetHashKey("weapon_cleaver")] = "Cleaver",
    [GetHashKey("weapon_kitchenknife")] = "Kitchen Knife",
    [GetHashKey("weapon_needle")] = "Needle",
    [GetHashKey("weapon_pickaxe")] = "Pickaxe",
    [GetHashKey("weapon_sickle")] = "Sickle",
    [GetHashKey("weapon_stoppolice")] = "Stop Police",
    [GetHashKey("weapon_twig")] = "Twig",
    [GetHashKey("weapon_leonkennedy")] = "LeonKennedy",
    [GetHashKey("weapon_fireaxe")] = "Fire Axe",             -- Fire Axe
    [GetHashKey("weapon_knuckles4")] = "Shadow Dagger 1",    -- Shadow Dagger 1
    [GetHashKey("weapon_knuckles5")] = "Shadow Dagger 2",    -- Shadow Dagger 2
    [GetHashKey("weapon_knuckles6")] = "Shadow Dagger 3",    -- Shadow Dagger 3
    [GetHashKey("weapon_knuckles7")] = "Shadow Dagger 4",    -- Shadow Dagger 4
    [GetHashKey("weapon_knuckles8")] = "Shadow Dagger 5",    -- Shadow Dagger 5
    [GetHashKey("weapon_bknife_01")] = "Butterfly knife 1",  -- Butterfly knife 1
    [GetHashKey("weapon_bknife_02")] = "Butterfly knife 2",  -- Butterfly knife 2
    [GetHashKey("weapon_bknife_03")] = "Butterfly knife 3",  -- Butterfly knife 3
    [GetHashKey("weapon_bknife_04")] = "Butterfly knife 4",  -- Butterfly knife 4
    [GetHashKey("weapon_bknife_05")] = "Butterfly knife 5",  -- Butterfly knife 5
    [GetHashKey("weapon_bknife_06")] = "Butterfly knife 6",  -- Butterfly knife 6
    [GetHashKey("weapon_bknife_07")] = "Butterfly knife 7",  -- Butterfly knife 7
    [GetHashKey("weapon_bknife_08")] = "Butterfly knife 8",  -- Butterfly knife 8
    [GetHashKey("weapon_bknife_09")] = "Butterfly knife 9",  -- Butterfly knife 9
    [GetHashKey("weapon_bknife_10")] = "Butterfly knife 10", -- Butterfly knife 10
    [GetHashKey("weapon_bknife_11")] = "Butterfly knife 11", -- Butterfly knife 11
    [GetHashKey("weapon_bknife_12")] = "Butterfly knife 12", -- Butterfly knife 12
    [GetHashKey("weapon_bknife_13")] = "Butterfly knife 13", -- Butterfly knife 13
    [GetHashKey("weapon_bknife_14")] = "Butterfly knife 14", -- Butterfly knife 14
    [GetHashKey("weapon_bknife_15")] = "Butterfly knife 15", -- Butterfly knife 15
    [GetHashKey("weapon_bknife_16")] = "Butterfly knife 16", -- Butterfly knife 16
    [GetHashKey("weapon_ctknife_01")] = "CT knife 1",        -- CT knife 1
    [GetHashKey("weapon_ctknife_02")] = "CT knife 2",        -- CT knife 2
    [GetHashKey("weapon_ctknife_03")] = "CT knife 3",        -- CT knife 3
    [GetHashKey("weapon_tknife_01")] = "T knife 1",          -- T knife 1
    [GetHashKey("weapon_tknife_02")] = "T knife 2",          -- T knife 2
    [GetHashKey("weapon_tknife_03")] = "T knife 3",          -- T knife 3
    [GetHashKey("weapon_tknife_04")] = "T knife 4",          -- T knife 4
    [GetHashKey("weapon_fknife_01")] = "Flip knife 1",       -- Flip knife 1
    [GetHashKey("weapon_fknife_01")] = "Flip knife 2",       -- Flip knife 2
    [GetHashKey("weapon_fknife_01")] = "Flip knife 3",       -- Flip knife 3
    [GetHashKey("weapon_gknife_01")] = "Gut knife 1",        -- Gut knife 1
    [GetHashKey("weapon_gknife_02")] = "Gut knife 2",        -- Gut knife 2
    [GetHashKey("weapon_gknife_03")] = "Gut knife 3",        -- Gut knife 3
    [GetHashKey("weapon_gknife_04")] = "Gut knife 4",        -- Gut knife 4
    [GetHashKey("weapon_kknife_01")] = "Karambit knife 1",   -- Karambit knife 1
    [GetHashKey("weapon_kknife_02")] = "Karambit knife 2",   -- Karambit knife 2
    [GetHashKey("weapon_kknife_03")] = "Karambit knife 3",   -- Karambit knife 3
    [GetHashKey("weapon_kknife_04")] = "Karambit knife 4",   -- Karambit knife 4
    [GetHashKey("weapon_kknife_05")] = "Karambit knife 5",   -- Karambit knife 5
    [GetHashKey("weapon_m9knife_1")] = "m9knife 1",          -- m9knife 1
    [GetHashKey("weapon_m9knife_2")] = "m9knife 2",          -- m9knife 2
    [GetHashKey("weapon_m9knife_3")] = "m9knife 3",          -- m9knife 3
    [GetHashKey("weapon_m9knife_4")] = "m9knife 4",          -- m9knife 4
    [GetHashKey("weapon_m9knife_5")] = "m9knife 5",          -- m9knife 5
    [GetHashKey("weapon_re5blade")] = "re5blade",
    [GetHashKey("weapon_skyrim")] = "skyrim sword",
    --ENV_EXPLOSION
    [-1568386805] = "Grenadelauncher", --grenadelauncher
    [-1312131151] = "RPG",             --rpg
    [2138347493] = "Firework",         --firework
    [1834241177] = "Railgun",          --railgun
    [1672152130] = "Homing Launcher",  --homing launcher
    [539292904] = "Car Explosion",     --Car exploding
    [125959754] = "Compact Launcher",  --compact launcher
    [-1813897027] = "Grenade",         --grenade
    [741814745] = "Stickybomb",        -- stickybomb
    [-1420407917] = "Proximitymine",   --proximitymine
    [-1169823560] = "Pipebomb",        --pipebomb
    --chemical
    [-1600701090] = "BZ Gas",          --bz gas
    [350665120] = "BZ Gas Launcher",   --bz gas
    --burn
    [615608432] = "Molotov",           -- molotov
    [1198879012] = "Flaregun",         -- flaregun
    [GetHashKey("FIRE")] = "Burns",
    [GetHashKey("WEAPON_FIRE")] = "Burns",
    --bite
    [4194021054] = "Animal",       --animal
    [148160082] = "Cougar",        --cougar
    [-868994466] = "Water Cannon", --water cannon
    --fall
    [3452007600] = "Fall",
    [-842959696] = "UNK",
    --vehicle
    [133987706] = "Vehicle",
    [2741846334] = "Vehicle",
    [-1553120962] = "Vehicle",
    -- drowning like noob
    [-10959621] = "Drowning",
    [1936677264] = "Drowning",
    -- Knocked the fuck out - mitigates damage and ragdolls player
    [600439132] = "Baseball", -- baseball (ball)
    [126349499] = "Snowball", -- snowball
    [GetHashKey("weapon_pineapple")] = "Pineapple",
    [GetHashKey("weapon_brick")] = "Brick",
    [GetHashKey("weapon_book")] = "Book",
    [GetHashKey("weapon_shoe")] = "Shoe",
    -- Unknown :D
    [1548172417] = "Umbrella Corp UNK", -- Umbrella Pistol Tazer
}


WeaponAmmoTypes = {}
local AmmoTable = {
    ammo_acp = {
        label = '.45 ACP',
        ammo_list = {
            'ammo_045_mag',
            'ammo_045_hp_mag',
            'ammo_combatpdw_mag',
            'ammo_microsmg_mag',
            'ammo_tec9_mag',
            'ammo_tommydrum_mag',
        }
    },
    ammo_9mm = {
        label = '9mm',
        ammo_list = {
            'ammo_9mm_ap_mag',
            'ammo_9mm_combat_mag',
            'ammo_9mm_mag',
            'ammo_9mm_smg_mag',
            'ammo_9mm_sns_mag',
        }
    },
    ammo_50bmg = {
        label = '.50 BMG',
        ammo_list = {
            'ammo_50bmg_mag',
        }
    },
    ammo_50cal = {
        label = '.50 cal',
        ammo_list = {
            'ammo_50mm_mag',
        }
    },
    ammo_762 = {
        label = '7.62x51mm',
        ammo_list = {
            'ammo_762_mag',
            'ammo_m1_mag',
            'ammo_marksman_mag',
            'ammo_mg_mag',
        }
    },
    ammo_762sti = {
        label = '7.62x51mm STI',
        ammo_list = {
            'ammo_762sti',
        }
    },
    ammo_556 = {
        label = '5.56x45mm',
        ammo_list = {
            'ammo_556mm_mag',
        }
    },
    ammo_918 = {
        label = '9x18mm',
        ammo_list = {
            'ammo_pp91_mag',
            'ammo_9x18mm',
        }
    },
    ammo_6x30 = {
        label = '4.6x30mm',
        ammo_list = {
            'ammo_6x30mm_mag',
        }
    },
    ammo_68 = {
        label = '6.8 Creedmore',
        ammo_list = {
            'ammo_spear_mag',
        }
    },
    ammo_umbrellamag = {
        label = 'Umbrella Corp UNK',
        ammo_list = {
            'ammo_umbrellamag',
        }
    },
    ammo_snipermag = {
        label = 'Sniper Magazine',
        ammo_list = {
            'snipermag',
        }
    },
    ammo_11Gauge = {
        label = '11 Gauge',
        ammo_list = {
            'ammo_11gauge_slug',
        }
    },
    ammo_12Gauge = {
        label = '12 Gauge',
        ammo_list = {
            'ammo_12gauge_slug',
        }
    },
    ammo_2Gauge = {
        label = '2 Gauge',
        ammo_list = {
            'ammo_2gauge_slug',
        }
    },
    ammo_308 = {
        label = '.308',
        ammo_list = {
            'ammo_308',
        }
    },
    ammo_beanbag = {
        label = 'Beanbag Shell',
        ammo_list = {
            'ammo_bb_slug',
        }
    },
    ammo_breech = {
        label = 'Breach Shell',
        ammo_list = {
            'ammo_breach_slug',
        }
    },
    ammo_bzgas = {
        label = 'BZ Gas Grenade',
        ammo_list = {
            'ammo_bzgas',
        }
    },
    ammo_smokeg = {
        label = 'Smoke Grenade',
        ammo_list = {
            'ammo_smokeg',
        }
    },
    ammo_crossbow_bolt = {
        label = 'Crossbow Bolt',
        ammo_list = {
            'ammo_crossbow_bolt',
        }
    },
    ammo_emp = {
        label = 'EMP Grenade',
        ammo_list = {
            'ammo_emp',
            'ammo_emp_grenade',
        }
    },
    ammo_flame_can = {
        label = 'Flamethrower',
        ammo_list = {
            'ammo_flame_can',
        }
    },
    ammo_grenade = {
        label = 'Grenade',
        ammo_list = {
            'ammo_gren',
            'weapon_grenade',
        }
    },
    ammo_laser = {
        label = 'LASER AMMO',
        ammo_list = {
            'ammo_laser',
        }
    },
    ammo_rifle = {
        label = 'Rifle Ammo',
        ammo_list = {
            'ammo_musketball',
        }
    },
    ammo_nails = {
        label = 'Nails',
        ammo_list = {
            'ammo_nails',
        }
    },
    ammo_paintballs = {
        label = 'Paintballs',
        ammo_list = {
            'ammo_paintballs',
        }
    },
    ammo_stungun = {
        label = 'Stungun Cartridge',
        ammo_list = {
            'ammo_stungun',
        }
    },
}

Citizen.CreateThread(function()
    TriggerServerCallback('base:GunData', function(GunData)
        for _, gun in pairs(GunData) do
            local _ammo = nil

            for ammoKey, ammoData in pairs(AmmoTable) do
                if ammoData.ammo_list then
                    for _, ammoItem in pairs(ammoData.ammo_list) do
                        if ammoItem == gun.ammo then
                            _ammo = ammoData.label
                            break
                        end
                    end
                end
                if _ammo then
                    break
                end
            end
            WeaponAmmoTypes[GetHashKey(gun.name)] = _ammo or "UNK"
        end
    end)
end)