-- Base Framework Init
Base = {}

baseLoaded = false

-- RegisterCommand("gc_"..GetCurrentResourceName(), function()
-- 	print("BEFORE:"..collectgarbage("count"))
-- 	print(collectgarbage("collect"))
-- 	print("AFTER:"..collectgarbage("count"))
-- end, true)

-- local resources = {
--     ["base_scripting"] = 120,
--     ["core_citizen"] = 120,
--     ["core_vehicles"] = 120,
--     ["core_inventory"] = 120,
-- }

-- local gcValue = resources[GetCurrentResourceName()]
-- if gcValue then
-- 	collectgarbage("setpause", gcValue)
-- end

-- CreateThread(function()
--     while true do
--         Wait(60000 + math.random(20000))
--         collectgarbage("collect")
--     end
-- end)

-- RegisterCommand("gcp_"..GetCurrentResourceName(), function(source, args)
-- 	collectgarbage("setpause", tonumber(args[1]))
-- end, true)

function onBaseReady(cb)
	Citizen.CreateThread(function()
		while not baseLoaded do Wait(0) end
        MySQL.ready(cb)
	end)

	AddEventHandler("base:reset", function(obj)
		baseLoaded = false
		Wait(100)
		baseLoaded = true

		Base = obj
        MySQL.ready(cb)
	end)
end

function init()
	TriggerEvent('base:init', function(obj)
		Base = obj
		baseLoaded = true
	end)

	-- This line is here in case the script never calls onBaseReady
	AddEventHandler("base:reset", function(obj)
		baseLoaded = false
		Wait(100)
		baseLoaded = true
		Base = obj
	end)
end

init()

-- Verbose Logging
local verboseLoggingEnabled = GetConvar("debug", "false") == "true"
function vlog(...)
    if verboseLoggingEnabled then
        print(...)
    end
end

AddEventHandler("verboseLogging", function(resourcename)
    if resourcename == GetCurrentResourceName() then
        verboseLoggingEnabled = not verboseLoggingEnabled
    end
end)

-- Synchronised States
aop = 'map'
RegisterNetEvent('updateaop')
AddEventHandler('updateaop', function(newaop) aop = newaop end)

-- Export Shortcuts
fdgMenus   = exports["core_menu"]
fdgJobs    = exports["core_jobs"]
fdgVehicle = exports["core_vehicles"]

-- Developer Functions
debugmode = false
function dbgprint(msg)
	if debugmode then print("["..GetCurrentResourceName().."] "..msg) end
end

locale = {}
function _U(str, ...)
    if locale[str] then
        return string.format(locale[str], ...):gsub("^%l", string.upper)
    else
        return "Language option ["..str.."] not found"
    end
end

function table.clone(orig, copies)
    copies = copies or {}
    local orig_type = type(orig)
    local copy
    if orig_type == 'table' then
        if copies[orig] then
            copy = copies[orig]
        else
            copy = {}
            copies[orig] = copy
            for orig_key, orig_value in next, orig, nil do
                copy[table.clone(orig_key, copies)] = table.clone(orig_value, copies)
            end
            setmetatable(copy, table.clone(getmetatable(orig), copies))
        end
    else -- number, string, boolean, etc
        copy = orig
    end
    return copy
end