fx_version 'adamant'
game 'gta5'

description 'Quack Framework'

version '1.0'

ui_page "sounds.html"

server_scripts {
	"@oxmysql/lib/MySQL.lua",
	"@base/server/utility.lua",
	"@base/server/weblink.lua",
	"@base/server/instance.lua",
	"@base/shared/worldgrid.lua",
	"reference/xInventory.lua",

	"server/main.lua",
	"shared/*.lua",

	"server/dependencies/*.lua",
	"server/classes/*.lua",
	"server/components/*.lua",
}

client_scripts {
	"@base/client/utility.lua",
	"client/main.lua",
	"client/login.lua",

	-- Load Component Dependencies
	"client/dependencies/*.lua",

	-- Load Shared Dependencies
	"shared/*.lua",

	-- Load Components (These build on top of base, and have no load order as they do not depend on each other)
	"client/components/*.lua",
}

dependencies {
	'yarn'
}

files({
	"reference/cinit.lua",
	"reference/sinit.lua",
	"reference/xInventory.lua",
	"sounds.html",
	"sounds/*.ogg",
})

exports {
	"Target",
	"resetTalkerDistance",
	"setTempTalkerDistance",
	"setTalkerDistance",
	"inForceAnimation",
}
