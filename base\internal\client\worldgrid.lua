local lastChunk = 0

CreateThread(function()
    while true do
        Wait(500)

        local ped = PlayerPedId()
        if DoesEntityExist(ped) then
            local chunk = WorldgridGetChunk(GetEntityCoords(ped))
            if chunk ~= lastChunk then
                lastChunk = chunk
                TriggerEvent("chunkUpdated", chunk)
                TriggerServerEvent("chunkUpdated", chunk)
            end
        end
    end
end)
