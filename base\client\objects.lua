---Links entity2 to entity1, such that if entity1 is deleted, entity2 is deleted as well
---@param entity1 any
---@param entity2 any
function LinkEntityToEntity(entity1, entity2)
    TriggerServerEvent(GetCurrentResourceName() .. ":linkEntity", NetworkGetNetworkIdFromEntity(entity1), NetworkGetNetworkIdFromEntity(entity2))
end

---Unlinks entity2 from entity1
---@param entity1 any
---@param entity2 any
function UnlinkEntityFromEntity(entity1, entity2)
    TriggerServerEvent(GetCurrentResourceName() .. ":unlinkEntity", NetworkGetNetworkIdFromEntity(entity1), NetworkGetNetworkIdFromEntity(entity2))
end

---Registers an entity to a script, such that when the script is stopped, the entity is deleted
---@param entity any
function RegisterScriptEntity(entity)
    TriggerServerEvent(GetCurrentResourceName() .. ":registerScriptEntity", NetworkGetNetworkIdFromEntity(entity))
end

---Registers an entity to a player, such that when the player leaves, the entity is deleted
---@param entity any
function RegisterPlayerEntity(entity)
    TriggerServerEvent(GetCurrentResourceName() .. ":registerPlayerEntity", NetworkGetNetworkIdFromEntity(entity))
end
