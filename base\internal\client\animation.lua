-- Clipsets
local activeClipsets = {}

local function getHighestClipset()
    local activeClipset = false
    local priority = -1

    for _, clipset in pairs(activeClipsets) do
        if clipset and clipset.priority > priority then
            priority = clipset.priority
            activeClipset = clipset
        end
    end

    return (activeClipset and activeClipset.name) or false
end

local function reloadActiveClipset()
    local ped = PlayerPedId()
    local clipset = getHighestClipset()

    -- If a clipset was found, and not already playing
    if clipset and lastClipset ~= clipset then
        RequestAnimSet(clipset)
        while (not HasAnimSetLoaded(clipset)) do Wait(0) end

        SetPedMovementClipset(ped, clipset, 1.0)

        lastClipset = clipset

    -- If no active clipsets, reset back to nothing
    elseif not clipset and lastClipset then
        ResetPedMovementClipset(ped, 0.0)
        lastClipset = false
    end
end

exports("AddClipset", function(identifier, clipset, priority, resource)
    activeClipsets[identifier] = {name = clipset, priority = priority, resource = resource}
    reloadActiveClipset()
end)

exports("RemoveClipset", function(identifier)
    activeClipsets[identifier] = nil
    reloadActiveClipset()
end)

-- animations
local function loadAnimationDict(dict)
    if ( DoesAnimDictExist(dict) and not HasAnimDictLoaded(dict) ) then
        local timeout = GetGameTimer() + 5000
        RequestAnimDict(dict)
        while ( not HasAnimDictLoaded(dict) and GetGameTimer() < timeout ) do
            Wait(0)
        end
    end
end

local function getAnimationFlag(looping, freeze, upperBodyOnly)
	if upperBodyOnly then
		if looping and freeze then
			return 17
		elseif looping and not freeze then
			return 49
		elseif not looping and not freeze then
			return 48
		end
	else
		if looping and freeze then
			return 3
		elseif looping and not freeze then
			return 1
		end
	end

	return 0
end

local function playAnimation(ped, dict, anim, looping, freeze, upperBodyOnly, phaseInTime, phaseOutTime, customFlag)
    if ( DoesAnimDictExist(dict) ) then
        loadAnimationDict(dict)
        local flag = customFlag or getAnimationFlag(looping, freeze, upperBodyOnly)
        TaskPlayAnim(ped, dict, anim, phaseInTime or 8.0, phaseOutTime or -8.0, -1, flag, 0.0, false, false, false)
        RemoveAnimDict(dict)
    end
end

local function stopAnimation(ped, dict, anim)
    if (dict and anim) then
        StopAnimTask(ped, dict, anim, -4.0)
    else
        StopAnimPlayback(ped, 0, 0)
    end
end

exports("PlayAnimation", function(ped, dict, anim, looping, freeze, upperBodyOnly, phaseInTime, phaseOutTime, customFlag)
    playAnimation(ped, dict, anim, looping, freeze, upperBodyOnly, phaseInTime, phaseOutTime, customFlag)
end)

exports("StopAnimation", function(ped, dict, anim)
    stopAnimation(ped, dict, anim)
end)

-- resource handler

AddEventHandler('onResourceStop', function(resourceName)
    -- Clipsets
    for id, clipset in pairs(activeClipsets) do
        if resourceName == clipset.resource then
            activeClipsets[id] = nil
        end
    end

    reloadActiveClipset()
end)