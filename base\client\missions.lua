-- These will be moved to their own module soon(tm)
local DEBUG = false

-- == Internal functions ==
local objectiveText = nil
local objectiveTextThread = function()
    while objectiveText do
        Wait(0)
        SetTextFont(4)
        SetTextProportional(1)
        SetTextScale(0.55, 0.55)
        SetTextColour(255, 255, 255, 255)
        SetTextDropShadow(0, 0, 0, 0, 255)
        SetTextEdge(1, 0, 0, 0, 255)
        SetTextDropShadow()
        SetTextOutline()
        SetTextWrap(0.0, 1.0)
        SetTextCentre(true)
        SetTextJustification(0)
        SetTextEntry('STRING')
        AddTextComponentString(objectiveText)
        DrawText(0.5, 0.85)
    end
end


-- == Exported functions ==
---Draws the mission text on the screen
---@param text string?
function SetObjectiveText(text)
    local shouldCreateThread = not objectiveText

    objectiveText = text

    if shouldCreateThread then
        CreateThread(objectiveTextThread)
    end
end

local waypoints = {}

---@class MarkerOptions
---@field type number?
---@field bob boolean?
---@field faceCamera boolean?
---@field heightOffset number?
---@field rotation vector3?
---@field direction vector3?

local function MarkerOptions(options)
    return {
        type = options.type or 2,
        bob = options.bob or true,
        faceCamera = options.faceCamera or true,
        heightOffset = options.heightOffset or 0.0,
        rotation = options.rotation or vector3(0.0, 0.0, 0.0),
        direction = options.direction or vector3(0.0, 0.0, 0.0)
    }
end

---Creates a blip for the mission
---@param coords vector3
---@param options {floatingText: {text: string?, renderDistance: number?, textOffset: number?}?, sprite: number, scale: number, color: number, alpha: number, route: boolean, text: string, marker?: MarkerOptions}
function CreateMissionWaypoint(coords, options)
    options = options or {}
    local blip = AddBlipForCoord(coords.x, coords.y, coords.z)

    SetBlipSprite(blip, options.sprite or 1)
    SetBlipScale(blip, options.scale or 1.0)
    SetBlipColour(blip, options.color or 5)
    SetBlipAlpha(blip, options.alpha or 255)
    SetBlipPriority(blip, 100)
    SetBlipAsShortRange(blip, false)
    SetBlipRoute(blip, options.route)
    SetBlipRouteColour(blip, options.color or 5)
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(options.text or "Objective")
    EndTextCommandSetBlipName(blip)

    if options.marker then
        CreateThread(function()
            local marker = MarkerOptions(options.marker)
            while DoesBlipExist(blip) do
                Wait(0)
                DrawMarker(marker.type, coords.x, coords.y, coords.z + marker.heightOffset, marker.direction.x, marker.direction.y, marker.direction.z, marker.rotation.x, marker.rotation.y, marker.rotation.z, 0.8, 0.8, 1.0, 238, 198, 78, 180, options.marker.bob or false,
                    options.marker.faceCamera or false, 2, false, false, false, false)
            end
        end)
    end

    if options.floatingText then
        local label = options.floatingText.text or "Objective"
        local renderDistance = options.floatingText.renderDistance or 20.0
        local textCoords = coords + vector3(0.0, 0.0, options.floatingText.textOffset or 0.0)

        CreateThread(function()
            while DoesBlipExist(blip) do
                Wait(0)
                local dist = #(GetEntityCoords(PlayerPedId()) - coords)
                if dist <= renderDistance then
                    DrawTextAtCoords(textCoords, label, { size = 0.35, color = { r = 255, g = 255, b = 255, a = 215 }, los = true })
                end
            end
        end)
    end

    table.insert(waypoints, blip)

    return blip
end

---Clears all mission waypoints
function ClearMissionWaypoints()
    for _, blip in ipairs(waypoints) do
        RemoveBlip(blip)
    end

    waypoints = {}
end

-- This is written like this for future expansion allowing selective stopping of certain threads
local entityPosCheckId = 1
local entityPosChecks = {}

---Awaits an entity to arrive at a set of coordinates
---@param entity number
---@param coords vector3
---@param radius number
---@param options {entity?: number, entityOffset?: vector3, entityBone?: string, heading?: number, headingTolerance?: number}?
function AwaitEntityArriveAtCoords(entity, coords, radius, options)
    options = options or {}
    local id = tostring(entityPosCheckId)
    entityPosCheckId = entityPosCheckId + 1
    entityPosChecks[id] = true

    while entityPosChecks[id] do
        Wait(0)

        -- Get the entity's position
        local entityCoords = GetEntityCoords(entity)
        local offset = options.entityOffset
        if options.entityBone then
            local boneIndex = GetEntityBoneIndexByName(entity, options.entityBone)
            entityCoords = GetWorldPositionOfEntityBone(entity, boneIndex)
        elseif options.entity and offset then
            entityCoords = GetOffsetFromEntityInWorldCoords(options.entity, offset.x, offset.y, offset.z)
        end

        -- Check if the entity is within the radius
        local distance = #(entityCoords - coords)
        local withinRadius = distance <= radius

        -- If the heading is not specified, then we don't care about the heading
        -- Otherwise, we check if the heading is within the tolerance
        local withinHeading = not options.heading
        if not withinHeading then
            local headingDelta = math.abs(GetEntityHeading(entity) - options.heading)
            withinHeading = headingDelta <= (options.headingTolerance or 10.0)
        end

        -- If the entity is within the radius and heading, then we can return
        if withinRadius and withinHeading then
            return true
        end

        -- Debug markers
        if DEBUG then
            DrawMarker(28, coords.x, coords.y, coords.z, 0, 0, 0, 0, 0, 0, radius, radius, radius, 46, 204, 113, 120, false, false, 2, false, false, false, false)
            DrawMarker(28, entityCoords.x, entityCoords.y, entityCoords.z, 0, 0, 0, 0, 0, 0, 0.4, 0.4, 0.4, 231, 76, 60, 120, false, false, 2, false, false, false, false)
        end
    end
end

---Stops all entity position checks
function StopEntityAwaits()
    entityPosChecks = {}
    entityPosCheckId = 1
end
