-- class
local function Teleport(enabled, id, ipl, dist, invite, voice, instance, vehicle, enterLabel, enterX, enterY, enterZ, enterR, exitLabel, exitX, exitY, exitZ, exitR, jobs, grades, licenses, property, items)
    local self = {}

    self.enabled = enabled

    self.id = id
    self.ipl = ipl
    self.dist = dist
    self.invite = invite
    self.voice = voice
    self.instance = instance
    self.vehicle = vehicle
    self.licenses = json.decode(licenses)
    self.property = property

    if items then
        self.items = json.decode(items)
    end

    self.jobs = json.decode(jobs)
    self.grades = json.decode(grades)

    self.Enter = {
        label = enterLabel,
        coords = vector3(enterX, enterY, enterZ),
        heading = enterR or 0.0
    }

    self.Exit = {
        label = exitLabel,
        coords = vector3(exitX, exitY, exitZ),
        heading = exitR or 0.0
    }

    -- job handling
    self.isWhitelist = function()
        if not self.jobs or #self.jobs == 0 then
            return true
        end

        for _, job in ipairs(self.jobs) do
            if DoesPlayerHaveOrg(job) then
                return true
            end
        end
    end

    return self
end

function createTeleport(enabled, id, ipl, dist, invite, voice, instance, vehicle, enterLabel, enterX, enterY, enterZ, enterR, exitLabel, exitX, exitY, exitZ, exitR, jobs, grades, licenses, property, items)
    return Teleport(enabled, id, ipl, dist, invite, voice, instance, vehicle, enterLabel, enterX, enterY, enterZ, enterR, exitLabel, exitX, exitY, exitZ, exitR, jobs, grades, licenses, property, items)
end

------------------
local teleports = {}

RegisterDataSyncHandler("teleports", function(data)
    clearTeleports()
    teleports = {}

    for k, v in pairs(data) do
        table.insert(teleports,
            createTeleport(v.enabled, v.name, v.ipl, v.dist, v.invite, v.voiceRange, v.instance, v.vehiclesAllowed, v.enterLabel, v.enterX, v.enterY, v.enterZ, v.enterR, v.exitLabel, v.exitX, v.exitY, v.exitZ, v.exitR, v.restrictedJob, v.restrictedGrades, v.licenses, v.property, v.items))
    end

    Wait(2000)
    refreshTeleports()
end)

-- main teleport handling

function clearTeleports()
    for i, v in ipairs(teleports) do
        if i % 20 == 0 then Wait(0) end
        Base.Markers.Delete("enter_" .. v.id)
        Base.Markers.Delete("exit_" .. v.id)
    end
end

local function canPlayerUseTeleporter(teleport)
    if teleport.items then
        for i, itemName in ipairs(teleport.items) do
            if exports.core_inventory:getItem(itemName) then
                return true
            end
        end

        Base.Notification("You do not have the required items to enter this area.")
        return false
    end

    return true
end

function exitTeleport(teleport)
    if not canPlayerUseTeleporter(teleport) then
        return
    end

    local instance = -1
    if LocalPlayer.state.inArena or LocalPlayer.state.inBattle then
        instance = false
    end

    TeleportPlayerToCoords(teleport.Enter.coords - vector3(0, 0, 0.95), teleport.Enter.heading, { allowVehicle = true, instance = instance })

    if (teleport.instance) then
        Base.Markers.Delete("exit_" .. teleport.id)
    end
end

function enterTeleport(teleport)
    if not canPlayerUseTeleporter(teleport) then
        return
    end

    local instance = (teleport.instance and "tp_" .. teleport.id) or false
    if LocalPlayer.state.inArena or LocalPlayer.state.inBattle then
        instance = false
    end

    TeleportPlayerToCoords(teleport.Exit.coords - vector3(0, 0, 0.95), teleport.Exit.heading, { allowVehicle = true, instance = instance })

    if (teleport.instance) then
        Base.Markers.Create("exit_" .. teleport.id, { x = teleport.Exit.coords.x, y = teleport.Exit.coords.y, z = teleport.Exit.coords.z, marker = 20, bobbing = false, noLower = true, size = -0.5 }, { r = 200, g = 0, b = 0 }, teleport.Exit.label, teleport, exitTeleport, false)
    end
end

function refreshTeleports()
    clearTeleports()

    for i, teleport in ipairs(teleports) do
        if i % 20 == 0 then
            Wait(0)
        end
        if (teleport.enabled) then
            if (teleport.isWhitelist() or hasLicense(teleport.licenses) or hasKeyToProperty(teleport.property)) then
                Base.Markers.Create("enter_" .. teleport.id, { x = teleport.Enter.coords.x, y = teleport.Enter.coords.y, z = teleport.Enter.coords.z, marker = 20, bobbing = false, noLower = true, size = -0.5 }, { r = 0, g = 191, b = 0 }, teleport.Enter.label, teleport, enterTeleport, false, true,
                    "Travelling")
            end
            if (not teleport.instance) then
                Base.Markers.Create("exit_" .. teleport.id, { x = teleport.Exit.coords.x, y = teleport.Exit.coords.y, z = teleport.Exit.coords.z, marker = 20, bobbing = false, noLower = true, size = -0.5 }, { r = 0, g = 191, b = 0 }, teleport.Exit.label, teleport, exitTeleport, false, true,
                    "Travelling")
            end
        end
    end
end

RegisterNetEvent("base:characterLoaded", function(data)
    Wait(1000)
    refreshTeleports()
end)

RegisterNetEvent('updatedPlayerOrgs', function()
    refreshTeleports()
end)

onBaseReady(function()
    refreshTeleports()
end)
