local dialogue = {
    {
        characterText = "Hello <PERSON>, how can I help you?",
        responses = {
            {
                responseText = "Request meeting with the Governor's office",
                event = "my-gmeet"
            },
            {
                responseText = "How do I make a news announcement",
                responses = {
                    {
                        characterText = "To make a news announcement, you simply need to contact Weazel news and ask them to put up a news announcement.",
                        responses = {
                            {
                                responseText = "Okay thank you",
                                event = "mayor-leave"
                            },
                        }
                    }
                }
            },
            {
                responseText = "Call Police for immediate assistance",
                event = "my-pc"
            },
            {
                responseText = "Just checking up on you",
                responses = {
                    {
                        characterText = "I work 24 hours a day, 7 days a week, I have to take meth to keep going",
                        responses = {
                            {
                                responseText = "Oh okay...",
                                event = "mayor-leave"
                            },
                        }
                    }
                }
            },
        }
    }
}

-- Below is all the dialouge actions for the listed above from the "EVENT" in the dialogue table

RegisterDialogueAction("mayor-leave", function() -- For the event mayor-leave to close the menu
end)

RegisterDialogueAction("my-gmeet", function()
    Wait(1000)
    if DoesPlayerHaveOrgPermission("mayorgmeet") then
        TriggerServerEvent("main_mayortools:discord")
    end
end)

RegisterDialogueAction("my-News", function()
    -- if DoesPlayerHaveOrgPermission(xPlayer.source, "mayorgmeet") then UNCOMMENT THIS AND DELETE PLAYERDATA.JOB FOR NEW PERMISSON SYSTEM WHEN IMPLEMENTED
    -- Display the on-screen keyboard
    if PlayerData and PlayerData.job.name == "mayor" then
        AddTextEntry('FMMC_KEY_TIP1', "Enter your message (max 50 characters):")
        DisplayOnscreenKeyboard(1, "FMMC_KEY_TIP1", "", "", "", "", "", 50)

        -- Wait for the player to finish typing
        while UpdateOnscreenKeyboard() == 0 do
            Citizen.Wait(0)
        end

        -- Check if the player confirmed the input
        if GetOnscreenKeyboardResult() then
            local words = GetOnscreenKeyboardResult()
            TriggerServerEvent("fdg_mayor:news", words)
        end
    end
end)

--[[RegisterDialogueAction("my-pc", function()
    -- if DoesPlayerHaveOrgPermission(xPlayer.source, "mayorpanicbutton") then UNCOMMENT THIS AND DELETE PLAYERDATA.JOB FOR NEW PERMISSON SYSTEM WHEN IMPLEMENTED
    if PlayerData and PlayerData.job.name == "mayor" then
        local rng = math.random(0, 2000)
        Wait(rng)
        Base.Sound.PlayOnEntity(PlayerPedId(), 2.0, "99dispatch", 0.5)
        local coords = GetEntityCoords(PlayerPedId())
        TriggerServerEvent("main_dispatch:StartCall", 'officer_' .. GetPlayerServerId(PlayerId()), "police", 'Mayor In Immediate Danger', 'Mayor requesting immediate assistance',
            {
                callsign = nil,
                location = exports["core_hud"]:getPlayerLocation(),
                code = "10-71"
            },
            {
                caller = PlayerId(),
                theme = "error",
                forceActive = true,
                gps = { x = coords.x, y = coords.y, z = coords.z },
                blip = {
                    label = '10-71 : Mayor Requesting Police assistance',
                    pos = { x = coords.x, y = coords.y, z = coords.z },
                    color = 1,
                },
            }
        )
    end
end)
]]

-- Below is the Ped (102-115) and how it is created in the server (117-128)
local peds = {
    {
        location = "Mayor's office",
        name = "Bradley Mathis",
        spawnLocation = { coords = vector3(-1798.7976074219, 437.5866394043, 127.50706481934), heading = 271.28 },
        model = "mp_m_freemode_01",
        anim = {
            dict = "anim@amb@nightclub@peds@",
            name = "WORLD_HUMAN_STAND_MOBILE_UPRIGHT",
        },
        skin =
        '{"cosmetics":{"eyebrows":{"colour":0,"highlights":0,"thickness":100,"style":0},"makeup":{"colour":0,"highlights":0,"thickness":0,"style":0},"blush":{"colour":0,"highlights":0,"thickness":0,"style":0},"beard":{"colour":0,"highlights":0,"thickness":100,"style":0},"head":{"colour":0,"highlights":0,"thickness":100,"style":21},"chest":{"colour":0,"highlights":0,"thickness":0,"style":0},"lipstick":{"colour":0,"highlights":0,"thickness":0,"style":0}},"skin":{"blemish":{"opacity":0,"texture":0},"shapemix":47,"age":{"opacity":0,"texture":0},"complexion":{"opacity":0,"texture":0},"colour":0,"nose":{"width":-38,"peak_length":0,"height":-1,"heigh":0,"peak_lowering":0,"twist":0,"peak_height":0},"eyes":{"colour":8,"size":0,"brow_forward":0,"brow_height":0},"mother":15,"neck_thickness":0,"father":40,"jaw":{"width":0,"length":0},"damage":{"opacity":0,"texture":0},"freckles":{"opacity":0,"texture":0},"chin":{"width":0,"tip":0,"lower":0,"length":0},"cheeks":{"width":0,"chub":0,"height":0},"lip_thickness":0},"tattoos":[{"hash":"MP_Gunrunning_Tattoo_015_M","collection":"mpgunrunning_overlays"},{"hash":"MP_MP_ImportExport_Tat_005_M","collection":"mpimportexport_overlays"}],"clothing":{"pants":{"model":10,"texture":0},"bproof":{"model":0,"texture":0},"decals":{"model":0,"texture":0},"shoes":{"model":10,"texture":0},"tshirt":{"model":6,"texture":0},"glasses":{"model":35,"texture":0},"bag":{"model":0,"texture":0},"mask":{"model":121,"texture":0},"ears":{"model":-1,"texture":0},"chain":{"model":0,"texture":0},"helmet":{"model":-1,"texture":0},"torso":{"model":11,"texture":0},"arms":{"model":11,"texture":0}},"model":"mp_m_freemode_01","sex":0}'
    },
}

CreateThread(function()
    Wait(1000)

    for i, ped in ipairs(peds) do
        CreateInteractablePed(ped.location .. "mayor_assistant", ped.model, ped.spawnLocation, function(entity)
            OpenDialogueWithPed(ped.name, entity, dialogue, { role = "Mayor's Assistant" })
        end, {
            skin = json.decode(ped.skin),
            anim = ped.anim,
        })
    end
end)
