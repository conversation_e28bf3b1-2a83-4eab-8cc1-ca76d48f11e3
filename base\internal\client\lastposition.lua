-- Simple script to keep track of the last position of the player if they are below ground level.
-- Used to get their "outside" position if they are in an interior.
local lastCoords = nil

CreateThread(function()
    while true do
        local coords = GetEntityCoords(PlayerPedId())

        local state = LocalPlayer.state
        if coords.z > 0.0 then
            if state.aboveGroundCoords then
                state:set("aboveGroundCoords", nil, true)
            end
        else
            state:set("aboveGroundCoords", lastCoords, true)
        end

        lastCoords = coords

        Wait(1000)
    end
end)
