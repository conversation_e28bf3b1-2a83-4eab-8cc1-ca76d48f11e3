local spawned_vehicles = {}

RegisterNetEvent("racing_vehicles:loadDisplayCars")
AddEventHandler("racing_vehicles:loadDisplayCars", function(vehicleData)
    -- print("[DEBUG] Received vehicle data to load")
    for _, vehicle in ipairs(vehicleData) do
        -- print("[DEBUG] Spawning vehicle: " .. vehicle.model)
        createDisplayVehicle(vehicle.model, vehicle.props, vehicle.coords, vehicle.heading)
    end
end)

function ApplyVehicleModifications(vehicle, props)
    if props.color1 and props.color2 then
        SetVehicleColours(vehicle, props.color1, props.color2)
    end

    if props.pearlescentColor and props.wheelColor then
        SetVehicleExtraColours(vehicle, props.pearlescentColor, props.wheelColor)
    end

    if props.neonColor then
        SetVehicleNeonLightsColour(vehicle, props.neonColor[1], props.neonColor[2], props.neonColor[3])
    end

    if props.plate then
        SetVehicleNumberPlateText(vehicle, props.plate)
        SetVehicleNumberPlateTextIndex(vehicle, props.plateIndex or 0)
    end
end

function createDisplayVehicle(model, props, coords, heading)
    exports["base_vehicle"]:createFromModel(model, coords, heading + 0.0, {}, function(vehicle)
        ApplyVehicleModifications(vehicle, props)
        SetVehicleDoorsLocked(vehicle, 2)
        FreezeEntityPosition(vehicle, true)
        SetEntityCollision(vehicle, true, true)
        ResetEntityAlpha(vehicle)
        table.insert(spawned_vehicles, vehicle)
    end, true)
end

RegisterNetEvent("racing_vehicles:deleteDisplayCars")
AddEventHandler("racing_vehicles:deleteDisplayCars", function()
    for _, vehicle in ipairs(spawned_vehicles) do
        if DoesEntityExist(vehicle) then
            DeleteEntity(vehicle)
        end
    end
    spawned_vehicles = {}
end)
