fx_version 'adamant'
game 'gta5'

ui_page 'ui/index.html'

files {
	'ui/index.html',
	'ui/json/*.json',
	'ui/styles/*.css',
	'ui/scripts/*.js',
	"playerLocations.json",

}

client_scripts {
	"@base_scripting/reference/cinit.lua",
	"@base/client/utility.lua",
	"@base/client/controls.lua",

	-- admin menu
	'client/main.lua',
	'client/spectate.lua',
	'client/adminids.lua',
	'client/adminmenu.lua',
	'client/afkkick.lua',
	'client/anticheat.lua',
	'client/report.lua',

	-- mellotrainer
	'client/mellotrainer/cl_utils.lua',               -- Clientsided utilites.
	'client/mellotrainer/cl_variables.lua',           -- Create all default variables.
	'client/mellotrainer/cl_general.lua',             -- User Managment/Trainer Controls/Global Functions
	'client/mellotrainer/cl_player.lua',              -- Player Toggles & Options
	'client/mellotrainer/cl_settings.lua',            -- General Settings (Player Blips etc)
	'client/mellotrainer/cl_player_skin.lua',         -- Player Skins & Props
	'client/mellotrainer/cl_player_online.lua',       -- Other Player Options (Teleport/Spectate)
	'client/mellotrainer/cl_vehicles.lua',            -- Vehicle Spawning/Modifications
	'client/mellotrainer/cl_weapons.lua',             -- Weapon Spawning/Attachments
	'client/mellotrainer/cl_noclip.lua',              -- Handles all No Clip features

	-- debug
	"client/debug.lua",

	-- scoreboard
	'client/scoreboard.lua',

	-- bugreport
	'client/bugreport.lua',
}

server_scripts {
	"@oxmysql/lib/MySQL.lua",
	"@base_scripting/reference/sinit.lua",
	"@base/server/utility.lua",
	"@base/server/weblink.lua",
	"@base/server/instance.lua",

	-- admin menu
	'server/main.lua',
	'server/adminmenu.lua',
	'server/afkkick.lua',
	'server/commands.lua',
	'server/anticheat.lua',
	'server/adminaction.lua',
	'server/report.lua',
	'server/flag.lua',
	'server/cron.lua',
	'server/tebex.lua',
	'server/cleanup.lua',
	'server/bulkcommands.lua',
	'server/raidcommand.lua',

	-- mellotrainer
	'server/mellotrainer/sv_config.lua',   -- MelloTrainer config file
	'server/mellotrainer/sv_data_saving.lua', -- Data saving system
	'server/mellotrainer/sv_player_skin.lua', -- Player Skin & Prop

	-- debug
	"server/debug.lua",

}

exports {
	"getAntiCheat",
	"revive"
}

dependencies {
	"base_scripting"
}
