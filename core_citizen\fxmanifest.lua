fx_version 'adamant'
game 'gta5'

client_scripts {
	"@base_scripting/reference/cinit.lua",
	"@base/client/utility.lua",
	'@polyzone/client.lua',
	"client/*.lua"

}

server_scripts {
	"@oxmysql/lib/MySQL.lua",
	'@base_scripting/shared/async.lua',
	"@base_scripting/reference/sinit.lua",
	"@base/server/utility.lua",
	"server/main.lua"
}

exports {
	"isPlayerCuffed",
	"isPlayerInjured",
	"isPlayerDragging",
	"isPlayerKnockedOut",

	-- Standard Function
	"idcard",
	"cuff",
	"drag",
	"search",
	"putInVehicle",
	"pullFromVehicle",
	"prisonTransport",
	"setPlayerUnconcious",
	"requestIDVehicle",
	"requestIDPlayer",
	"returnIDVehicle",
	"returnIDPlayer"

}

dependencies {
	"base_scripting"
}
