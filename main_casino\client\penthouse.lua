-- Enable or disable the specified props in an interior
function SetIplPropState(interiorId, props, state, refresh)
    if refresh == nil then refresh = false end
    if IsTable(interiorId) then
        for key, value in pairs(interiorId) do
            SetIplPropState(value, props, state, refresh)
        end
    else
        if IsTable(props) then
            for key, value in pairs(props) do
                SetIplPropState(interiorId, value, state, refresh)
            end
        else
            if state then
                if not IsInteriorPropEnabled(interiorId, props) then EnableInteriorProp(interiorId, props) end
            else
                if IsInteriorPropEnabled(interiorId, props) then DisableInteriorProp(interiorId, props) end
            end
        end
        if refresh == true then RefreshInterior(interiorId) end
    end
end

-- Check if a variable is a table
function IsTable(T)
    return type(T) == 'table'
end
-- Return the number of elements of the table
function Tablelength(T)
    local count = 0
    for _ in pairs(T) do count = count + 1 end
    return count
end

local interiorId = 274689
local colorId = 3
local pattern = "Set_Pent_Pattern_09"

Citizen.CreateThread(function()
    Wait(1000)
    RequestIpl("vw_casino_penthouse")

    -- Reset
    SetIplPropState(interiorId, "Set_Pent_Pattern_01", false, false)
    SetIplPropState(interiorId, "Set_Pent_Pattern_02", false, false)
    SetIplPropState(interiorId, "Set_Pent_Pattern_03", false, false)
    SetIplPropState(interiorId, "Set_Pent_Pattern_04", false, false)
    SetIplPropState(interiorId, "Set_Pent_Pattern_05", false, false)
    SetIplPropState(interiorId, "Set_Pent_Pattern_06", false, false)
    SetIplPropState(interiorId, "Set_Pent_Pattern_07", false, false)
    SetIplPropState(interiorId, "Set_Pent_Pattern_08", false, false)
    SetIplPropState(interiorId, "Set_Pent_Pattern_09", false, false)    
    SetIplPropState(interiorId, "Set_Pent_Arcade_Retro", false, false)
    SetIplPropState(interiorId, "Set_Pent_Arcade_Modern", false, false)
    SetIplPropState(interiorId, "Set_Pent_Bar_Clutter", false, false)
    SetIplPropState(interiorId, "set_pent_bar_light_0", false, false)
    SetIplPropState(interiorId, "set_pent_bar_light_01", false, false)
    SetIplPropState(interiorId, "set_pent_bar_light_02", false, false)
    SetIplPropState(interiorId, "set_pent_bar_party_0", false, false)
    SetIplPropState(interiorId, "set_pent_bar_party_1", false, false)
    SetIplPropState(interiorId, "set_pent_bar_party_2", false, false)
    SetIplPropState(interiorId, "set_pent_bar_party_after", false, false)
    SetIplPropState(interiorId, "Set_Pent_Clutter_03", false, false)
    SetIplPropState(interiorId, "Set_Pent_Clutter_03", false, false)
    SetIplPropState(interiorId, "Set_Pent_Clutter_03", false, false)
    SetIplPropState(interiorId, "Set_Pent_Clutter_03", false, false)
    SetIplPropState(interiorId, "Set_Pent_Spa_Bar_Open", false, false)
    SetIplPropState(interiorId, "Set_Pent_Spa_Bar_Closed", false, false)
    SetIplPropState(interiorId, "Set_Pent_GUEST_BLOCKER", false, false)
    SetIplPropState(interiorId, "Set_Pent_LOUNGE_BLOCKER", false, false)
    SetIplPropState(interiorId, "Set_Pent_OFFICE_BLOCKER", false, false)
    SetIplPropState(interiorId, "Set_Pent_CINE_BLOCKER", false, false)
    SetIplPropState(interiorId, "Set_Pent_SPA_BLOCKER", false, false)
    SetIplPropState(interiorId, "Set_Pent_BAR_BLOCKER", false, false)

    -- Set
    SetIplPropState(interiorId, "Set_Pent_Tint_Shell", true, true)
    SetInteriorEntitySetColor(interiorId, "Set_Pent_Tint_Shell", colorId)
    SetIplPropState(interiorId, pattern, true, false)
    SetInteriorEntitySetColor(interiorId, pattern, colorId)
    SetIplPropState(interiorId, "Set_Pent_Spa_Bar_Open", true, false)
    SetInteriorEntitySetColor(interiorId, "Set_Pent_Spa_Bar_Open", colorId)
    SetIplPropState(interiorId, "Set_Pent_Media_Bar_Open", true, false)
    SetInteriorEntitySetColor(interiorId, "Set_Pent_Media_Bar_Open", colorId)
    SetIplPropState(interiorId, "Set_Pent_Dealer", true, false)
    SetInteriorEntitySetColor(interiorId, "Set_Pent_Dealer", colorId)
    SetIplPropState(interiorId, "Set_Pent_Arcade_Modern", true, false)
    SetIplPropState(interiorId, "Set_Pent_Bar_Clutter", true, false)
    SetIplPropState(interiorId, "set_pent_bar_light_0", true, false)
    SetIplPropState(interiorId, "", true, false)
    RefreshInterior(interiorId)
end)