local blind = false
local lastBlindCheck = 0

local sedationEffect = TimedStatusEffect(StatusEffect("sedative", "Sedated",
    {
        onStart = function(effect)
            -- exports.main_progressbar:start(4000, "Sedated...")
            TriggerScreenblurFadeIn(0)
            lastBlindCheck = GetGameTimer()
        end,

        onTick = function(effect)
            SetPedToRagdoll(PlayerPedId(), 100, 100, 0, false, false, false)
            ResetPedRagdollTimer(PlayerPedId())
            TriggerScreenblurFadeIn(0)

            if GetGameTimer() - lastBlindCheck > 1000 then

                local blindChance = math.random(1, 5)
                if blindChance == 1 then
                    blind = not blind

                    if blind then
                        DoScreenFadeOut(500)
                    else
                        DoScreenFadeIn(500)
                    end
                end
                lastBlindCheck = GetGameTimer()
            end
        end,

        onStop = function()
            TriggerScreenblurFadeOut(0)
            DoScreenFadeIn(500)
        end,
    },
    {
        alert = false,
        desc = "You've been sedated",
        tickRate = 0,
    }
), { maxTime = 5 * 60 })

RegisterInteraction("player", "inject_droperidol", { label = "Inject Droperidol" },
    function(player)
        exports.main_roleplay:startDatabaseAnim("inject")
        TriggerServerEvent("effect:injectDroperidol", GetPlayerServerId(player))
    end,
    function(player)
        return exports["core_inventory"]:getItem("syringe_droperidol")
    end
)

-- RegisterInteraction("player", "inject_", { label = "Inject " },
--     function(player)
--         exports.main_roleplay:startDatabaseAnim("inject")
--         TriggerServerEvent("effect:injectDroperidol", GetPlayerServerId(player))
--     end,
--     function(player)
--         return exports["core_inventory"]:getItem("syringe_droperidol")
--     end
-- )

RegisterNetEvent("effects:startSedation", function(startDelay, duration, shouldKill)
    Wait(startDelay)

    if not shouldKill then
        print("Starting sedation", duration)
        sedationEffect.addTime(duration)
    else
        ApplyDamageToPed(PlayerPedId(), 1000, false)
        Wait(3000)

        TriggerEvent("playerDamage", {
            weaponLabel = "Sudden Cardiac Death",
            weaponType = "OVRD_SEVERE",
            damageOverTime = false,
            fatal = true,
            isMelee = false,
            boneLabel = "Unknown",
            damage = 100,
        })
    end
end)
