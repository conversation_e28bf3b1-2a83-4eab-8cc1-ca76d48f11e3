-- Specifically handles effects which work on a linear scale, which declines over time
-- Example: hunger level and deciding which buff / debuff to apply

---@class LinearStatusEffectManagerOptions
---@field startValue number Starting value for the linear scale. Default 100
---@field tickRate number How often to adjust the effect value in ms. Default 1000ms
---@field min number Minimum effect value. Default 0
---@field max number Maximum effect value. Default 100
---@field databaseSave boolean Specifies whether the effect value should be saved to the database. Default false

---@class LinearStatusEffectManager

---Manager object to handle multiple effects on a single linear scale
---@param name string Identifier of the effect
---@param options LinearStatusEffectManagerOptions
---@param effects StatusEffect[]
---@return LinearStatusEffectManager
function LinearStatusEffectManager(name, options, effects)
    local self = StatusEffectManager(name)

    if not options then options = {} end

    self.effectValue = options.startValue or 100
    self.tickRate = options.tickRate or 1000
    self.min = options.min or 0
    self.max = options.max or 100

    self.databaseSave = options.databaseSave or false
    self.databaseCooldown = false
    self.pendingDatabaseRequest = false

    self.effects = effects or {}

    Citizen.CreateThread(function()
        Wait(1000)
        while true do
            self.effectValue = math.max(math.min(self.effectValue - 1, self.max), self.min)
            self.applyEffects()
            Wait(self.tickRate)
            self.saveValue()
        end
    end)

    self.add = function(val)
        self.effectValue = math.min(self.effectValue + val, self.max)
        self.applyEffects()
        self.saveValue()
    end

    self.remove = function(val)
        self.effectValue = math.max(self.effectValue - val, self.min)
        self.applyEffects()
        self.saveValue()
    end

    self.set = function(val)
        self.effectValue = val
        self.applyEffects()
        self.saveValue()
    end

    self.shouldApplyEffect = function (effect)
        return self.effectValue >= effect.min and self.effectValue <= effect.max
    end

    self.applyEffects = function()
        for i, effect in ipairs(self.effects) do

            -- If the effect should activate
            if self.shouldApplyEffect(effect) then

                -- Start if not already activated
                if not effect.active then
                    effect.start()
                end

            -- If it shouldn't activate, and is currently active, stop it
            elseif effect.active then
                effect.stop()
            end
        end
    end

    self.saveValue = function()
        if self.databaseSave then

            if self.databaseCooldown then
                self.pendingDatabaseRequest = true
                return
            end

            self.databaseCooldown = true
            TriggerServerEvent("effects:saveValue", self.name, self.effectValue)

            Wait(10000)

            if self.pendingDatabaseRequest then
                TriggerServerEvent("effects:saveValue", self.name, self.effectValue)
            end

            self.pendingDatabaseRequest = false
            self.databaseCooldown = false
        end
    end

    self.getValue = function()
        return self.effectValue
    end

    RegisterNetEvent("effects:"..self.name..":add", self.add)
    RegisterNetEvent("effects:"..self.name..":remove", self.remove)
    RegisterNetEvent("effects:"..self.name..":set", self.set)

    return self
end