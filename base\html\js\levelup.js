window.addEventListener('message', function(event) {
    if (event.data.type === 'skillLevelUp') {
        console.log("Start")
        var skill = event.data.skill;
        var level = event.data.level;
        
        // Update level-up message
        updateLevelUpMessage(skill, level);
        document.getElementById('level-ui').style.display = 'flex';
        
        
        // Set timeout to hide the UI after 5 seconds
        setTimeout(function() {
            document.getElementById('level-ui').style.display = 'none';
        }, 5000);
    }
});


function updateLevelUpMessage(skill, level) {
    document.getElementById('level-up-message').textContent = "You are now level " + level + " in " + skill + "!";
    console.log("UPDATE Level up message: " + skill + " " + level);
}
