local objects_spawnedObjects = {}
local objects_objectCount = 0
local objects_playerObjects = {}
local objects_jobObjects = {}
local objects_closestObject = nil

--[[INTERNAL FUNCTIONS]]

local objects_getObjectAprox = function(model, coords, heading)
    local obj = CreateObject((type(model) == 'number' and model or GetHashKey(model)), coords.x, coords.y, coords.z, 0, 0, 1)
    FreezeEntityPosition(obj, true)
    SetEntityHeading(obj, heading)
    PlaceObjectOnGroundProperly(obj)
    local p = GetEntityCoords(obj)
    local r = GetEntityRotation(obj, 2)
    DeleteObject(obj)
    return p, r
end

local objects_getObject = function(model, coords, heading)
    local obj = CreateObject((type(model) == 'number' and model or GetHashKey(model)), coords.x, coords.y, coords.z, 0, 0, 1)
    FreezeEntityPosition(obj, true)
    SetEntityHeading(obj, heading)
    local p = GetEntityCoords(obj)
    local r = GetEntityRotation(obj, 2)
    DeleteObject(obj)
    return p, r
end

local objects_loadWeaponAsset = function(asset)
    if (not HasWeaponAssetLoaded(asset)) then
        local timeout = GetGameTimer() + 5000
        RequestWeaponAsset(asset, 31, 0)
        while (not HasWeaponAssetLoaded(asset) and GetGameTimer() < timeout) do
            Wait(0)
        end
    end
end

local objects_applyWeaponObjectAttachment = function(obj, hash)
    if (not HasWeaponGotWeaponComponent(obj, hash)) then
        GiveWeaponComponentToWeaponObject(obj, hash)
    end
end

local objects_getAllWeaponObjectAttachmentHashes = function(model, data)
    local attachmentNames = {
        ["attachment_engrave"] = true,
        ["attachment_extendedmag"] = true,
        ["attachment_extendedmag2"] = true,
        ["attachment_grip"] = true,
        ["attachment_flashlight"] = true,
        ["attachment_reddot"] = true,
        ["attachment_scope"] = true,
        ["attachment_scope2"] = true,
        ["attachment_heavybarrel"] = true,
        ["attachment_suppressor"] = true,
        ["attachment_muzzleboost"] = true
    }
    local attachmentHashes = {}

    -- gather hashes
    for key, v in pairs(data) do
        if (v and attachmentNames[key]) then
            local attachment = exports["core_inventory"]:getWeaponAttachment(model, key)
            if attachment then
                table.insert(attachmentHashes, attachment)
            end
        end
    end

    return attachmentHashes
end

local objects_createObject = function(id, data)
    if (objects_spawnedObjects[id]) then
        if (type(objects_spawnedObjects[id]) ~= "boolean") then
            DeleteObject(objects_spawnedObjects[id])
        end
        objects_spawnedObjects[id] = nil
    end

    if (data.weapon) then
        objects_loadWeaponAsset(data.model)
    end

    local hash = (type(data.model) == "string" and GetHashKey(data.model) or data.model)
    local obj = (data.weapon and CreateWeaponObject(hash, -1, data.coords.x, data.coords.y, data.coords.z, 1, 0, 0, 1) or CreateObject(hash, data.coords.x, data.coords.y, data.coords.z, 0, 0, 1))

    -- if the object is a weapon object
    if (data.weapon) then
        local attachmentHashes = objects_getAllWeaponObjectAttachmentHashes(data.model, data.data)
        for _, v in pairs(attachmentHashes) do
            objects_applyWeaponObjectAttachment(obj, v)
        end
        if data.data.attachment_engrave then
            SetWeaponObjectTintIndex(obj, 2) --Gold tint as that's as close as I can get to the engraving
        end
        RemoveWeaponAsset(data.model)
    end

    -- if the object is attached to a ped or not
    if (data.attached) then
        local player = GetPlayerFromServerId(data.attached.target)

        -- cached objects = player attached objects?
        if (objects_playerObjects[data.attached.target] == nil) then
            objects_playerObjects[data.attached.target] = {}
        end

        objects_playerObjects[data.attached.target][id] = data

        if (player == -1) then
            DeleteObject(obj)
            return
        end

        local target = GetPlayerPed(player)
        local bone = GetPedBoneIndex(target, data.attached.bone)
        FreezeEntityPosition(obj, false)
        AttachEntityToEntity(
            obj,
            target,
            bone,
            data.attached.x,
            data.attached.y,
            data.attached.z,
            data.attached.xr,
            data.attached.yr,
            data.attached.zr,
            data.attached.p9,
            data.attached.useSoftPinning,
            data.attached.collision,
            data.attached.isPed,
            data.attached.vertexIndex,
            data.attached.fixedRot
        )
    else
        FreezeEntityPosition(obj, true)
        SetEntityRotation(obj, data.rotation.x, data.rotation.y, data.rotation.z, 2, true)
    end

    -- disable collisions test
    if (data.noCollide) then
        SetEntityCollision(obj, false, true)
        SetEntityCompletelyDisableCollision(obj, false, true)
    end

    -- job object
    if (data.job ~= nil and data.job ~= "") then
        PlayerData = Base.GetPlayerData()
        if data.job == "internal" and data.owner == GetPlayerServerId(PlayerId()) then
            if (objects_jobObjects["internal"] == nil) then
                objects_jobObjects["internal"] = {}
            end
            objects_jobObjects["internal"][id] = obj
        elseif DoesPlayerHaveOrg(data.job) then
            if (objects_jobObjects[data.job] == nil) then
                objects_jobObjects[data.job] = {}
            end
            objects_jobObjects[data.job][id] = obj
        end
    end

    objects_spawnedObjects[id] = obj
end

--[[NET EVENTS]]

RegisterNetEvent("fdg_sync:Sync", function(values)
    local resourcevalues = values["objectspawner"] or {}
    for k, v in pairs(resourcevalues) do
        objects_createObject(k, v)
    end
end)

RegisterNetEvent("fdg_sync:Add", function(resourcename, key, value)
    if resourcename == "objectspawner" then
        objects_createObject(key, value)
    end
end)

RegisterNetEvent("fdg_sync:Remove", function(resourcename, key, source)
    if resourcename == "objectspawner" then
        objects_closestObject = nil

        if (objects_playerObjects[source] ~= nil) then
            objects_playerObjects[source][key] = nil
        end

        if (objects_spawnedObjects[key]) then
            -- initial cleanup
            DeleteObject(objects_spawnedObjects[key])
            objects_spawnedObjects[key] = nil
            -- overcomplicated job cleanup
            for job in pairs(objects_jobObjects) do
                if (objects_jobObjects[job][key]) then
                    objects_jobObjects[job][key] = nil
                end
            end
        end
    end
end)

RegisterNetEvent("fdg_sync:Update", function(resourcename, newData)
    if resourcename == "objectspawner" then
        objects_closestObject = nil
        local i = 0
        for key in pairs(objects_spawnedObjects) do
            for source in pairs(objects_playerObjects) do
                if (objects_playerObjects[source][key] and not newData[key]) then
                    objects_playerObjects[source][key] = nil
                end
            end

            for job in pairs(objects_jobObjects) do
                if (objects_jobObjects[job][key] and not newData[key]) then
                    objects_jobObjects[job][key] = nil
                end
            end

            if (not newData[key]) then
                DeleteObject(objects_spawnedObjects[key])
                objects_spawnedObjects[key] = nil
            end

            i = i + 1
            if i % 10 == 0 then Wait(0) end
        end
    end
end)

-- this uses the exact same logic as above, but cbf complining and making a function
RegisterNetEvent("onPlayerDropped", function(source)
    Wait(500) -- wait for some reason, unsure why
    if (objects_playerObjects[source]) then
        for key in pairs(objects_playerObjects[source]) do
            if (objects_spawnedObjects[key]) then
                -- cleanup the main object
                DeleteObject(objects_spawnedObjects[key])
                objects_spawnedObjects[key] = nil
                -- overcomplicated job cleanup again
                for job in pairs(objects_jobObjects) do
                    if (objects_jobObjects[job][key]) then
                        objects_jobObjects[job][key] = nil
                    end
                end
            end
        end
    end
end)
-- i don't even think this would be triggered? judging that above the objects are cleaned up.
RegisterNetEvent("onPlayerJoining", function(source)
    Wait(500)
    if (objects_playerObjects[source]) then
        for k, v in pairs(objects_playerObjects[source]) do
            objects_createObject(k, v)
        end
    end
end)

--[[BASE INIT]]

Base.Objects = {}

Base.Objects.Create = function(model, x, y, z, heading, job, freeze, attached, noCollide, weapon, data, float)
    local objSettings = {
        owner = GetPlayerServerId(PlayerId()),
        model = model,
        coords = vector3(x, y, z),
        heading = heading,
        job = job,
        freeze = freeze,
        attached = attached,
        noCollide = noCollide,
        weapon = weapon or false,
        data = data or {}
    }

    if attached then
        if objects_spawnedObjects[attached.target .. "_obj_" .. model] then
            TriggerServerEvent("fdg_sync:Remove", "objectspawner", attached.target .. "_obj_" .. model)
            Wait(1000)
        end
        TriggerServerEvent("fdg_sync:Add", "objectspawner", attached.target .. "_obj_" .. model, objSettings)
        return attached.target .. "_obj_" .. model
    else
        -- this grabs the appoximate positions of where the object will be from the initial client, this will stop objects from floating in the sky when placed for others.
        local coords, rotation = objects_getObjectAprox(model, vector3(x, y, z), heading)
        if float then
            coords, rotation = objects_getObject(model, vector3(x, y, z), heading)
        end

        objSettings.coords = coords
        objSettings.rotation = rotation

        objects_objectCount = objects_objectCount + 1
        TriggerServerEvent("fdg_sync:Add", "objectspawner", GetPlayerServerId(PlayerId()) .. "_obj_" .. objects_objectCount, objSettings)
        return GetPlayerServerId(PlayerId()) .. "_obj_" .. objects_objectCount
    end
end

Base.Objects.Delete = function(key)
    if (objects_spawnedObjects[key] and type(objects_spawnedObjects[key]) ~= "boolean") then
        TriggerServerEvent("fdg_sync:Remove", "objectspawner", key)
    end
end

Base.Objects.GetWithModel = function(model, maxDistance)
    local coords = GetEntityCoords(PlayerPedId())
    local hash = (type(model) == "string" and GetHashKey(model) or model)
    local data = {}

    for _, obj in pairs(objects_spawnedObjects) do
        if (GetEntityModel(obj) == hash) then
            local dist = #(GetEntityCoords(obj) - coords)
            if (dist <= maxDistance) then
                table.insert(data, obj)
            end
        end
    end

    return data
end

RegisterNetEvent("fdg_objspawn:SpawnObj", function(model, x, y, z, heading, job, freeze, attached, noCollide, weapon, data)
    Base.Objects.Create(model, x, y, z, heading, job, freeze, attached, noCollide, weapon, data)
end)

RegisterNetEvent("fdg_objspawn:DeleteObj", function(key)
    Base.Objects.Delete(key)
end)

--[[OBJECT REMOVAL THREAD]]

Citizen.CreateThread(function()
    while true do
        Wait(500)
        local coords = GetEntityCoords(GetPlayerPed(-1))
        local closestDist = 0.0
        local closestObject = nil

        local orgs = GetPlayerOrgs()

        for org in pairs(orgs) do
            if objects_jobObjects[org] then
                for key, object in pairs(objects_jobObjects[org]) do
                    local dist = GetDistanceBetweenCoords(coords, GetEntityCoords(object), true)

                    if dist < closestDist or closestDist == 0.0 then
                        closestDist = dist
                        closestObject = { key = key, org = org }
                    end
                end
            end
        end

        -- Player Spawned Objects
        if objects_jobObjects and objects_jobObjects["internal"] then
            for key, object in pairs(objects_jobObjects["internal"]) do
                local dist = GetDistanceBetweenCoords(coords, GetEntityCoords(object), true)

                if dist < closestDist or closestDist == 0.0 then
                    closestDist = dist
                    closestObject = { key = key, org = "internal" }
                end
            end
        end


        if closestDist < 2.0 and closestObject then
            objects_closestObject = closestObject
        else
            objects_closestObject = nil
        end
    end
end)

Citizen.CreateThread(function()
    while true do
        Wait(0)
        if objects_closestObject then
            local org = objects_closestObject.org
            local key = objects_closestObject.key
            if objects_jobObjects[org] then
                local coords = GetEntityCoords(objects_jobObjects[org][key])
                DrawText3Ds(coords.x, coords.y, coords.z, "[~g~G~s~]  Pick up object")

                if IsControlJustPressed(1, 47) then
                    TriggerServerEvent("fdg_sync:Remove", "objectspawner", key)
                end
            end
        end
    end
end)

-- Need a better solution for removing breakable objects
AddEventHandler("entityDamaged", function(entity, attacker)
    if not DoesEntityExist(entity) then return end
    local entityModel = GetEntityModel(entity)
    if entityModel ~= GetHashKey("prop_barrier_work05") then return end
    if not IsEntityAVehicle(attacker) then return end

    local srcId = GetPlayerServerId(PlayerId())
    local driver = GetPedInVehicleSeat(attacker, -1)
    if srcId ~= GetPlayerServerId(NetworkGetEntityOwner(driver)) then return end

    SetTimeout(180000, function() -- 3 minutes
        if not DoesEntityExist(entity) then return end

        for key, obj in pairs(objects_spawnedObjects) do
            if obj == entity then
                TriggerServerEvent("fdg_sync:Remove", "objectspawner", key)
                break
            end
        end
    end)
end)
