local function alarmMetalDetector(coords)
    print(coords, #(coords - GetEntityCoords(PlayerPedId())))
    if #(coords - GetEntityCoords(PlayerPedId())) > 10.0 then return end

    local endTime = GetGameTimer() + 3000

    -- Sound Effect
    CreateThread(function()
        PlaySoundFromCoord(-1, "Keycard_Fail", coords, "DLC_HEISTS_BIOLAB_FINALE_SOUNDS", false, false)
        Wait(700)
        PlaySoundFromCoord(-1, "Keycard_Fail", coords, "DLC_HEISTS_BIOLAB_FINALE_SOUNDS", false, false)
        Wait(700)
        PlaySoundFromCoord(-1, "Keycard_Fail", coords, "DLC_HEISTS_BIOLAB_FINALE_SOUNDS", false, false)
    end)

    -- Spotlight
    CreateThread(function()
        while GetGameTimer() < endTime do
            Wait(0)
            DrawSpotLight(coords.x, coords.y, coords.z + 1.15, 0.0, 0.0, -1.0, 255, 0, 0, 2.2, 100.0, 1000.0, 15.0, 0.4)
        end
    end)
end

local function successMetalDetector(coords)
    print(coords, #(coords - GetEntityCoords(PlayerPedId())))
    if #(coords - GetEntityCoords(PlayerPedId())) > 10.0 then return end

    local endTime = GetGameTimer() + 3000

    -- Sound Effect
    CreateThread(function()
        PlaySoundFromCoord(-1, "Hack_Success", coords, "DLC_GR_Steal_Railguns_Sounds", false, false)
        Wait(700)
    end)

    -- Spotlight
    CreateThread(function()
        while GetGameTimer() < endTime do
            Wait(0)
            DrawSpotLight(coords.x, coords.y, coords.z + 1.15, 0.0, 0.0, -1.0, 0, 255, 0, 2.2, 100.0, 1000.0, 15.0, 0.4)
        end
    end)
end

local function doesPlayerHaveWeapons()
    local pData = Base.GetPlayerData()
    for _, item in ipairs(pData.inventory) do
        if item.type == "weapon" then
            print(item.name)
            return true
        end
    end
    return false
end

-- Thread to get nearby metal detectors
local nearbyDetectors = {}

CreateThread(function()
    while true do
        Wait(1000)

        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)

        local _nearbyDetectors = {}

        for obj in EnumerateObjects() do
            local model = GetEntityModel(obj)

            if model == GetHashKey('ch_prop_ch_metal_detector_01a') then
                local objCoords = GetEntityCoords(obj)
                local distance = #(playerCoords - objCoords)

                if distance < 10.0 then
                    _nearbyDetectors[obj] = true
                end
            end
        end

        nearbyDetectors = _nearbyDetectors
    end
end)

-- Thread to check if player is near a metal detector
CreateThread(function()
    while true do
        Wait(0)

        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)

        for obj in pairs(nearbyDetectors) do
            local objCoords = GetEntityCoords(obj) + vector3(0.0, 0.0, 1.0)
            local distance = #(playerCoords - objCoords)

            if distance < 0.5 then
                TriggerServerEvent("world:alarmMetalDetector", objCoords, doesPlayerHaveWeapons())
                Wait(3000)
            end
        end
    end
end)

RegisterNetEvent("world:alarmMetalDetector", function(objCoords, hasWeapons)
    if hasWeapons then
        alarmMetalDetector(objCoords)
    else
        successMetalDetector(objCoords)
    end
end)
