local menuOpen = false
menuOptions = {}

-- Register a menu action from outside the menu
function registerMenuAction(name, cb, options)
    if not options then options = {} end
	menuOptions[name] = {
        trigger = cb,
		requires = options.requires or {},
        maxEntityDist = options.maxEntityDist or 2.5
	}
end

RegisterNUICallback("toggleRadialMenu", function(state, cb)
    if state then
        showRadialMenu()
    else
        closeRadialMenu()
    end

    cb("OK")
end)

function setRadialMenuItems(elements)
    sendQUIEvent("setRadialMenuItems", elements)
end

function openRadialMenu()
    menuOpen = true
    sendQUIEvent("toggleRadialMenu", true)
    toggleQUIState(true, true, true)
    -- exports["core_inventory"]:toggleKeys(false)
    -- exports["core_inventory"]:toggleHotbar(true)
    SetCursorLocation(0.5, 0.5)
end

function closeRadialMenu()
    menuOpen = false
    sendQUIEvent("toggleRadialMenu", false)
    toggleQUIState(false, false, false)
    -- exports["core_inventory"]:toggleKeys(true)
    -- exports["core_inventory"]:toggleHotbar(true)
end

RegisterNUICallback("triggerRadialAction", function(data, cb)
    cb("OK")
    local action = data.action
    local target = data.target
    local menuData = data.data

    local menuOption = menuOptions[action]
    if (not menuOption) then
        print("[UI] [MA-"..action.."] Menu option did not exist")
        return
    end

    local playerPed = PlayerPedId()
    local coords = GetEntityCoords(playerPed)

    if menuOption.requires.player then
        if not target or not DoesEntityExist(target) then
            print("[UI] [MA-"..action.."] Option required player but none specified")
            return
        end

        if GetEntityType(target) ~= 1 then
            print("[UI] [MA-"..action.."] Option required player but entity was not a ped")
            return
        end

        if #(coords - GetEntityCoords(target)) > menuOption.maxEntityDist then
            print("[UI] [MA-"..action.."] Target was too far away")
            return
        end
    end

    menuOption.trigger(menuData or {}, target or false)
end)

Citizen.CreateThread(function()
    while true do
        Wait(1)

        if menuOpen then
            DisableControlAction(1, 1, true)
            DisableControlAction(1, 2, true)
            DisableControlAction(1, 24, true)
            DisableControlAction(1, 25, true)
            DisableControlAction(1, 257, true)
        end
    end
end)