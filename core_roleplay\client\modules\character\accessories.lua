local accessories_CLOTHING_TOGGLES = {

    {
        label     = "Hat",
        command   = "hat",
        draw      = "helmet",
        animation = {
            on = {
                dict = "missfbi4",
                anim = "takeoff_mask",
                time = 1000
            },
            off = {
                dict = "missfbi4",
                anim = "takeoff_mask",
                time = 1000
            }
        }
    },

    {
        label     = "Mask",
        command   = "mask",
        draw      = "mask",
        animation = {
            on = {
                dict = "mp_masks@on_foot",
                anim = "put_on_mask",
                time = 400
            },
            off = {
                dict = "missfbi4",
                anim = "takeoff_mask",
                time = 850
            }
        }
    },

    {
        label     = "Glasses",
        command   = "glasses",
        draw      = "glasses",
        animation = {
            on = {
                dict = "clothingspecs",
                anim = "try_glasses_positive_a",
                time = 1000,
                stop = true
            },
            off = {
                dict = "clothingspecs",
                anim = "try_glasses_positive_a",
                time = 1000,
                stop = true
            }
        }
    },

    {
        label     = "Chain",
        command   = "chain",
        draw      = "chain",
        animation = {

        }
    },

    {
        label     = "Vest",
        command   = "vest",
        draw      = "bproof",
        animation = {
            on = {
                dict = "clothingtie",
                anim = "try_tie_negative_a",
                time = 1200,
                stop = true
            },
            off = {
                dict = "clothingtie",
                anim = "try_tie_negative_a",
                time = 1200,
                stop = true
            }
        }
    },

    {
        label     = "Shirt",
        command   = "shirt",
        draw      = "shirt",
        animation = {
            on = {
                dict = "missmic4",
                anim = "michael_tux_fidget",
                time = 1500,
                stop = true
            },
            off = {
                dict = "missmic4",
                anim = "michael_tux_fidget",
                time = 1500,
                stop = true
            }
        }
    },

    {
        label     = "Pants",
        command   = "pants",
        draw      = "pants",
        animation = {

        }
    },

    {
        label     = "Shoes",
        command   = "shoes",
        draw      = "shoes",
        animation = {

        }
    },

    {
        label     = "Gloves",
        command   = "gloves",
        draw      = "arms",
        animation = {
            on = {
                dict = "nmt_3_rcm-10",
                anim = "cs_nigel_dual-10",
                time = 1200,
                stop = true
            },
            off = {
                dict = "nmt_3_rcm-10",
                anim = "cs_nigel_dual-10",
                time = 1200,
                stop = true
            }
        }
    },

    {
        label     = "Bag",
        command   = "bag",
        draw      = "bag",
        animation = {

        }
    },

    {
        label     = "Decals",
        command   = "decals",
        draw      = "decals",
        animation = {

        }
    },

}

local accessories_doesPlayerHaveItemToToggle = function(item)
    local skin = Base.Skin.getCurrentSkin()
    if (skin) then
        if (item == "helmet") then
            if (skin.clothing.helmet.model == -1) then
                return false
            end
        end
        if (item == "glasses") then
            if ((skin.sex == 0 and skin.clothing.glasses.model == 0) or (skin.sex == 1 and skin.clothing.glasses.model == 5)) then
                return false
            end
        end
        if (item == "bproof") then
            if (skin.clothing.bproof.model == 0) then
                return false
            end
        end
        if (item == "arms") then
            if (skin.clothing.arms.model < 16) then
                return false
            end
        end
    end
    return true
end

local accessories_toggle = function(data, hidden, blockAnimation)
    if LocalPlayer.state.bagged then return end

    if LocalPlayer.state.hasCayoDuffel then return end

    if (accessories_doesPlayerHaveItemToToggle(data.draw)) then
        local state = (Base.Skin.isItemHidden(data.draw) and true or false)
        if hidden ~= nil then state = not hidden end

        if not blockAnimation then
            local animation = (data.animation and (state and (data.animation.on) or (data.animation.off)) or nil)
            if (animation) and not (IsPedInAnyVehicle(PlayerPedId(), false) and isCuffed) then
                PlayAnimation(PlayerPedId(), animation.dict, animation.anim, false, false, true, 3.0, 3.0)
                local finish = GetGameTimer() + animation.time
                while (GetGameTimer() < finish) do Wait(0) end
                if (animation.stop) then
                    StopAnimation(PlayerPedId(), animation.dict, animation.anim)
                end
            end
        end
        Base.Skin.toggleItem(data.draw)
        SetResourceKvp("accs_" .. tostring(PlayerData.charid) .. "_" .. tostring(data.draw), state == true and "0" or "1")
        if (data.draw == "bproof") then
            Base.Skin.toggleItem("decals")
        end
    end
end

function accessories_menu()
    Base.UI.Menu.Open("default", GetCurrentResourceName(), "roleplay_acc",
        {
            title    = "Accessory Menu",
            align    = 'bottom-right',
            elements = accessories_CLOTHING_TOGGLES
        },

        function(data, menu)
            if (data.current) then
                accessories_toggle(data.current)
            end
        end,

        function(data, menu)
            menu.close()
            if not isCuffed and not isDead then
                exports["main_roleplay"]:RolePlayMenu()
            end
        end
    )
end

exports("accessories_menu", accessories_menu)

function accessories_toggle_external(data)
    for k, v in pairs(accessories_CLOTHING_TOGGLES) do
        if v.command == data.value then
            accessories_toggle(v)
            break
        end
    end
end

exports("accessories_toggle_external", accessories_toggle_external)

onBaseReady(function()
    for _, toggle in pairs(accessories_CLOTHING_TOGGLES) do
        RegisterCommand(toggle.command, function()
            accessories_toggle(toggle)
        end)

        -- Create QUI menu actions
        QUI.registerMenuAction("clothing_" .. toggle.command, function()
            accessories_toggle(toggle)
        end, {})
    end
end)

RegisterNetEvent("base:characterLoaded", function(data)
    Wait(500)
    local charid = data.charid

    for k, v in pairs(accessories_CLOTHING_TOGGLES) do
        local hidden = GetResourceKvpString("accs_" .. tostring(charid) .. "_" .. tostring(v.draw))

        if hidden == "1" then
            accessories_toggle(v, true, true)
        end
    end
end)
