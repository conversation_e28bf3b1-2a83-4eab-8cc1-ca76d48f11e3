lastSuburb = ""

function getPlayerDirection(ped)
  return config.directions[math.floor((GetEntityHeading(PlayerPedId()) + 22.5) / 45.0)] .. " Bound"
end

function getPlayerLocation(ped)
  local pos = GetEntityCoords(PlayerPedId())
  local str1, str2 = GetStreetNameAtCoord(pos.x, pos.y, pos.z)
  return GetStreetNameFromHashKey(str1) .. ", " .. lastSuburb
end

function getCoordsLocation(pos)
  local a = config.zones[GetNameOfZone(pos.x, pos.y, pos.z)]
  local str1, str2 = GetStreetNameAtCoord(pos.x, pos.y, pos.z)
  if (a) then return GetStreetNameFromHashKey(str1) .. ", " .. a end
  return GetStreetNameFromHashKey(str1)
end

function getPlayerZone(pos)
  local a = config.zones[GetNameOfZone(pos.x, pos.y, pos.z)]
  return a
end

function getVehicleFuel(veh)
  if (Entity(veh).state.fuelAmount) then
      return Entity(veh).state.fuelAmount
  else
      return 0.0
  end
end