Base.Camera = {}

Base.Camera._cache = {}
Base.Camera._registered = {}
Base.Camera._registered_coords = {}

local CAMERA_TYPES = {
    [1] = "DEFAULT_SCRIPTED_CAMERA",
    [2] = "DEFAULT_ANIMATED_CAMERA",
    [3] = "DEFAULT_SPLINE_CAMERA",
    [4] = "DEFAULT_SCRIPTED_FLY_CAMERA",
    [5] = "TIMED_SPLINE_CAMERA",
}

Base.Camera.create = function(id, camType, coords, rotation, fov)
    if (CAMERA_TYPES[camType] == nil) then
        print("INVALID CAMERA TYPE : " .. camType)
        return
    end

    if (Base.Camera.registered(id)) then
        Base.Camera.destroy(id)
    end

    local camId = CreateCam(CAMERA_TYPES[camType], true)
    if (coords) then
        SetCamCoord(camId, coords)
        Base.Camera._registered_coords[id] = coords
    end

    if (rotation) then
        SetCamRot(camId, rotation, 2)
    end

    if (fov) then
        SetCamFov(camId, fov)
    end

    Base.Camera._registered[id] = camId
    -- table.insert(Base.Camera._cache, camId)

    return camId
end

Base.Camera.render = function(id, toggle, ease)
    if (Base.Camera._registered[id] == nil) then
        print("INVALID CAMERA ID : " .. id .. " : Try Registering a Camera with Base.Camera.create ")
        return
    end

    for _, camId in pairs(Base.Camera._registered) do
        SetCamActive(camId, false)
    end

    if (toggle) then
        SetCamActive(Base.Camera._registered[id], true)
        RenderScriptCams(true, ease ~= 0, ease, true, true)
        if (Base.Camera._registered_coords[id]) then
            local coords = Base.Camera._registered_coords[id]
            SetFocusPosAndVel(coords.x, coords.y, coords.z, 0.0, 0.0, 0.0)
        end
    else
        RenderScriptCams(false, ease ~= 0, ease, true, true)
        ClearFocus()
    end
end

Base.Camera.destroy = function(id, ease)
    if (Base.Camera._registered[id]) then
        if (GetRenderingCam() == Base.Camera._registered[id]) then
            RenderScriptCams(false, ease ~= 0, ease, true, true)
            ClearFocus()
        end
        SetCamActive(Base.Camera._registered[id], false)
        DestroyCam(Base.Camera._registered[id], true)
        Base.Camera._registered[id] = nil
        Base.Camera._registered_coords[id] = nil
    end
end

Base.Camera.destroyAll = function()
    RenderScriptCams(false, false, 0, true, true)
    for _, camId in pairs(Base.Camera._registered) do
        SetCamActive(camId, false)
        DestroyCam(camId, true)
    end
    Base.Camera._registered = {}
    Base.Camera._registered_coords = {}
    ClearFocus()
end

Base.Camera.exists = function(id)
    if (Base.Camera._registered[id] == nil) then
        return false
    end
    return DoesCamExist(Base.Camera._registered[id])
end

Base.Camera.rendering = function(id)
    if (Base.Camera._registered[id] == nil) then
        return false
    end
    return Base.Camera._registered[id] == GetRenderingCam()
end

Base.Camera.registered = function(id)
    return Base.Camera._registered[id] ~= nil
end

Base.Camera.getCamId = function(id)
    if (Base.Camera._registered[id] == nil) then
        return false
    end
    return Base.Camera._registered[id]
end

Base.Camera.update = function(id, coords, rotation, fov)
    if (Base.Camera._registered[id] == nil) then
        print("INVALID CAMERA ID : " .. id .. " : Try Registering a Camera with Base.Camera.create ")
        return
    end

    local camId = Base.Camera._registered[id]
    if (coords) then
        SetCamCoord(camId, coords)
        Base.Camera._registered_coords[id] = coords
        if (GetRenderingCam() == Base.Camera._registered[id]) then
            SetFocusPosAndVel(coords.x, coords.y, coords.z, 0.0, 0.0, 0.0)
        end
    end

    if (rotation) then
        SetCamRot(camId, rotation, 2)
    end

    if (fov) then
        SetCamFov(camId, fov)
    end
end

Base.Camera.attachToEntity = function(id, entity, offset, isRelative)
    if (Base.Camera._registered[id] == nil) then
        print("INVALID CAMERA ID : " .. id .. " : Try Registering a Camera with Base.Camera.create ")
        return
    end
    AttachCamToEntity(Base.Camera._registered[id], entity, offset, isRelative)
end

Base.Camera._isCameraRegisteredWithBase = function(camId)
    for _, _camId in pairs(Base.Camera._registered) do
        if (camId == _camId) then
            return true
        end
    end
    return false
end
