local ELS_VEHICLES = {
    [-833546801] = true,
    [-212730111] = true,
    [889581168] = true,
    [-405002220] = true,
    [-808881333] = true,
    [906998194] = true,
    [GetHashKey("afp530d21")] = true,
    [-633779554] = true,
}

local ELSGD_VEHICLES = {
    [-96854932] = true,
    [-1490528194] = true,
    [-1441979133] = true,
    [-2012918729] = true,



}

local ELSKLUGER_VEHICLES = {
    [GetHashKey("pdkluger")] = true,
}

local function IsVehicleELS(veh)
    return ELS_VEHICLES[GetEntityModel(veh)] or false
end

local function IsVehicleELSGD(veh)
    return ELSGD_VEHICLES[GetEntityModel(veh)] or false
end

local function IsVehicleELSKLUGER(veh)
    return ELSKLUGER_VEHICLES[GetEntityModel(veh)] or false
end

local flashPattern = 1
local dashLights = true
local messageBoard = 0

local function SetVehicleELSExtras(vehicle)

    if IsVehicleELS(vehicle) then
        SetVehicleAutoRepairDisabled(vehicle, true)

        if flashPattern == 1 then
            SetVehicleExtra(vehicle, 2, 0)
            SetVehicleExtra(vehicle, 3, 1)
            SetVehicleExtra(vehicle, 4, 1)
            SetVehicleExtra(vehicle, 9, 0)
            SetVehicleExtra(vehicle, 10, 1)
            SetVehicleExtra(vehicle, 12, 0)


            if dashLights then
                SetVehicleExtra(vehicle, 12, 0)
            else
                SetVehicleExtra(vehicle, 12, 1)
            end

        elseif flashPattern == 2 then
            SetVehicleExtra(vehicle, 2, 1)
            SetVehicleExtra(vehicle, 3, 0)
            SetVehicleExtra(vehicle, 4, 1)
            SetVehicleExtra(vehicle, 9, 1)
            SetVehicleExtra(vehicle, 10, 0)

            if dashLights then
                SetVehicleExtra(vehicle, 12, 0)
            else
                SetVehicleExtra(vehicle, 12, 1)
            end

        elseif flashPattern == 3 then
            SetVehicleExtra(vehicle, 2, 1)
            SetVehicleExtra(vehicle, 3, 1)
            SetVehicleExtra(vehicle, 4, 0)
            SetVehicleExtra(vehicle, 10, 1)
            SetVehicleExtra(vehicle, 9, 1)
            SetVehicleExtra(vehicle, 12, 1)

        end
    
    elseif IsVehicleELSGD(vehicle) then
        SetVehicleAutoRepairDisabled(vehicle, true)
    
        if flashPattern == 1 then
            SetVehicleExtra(vehicle, 2, 0)
            SetVehicleExtra(vehicle, 3, 1)
            SetVehicleExtra(vehicle, 4, 1)
            SetVehicleExtra(vehicle, 11, 0)



    
            if dashLights then
                SetVehicleExtra(vehicle, 12, 0)
            else
                SetVehicleExtra(vehicle, 12, 1)
            end
    
        elseif flashPattern == 2 then
            SetVehicleExtra(vehicle, 2, 1)
            SetVehicleExtra(vehicle, 3, 0)
            SetVehicleExtra(vehicle, 4, 1)
            SetVehicleExtra(vehicle, 11, 0)

    
            if dashLights then
                SetVehicleExtra(vehicle, 12, 0)
            else
                SetVehicleExtra(vehicle, 12, 1)
            end
    
        elseif flashPattern == 3 then
            SetVehicleExtra(vehicle, 2, 1)
            SetVehicleExtra(vehicle, 3, 1)
            SetVehicleExtra(vehicle, 4, 0)
            SetVehicleExtra(vehicle, 11, 1)
            SetVehicleExtra(vehicle, 12, 1)


    
        end
		
		elseif IsVehicleELSKLUGER(vehicle) then
        SetVehicleAutoRepairDisabled(vehicle, true)
    
        if flashPattern == 1 then
            SetVehicleExtra(vehicle, 2, 0)
			SetVehicleExtra(vehicle, 3, 0)
            SetVehicleExtra(vehicle, 4, 1)
            SetVehicleExtra(vehicle, 10, 0)
			SetVehicleExtra(vehicle, 11, 1)
            SetVehicleExtra(vehicle, 12, 0)
            
    
        elseif flashPattern == 2 then
            SetVehicleExtra(vehicle, 2, 0)
            SetVehicleExtra(vehicle, 4, 1)
            SetVehicleExtra(vehicle, 11, 0)
            
    
        elseif flashPattern == 3 then
            SetVehicleExtra(vehicle, 2, 1)
            SetVehicleExtra(vehicle, 3, 0)
            SetVehicleExtra(vehicle, 4, 0)
            SetVehicleExtra(vehicle, 10, 1)
            SetVehicleExtra(vehicle, 11, 1)
            SetVehicleExtra(vehicle, 12, 0)


    
        end

    end
end

local ELS_MENU_CONFIG = {
    text = "QLS",
    icon = "&#xF0BEA;",
    subMenu = {
        { text = "Switch Lightbar", icon = "&#xF0BEA;", action = "flash_primary" },
        { text = "Toggle Dash Lights", icon = "&#xF0BEA;", action = "dash_toggle" },
        { text = "Message Board", icon = "&#xF116F;", subMenu =  {
            { text = "On", icon = "&#xF0BEA;", action = "qls_mb_on" },
            { text = "Random Breath Testing", icon = "&#xF0BEA;", action = "qls_mb_rbt" },
            { text = "Do Not Pass", icon = "&#xF0BEA;", action = "qls_mb_donotpass" },
            { text = "Road Spikes Ahead", icon = "&#xF0BEA;", action = "qls_mb_rsa" },
            { text = "Accident Ahead", icon = "&#xF0BEA;", action = "qls_mb_accident" },
            { text = "Road Closed", icon = "&#xF0BEA;", action = "qls_mb_roadclosed" },
            { text = "Colours", icon = "&#xF0BEA;", subMenu = {
                { text = "Amber", icon = "&#xF0BEA;", action = "qls_mb_color_amber" },
                { text = "Red", icon = "&#xF0BEA;", action = "qls_mb_color_red" },
                { text = "Blue", icon = "&#xF0BEA;", action = "qls_mb_color_blue" },
                { text = "Green", icon = "&#xF0BEA;", action = "qls_mb_color_green" },
            }},
            { text = "Off", icon = "&#xF0BEA;", action = "qls_mb_off" },
        }},
    }
}

QUI.onReady(function()
    QUI.registerMenuAction("flash_primary", function()
        if flashPattern == 1 then
            flashPattern = 3
        else
            flashPattern = 1
        end 
        SetVehicleELSExtras(GetVehiclePedIsIn(PlayerPedId(), false))
    end)

    QUI.registerMenuAction("flash_alt", function()
        flashPattern = 2
        SetVehicleELSExtras(GetVehiclePedIsIn(PlayerPedId(), false))
    end)

    QUI.registerMenuAction("flash_sec", function()
        flashPattern = 3
        SetVehicleELSExtras(GetVehiclePedIsIn(PlayerPedId(), false))
    end)

    QUI.registerMenuAction("dash_toggle", function()
        dashLights = not dashLights
        SetVehicleELSExtras(GetVehiclePedIsIn(PlayerPedId(), false))
    end)

    QUI.registerMenuAction("message_clear", function()
        messageBoard = 0
        SetVehicleELSExtras(GetVehiclePedIsIn(PlayerPedId(), false))
    end)

    QUI.registerMenuAction("message_stop", function()
        messageBoard = 1
        SetVehicleELSExtras(GetVehiclePedIsIn(PlayerPedId(), false))
    end)

    QUI.registerMenuAction("message_roadblock", function()
        messageBoard = 2
        SetVehicleELSExtras(GetVehiclePedIsIn(PlayerPedId(), false))
    end)

    local qls = exports["qls"]
    QUI.registerMenuAction("qls_mb_on", function()
        qls:setMessageBoardState(1)
    end)
    QUI.registerMenuAction("qls_mb_off", function()
        qls:setMessageBoardState(0)
    end)
    QUI.registerMenuAction("qls_mb_rbt", function()
        qls:setMessageBoardText("RANDOM BREATH TESTING")
    end)
    QUI.registerMenuAction("qls_mb_donotpass", function()
        qls:setMessageBoardText("DO NOT PASS")
    end)

    QUI.registerMenuAction("qls_mb_slow", function()
        qls:setMessageBoardText("SLOW DOWN")
    end)
    QUI.registerMenuAction("qls_mb_accident", function()
        qls:setMessageBoardText("ACCIDENT AHEAD")
    end)
    QUI.registerMenuAction("qls_mb_rsa", function()
        qls:setMessageBoardText("ROAD SPIKES AHEAD")
    end)
    QUI.registerMenuAction("qls_mb_roadclosed", function()
        qls:setMessageBoardText("ROAD CLOSED")
    end)
    
    QUI.registerMenuAction("qls_mb_color_amber", function()
        qls:setMessageBoardColor("rgba(255, 123, 0, 0.533)")
    end)
    QUI.registerMenuAction("qls_mb_color_red", function()
        qls:setMessageBoardColor("rgba(238, 13, 13, 0.533)")
    end)
    QUI.registerMenuAction("qls_mb_color_blue", function()
        qls:setMessageBoardColor("rgb(6, 114, 255)")
    end)
    QUI.registerMenuAction("qls_mb_color_green", function()
        qls:setMessageBoardColor("rgba(13, 238, 13, 0.533)")
    end)
end)

AddVehicleInteractionAction(
    function() -- Condition
        local veh = GetVehiclePedIsIn(PlayerPedId(), false)
	    return (IsPedInAnyVehicle(PlayerPedId(), false) and IsVehicleELS(veh) or IsVehicleELSKLUGER(veh) or IsVehicleELSGD(veh))
    end,

    function()
    end,

    ELS_MENU_CONFIG,  -- Menu Item

    {requires = {Vehicle = true}}  -- Requires
)


-- Universal QLS - Highway Patrol

-- Extra 1: Marked Setup
-- Extra 2: Pattern 1
-- Extra 3: Pattern 2
-- Extra 4: Secondary Lights
-- Extra 5: Board Option 
-- Extra 6: Board Option 2
-- Extra 7: Board Option 3
-- Extra 8: Board Option 4

-- Grill LEDS
-- Extra 9: Daytime setup
-- Extra 10: Night-Time setup

-- Extra 11 : Unmarked Setup
-- Extra 12: Dash LEDs 

-- Flash Patterns
-- GD (1) - 2+, 3-, 7+, 9~
-- Alternate (Night) (2) - 2-, 3+, 7+, 9~
-- SEC (3) - 2-, 3-, 4+, 7-, 9-

-- GD Setup Configuration

--Extra 1 - Marked setup (lightbar)
--Extra 2 - Daytime flash patern
--Extra 3 - Nighttime flash patern
--Extra 4 - Secondaries
--Extra 5 - Message Board
--Extra 6 - Message Board
--Extra 7 - Message Board
--Extra 8 - Message Board
--Extra 9 - Message Board
--Extra 10 - Unmarked setup
--Extra 11 - Grill lights
--Extra 12 - Dash Light
