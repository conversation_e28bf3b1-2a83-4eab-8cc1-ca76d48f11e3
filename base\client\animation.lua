--[====[
    @filename Animations (Client)
    @filebrief Centralized animation library
--]====]

--[====[
    @type func
    @name AddClipset(identifier, clipset, priority)
    @param identifier: string - An identifier used to refer to a clipset
    @param clipset: string - The name of the clipset to apply on the player
    @param priority: number - The priority of the clipset
    @brief Sets a clipset to apply to the player with a priority system
    @description [
        Clipset logic handles multiple clipsets with differing priorities. A higher priority means a clipset will overrule another.

        An infinite amount of clipsets can be added to the list, and the script will use the priority of each one to determine which one should be active.
    ]
--]====]
AddClipset = function(identifier, clipset, priority)
    exports["base"]:AddClipset(identifier, clipset, priority, GetCurrentResourceName())
end

--[====[
    @type func
    @name RemoveClipset(identifier)
    @param name: string - The identifier of the clipset
    @brief Removes a clipset from the active clipset queue
    @description [
        By removing a clipset using identifiers, the other clipsets remain unaffected.

        This simply removed a single clipset from the queue, forcing the script to reselect the active clipset.
    ]
--]====]
RemoveClipset = function(identifier)
    exports["base"]:RemoveClipset(identifier)
end

--[====[
    @type func
    @name PlayAnimation(ped, dict, anim, looping, freeze, upperBodyOnly, phaseInTime, phaseOutTime, customFlag)
    @param ped: Ped - target ped
    @param dict: string - dictionary name
    @param anim: string - animation name
    @param looping: bool - whether the animation should looping
    @param freeze: bool - whether the animation should freeze the ped
    @param upperBodyOnly: bool - whether the animation should only affect the upper body
    @param phaseInTime: float - (optional) fade in time for animation
    @param phaseOutTime: float - (optional) fade out time for animation
    @param customFlag: int - (optional) using this ignores looping, freeze adn upperBodyOnly
    @brief plays animation on target ped
    @description [
        Simplified function for calling gta 5 animations with some advanced functionality built in.
    ]
--]====]
PlayAnimation = function(ped, dict, anim, looping, freeze, upperBodyOnly, phaseInTime, phaseOutTime, customFlag)
    exports["base"]:PlayAnimation(ped, dict, anim, looping, freeze, upperBodyOnly, phaseInTime, phaseOutTime, customFlag)
end

--[====[
    @type func
    @name StopAnimation(ped, dict, anim)
    @param ped: Ped - target ped
    @param dict: string - (optional) dictionary name
    @param anim: string - (optional) animation name
    @brief stops animation on target ped
    @description [
        Simplified function for stopping animations.
    ]
--]====]
StopAnimation = function(ped, dict, anim)
    exports["base"]:StopAnimation(ped, dict, anim)
end