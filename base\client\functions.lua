RegisterClientCallback("base_client:monitorForEvent", function(cb, tgtEntity, condition, event, options)
    local event = MonitorForEvent(tgtEntity, condition, event, options, cb)
    cb(event)
end)

function MonitorForEvent(tgtEntity, condition, event, options, cb)
    if tgtEntity == nil then return end
    if condition then
        local loopWait = options and options.waitDuration or 0
        if options and options.timeoutCount then
            Citizen.CreateThread(function()
                for count = 0, options.timeoutCount, 1 do
                    if condition and condition(tgtEntity) then
                        if event then
                            return event(tgtEntity)
                        end
                    end
                    Wait(loopWait)
                end
            end)
        end
        if options and options.timeoutMs then
            local msCount = 0
            Citizen.CreateThread(function()
                while true do
                    if msCount >= options.timeoutMs * 1000 then
                        return
                    else
                        if condition and condition(tgtEntity) then
                            return event(tgtEntity)
                        end

                        msCount = msCount + 1
                    end
                    Wait(1)
                end
            end)
        end
        if options and options.goUntilEvent then
            Citizen.CreateThread(function()
                while true do
                    if condition and condition(tgtEntity) then
                        if event then
                            local eventOutcome = event(tgtEntity)
                            return eventOutcome
                        end
                    end
                    Wait(loopWait)
                end
            end)
        end
    end
end