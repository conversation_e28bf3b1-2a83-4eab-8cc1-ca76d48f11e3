Base.Worldgrid = {}

local OFFSET = 8192      -- Determines where the grid starts in each direction
local GRID_SIZE = 128    -- Determines the width and height of each chunk
local GRID_ROW_SIZE = math.ceil(OFFSET * 2 / GRID_SIZE)

local deltas = {
	vector2(-1, -1),
	vector2(-1, 0),
	vector2(-1, 1),
	vector2(0, -1),
	vector2(0, 0),
	vector2(0, 1),
	vector2(1, -1),
	vector2(1, 0),
	vector2(1, 1),
}
local bitShift = 2

function GetGridChunk(x)
	return math.floor((x + OFFSET) / GRID_SIZE)
end

function GetGridBase(x)
	return (x * GRID_SIZE) - OFFSET
end

function GetChunkId(v)
	if v.x <= 0 or v.y <= 0 or v.x > OFFSET * 2 or v.y > OFFSET * 2 then
		return 0
	else
		return math.ceil((v.x * GRID_ROW_SIZE) + v.y)
	end
end

function GetChunkOrigin(id)

end

function GetMaxChunkId()
	-- return GRID_SIZE << bitShift
end

Base.Worldgrid.getCurrentChunk = function(pos)
	local chunk = vector2(GetGridChunk(pos.x), GetGridChunk(pos.y))
	local chunkId = GetChunkId(chunk)

	return chunkId
end

-- Gets nearby chunk IDs within a specified range of the player
Base.Worldgrid.getNearbyChunks = function(pos, range)
    local nearbyChunksList = {}
	local nearbyChunks = {}

    for i = 1, #deltas do -- Get nearby chunks
        local chunkSize = pos.xy + (deltas[i] * (range or 20.0)) -- edge size
        local chunk = vector2(GetGridChunk(chunkSize.x), GetGridChunk(chunkSize.y)) -- get nearby chunk
        local chunkId = GetChunkId(chunk) -- Get id for chunk

		if not nearbyChunksList[chunkId] then
			nearbyChunks[#nearbyChunks + 1] = chunkId
			nearbyChunksList[chunkId] = true
		end
    end

    return nearbyChunks
end

local chunkView = false
RegisterCommand("debug_chunks", function(source, args, raw)
	chunkView = not chunkView
	while chunkView do Wait(0)
		local coords = GetEntityCoords(PlayerPedId())
		local xGridChunk = GetGridChunk(coords.x)
		local yGridChunk = GetGridChunk(coords.y)

		local xBase = GetGridBase(xGridChunk)
		local yBase = GetGridBase(yGridChunk)

        -- X Borders
		DrawMarker(43, xBase + 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 90.0, 16000.0, 0.0, 500.0, 255, 0, 0, 30, false, false, 2, false, false, false, false)
        DrawMarker(43, xBase + GRID_SIZE + 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 90.0, 16000.0, 0.0, 500.0, 255, 0, 0, 30, false, false, 2, false, false, false, false)

        -- X Borders
		DrawMarker(43, 0.0, yBase + 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 16000.0, 0.0, 500.0, 0, 255, 0, 30, false, false, 2, false, false, false, false)
        DrawMarker(43, 0.0, yBase + GRID_SIZE + 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 16000.0, 0.0, 500.0, 0, 255, 0, 30, false, false, 2, false, false, false, false)

        -- Draw nearby chunk IDs
		for i = 1, #deltas do -- Get nearby chunks
			local chunk = coords.xy + (deltas[i] * GRID_SIZE) -- edge size
			local xChunk = GetGridChunk(chunk.x)
			local yChunk = GetGridChunk(chunk.y)
			local xCenter = GetGridBase(xChunk) + (GRID_SIZE / 2)
			local yCenter = GetGridBase(yChunk) + (GRID_SIZE / 2)
			local chunkId = GetChunkId(vector2(xChunk, yChunk)) -- Get id for chunk

			-- Draw text on nearby coords
			Base.DrawText(xCenter, yCenter, 100.0, tostring(chunkId), 1.0)
		end
	end
end)