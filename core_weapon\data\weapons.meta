<?xml version="1.0" encoding="UTF-8"?>
<CWeaponInfoBlob>
	<SlotNavigateOrder>
		<Item>
			<WeaponSlots>
				<Item>
					<OrderNumber value="395" />
					<Entry>SLOT_FLASHBANG</Entry>
				</Item>
			</WeaponSlots>
		</Item>
	</SlotNavigateOrder>
	<SlotBestOrder>
		<WeaponSlots>
			<Item>
				<OrderNumber value="395" />
				<Entry>SLOT_FLASHBANG</Entry>
			</Item>
		</WeaponSlots>
	</SlotBestOrder>
	<TintSpecValues />
	<FiringPatternAliases />
	<UpperBodyFixupExpressionData />
	<AimingInfos />
	<Infos>
		<Item>
      <Infos>
        <Item type="CAmmoThrownInfo">
          <Name>AMMO_FLASH</Name>
          <Model>w_ex_flashbang</Model>
          <Audio />
          <Slot />
          <AmmoMax value="25" />
          <AmmoMax50 value="25" />
          <AmmoMax100 value="25" />
          <AmmoMaxMP value="25" />
          <AmmoMax50MP value="25" />
          <AmmoMax100MP value="25" />
          <AmmoFlags />
          <Damage value="0.000000" />
          <LifeTime value="3.000000" />
          <FromVehicleLifeTime value="3.000000" />
          <LifeTimeAfterImpact value="1.000000" />
          <LifeTimeAfterExplosion value="0.000000" />
          <ExplosionTime value="0.000000" />
          <LaunchSpeed value="25.000000" />
          <SeparationTime value="0.000000" />
          <TimeToReachTarget value="0.000000" />
          <Damping value="0.050000" />
          <GravityFactor value="1.000000" />
          <RicochetTolerance value="1.000000" />
          <PedRicochetTolerance value="1.000000" />
          <VehicleRicochetTolerance value="1.000000" />
          <Explosion>
            <Default>DONTCARE</Default>
            <HitCar>DONTCARE</HitCar>
            <HitTruck>DONTCARE</HitTruck>
            <HitBike>DONTCARE</HitBike>
            <HitBoat>DONTCARE</HitBoat>
            <HitPlane>DONTCARE</HitPlane>
          </Explosion>
          <FuseFx />
          <TrailFx />
          <TrailFxUnderWater />
          <TrailFxFadeInTime value="0.000000" />
          <TrailFxFadeOutTime value="0.000000" />
          <PrimedFx />
          <DisturbFxDefault />
          <DisturbFxSand />
          <DisturbFxWater />
          <DisturbFxDirt />
          <DisturbFxFoliage />
          <DisturbFxProbeDist value="0.000000" />
          <DisturbFxScale value="0.000000" />
          <LightOnlyActiveWhenStuck value="false" />
          <LightFlickers value="false" />
          <LightBone />
          <LightColour x="0.000000" y="0.000000" z="0.000000" />
          <LightIntensity value="0.000000" />
          <LightRange value="0.000000" />
          <LightFalloffExp value="0.000000" />
          <LightFrequency value="0.000000" />
          <LightPower value="0.000000" />
          <CoronaSize value="0.000000" />
          <CoronaIntensity value="0.000000" />
          <CoronaZBias value="0.000000" />
          <ProjectileFlags>ProcessImpacts CanBounce DoubleDamping</ProjectileFlags>
          <ThrownForce value="0.000000" />
          <ThrownForceFromVehicle value="5.000000" />
        </Item>
      </Infos>
		</Item>
		<Item>
			<Infos>
				<Item type="CWeaponInfo">
					<Name>WEAPON_FLASHBANG</Name>
					<Model>w_ex_flashbang</Model>
					<Audio />
					<Slot>SLOT_FLASHBANG</Slot>
					<DamageType />
					<Explosion>
						<Default>DONTCARE</Default>
						<HitCar>DONTCARE</HitCar>
						<HitTruck>DONTCARE</HitTruck>
						<HitBike>DONTCARE</HitBike>
						<HitBoat>DONTCARE</HitBoat>
						<HitPlane>DONTCARE</HitPlane>
					</Explosion>
					<FireType>PROJECTILE</FireType>
					<WheelSlot>WHEEL_THROWABLE_SPECIAL</WheelSlot>
					<Group>GROUP_THROWN</Group>
					<AmmoInfo ref="AMMO_FLASH" />
					<AimingInfo ref="PISTOL_2H_BASE_STRAFE" />
					<ClipSize value="1" />
					<AccuracySpread value="1.000000" />
					<AccurateModeAccuracyModifier value="0.500000" />
					<RunAndGunAccuracyModifier value="2.000000" />
					<RunAndGunAccuracyMaxModifier value="1.000000" />
					<RecoilAccuracyMax value="1.000000" />
					<RecoilErrorTime value="0.000000" />
					<RecoilRecoveryRate value="1.000000" />
					<RecoilAccuracyToAllowHeadShotAI value="1000.000000" />
					<MinHeadShotDistanceAI value="1000.000000" />
					<MaxHeadShotDistanceAI value="1000.000000" />
					<HeadShotDamageModifierAI value="1000.000000" />
					<RecoilAccuracyToAllowHeadShotPlayer value="0.175000" />
					<MinHeadShotDistancePlayer value="5.000000" />
					<MaxHeadShotDistancePlayer value="40.000000" />
					<HeadShotDamageModifierPlayer value="18.000000" />
					<Damage value="0.000000" />
					<DamageTime value="0.000000" />
					<DamageTimeInVehicle value="0.000000" />
					<DamageTimeInVehicleHeadShot value="0.000000" />
					<HitLimbsDamageModifier value="0.500000" />
					<NetworkHitLimbsDamageModifier value="0.800000" />
					<LightlyArmouredDamageModifier value="0.750000" />
					<Force value="0.000000" />
					<ForceHitPed value="0.000000" />
					<ForceHitVehicle value="0.000000" />
					<ForceHitFlyingHeli value="0.000000" />
					<OverrideForces />
					<ForceMaxStrengthMult value="1.000000" />
					<ForceFalloffRangeStart value="0.000000" />
					<ForceFalloffRangeEnd value="50.000000" />
					<ForceFalloffMin value="1.000000" />
					<ProjectileForce value="10.000000" />
					<FragImpulse value="1000.000000" />
					<Penetration value="0.000000" />
					<VerticalLaunchAdjustment value="0.500000" />
					<DropForwardVelocity value="4.000000" />
					<Speed value="2000.000000" />
					<BulletsInBatch value="1" />
					<BatchSpread value="0.000000" />
					<ReloadTimeMP value="-1.000000" />
					<ReloadTimeSP value="-1.000000" />
					<VehicleReloadTime value="0.500000" />
					<AnimReloadRate value="1.000000" />
					<BulletsPerAnimLoop value="1" />
					<TimeBetweenShots value="0.000000" />
					<TimeLeftBetweenShotsWhereShouldFireIsCached value="-1.000000" />
					<SpinUpTime value="0.000000" />
					<SpinTime value="0.000000" />
					<SpinDownTime value="0.000000" />
					<AlternateWaitTime value="-1.000000" />
					<BulletBendingNearRadius value="0.000000" />
					<BulletBendingFarRadius value="0.000000" />
					<BulletBendingZoomedRadius value="0.000000" />
					<Fx>
						<EffectGroup>WEAPON_EFFECT_GROUP_GRENADE</EffectGroup>
						<FlashFx />
						<FlashFxAlt />
						<MuzzleSmokeFx />
						<MuzzleSmokeFxMinLevel value="0.000000" />
						<MuzzleSmokeFxIncPerShot value="0.000000" />
						<MuzzleSmokeFxDecPerSec value="0.000000" />
						<ShellFx />
						<TracerFx />
						<PedDamageHash />
						<TracerFxChanceSP value="0.000000" />
						<TracerFxChanceMP value="0.000000" />
						<FlashFxChanceSP value="0.000000" />
						<FlashFxChanceMP value="0.000000" />
						<FlashFxAltChance value="0.000000" />
						<FlashFxScale value="0.000000" />
						<FlashFxLightEnabled value="false" />
						<FlashFxLightCastsShadows value="true" />
						<FlashFxLightOffsetDist value="0.000000" />
						<FlashFxLightRGBAMin x="0.000000" y="0.000000" z="0.000000" />
						<FlashFxLightRGBAMax x="0.000000" y="0.000000" z="0.000000" />
						<FlashFxLightIntensityMinMax x="0.000000" y="0.000000" />
						<FlashFxLightRangeMinMax x="0.000000" y="0.000000" />
						<FlashFxLightFalloffMinMax x="0.000000" y="0.000000" />
						<GroundDisturbFxEnabled value="false" />
						<GroundDisturbFxDist value="5.000000" />
						<GroundDisturbFxNameDefault />
						<GroundDisturbFxNameSand />
						<GroundDisturbFxNameDirt />
						<GroundDisturbFxNameWater />
						<GroundDisturbFxNameFoliage />
					</Fx>
					<InitialRumbleDuration value="0" />
					<InitialRumbleIntensity value="0.000000" />
					<InitialRumbleIntensityTrigger value="0.000000" />
					<RumbleDuration value="50" />
					<RumbleIntensity value="0.100000" />
					<RumbleIntensityTrigger value="0.000000" />
					<RumbleDamageIntensity value="1.000000" />
					<NetworkPlayerDamageModifier value="1.000000" />
					<NetworkPedDamageModifier value="1.000000" />
					<NetworkHeadShotPlayerDamageModifier value="1.000000" />
					<LockOnRange value="24.000000" />
					<WeaponRange value="30.000000" />
					<BulletDirectionOffsetInDegrees value="0.000000" />
					<AiSoundRange value="-1.000000" />
					<AiPotentialBlastEventRange value="-1.000000" />
					<DamageFallOffRangeMin value="0.000000" />
					<DamageFallOffRangeMax value="0.000000" />
					<DamageFallOffModifier value="0.000000" />
					<VehicleWeaponHash />
					<DefaultCameraHash>THROWN_AIM_CAMERA</DefaultCameraHash>
					<CoverCameraHash>THROWN_AIM_IN_COVER_CAMERA</CoverCameraHash>
					<CoverReadyToFireCameraHash />
					<RunAndGunCameraHash>THROWN_RUN_AND_GUN_CAMERA</RunAndGunCameraHash>
					<CinematicShootingCameraHash />
					<AlternativeOrScopedCameraHash />
					<RunAndGunAlternativeOrScopedCameraHash />
					<CinematicShootingAlternativeOrScopedCameraHash />
					<CameraFov value="50.000000" />
					<FirstPersonAsThirdPersonIdleOffset x="-0.025" y="-0.1" z="-0.15" />
					<FirstPersonAsThirdPersonRNGOffset x="0.000000" y="0.000000" z="0.000000" />
					<FirstPersonAsThirdPersonLTOffset x="0.000000" y="0.000000" z="0.000000" />
					<FirstPersonAsThirdPersonScopeOffset x="0.000000" y="0.000000" z="0.000000" />
					<FirstPersonAsThirdPersonWeaponBlockedOffset x="-0.1000000" y="0.000000" z="-0.100000" />
					<ZoomFactorForAccurateMode value="1.000000" />
					<RecoilShakeHash />
					<RecoilShakeHashFirstPerson />
					<AccuracyOffsetShakeHash />
					<MinTimeBetweenRecoilShakes value="150" />
					<RecoilShakeAmplitude value="1.000000" />
					<ExplosionShakeAmplitude value="-1.000000" />
					<ReticuleHudPosition x="0.000000" y="0.000000" />
					<AimOffsetMin x="0.000000" y="0.000000" z="0.000000" />
					<AimProbeLengthMin value="0.000000" />
					<AimOffsetMax x="0.000000" y="0.000000" z="0.000000" />
					<AimProbeLengthMax value="0.000000" />
					<AimOffsetMinFPSIdle x="0.178000" y="0.392000" z="0.135000" />
					<AimOffsetMedFPSIdle x="0.169000" y="0.312000" z="0.420000" />
					<AimOffsetMaxFPSIdle x="0.187000" y="0.064000" z="0.649000" />
					<AimOffsetMinFPSLT x="0.009000" y="0.384000" z="0.555000" />
					<AimOffsetMaxFPSLT x="0.062000" y="-0.164000" z="0.588000" />
					<AimOffsetMinFPSRNG x="0.114000" y="0.390000" z="0.485000" />
					<AimOffsetMaxFPSRNG x="0.113000" y="-0.263000" z="0.586000" />
					<AimOffsetMinFPSScope x="0.009000" y="0.421000" z="0.462000" />
					<AimOffsetMaxFPSScope x="0.037000" y="-0.224000" z="0.639000" />
					<AimOffsetEndPosMinFPSIdle x="0.208000" y="0.700000" z="0.003000" />
					<AimOffsetEndPosMedFPSIdle x="0.203000" y="0.604000" z="0.553000" />
					<AimOffsetEndPosMaxFPSIdle x="0.207000" y="-0.040000" z="0.942000" />
					<AimOffsetEndPosMinFPSLT x="0.144000" y="0.157000" z="0.130000" />
					<AimOffsetEndPosMedFPSLT x="0.143000" y="0.567000" z="0.488000" />
					<AimOffsetEndPosMaxFPSLT x="0.158000" y="-0.112000" z="0.879000" />
					<AimProbeRadiusOverrideFPSIdle value="0.000000" />
					<AimProbeRadiusOverrideFPSIdleStealth value="0.000000" />
					<AimProbeRadiusOverrideFPSLT value="0.200000" />
					<AimProbeRadiusOverrideFPSRNG value="0.000000" />
					<AimProbeRadiusOverrideFPSScope value="0.000000" />
					<TorsoAimOffset x="0.000000" y="0.000000" />
					<TorsoCrouchedAimOffset x="0.000000" y="0.000000" />
					<LeftHandIkOffset x="0.000000" y="0.000000" z="0.000000" />
					<ReticuleMinSizeStanding value="1.000000" />
					<ReticuleMinSizeCrouched value="1.000000" />
					<ReticuleScale value="1.000000" />
					<ReticuleStyleHash>WEAPON_THROWN</ReticuleStyleHash>
					<FirstPersonReticuleStyleHash />
					<PickupHash>PICKUP_WEAPON_SMOKEGRENADE</PickupHash>
					<MPPickupHash>PICKUP_WEAPON_SMOKEGRENADE</MPPickupHash>
					<HumanNameHash>WT_GNADE_FLSH</HumanNameHash>
					<MovementModeConditionalIdle />
					<StatName>SMKGRENADE</StatName>
					<KnockdownCount value="-1" />
					<KillshotImpulseScale value="1.000000" />
					<NmShotTuningSet>Normal</NmShotTuningSet>
					<AttachPoints />
					<GunFeedBone />
					<TargetSequenceGroup />
					<WeaponFlags>CarriedInHand Silenced ArmourPenetrating CanLockonOnFoot CanFreeAim AnimReload AnimCrouchFire Thrown UsableOnFoot UsableInCover AllowCloseQuarterKills HasLowCoverSwaps CookWhileAiming DropWhenCooked DisableLeftHandIkWhenOnFoot UseFPSAimIK UseFPSSecondaryMotion AttachFPSLeftHandIKToRight DisableFPSScope HasFPSProjectileWeaponAnims</WeaponFlags>
					<TintSpecValues ref="TINT_DEFAULT" />
					<FiringPatternAliases ref="NULL" />
					<ReloadUpperBodyFixupExpressionData ref="default" />
					<AmmoDiminishingRate value="3" />
					<AimingBreathingAdditiveWeight value="1.000000" />
					<FiringBreathingAdditiveWeight value="1.000000" />
					<StealthAimingBreathingAdditiveWeight value="0.000000" />
					<StealthFiringBreathingAdditiveWeight value="0.000000" />
					<AimingLeanAdditiveWeight value="1.000000" />
					<FiringLeanAdditiveWeight value="1.000000" />
					<StealthAimingLeanAdditiveWeight value="0.000000" />
					<StealthFiringLeanAdditiveWeight value="0.000000" />
					<ExpandPedCapsuleRadius value="0.000000" />
					<AudioCollisionHash />
					<HudDamage value="0" />
					<HudSpeed value="20" />
					<HudCapacity value="10" />
					<HudAccuracy value="10" />
					<HudRange value="5" />
				</Item>
			</Infos>
		</Item>
		<Item>
			<Infos />
		</Item>
		<Item>
			<Infos />
		</Item>
	</Infos>
	<VehicleWeaponInfos />
	<WeaponGroupDamageForArmouredVehicleGlass />
	<Name>DLC - weapon</Name>
</CWeaponInfoBlob>