-- CONFIG --
secondsUntilKick = 1200
kickWarning = true

-- CODE --
time = secondsUntilKick
Citizen.CreateThread(function()
    while true do
        Wait(1000)
        playerPed = GetPlayerPed(-1)
        if playerPed then
            currentPos = GetEntityCoords(playerPed, true)
            charCreate = vector3(402.74, -996.51, -98.9)
            if currentPos == prevPos and currentPos ~= charCreate and not IsEntityDead(playerPed) and not NetworkIsPlayerTalking(PlayerId()) then
                if time > 0 then
                    if kickWarning and time == math.ceil(secondsUntilKick / 4) then
                        TriggerEvent('chat:addMessage', "WARNING", {255, 0, 0}, "^1You'll be kicked in " .. time .. " seconds for being AFK!")
                    end

                    time = time - 1
                else
                    TriggerServerEvent("kickForBeingAnAFKDouchebag")
                end
            else
                time = secondsUntilKick
            end

            prevPos = currentPos
        end
    end
end)