local function generateUniformElements(uniforms, blockCivilian)
    local elements = {}

    if not blockCivilian then
        table.insert(elements, { label = "<font color=\"green\">Civilian Clothing</font>", value = "civilian" })
    end

    for label in pairs(uniforms) do
        table.insert(elements, { label = label })
    end

    local function orderUniforms(a, b)
        return a.label < b.label
    end

    table.sort(elements, orderUniforms)

    return elements
end

function OpenUniformsMenu(org, animation)
    if LocalPlayer.state.cuffed or LocalPlayer.state.wounded then
        return Base.Notification("You cannot do this while cuffed or wounded.")
    end

    if animation then
        Base.Animation.StartForcedAnim(playerPed, "amb@prop_human_bum_bin@idle_a", "idle_a", true, true, true)
    end

    -- Generate Elements
    local model = Base.Skin.getCurrentSkin().model
    local uniforms = AwaitServerCallback("orgs:getUniforms", org, model, false)
    local elements = generateUniformElements(uniforms)
    local oldSkin = Base.Skin.getCurrentSkin().clothing

    Base.UI.Menu.Open('default', GetCurrentResourceName(), 'clothingmenu',
        {
            title    = 'Uniforms',
            align    = 'bottom-right',
            elements = elements,
        },
        function(data, menu)
            if data.current.value == 'civilian' then
                Base.Skin.applyClothing(Base.Skin.getCharSkin().clothing, false)
            elseif data.current.value == 'uniform' then
                local uniform = uniforms[data.current.label]
                Base.Skin.applyClothing(uniform, false)
            end

            Base.Animation.StopForcedAnim(PlayerPedId())

            menu.close()
        end,
        function(data, menu)
            Base.Animation.StopForcedAnim(PlayerPedId())
            menu.close()
            Base.Skin.applyClothing(oldSkin, false)
        end,
        function(data, menu)
            local uniform = uniforms[data.current.label]
            Base.Skin.applyClothing(uniform, false)
        end
    )
end

-- World marker interface
AddEventHandler("org:markerInteraction", function(type, marker)
    if type ~= "uniforms" then return end
    print("Open Uniform Menu", marker.organisation, marker.identifier)
    OpenUniformsMenu(marker.organisation, true)
end)

-- E menu interface
RegisterInteraction("vehicle", "uniforms", { label = "Job Uniforms", emoji = "👔" },
    function(vehicle)
        if not DoesEntityExist(vehicle) then return end

        local org = Entity(vehicle).state.job
        if org == nil then return end

        OpenUniformsMenu(org, true)
        Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
    end, function(entity)
        local state = Entity(entity).state

        if not state.job then return false end
        -- if not state.equipment then return false end

        return DoesPlayerHaveOrg(state.job)
    end
)

-- Uniform Configuration
local function openClothingEditMenu(org, model, label)
    print("Open Clothing Edit Menu", org, model, label)
    local currentSkin = Base.Skin.getCharSkin()
    Base.Skin.setModel(model)

    exports["core_skin"]:openSkinMenu({ "clothing-restricted" }, false, false,
        -- onSave - triggers when the user presses save in the menu
        function()
            local clothing = Base.Skin.getCurrentSkin().clothing
            TriggerServerEvent("orgs:saveUniform", org, model, label, clothing)
            Base.Skin.setSkin(currentSkin)
        end,

        -- onReset - triggers when the user presses reset in the menu
        function()
            Base.Skin.setSkin(currentSkin)
        end
    )
end

local function editUniformMenu(org, model, label)
    local elements = {
        { label = "Edit",   value = "edit" },
        { label = "Delete", value = "delete" },
    }

    Base.UI.Menu.Open('default', GetCurrentResourceName(), 'uniformedit',
        {
            title    = label,
            align    = 'bottom-right',
            elements = elements,
        },
        function(data, menu)
            if data.current.value == "edit" then
                print("Edit", org, model, label)
                openClothingEditMenu(org, model, label)
            elseif data.current.value == "delete" then
                print("Delete", org, model, label)
                TriggerServerEvent("orgs:deleteUniform", org, model, label)
                Base.Skin.applyClothing(Base.Skin.getCharSkin().clothing, false)
            end

            Base.UI.Menu.CloseAll()
        end,
        function(data, menu)
            menu.close()
        end
    )
end

local function openUniformConfigurationList(org, model)
    local currentSkin = Base.Skin.getCharSkin()
    Base.Skin.setModel(model)

    local uniforms = AwaitServerCallback("orgs:getOrganisationUniforms", org, model)
    local elements = generateUniformElements(uniforms, true)

    table.insert(elements, { label = "<font color=\"green\">Add New Uniform</font>", value = "add" })

    -- Load the first uniform
    if #elements > 1 then
        local firstUniform = elements[1]
        local uniform = uniforms[firstUniform.label]
        Base.Skin.applyClothing(uniform, false)
    end

    Base.UI.Menu.Open('default', GetCurrentResourceName(), 'uniformlist',
        {
            title    = 'Uniforms',
            align    = 'bottom-right',
            elements = elements,
        },
        function(data, menu)
            if data.current.value == "add" then
                OpenDialog("New Uniform Label", function(label)
                    if uniforms[label] then
                        return Base.Notification("Uniform with that label already exists")
                    end
                    openClothingEditMenu(org, model, label)
                end)
            else
                editUniformMenu(org, model, data.current.label)
            end
        end,
        function(data, menu)
            menu.close()
            Base.Skin.setSkin(currentSkin)
        end,
        function(data, menu)
            local uniform = uniforms[data.current.label]
            print("selected uniform", data.current.label, uniform)
            Base.Skin.applyClothing(uniform, false)
        end
    )
end

local function openModelSelectionMenu(org)
    local elements = {
        { label = "Male",   value = "mp_m_freemode_01" },
        { label = "Female", value = "mp_f_freemode_01" },
        { label = "Custom", value = "custom_model" },
    }

    Base.UI.Menu.Open('default', GetCurrentResourceName(), 'modelselection',
        {
            title    = 'Select Model',
            align    = 'bottom-right',
            elements = elements,
        },
        function(data, menu)
            local model = data.current.value

            if data.current.value == "custom_model" then
                OpenDialog("Enter Model Name", function(customModel)
                    if IsModelInCdimage(customModel) then
                        openUniformConfigurationList(org, customModel)
                    else
                        Base.Notification("Invalid Model Name")
                    end
                end)
            else
                print("model select", org, model)
                openUniformConfigurationList(org, model)
            end
        end,
        function(data, menu)
            menu.close()
        end
    )
end

AddEventHandler("org:markerInteraction", function(type, marker)
    if type ~= "uniformmodify" then return end
    print("Open Uniform Menu", marker.organisation, marker.identifier)
    openModelSelectionMenu(marker.organisation)
end)

-- Saved uniforms
RegisterInteraction("vehicle", "savedoutfits", { label = "Saved Outfits", emoji = "👓" },
    function(vehicle)
        TriggerEvent("fdg_properties:OpenWardrobe")
        Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
    end, function(entity)
        local state = Entity(entity).state

        if not state.job then return false end
        -- if not state.equipment then return false end

        return DoesPlayerHaveOrgPermission("savedoutfits", state.job)
    end
)
