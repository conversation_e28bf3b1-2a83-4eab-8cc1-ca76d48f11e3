CreateThread(function()
    Wait(2000)

    if LocalPlayer.state.accountid then
        TriggerServerEvent("reloadPlayerOrgs")
    end
end)

local organisations = {}
local orgPerms = {}

RegisterNetEvent("base:updatedPlayerOrgs", function(orgs, perms)
    organisations = orgs
    orgPerms = perms
end)

local function getPlayerOrgs()
    return organisations
end
exports("getPlayerOrgs", getPlayerOrgs)

local function doesPlayerHaveOrg(org)
    return organisations[org] ~= nil
end
exports("doesPlayerHaveOrg", doesPlayerHaveOrg)

local function getPlayerOrgRank(org)
    return organisations[org]
end
exports("getPlayerOrgRank", getPlayerOrgRank)

local function doesPlayerHaveOrgPermission(permission, orgName)
    -- If no role specified, check all roles
    if not orgName then
        for orgName, _ in pairs(organisations) do
            local org = orgPerms[orgName]
            if not org then goto continue end

            if org[permission] then
                return true, orgName
            end

            ::continue::
        end
    else
        local org = orgPerms[orgName]
        if not org then return false end

        if org[permission] then
            return true
        end
    end

    return false
end
exports("doesPlayerHaveOrgPermission", doesPlayerHaveOrgPermission)
