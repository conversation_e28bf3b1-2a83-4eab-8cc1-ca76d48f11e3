activeProfile = "Default"
hotkeyActions = {}
allHotkeys = {}
hotkeys = {}
selectedSlot = false
inventoryOpen = false
hotkeysEnabled = true

availableHotkeys = {
	[157] = "1",
	[158] = "2",
	[160] = "3",
	[164] = "4",
	[165] = "5",
	[159] = "6",
	[161] = "7",
	[162] = "8",
	[163] = "9",
}

defaultProfile = {
	[160] = { frozen = false, value = "calmdown", dict = "gestures@m@standing@casual", anim = "gesture_easy_now", label = "Calm Down", action = "anim", loop = false, upper = true },
	[161] = { frozen = false, value = "whatever", dict = "gestures@m@standing@casual", anim = "gesture_damn", label = "Whatever", action = "anim", loop = false, upper = true },
	[162] = { frozen = false, value = "stretch", dict = "anim@deathmatch_intros@unarmed", anim = "intro_male_unarmed_e", label = "Stretch before Fight", action = "anim", loop = false, upper = false },
	[163] = { frozen = false, value = "bird1", dict = "anim@mp_player_intselfiethe_bird", anim = "idle_a", label = "Bird 1", action = "anim", loop = true, upper = true },
	[164] = { frozen = false, value = "facepalm", dict = "anim@mp_player_intcelebrationmale@face_palm", anim = "face_palm", label = "Facepalm", action = "anim", loop = false, upper = true },
	[165] = { frozen = false, value = "come1", dict = "gestures@m@standing@casual", anim = "gesture_come_here_soft", label = "Come here - Nicely", action = "anim", loop = false, upper = true },
	[157] = { frozen = false, value = "hi", dict = "gestures@m@standing@casual", anim = "gesture_hello", label = "Wave Hello", action = "anim", loop = false, upper = true },
	[158] = { frozen = false, value = "pointself", dict = "gestures@m@standing@casual", anim = "gesture_me", label = "Point at Self", action = "anim", loop = false, upper = true },
	[159] = { frozen = false, value = "come2", dict = "gestures@m@standing@casual", anim = "gesture_bring_it_on", label = "Come here - Aggresive", action = "anim", loop = false, upper = true },
}

RegisterNetEvent("base:characterLoaded", function(data)
	loadHotkeys(data.hotkeys)
end)

RegisterNetEvent("inventory:loadHotkeys", function(hotkeys)
	loadHotkeys(hotkeys)
end)

loadHotkeys = function(hotkeyData)
	if hotkeyData and hotkeyData ~= "[]" then
		for label, profile in pairs(json.decode(hotkeyData)) do
			allHotkeys[label] = {}
			for k, v in pairs(profile) do
				allHotkeys[label][tonumber(k)] = v
			end
		end
	else
		allHotkeys["Default"] = defaultProfile
		Base.Notification("Tip:  You can use /hotkeys to configure key commands!")
	end

	hotkeys = allHotkeys[activeProfile] or {}
	updateHotbarDisplay()
end

updateHotbarDisplay = function()
	local hotkeyDisplay = {}

	for k, v in pairs(hotkeys) do
		hotkeyDisplay[availableHotkeys[k]] = v.value or ""
	end

	SendNUIMessage({ hotbar = hotkeyDisplay })
end

registerHotkeyAction = function(identifier, cb)
	hotkeyActions[identifier] = { label = label, cb = cb }
end

setHotkeyProfile = function(profileName)
	TriggerServerEvent("base_scripting:saveHotkeys", allHotkeys)

	Base.Notification("Profile " .. profileName .. " selected")
	activeProfile = profileName

	if not allHotkeys[activeProfile] then
		allHotkeys[activeProfile] = {}
	end

	hotkeys = allHotkeys[activeProfile]
end

selectSlot = function(keyLabel, time)
	Citizen.CreateThread(function()
		SendNUIMessage({ selectSlot = keyLabel })
		if time then
			Wait(time)
			SendNUIMessage({ deselectSlot = keyLabel })
		end
	end)
end

deselectSlot = function(keyLabel)
	SendNUIMessage({ deselectSlot = keyLabel })
end

RegisterNUICallback("setHotbarAnim", function(data)
	local anim = animations[data.cat][data.anim]
	local control = tonumber(data.control)

	if anim.dict == "clipset" then
		hotkeys[control] = { label = anim.label, action = "walkstyle", anim = anim.anim, value = anim.command }
	elseif anim.dict then
		hotkeys[control] = { label = anim.label, action = "anim", anim = anim.anim, dict = anim.dict, loop = anim.loop, upper = anim.upper, frozen = false, value = anim.command }
	else
		hotkeys[control] = { label = anim.label, action = "clothing", anim = anim.anim, index = anim.index, value = anim.anim }
	end

	saveHotkeys()
end)

RegisterNUICallback("setHotbarItem", function(data)
	local item = Base.GetPlayerData().inventory[tonumber(data.index) + 1]
	local control = tonumber(data.control)

	hotkeys[control] = { action = item.type, value = item.name, label = item.label }

	saveHotkeys()
end)

RegisterNUICallback("swapHotbar", function(data)
	local control1 = tonumber(data.control1)
	local control2 = tonumber(data.control2)

	local hotkey1 = hotkeys[control1]
	hotkeys[control1] = hotkeys[control2]
	hotkeys[control2] = hotkey1

	saveHotkeys()
end)

RegisterNUICallback("clearHotbarSlot", function(data)
	hotkeys[tonumber(data.control)] = {}
	saveHotkeys()
end)

---
--- HOTKEY ACTIONS
---
Citizen.CreateThread(function()
	while true do
		Wait(0)
		HideHudComponentThisFrame(19) -- weapon wheel
		--HideHudComponentThisFrame(2) -- Weapon icon
	end
end)

Citizen.CreateThread(function()
	while true do
		Wait(0)
		if hotkeysEnabled then
			DisableControlAction(1, 37)
			DisableControlAction(1, 140)

			for control, keyLabel in pairs(availableHotkeys) do
				DisableControlAction(1, control)

				if not isDead and not inventoryOpen and IsDisabledControlJustReleased(0, control) and hotkeys[control] and GetLastInputMethod(2) then
					hotkeyAction = hotkeys[control]

					if not itemUseCooldown and hotkeyAction and hotkeyAction.action and Base ~= nil then
						if hotkeyActions[hotkeyAction.action] then
							PlayerData = Base.GetPlayerData()
							hotkeyActions[hotkeyAction.action].cb(hotkeyAction, keyLabel)
						end
					end
				end
			end

			-- Inventory TAB open/close
			if IsDisabledControlJustReleased(1, 37) and GetLastInputMethod(2) then
				if not inventoryOpen and not IsPauseMenuActive() then
					local c = GetVehicleClass(GetVehiclePedIsIn(PlayerPedId(), false))
					if (IsPedInAnyVehicle(PlayerPedId(), false) and c ~= 8 and c ~= 13 and c ~= 13 and c ~= 15 and c ~= 16 and c ~= 21) then
						exports["core_vehicles"]:openBoot(GetVehiclePedIsIn(PlayerPedId(), false), true)
					else
						showInventory(false, false)
					end
				else
					hideInventory()
				end
				Wait(100)
			end

			-- Hide inventory with G or ESC when menu open
			if inventoryOpen then
				DisableControlAction(1, 200)
				DisableControlAction(1, 47)

				if IsDisabledControlJustReleased(1, 200) and GetLastInputMethod(2) then
					hideInventory()
					SetPauseMenuActive(false)
				end
			end
		end

		if hotbarKeyEnabled then
			DisableControlAction(1, 37)
			DisableControlAction(1, 140)

			for control, keyLabel in pairs(availableHotkeys) do
				DisableControlAction(1, control)

				if not isDead and not inventoryOpen and IsDisabledControlJustReleased(0, control) and hotkeys[control] and GetLastInputMethod(2) then
					hotkeyAction = hotkeys[control]

					if not itemUseCooldown and hotkeyAction and hotkeyAction.action and Base ~= nil then
						if hotkeyActions[hotkeyAction.action] then
							PlayerData = Base.GetPlayerData()
							hotkeyActions[hotkeyAction.action].cb(hotkeyAction, keyLabel)
						end
					end
				end
			end
		end
	end
end)

registerHotkeyAction("clothing", function(hotkeyAction, keyLabel)
	exports["core_roleplay"]:accessories_toggle_external(hotkeyAction)
	selectSlot(keyLabel, 1000)
end)

registerHotkeyAction("anim", function(hotkeyAction, keyLabel)
	-- if IsEntityPlayingAnim(PlayerPedId(), hotkeyAction.dict, hotkeyAction.anim, 3) or IsPedUsingScenario(PlayerPedId(), hotkeyAction.anim) then
	-- 	Base.Animation.Stop(PlayerPedId())
	-- 	deselectSlot(keyLabel)
	-- else
	local PlayerData = Base.GetPlayerData()
	local playerPed = PlayerPedId()

	Citizen.Wait(50)
	if IsPedRagdoll(playerPed) or IsPedGettingUp(playerPed) then
		return
	end

	if (IsPedInAnyVehicle(PlayerPedId(), true)) then
		if (hotkeyAction.dict ~= "scenario") then
			Base.Animation.Start(PlayerPedId(), hotkeyAction.dict, hotkeyAction.anim, hotkeyAction.loop, false, true)
			selectSlot(keyLabel, 1000)
			GeneralLog("Play Anim", PlayerData.logName .. " has started " .. hotkeyAction.value, "green", "animations")
		end
	else
		if hotkeyAction.dict == "expression" then
			SetFacialIdleAnimOverride(PlayerPedId(), hotkeyAction.anim, 0)
		else
			--dont do animation if ragdoll
			if IsPedRagdoll(PlayerPedId()) then
				return
			end
			print(hotkeyAction.anim)
			Base.Animation.Start(PlayerPedId(), hotkeyAction.dict, hotkeyAction.anim, hotkeyAction.loop, hotkeyAction.frozen, hotkeyAction.upper)
			selectSlot(keyLabel, 1000)
			GeneralLog("Play Anim", PlayerData.logName .. " has started " .. hotkeyAction.value, "green", "animations")
		end
	end
	--end
end)

registerHotkeyAction("walkstyle", function(hotkeyAction, keyLabel)
	if Base.Animation.GetClipset() == hotkeyAction.anim then
		RemoveClipset("playerwalkstyle")
	else
		AddClipset("playerwalkstyle", hotkeyAction.anim, 0)
		selectSlot(keyLabel, 1000)
	end
end)

registerHotkeyAction("briefcase1", function(hotkeyAction, keyLabel)
	if HasPedGotWeapon(GetPlayerPed(-1), GetHashKey("WEAPON_BRIEFCASE"), false) then
		RemoveWeaponFromPed(GetPlayerPed(-1), GetHashKey("WEAPON_BRIEFCASE"))
		deselectSlot(keyLabel)
	else
		GiveWeaponToPed(GetPlayerPed(-1), GetHashKey("WEAPON_BRIEFCASE"), 1, true, true)
		selectSlot(keyLabel)
	end
end)

registerHotkeyAction("briefcase2", function(hotkeyAction, keyLabel)
	if HasPedGotWeapon(GetPlayerPed(-1), GetHashKey("WEAPON_BRIEFCASE_02"), false) then
		RemoveWeaponFromPed(GetPlayerPed(-1), GetHashKey("WEAPON_BRIEFCASE_02"))
		deselectSlot(keyLabel)
	else
		GiveWeaponToPed(GetPlayerPed(-1), GetHashKey("WEAPON_BRIEFCASE_02"), 1, true, true)
		selectSlot(keyLabel)
	end
end)

registerHotkeyAction("weapon", function(hotkeyAction, keyLabel)
	if (LocalPlayer.state.disarmed and LocalPlayer.state.disarmed == true) then
		TriggerEvent("fdg_ui:SendNotification", "You cannot use weapons whilst disarmed")
		TriggerEvent('fdg_ui:SendNotification', "You can rearm at a vehicle you have keys for, or by entering a property")
		return
	end
	if not isCuffed then
		equipWeapon(hotkeyAction.value, hotkeyAction.label, keyLabel)
		Wait(200)
	end
end)

registerHotkeyAction("item", function(hotkeyAction, keyLabel)
	local item, i = getItem(hotkeyAction.value)
	if item and item.count > 0 then
		useItem(i)
		selectSlot(keyLabel, 2000)
		Wait(500)
	else
		Base.Notification("You don't have " .. hotkeyAction.label .. " in your inventory")
	end
end)

function setHotbarSlot(selectedKey, data)
	hotkeys[selectedKey] = data
	updateHotbarDisplay()
end

function saveHotkeys()
	allHotkeys[activeProfile] = hotkeys
	TriggerServerEvent("base_scripting:saveHotkeys", allHotkeys)
	updateHotbarDisplay()
end

function toggleKeys(state)
	hotkeysEnabled = state
end

function toggleHotbar(state)
	hotbarKeyEnabled = state
end
