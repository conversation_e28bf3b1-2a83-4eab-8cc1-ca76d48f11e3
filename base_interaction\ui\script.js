$(document).ready(function () {
  // Mouse Controls
  var documentWidth = document.documentElement.clientWidth;
  var documentHeight = document.documentElement.clientHeight;
  var cursor = $("#cursorPointer");
  var cursorX = documentWidth / 2;
  var cursorY = documentHeight / 2;
  var idEnt = 0;

  function UpdateCursorPos() {
    $("#cursorPointer").css("left", cursorX);
    $("#cursorPointer").css("top", cursorY);
  }

  function triggerClick(x, y) {
    var element = $(document.elementFromPoint(x, y));
    element.focus().click();
    return true;
  }

  function closeAllMenu() {
    $(".crosshair").removeClass("fadeIn").removeClass("active");
    $(".menu").removeClass("fadeIn");
    $.post(
      "http://base_interaction/disablenuifocus",
      JSON.stringify({
        nuifocus: false,
        menu: "*",
      })
    );
  }

  // Listen for NUI Events
  window.addEventListener("message", function (event) {
    // Crosshair
    if (event.data.crosshair == true) {
      $(".crosshair").addClass("fadeIn");
    }
    if (event.data.crosshair == false) {
      $(".crosshair").removeClass("fadeIn");
    }

    // Click
    if (event.data.type == "click") {
      triggerClick(cursorX - 1, cursorY - 1);
    }
    if (event.data.type == "closeall") {
      closeAllMenu();
    }
    if (event.data.type == "searchSprite") {
      if (event.data.toggle) {
        var x = event.data.x * 100 + "%";
        var y = event.data.y * 100 + "%";
        var scale = event.data.scale * 100 + "%";
        if (event.data.scale * 100 < 0) {
          $("#searchSprite").css({ display: "none" });
        } else {
          $("#searchSprite").css({ display: "block", top: x, left: y, "max-width": scale, "max-height": scale });
        }
      } else {
        $("#searchSprite").css({ display: "none" });
      }
    }

    // Open Menu
    if (event.data.type == "openMenu") {
      idEnt = event.data.idEntity;
      menuElements = event.data.menuElements;
      menu = event.data.menuName;

      $(".menu-custom").empty();

      var i;
      for (i = 0; i < menuElements.length; i++) {
        var label = menuElements[i].label;
        var emoji = menuElements[i].emoji;
        var action = menuElements[i].action;

        if (emoji != undefined) {
          $(".menu-custom").append(
            '<a class="action" id="' +
              action +
              '" menu="' +
              menu +
              '"><span class="emoji">' +
              emoji +
              '</span><span class="text">' +
              label +
              "</span></a>"
          );
        } else {
          $(".menu-custom").append(
            '<a class="action" id="' + action + '" menu="' + menu + '"><span class="text">' + label + "</span></a>"
          );
        }
      }

      $(".action").click(function (e) {
        e.preventDefault();
        closeAllMenu();

        var action = $(this).attr("id");
        var menu = $(this).attr("menu");

        $.post(
          "http://base_interaction/optionSelected",
          JSON.stringify({
            action: action,
            menu: menu,
          })
        );
      });

      $(".menu-custom").addClass("fadeIn");
    }

    // Menu
    if (event.data.menu == "vehicle") {
      $(".crosshair").addClass("active");
      $(".menu-car").addClass("fadeIn");
      idEnt = event.data.idEntity;
    }
  });

  // Mousemove
  $(document).mousemove(function (event) {
    cursorX = event.pageX;
    cursorY = event.pageY;
    UpdateCursorPos();
  });

  // Click Crosshair
  $(".crosshair").on("click", function (e) {
    e.preventDefault();
    $(".crosshair").removeClass("fadeIn").removeClass("active");
    $(".menu").removeClass("fadeIn");
    $.post("http://base_interaction/disablenuifocus", JSON.stringify({ nuifocus: false }));
  });

  $(document).keypress(function (e) {
    if (e.which == 101 || e.which == 27) {
      // if "E" or "ESC" is pressed
      closeAllMenu();
    }
  });
});
