-- Init Base Params
Base.PlayerData = {}
Base.PlayerLoaded = false

------------------------------
-- Handle player connection --
------------------------------

local awaitingSpawn = true
local _anticheat = nil
Citizen.CreateThread(function()
    while awaitingSpawn do
        Wait(50)
        local playerPed = GetPlayerPed(-1)
        if playerPed and playerPed ~= -1 then
            if NetworkIsPlayerActive(PlayerId()) then
                SetPlayerInvincible(PlayerId(), true)
                Base.PlayerLoaded = false
                awaitingSpawn = false
            end
        end
    end
end)

RegisterNetEvent("base:characterLoaded")
AddEventHandler(
    "base:characterLoaded",
    function(data)
        Base.PlayerLoaded = true
        Base.PlayerData = data
        SetEntityHealth(PlayerPedId(), data.health or GetEntityMaxHealth(PlayerPedId()))
    end
)

RegisterNetEvent("esx:setAccountMoney")
AddEventHandler(
    "esx:setAccountMoney",
    function(account)
        if (Base.PlayerData.accounts) then
            for i = 1, #Base.PlayerData.accounts, 1 do
                if Base.PlayerData.accounts[i].name == account.name then
                    Base.PlayerData.accounts[i] = account
                end
            end
        end

        if account.name == "money" then
            Base.PlayerData.money = account.money
        end
    end
)

RegisterNetEvent("esx:setICname")
AddEventHandler(
    "esx:setICname",
    function(name)
        Base.PlayerData.icname = name
    end
)

RegisterNetEvent("main_dispatch:SyncCallsign")
AddEventHandler(
    "main_dispatch:SyncCallsign",
    function(callsign)
        Base.PlayerData.callsign = callsign
    end
)
