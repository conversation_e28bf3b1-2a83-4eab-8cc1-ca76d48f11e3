RegisterInteraction("vehicle", "openhood", { label = "Open Hood", emoji = "⬆️", order = 4 }, function(vehicle)
    if DoesEntityExist(vehicle) then
        TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(vehicle), "setDoorState", 4, true)
        Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
    end
end, function(veh)
    return GetVehicleDoorAngleRatio(veh, 4) == 0
end)

RegisterInteraction("vehicle", "closehood", { label = "Close Hood", emoji = "⬇️", order = 4 }, function(vehicle)
    if DoesEntityExist(vehicle) then
        TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(vehicle), "setDoorState", 4,
            false)
        Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
    end
end, function(veh)
    return GetVehicleDoorAngleRatio(veh, 4) ~= 0
end)

RegisterInteraction("vehicle", "boot", { label = "Boot", emoji = "📦", order = 15 }, function(vehicle)
    if DoesEntityExist(vehicle) then
        exports["core_vehicles"]:openBoot(vehicle)
    end
end, function()
    return not exports["main_roleplay"]:IsPlayerInTrunk()
end)

RegisterInteraction("vehicle", "clean", { label = "Clean", emoji = "🧼", order = 2 }, function(vehicle)
    fdgVehicle:clean(vehicle)
    Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
end, "vehicle.clean")

RegisterInteraction("vehicle", "cleanwithcloth", { label = "Clean With Cloth", emoji = "🧼", order = 2 },
    function(vehicle)
        fdgVehicle:clean(vehicle)
        TriggerServerEvent("core_vehicles:usecloth")
        Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
    end, function()
        return exports.core_inventory:getItem("cloth") ~= false
    end)

RegisterInteraction("vehicle", "repairengine", { label = "Repair", emoji = "🔧", order = 3 }, function(vehicle)
    print("repair", vehicle)
    fdgVehicle:repairEngine(vehicle)
    Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
end, "vehicle.repair")

RegisterInteraction("vehicle", "repairenginehalf", { label = "Patch Engine", emoji = "🛠️", order = 3 }, function(vehicle)
    fdgVehicle:repairEngine(vehicle, 500)
    Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
end, function()
    return exports.core_inventory:getItem("fixkitsmall") ~= false
end)

RegisterInteraction("vehicle", "checkenginehealth", { label = "Check Engine", emoji = "🔧", order = 1 }, function(vehicle)
    fdgVehicle:checkEngineHealth(vehicle)
    Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
end)

RegisterInteraction("vehicle", "lockpick", { label = "Lockpick", emoji = "🔏", order = 7 }, function(vehicle)
    fdgVehicle:lockpick(vehicle)
    Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
end, "vehicle.lockpick")

RegisterInteraction("vehicle", "flatbed", { label = "Flatbed", emoji = "🛻", order = 9 }, function(vehicle)
    fdgVehicle:flatbedVehicle(vehicle)
    Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
end, "vehicle.flatbed")

RegisterInteraction("vehicle", "tow", { label = "Tow", emoji = "📄", order = 8 }, function(vehicle)
    fdgVehicle:impound(vehicle)
    Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
end, "vehicle.tow")

RegisterInteraction("vehicle", "impound", { label = "Impound", emoji = "📜", order = 8 }, function(vehicle)
    OpenDialog("Impound for how many hours? (Refer to demerits)", function(time)
        fdgVehicle:impound(vehicle, time)
    end, false, "number", 1, 337)
    Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
end, "vehicle.impound")

RegisterInteraction("vehicle", "rego", { label = "Rego Check", emoji = "📋", order = 7 }, function(vehicle)
    fdgVehicle:regoCheck(vehicle)
    Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
end, "vehicle.rego")

RegisterInteraction("vehicle", "lockvehicle", { label = "Lock Vehicle", emoji = "🔒", order = 5 }, function(vehicle)
    fdgVehicle:lockVehicle(vehicle)
end, function(vehicle)
    return not Entity(vehicle).state.locked
end)

RegisterInteraction("vehicle", "unlockvehicle", { label = "Unlock Vehicle", emoji = "🔓", order = 5 }, function(vehicle)
    fdgVehicle:unlockVehicle(vehicle)
end, function(vehicle)
    return Entity(vehicle).state.locked
end)

RegisterInteraction("vehicle", "lockvehicleboot", { label = "Lock Vehicle Boot", emoji = "🔒", order = 15 },
    function(vehicle)
        fdgVehicle:lockVehicleBoot(vehicle)
        Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
    end, function(vehicle)
        return not Entity(vehicle).state.bootlocked
    end)

RegisterInteraction("vehicle", "unlockvehicleboot", { label = "Unlock Vehicle Boot", emoji = "🔓", order = 15 },
    function(vehicle)
        fdgVehicle:unlockVehicleBoot(vehicle)
        Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
    end, function(vehicle)
        return Entity(vehicle).state.bootlocked
    end)

RegisterInteraction("vehicle", "flipvehicle", { label = "Flip Vehicle", emoji = "🔁" }, function(vehicle)
    fdgVehicle:flipVehicle(vehicle)
    Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
end, function(vehicle)
    return not IsVehicleOnAllWheels(vehicle)
end)

RegisterInteraction("vehicle", "removestolenplate", { label = "Remove Stolen Plate", emoji = "🚫", order = 16 },
    function(vehicle)
        TriggerServerEvent("main_roleplay:removeStolenPlate", NetworkGetNetworkIdFromEntity(vehicle))
        Entity(vehicle).state:set("interactionTime", GetNetworkTime(), true)
    end, function(vehicle)
        local hasItem = AwaitServerCallback("main_roleplay:hasStolenPlateItem")
        return DoesEntityExist(vehicle) and hasItem
    end)

local function enterVehicle(vehicle, seat)
    if Entity(vehicle).state.locked then
        TriggerEvent("fdg_ui:SendNotification", "This vehicle is locked")
        return
    end
    if exports["core_inventory"]:isCrafting() then
        TriggerEvent("fdg_ui:SendNotification", "You cannot do that while crafting")
        return
    end
    TaskEnterVehicle(PlayerPedId(), vehicle, 7000, seat, 1.0, 8, 0)
    Citizen.SetTimeout(6000, function()
        if not IsPedInVehicle(PlayerPedId(), vehicle, true) then
            ClearPedTasks(PlayerPedId())
        end
    end)
end

local doorOptions = {
    door1 = {
        displayData = {
            label = "Driver"
        },
        onInteract = function(vehicle)
            enterVehicle(vehicle, -1)
        end
    },
    door2 = {
        displayData = {
            label = "Passenger Seat"
        },
        onInteract = function(vehicle)
            enterVehicle(vehicle, 0)
        end
    },
    door3 = {
        displayData = {
            label = "Rear Left"
        },
        onInteract = function(vehicle)
            enterVehicle(vehicle, 1)
        end
    },
    door4 = {
        displayData = {
            label = "Rear Right"
        },
        onInteract = function(vehicle)
            enterVehicle(vehicle, 2)
        end
    },
    door5 = {
        displayData = {
            label = "Far Rear Left"
        },
        onInteract = function(vehicle)
            enterVehicle(vehicle, 3)
        end
    },
    door6 = {
        displayData = {
            label = "Far Rear Right"
        },
        onInteract = function(vehicle)
            enterVehicle(vehicle, 4)
        end
    }
}

RegisterInteraction("vehicle", "enterVehiclemenu", { label = "Enter Vehicle", emoji = "🚗", order = 6 }, function(vehicle)
    exports.base_interaction:OpenInteractMenu(doorOptions, vehicle)
end)
