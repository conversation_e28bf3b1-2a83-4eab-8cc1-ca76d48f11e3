(function(e){function t(t){for(var r,c,i=t[0],s=t[1],u=t[2],l=0,d=[];l<i.length;l++)c=i[l],Object.prototype.hasOwnProperty.call(o,c)&&o[c]&&d.push(o[c][0]),o[c]=0;for(r in s)Object.prototype.hasOwnProperty.call(s,r)&&(e[r]=s[r]);p&&p(t);while(d.length)d.shift()();return a.push.apply(a,u||[]),n()}function n(){for(var e,t=0;t<a.length;t++){for(var n=a[t],r=!0,i=1;i<n.length;i++){var s=n[i];0!==o[s]&&(r=!1)}r&&(a.splice(t--,1),e=c(c.s=n[0]))}return e}var r={},o={app:0},a=[];function c(t){if(r[t])return r[t].exports;var n=r[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,c),n.l=!0,n.exports}c.m=e,c.c=r,c.d=function(e,t,n){c.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},c.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.t=function(e,t){if(1&t&&(e=c(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(c.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)c.d(n,r,function(t){return e[t]}.bind(null,r));return n},c.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return c.d(t,"a",t),t},c.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},c.p="";var i=window["webpackJsonp"]=window["webpackJsonp"]||[],s=i.push.bind(i);i.push=t,i=i.slice();for(var u=0;u<i.length;u++)t(i[u]);var p=s;a.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("56d7")},"034f":function(e,t,n){"use strict";n("85ec")},4709:function(e,t,n){var r=n("7338"),o=[],a=[];e.exports=function(e){return{TriggerServerEvent:function(t){for(var n=[],o=1;o<arguments.length;o++)n.push(arguments[o]);r.post("http://"+e+"/triggerServerEvent",{eventName:t,data:n})},TriggerServerCallback:function(t,n){for(var o=[],a=2;a<arguments.length;a++)o.push(arguments[a]);r.post("http://"+e+"/triggerServerCallback",{eventName:t,data:o}).then((function(e){"OK"==e.data?n():n.apply(this,JSON.parse(e.data))})).catch((function(e){console.log(e)}))},RegisterNetEvent:function(t,n){r.post("http://"+e+"/registerNetEvent",{eventName:t}).catch((function(e){console.log(e)})),o[t]=n},TriggerClientEvent:function(t){for(var n=[],o=1;o<arguments.length;o++)n.push(arguments[o]);r.post("http://"+e+"/triggerClientEvent",{eventName:t,data:n})},TriggerNUICallback:async function(t,n){return await r.post("http://"+e+"/"+t,n)},RegisterNUIEvent:function(e,t){a[e]=t}}},window.addEventListener("message",(function(e){var t=e.data;t.netEvent&&o[t.netEvent]&&o[t.netEvent].apply({},t.data),t.nuiEvent&&a[t.nuiEvent]&&a[t.nuiEvent].apply({},t.data)}))},"56d7":function(e,t,n){"use strict";n.r(t);var r=n("2b0e"),o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{attrs:{id:"container"}},e._l(e.apps,(function(t,r){return n("div",{key:r,staticClass:"app-container"},[e.currentScreen==r?n("div",{staticClass:"app-header"},[n("span",{staticClass:"title"},[e._v(e._s(t.title))]),n("div",{staticClass:"close-button",on:{click:e.close}},[e._v("Close")])]):e._e(),n("iframe",{directives:[{name:"show",rawName:"v-show",value:e.currentScreen==r,expression:"currentScreen == name"}],attrs:{src:t.src,id:r}})])})),0)},a=[],c=n("4709"),i=n.n(c);const s=i()("core_webpanel");var u={name:"App",components:{},data(){return{currentScreen:"",apps:{support:{src:"https://support.fatduckgaming.com/",id:"support",title:"Support"},cops:{src:"https://cops.fatduckgaming.com",id:"cops",title:"Cops"}}}},methods:{close(){this.currentScreen="",s.TriggerNUICallback("setFocus",!1)},sendServiceMessage(e,t){let n=document.getElementById(e);if(!n)return console.warn(`Window ${e} not found`);n.contentWindow.postMessage(t,"*")}},mounted(){s.RegisterNetEvent("panel:token",(e,t)=>{this.sendServiceMessage(t,{token:e})}),s.RegisterNetEvent("panel:showService",(e,t)=>{if(this.currentScreen=e,s.TriggerNUICallback("setFocus",!0),t){let t=document.getElementById(e);if(!t)return console.warn(`Window ${e} not found`);t.src=this.apps[e].src}}),s.RegisterNetEvent("panel:navigateService",(e,t)=>{let n=document.getElementById(e);if(!n)return console.warn(`Window ${e} not found`);n.src=t}),document.addEventListener("keydown",e=>{"Escape"===e.code&&this.close()})}},p=u,l=(n("034f"),n("2877")),d=Object(l["a"])(p,o,a,!1,null,null,null),f=d.exports;r["a"].config.productionTip=!1,new r["a"]({render:e=>e(f)}).$mount("#app")},"85ec":function(e,t,n){}});
//# sourceMappingURL=app.489838dd.js.map