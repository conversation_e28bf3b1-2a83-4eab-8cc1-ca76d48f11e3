@import url(http://fonts.googleapis.com/css?family=Roboto:400,400italic,500,500italic,700,700italic,900,900italic,300italic,300,100italic,100);

.menu {
	font-family: 'Roboto';
	position: absolute;
	left: 50%;
	top: 50%;
	transform: translate(-50%, -50%);
	box-shadow: 0px 0px 50px 0px #000;
	overflow-y: auto;
	max-height: 90%;
}

.menu button {
	background-color: rgba(30,30,30,1.0);
	font-family: 'Roboto';
	position: relative;
	color: #fff;
	min-width: 50%;
	min-height: 30px;
	border: 0px;
}

.menu>table {
	border: 1px transparent;
	border-spacing: 0;
	width: 100%;
	background-color: rgba(0,0,0,0.0);
}

.menu>table>thead {
	background-color: rgba(36,36,36);
	text-align: center;
	height: 40px;
	line-height: 40px;
	color: #fff;
}

.menu td {
	text-align: center;
	padding: 8px;
	color: #fff;
}

.menu tbody tr:nth-child(even) {
	background-color: rgba(60,60,60,0.95);
}

.menu tbody tr:nth-child(odd) {
	background-color: rgba(60,60,60,0.95);
}