------------------------------------
-- Invoice History Menu           --
------------------------------------

function OpenInvoiceHistoryMenu(org)
    local function orderInvoices(a, b)
        if (a.datetime < b.datetime) then
            return true
        elseif (a.datetime > b.datetime) then
            return false
        end
    end

    local invoices = AwaitServerCallback("orgs:getInvoiceHistory", org)

    local elements = {
        head = { 'Recipient', 'Sender', 'Amount', 'Phone', 'Remove' },
        rows = {}
    }

    --table.sort(invoices, orderInvoices)

    for i, inv in ipairs(invoices) do
        if i > 200 then break end

        table.insert(elements.rows, {
            data = i,
            cols = {
                inv.target_icname,
                inv.sender_icname,
                "$" .. inv.amount,
                "#" .. inv.target_phone,
                "{{Remove|remove}}"
            }
        })
    end

    Base.UI.Menu.Open('list', GetCurrentResourceName(), 'business_actions_invoices', elements,
        function(data, menu)
            menu.close()
            local inv = invoices[data.data]
            if data.value == 'remove' then
                TriggerServerEvent("jobs:removeInvoice", inv.id)
                Wait(250)
                OpenInvoiceHistoryMenu(job)
            end
        end,

        function(data, menu)
            menu.close()
        end
    )
end
