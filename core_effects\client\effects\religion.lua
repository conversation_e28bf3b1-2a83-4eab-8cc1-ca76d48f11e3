local blessedEffect = TimedStatusEffect(StatusEffect("blessed", "Feeling Blessed", {}, { desc = "You feel empowered!" }), { maxTime = 30 * 60 })

local churches = {
    vector3(-319.39810180664, 2805.2985839844, 59.449867248535),
    vector3(-775.0, -2.0, -140.0),
}

Citizen.CreateThread(function()
    while true do
        local coords = GetEntityCoords(PlayerPedId())

        for i, churchCoords in ipairs(churches) do
            if #(coords - churchCoords) < 100.0 then
                Wait(20000)

                -- yuck
                if #(coords - churchCoords) < 100.0 then
                    blessedEffect.addTime(120)
                end
            end
        end

        Wait(5000)
    end
end)
