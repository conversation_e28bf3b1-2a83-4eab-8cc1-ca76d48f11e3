-- config.lua
Config = {}

Config.Music = true        --[[ Set the club's music on/off - 'true' for on, 'false' for off                                 ]]

Config.TargetSystem = 'ox_target'  --[[ Set the target system for the poles, can be ox_target, qb-target or false for Draw3D                                ]]

Config.DanceKey = 51	--[[ Set the key for starting the animation                                 ]]

Config.StopKey = 73		--[[ Set the key for stopping the animation                                 ]]

Config.Icon = 'fa-solid fa-arrow-right' --[[ Set the icon for ox_target or qb-target                                 ]]

Config.Text = 'Dance' --[[ Set the text for ox_target or qb-target                                 ]]

Config.Text3D = '[~q~E~w~] Dance' --[[ Set the text for when ox_target or qb-target are set to false                                 ]]

Config.Dancers = 'u_f_y_danceburl_01' --[[ Set the name of the ped you want to be displayed at the booths                                 ]]
