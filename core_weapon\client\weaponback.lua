local lastPed = -1
local lastWeapon = -1
local lastVehicle = -1
local weaponObjects = {}
local customWeaponPositions = {}

local function updateWeaponLocation(wepHash, option)
	local gender = -1
	local pModel = GetEntityModel(PlayerPedId())
	if (pModel == GetHashKey("mp_m_freemode_01")) then
		gender = 0
	elseif (pModel == GetHashKey("mp_f_freemode_01")) then
		gender = 1
	end
	if (pModel == GetHashKey("player_zero")) then
		return
	end
	if option == 0 then
		customWeaponPositions[wepHash] = nil
	else
		customWeaponPositions[wepHash] = Config.BackWeaponPositions[gender][option]
	end
end

local function isWeaponVisibleInVehicle()
	if (GetVehiclePedIsUsing(PlayerPedId()) == 0) then
		return false
	end

	local class = GetVehicleClass()
	if (class == 15 or class == 16) then
		return false
	end

	return true
end

local function refreshWeaponObjects(refreshAll)
	local source              = GetPlayerServerId(PlayerId())
	local bool, currentWeapon = GetCurrentPedWeapon(PlayerPedId())

	local gender = -1
	local pModel = GetEntityModel(PlayerPedId())
	if (pModel == GetHashKey("mp_m_freemode_01")) then
		gender = 0
	elseif (pModel == GetHashKey("mp_f_freemode_01")) then
		gender = 1
	end

	if (pModel == GetHashKey("player_zero")) then
		return
	end

	local hasWeapon = false
	local hasFirearm = false

	for wepHash, weapon in pairs(Config.WeaponPositions[(gender == -1 and 0 or gender)]) do
		local item = exports["core_inventory"]:getItem(string.lower(wepHash))
		if (currentWeapon ~= GetHashKey(wepHash) or isWeaponVisibleInVehicle()) and item and IsEntityVisible(PlayerPedId()) then
			if not weaponObjects[wepHash] or refreshAll then
				local model = weapon.model
				local coords = GetEntityCoords(PlayerPedId())
				local customWeaponPosition = customWeaponPositions[wepHash]

				local attachedSettings = {
					target = source,
					bone = customWeaponPosition and customWeaponPosition.bone or weapon.bone,
					x = customWeaponPosition and customWeaponPosition.x or weapon.x,
					y = customWeaponPosition and customWeaponPosition.y or weapon.y,
					z = customWeaponPosition and customWeaponPosition.z or weapon.z,
					xr = customWeaponPosition and customWeaponPosition.xRot or weapon.xRot,
					yr = customWeaponPosition and customWeaponPosition.yRot or weapon.yRot,
					zr = customWeaponPosition and customWeaponPosition.zRot or weapon.zRot,
					p9 = false,
					useSoftPinning = false,
					collision = false,
					isPed = -1,
					vertexIndex = 2,
					fixedRot = true,
				}

				if model ~= nil or model ~= "" then
					Base.Objects.Create((weapon.isCustom and weapon.model or item.name), coords.x, coords.y, coords.z, 0.0, "", false, attachedSettings, true, not weapon.isCustom, item.data)
					weaponObjects[wepHash] = (weapon.isCustom and weapon.model or item.name)
					hasWeapon = true

					print(weapon.firearm)
					if weapon.firearm then
						hasFirearm = true
					end
				end
			end

		else
			if weaponObjects[wepHash] then
				TriggerEvent("fdg_objspawn:DeleteObj", source .. "_obj_" .. weaponObjects[wepHash])
				weaponObjects[wepHash] = nil
			end
		end
		Wait(0)
	end

	LocalPlayer.state.hasWeapon = hasWeapon
	LocalPlayer.state.hasFirearm = hasFirearm
end

Citizen.CreateThread(function()
	while true do Wait(500)
		local ped = PlayerPedId()
		local bool, currentWeapon = GetCurrentPedWeapon(ped)
		local currentVehicle = GetVehiclePedIsUsing(ped)
		if ((ped ~= lastPed) or (lastWeapon ~= currentWeapon) or (lastVehicle ~= currentVehicle)) then
			lastPed = ped
			lastWeapon = currentWeapon
			lastVehicle = currentVehicle
			refreshWeaponObjects()
		end
	end
end)

RegisterNetEvent("inventory:clientSync", function(name)
	if name == "playerinventory-" .. GetPlayerServerId(PlayerId()) then
		Wait(200)
		refreshWeaponObjects()
	end
end)

RegisterNetEvent("onPlayerModelUpdated", function()
	Wait(200)
	refreshWeaponObjects(true)
end)

AddEventHandler("core_admin:toggleInvis", function()
	Wait(200)
	refreshWeaponObjects(true)
end)

RegisterCommand("weaponback", function(source, args)
	local option = tonumber(args[1])
	if not option then return end

	local tbl = {}
	if option == 4 then -- Hip
		tbl = Config.AllowedHipWeapons
	else
		tbl = Config.AllowedBackWeapons
	end

	for wepHash, weapon in pairs(tbl) do
		if GetHashKey(wepHash) == lastWeapon then
			updateWeaponLocation(wepHash, option)
			SetResourceKvpInt("core_weapon:customWpnPosition_" .. wepHash, tonumber(option))
			Wait(500)
			refreshWeaponObjects(true)
			return
		end
	end

	TriggerEvent("fdg_ui:SendNotification", "You can not change this weapon's location or wrong option number.")
end)

RegisterNetEvent("fdg:characterLoaded")
AddEventHandler('fdg:characterLoaded', function()
	for wepHash, weapon in pairs(Config.AllowedBackWeapons) do
		local option = tonumber(GetResourceKvpInt("core_weapon:customWpnPosition_" .. wepHash))
		if option then
			updateWeaponLocation(wepHash, option)
		end
	end

	refreshWeaponObjects(true)
end)