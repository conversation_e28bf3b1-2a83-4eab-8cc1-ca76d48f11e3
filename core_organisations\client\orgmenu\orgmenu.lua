-- World marker interface
AddEventHandler("org:markerInteraction", function(type, marker)
    if type ~= "bossmenu" then return end
    print("Open Org Menu", marker.organisation, marker.identifier)

    Base.UI.Menu.CloseAll()

    local elements = {
        { label = "Invoice History", value = "invoice_history" },
    }

    Base.UI.Menu.Open('default', GetCurrentResourceName(), 'orgmenu',
        {
            title = "Organisation Menu - " .. marker.organisation,
            align = "bottom-right",
            elements = elements
        },
        function(data, menu)
            menu.close()
            if data.current.value == "invoice_history" then
                OpenInvoiceHistoryMenu(marker.organisation)
            end
        end,
        function(data, menu)
            menu.close()
            print("Closed Org Menu")
        end
    )
end)
