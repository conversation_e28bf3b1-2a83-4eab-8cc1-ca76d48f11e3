@import url(http://fonts.googleapis.com/css?family=Roboto:400,400italic,500,500italic,700,700italic,900,900italic,300italic,300,100italic,100);

#cursor {
  position: absolute;
  z-index: 999999999;
  display:  none;
}

#controls {
  font-family   : 'Roboto';
  font-size     : 3em;
  color         : rgba(36,36,36);
  position      : absolute;
  bottom        : 40;
  right         : 40;
  
  text-shadow:
    -1px -1px 0 #000,
     1px -1px 0 #000,
    -1px  1px 0 #000,
     1px  1px 0 #000
  ;
}

.controls {
  display: none;
}

.dialog {

  font-family     : 'Roboto';  
  background-color: rgba(60,60,60,0.8);
  color           : #fff;
  box-shadow      : 0px 0px 50px 0px #000;
  position        : absolute;
  overflow        : hidden;
  top             : 50%;
  left            : 50%;
  width           : 400px;
  height          : 104px;
  transform       : translate(-50%, -50%);
}

.dialog.big {
  height: 100px;
}

.dialog .head {
  background-color: rgba(36,36,36);
  text-align      : center;
  height          : 40px;
}

.dialog .head span:before {
  content: "";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}

.dialog input[type="text"] {
  width : 100%;
  height: 32px;
  font-family     : 'Roboto';  
  background-color: rgba(60,60,60,0.8);
  color           : #fff;
  border-color: rgba(0,0,0);
  border: 0px;  
}

.dialog textarea {
  width : 100%;
  height: 128px;  
}

.dialog button[name="submit"] {
  width : 50%;
  height: 32px;
  font-family     : 'Roboto';  
  background-color: rgba(36,36,36,0.8);
  color           : #fff;
  border-color: rgba(0,0,0);
  border-right: 10px;
  border-left: 0px;
  border-top: 0px;
  border-bottom: 0px;
}

.dialog button[name="cancel"] {
  width : 50%;
  float: right;
  height: 32px;
  font-family     : 'Roboto';  
  background-color: rgba(36,36,36,0.8);
  color           : #fff;
  border-color: rgba(0,0,0);
  border-right: 0px;
  border-left: 10px;
  border-top: 0px;
  border-bottom: 0px;
}