fx_version "adamant"
game "gta5"
description "FDG Vehicle Core Component"
version "1.0"

dependencies {
	"base_scripting"
}

client_scripts {
	"@base_scripting/reference/cinit.lua",
	"@base/client/utility.lua",
	"@base/client/markers.lua",
	"@base/client/interactionmenu.lua",
	"@polyzone/client.lua",
	"@base/client/qui.lua",
	"client/*.lua",
}

server_scripts {
	"@oxmysql/lib/MySQL.lua",
	"@base_scripting/reference/sinit.lua",
	"@base/server/utility.lua",
	"@base/server/organisations.lua",
	"@base_scripting/reference/xInventory.lua",
	"@polyzone/client.lua",
	"server/*.lua",
}

exports {
	"clean",
	"repairEngine",
	"lockpick",
	"impound",
	"checkEngineHealth",
	"flatbedVehicle",
	"regoCheck",
	"openBoot",
	"flipVehicle",
	-- Vehicle Locks
	"toggleVehicleLocks",
	"lockVehicle",
	"unlockVehicle",
	"lockVehicleBoot",
	"unlockVehicleBoot",
	"canUnlockVehicle",
	"getRawPlate",
	-- keys
	"ClearKeys",
	-- fuel
	"getVehicleFuel",
	"setVehicleFuel",
	"toggleSeatbelt",
	"setSeatbelt",
	-- Garages
	"OpenGarageMenu",
	"OpenRaidingGarageMenu",
	-- controls
	"setWindow",
	"setAllWindows",
	"toggleDoor",
	"getDoor",
	"setDoor",
	"toggleAllDoors",
	"toggleAirUI",
	-- nos
	"canVehicleTakeNos",
	"doesVehicleHaveNos",
	"setVehicleHasNos",
	"getVehicleNosAmount",
	"setVehicleNosAmount",
	-- motocross
	"IsDoingTricks"
}

server_exports {
	-- nos
	"getVehicleHasNos",
	"setVehicleHasNos",
	"getVehicleNosAmount",
	"setVehicleNosAmount"
}

ui_page "html/ui.html"

files {
	"html/ui.html",
	"html/main.css",
}

shared_scripts {
	"config.lua"
}
