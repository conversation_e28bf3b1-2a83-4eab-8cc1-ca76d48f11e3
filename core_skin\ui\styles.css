body {
    font-family: <PERSON><PERSON>;
    color: white;
    padding: 2%;
    /* background-color: rgba(50,50,50); */
    display: none;
}

/* Scrollbar */
::-webkit-scrollbar {width: 10px;}
::-webkit-scrollbar-track {background: rgba(0, 0, 0, 0.0);}
::-webkit-scrollbar-thumb {background: rgba(60, 60, 60, 0.9);}
::-webkit-scrollbar-thumb:hover {background: rgba(30, 30, 30, 0.9);}

/* Tabs Display */
ul {
    display: flex;
    list-style-type: none;
    margin: 0;
    padding: 0;
    overflow: hidden;
}
  
li {
    float: left;
    width: 100%;
}

li a {
    display: block;
    color: white;
    text-align: center;
    padding: 16px;
    text-decoration: none;
}

li a:hover {
    background-color: rgb(11,102,190);
}

li a[selected="selected"] {
    font-weight: bold;
    background-color: rgb(11,102,190);
}

/* Content Containers */
#content-container {
    overflow-y: scroll;
    height: 85%;
}

#container {
    text-align: center;
    position: absolute;    
    border-spacing: 0 20px;
    bottom: 5%;
    right: 5%;
    width: 25%;    
    background-color: rgba(20,20,20,0.85);
    border-radius: 6px;
    height: 90%;
}

/* Skin Table */
.skin-container {
    table-layout: fixed;
    padding: 15px;
    width: 100%;
    display: none;
}

.skin-container h3 {
    margin-top: 20px;
    margin-bottom: 5px;
}

.skin-container td {
    padding-left: 10px;
    padding-right: 10px;
}

.skin-container p {
    margin-bottom: 5px;
}

/* Skin Inputs */
.featureLabel {
    text-align: left;
    display: inline-block;
    width: 80%;
}

input[type=range]:focus {outline: none;}

input[type=range] {
    -webkit-appearance: none;

    background-image: -webkit-gradient(linear,
        left top, 
        right top, 
        color-stop(50%, rgb(11,102,190)),
        color-stop(50%, rgba(11,102,190,0.38)));

        border-radius: 5px;
        height: 5px;
        width: 100%;
}

input[type=range]::-webkit-slider-thumb {
    height: 15px;
    width: 15px;
    border-radius: 15px;
    background: rgb(11,102,190);
    cursor: pointer;
    -webkit-appearance: none;
}

input[type=text] {
    width: 35px;
    height: 35px;
    border: 0px solid transparent;
    text-align: center;
    float: right;
    background-color: rgba(60, 60, 60, 0.3);
    color: white;
}

.skin-container th {
    width: 50%;
}

.arrowSelector {
    border: 0px solid transparent;
    width: 50%;
}

.raise, .raiseLarge, .lower, .lowerLarge {
    width: 50px;
    height: 35px;
    border: 0px solid transparent;
    float: right;
    background-color: transparent;
    color: white;
    font-weight: bolder;
    font-size: 12;    
}

#rangeTooltip {
    background-color: rgba(40,40,40,1.0);
    position: absolute;
    color: white;
}

.modelButton {

    width: 100%;
    background-color: rgb(11,102,190);
    padding: 10px;
    color:white;
    border: 0px solid transparent;
    border-radius: 5px;
}

/* Button Panel */
.skinChangeButton {
    position: absolute;
    padding: 10px;
    background-color: rgb(11,102,190);
    color: white;
    border: 0px solid transparent;
    border-radius: 5px;
    margin-top: 15px;
    width: 90px;
    bottom: 25px;
}

#resetSkin {left: 25px;}
#saveSkin {right: 25px;}

/* Camera Bar */
#cameraBar {
    display: table;
    text-align: center;
    position: absolute;    
    border-spacing: 0 20px;
    top: 5%;
    left: 1%;
    width: 67%;    
    background-color: rgba(20,20,20,0.85);
    border-radius: 6px;
}

#cameraViewBar {
    display: table;
    position: absolute;    
    border-spacing: 0 20px;
    top: 70%;
    left: 1%;
    width: 25%;    
    background-color: rgba(20,20,20,0.85);
    border-radius: 6px;
}

#cameraBar button {
    float: left;
    display: table-cell;
    vertical-align: middle;
    margin-left: 7px;
    padding: 10px;
    background-color: rgb(11,102,190);
    color: white;
    border: 0px solid transparent;
    border-radius: 6px;
    width: 80px;
}

#cameraBar label {
    float: right;
    display: table-cell;
    vertical-align: middle;
    margin-right: 15px;
    color: white;
}

#rotationBar {
    width: 60%;
    margin-left: 15px;
}

#cameraViewBar button {
    display: table-cell;
    vertical-align: middle;
    margin-left: 7px;
    margin-bottom: 10px;
    padding: 10px;
    background-color: rgb(11,102,190);
    color: white;
    border: 0px solid transparent;
    border-radius: 6px;
    width: 80px;
}

.select {
    background:rgb(255,145,0);
}
.select.highlight {
    background:rgb(26, 255, 0);
}
