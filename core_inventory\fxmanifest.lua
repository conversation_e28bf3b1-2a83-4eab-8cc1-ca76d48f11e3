fx_version 'adamant'
game 'gta5'

client_scripts {
    "@base_scripting/reference/cinit.lua",
	"@base/client/animation.lua",
	"@base/client/utility.lua",
	"@base/client/qui.lua",
    "@main_roleplay/client/carry.lua",
    "client/*.lua",
}

exports {
    "loadExternalInventory",
    "showInventory",
    "hideInventory",
    "getWeapons",
    "getItem",
    "getItems",
    "toggleKeys",
    "getWeaponAttachment",
    "toggleHotbar",
    "displayItemsOnScreen"
}

server_exports {
    "RegisterDraggableItem",
}

server_scripts {
    "@oxmysql/lib/MySQL.lua",
    "@base_scripting/reference/sinit.lua",
	"@base/server/utility.lua",
    "server/*.lua",
}

ui_page "html/index.html"

files({
    "html/index.html",
    "html/*.png",
    "html/css/*.css",
    "html/js/*.js",
    "@stream_graphics/inventory/*.png",
})

dependencies {
    "base_scripting"
}