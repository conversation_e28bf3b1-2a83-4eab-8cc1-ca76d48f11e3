local makingCoffee = false
local coffeeDesc = nil

local coffeeMachines = {
    vector3(-635.29968261719, 235.99429321289, 81.887878417969), -- bean machine city
    -- vector3(1305.5500488281, 1121.3666992188, 105.***********)
}

CreateThread(function()
    for i, coords in ipairs(coffeeMachines) do
        CreateInteractableMarker(coords, function()
            if makingCoffee then
                return Base.Notification("You are already making coffee!")
            end

            exports.main_progressbar:start(18000, "Making Coffee")
            makingCoffee = true

            Base.Animation.ActionAnimation("anim@amb@business@meth@meth_monitoring_cooking@monitoring@", "check_guages_v1_monitor", false, true, false, 18,
                function()
                    TriggerServerEvent("makeCoffee", coffeeDesc)
                    exports.main_progressbar:stop()
                    makingCoffee = false
                    coffeeDesc = nil
                end,
                function()
                    exports.main_progressbar:stop()
                    makingCoffee = false
                    coffeeDesc = nil
                end
            )

            OpenDialog("Coffee Type (blank for none)", function(desc)
                coffeeDesc = desc
            end)
        end, { text = "Make Coffee", textRenderDistance = 1.2 }, { scale = 0.5, color = { r = 113, b = 91, g = 64, a = 255 } })
    end
end)
