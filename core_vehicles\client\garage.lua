local GARAGES = {}
local GARAGES_SHARED = {}
local map_blips = {}
local ghost_vehicles = {}
local local_cam = nil
local header = nil
local BLOCKED_GARAGES = {}
local BLOCKED_VEHICLES = {}
local TOW_BLOCKED_ITEMS = {}

local garages_shared_markers = {}
local garage_markers = {}

local STATE_LABELS = {
	[1] = "GARAGE",
	[2] = "OUT",
	[3] = "IMPOUNDED",
	[4] = "BEING TOWED"
}

function clearGhostVehicles()
	for i = #ghost_vehicles, 1, -1 do
		DeleteEntity(ghost_vehicles[i])
		ghost_vehicles[i] = nil
	end
end

function createGhostVehicle(data, coords, heading, cb)
	clearGhostVehicles()
	exports["base_vehicle"]:createFromModel(data.model, coords, heading + 0.0, {}, function(vehicle)
		SetEntityCollision(vehicle, false, false)
		SetEntityAlpha(vehicle, 230, false)
		FreezeEntityPosition(vehicle, true)
		Base.Vehicles.Props.Set(vehicle, data.vehicleProps)
		Base.Vehicles.Damage.Set(vehicle, data.damage)
		SetVehicleNumberPlateText(vehicle, data.plate)
		SetVehicleEngineOn(vehicle, true, true, false)
		SetVehicleLights(vehicle, 2)
		if data.rgb_headlights_color ~= "" then
			local headlightColour = json.decode(data.rgb_headlights_color)
			SetVehicleXenonLightsCustomColor(vehicle, headlightColour.r, headlightColour.g, headlightColour.b)
		end
		if data.rgb_underglow_color ~= "" then
			local underglowColour = json.decode(data.rgb_underglow_color)
			SetVehicleNeonLightsColour(vehicle, underglowColour.r, underglowColour.g, underglowColour.b)
		end
		table.insert(ghost_vehicles, vehicle)
		menu_check()
	end, true)
end

function createOwnedVehicle(id, coords, heading, cb)
	exports["base_vehicle"]:createFromId(id, coords, heading, cb)
end

function getTimeLabel(time, current)
	local timeLeft = time - current
	if (timeLeft <= 3600) then
		return math.ceil(timeLeft / 60) .. " Minutes(s)"
	else
		return math.ceil(timeLeft / 3600) .. " Hour(s)"
	end
end

function createMapBlip(id, coords, sprite, display, scale, colour, short, label)
	if (map_blips[id]) then
		RemoveBlip(map_blips[id])
		map_blips[id] = nil
	end
	local blip = AddBlipForCoord(coords)
	SetBlipSprite(blip, sprite)
	SetBlipDisplay(blip, display)
	SetBlipScale(blip, 0.6)
	SetBlipColour(blip, colour)
	SetBlipAsShortRange(blip, short)
	BeginTextCommandSetBlipName("STRING")
	AddTextComponentString(label)
	EndTextCommandSetBlipName(blip)
	map_blips[id] = blip
end

function menu_check()
	while (true) do
		Wait(100)
		if (not Base.UI.Menu.IsOpen("default", GetCurrentResourceName(), "garage_vehiclelist")) then
			Base.Camera.destroy("garage_cam", 0)
			clearGhostVehicles()
			break
		end
	end
end

local function createGarage(i, garage)
	if (not garage.isPropertyGarage) then
		if garage.Name == "Garage" then
			Base.Markers.Create("core_vehicles_garages_spawn:" .. i,
				{ x = garage.blipX, y = garage.blipY, z = garage.blipZ }, { r = 0, g = 255, b = 255 },
				"Access Garage",
				{
					x = garage.spawnX,
					y = garage.spawnY,
					z = garage.spawnZ,
					r = garage.spawnR + 0.0,
					location = garage.location,
					locationLabel = garage
						.locationLabel,
					boatGarage = garage.boatGarage,
					heliGarage = garage.heliGarage
				}, openGarageMenuSelection, false)
			Base.Markers.Create("core_vehicles_garages_delete:" .. i,
				{ x = garage.deleteX, y = garage.deleteY, z = garage.deleteZ },
				{ r = 255, g = 0, b = 0 }, "Store Vehicle", garage, StoreVehicle, true)
			if not garage.hideMapBlip then
				createMapBlip(i, vector3(garage.blipX, garage.blipY, garage.blipZ), 290, 4, 0.6,
					(garage.boatGarage and 50 or (garage.heliGarage and 69 or 38)), true,
					(garage.boatGarage and "Garage Boat" or (garage.heliGarage and "Garage Heli" or "Garage Car")))
			end
		elseif garage.Name == "Pound" then
			Base.Markers.Create("core_vehicles_pound_spawn:" .. i,
				{ x = garage.blipX, y = garage.blipY, z = garage.blipZ }, { r = 0, g = 255, b = 255 },
				"Access Impound",
				{
					x = garage.spawnX,
					y = garage.spawnY,
					z = garage.spawnZ,
					location = garage.location,
					locationLabel =
						garage.locationLabel
				},
				OpenPoundMenu)
			Base.Markers.Create("core_vehicles_pound_delete:" .. i,
				{ x = garage.deleteX, y = garage.deleteY, z = garage.deleteZ }, { r = 255, g = 0, b = 0 },
				"Impound Vehicle", garage, ImpoundVehicle, true)
			createMapBlip(i, vector3(garage.blipX, garage.blipY, garage.blipZ), 67, 4, 0.6, 64, true, "Impound Lot")
		elseif garage.Name == "Parking" then
			Base.Markers.Create("core_vehicles_parking_spawn:" .. i,
				{ x = garage.blipX, y = garage.blipY, z = garage.blipZ }, { r = 0, g = 255, b = 255 },
				"Access Parking",
				{
					x = garage.spawnX,
					y = garage.spawnY,
					z = garage.spawnZ,
					r = garage.spawnR,
					location = garage.location,
					locationLabel = garage
						.locationLabel
				}, OpenParkMenu)
			Base.Markers.Create("core_vehicles_parking_delete:" .. i,
				{ x = garage.deleteX, y = garage.deleteY, z = garage.deleteZ },
				{ r = 255, g = 0, b = 0 }, "Park Vehicle", garage, ParkVehicle, true)
			if not garage.hideMapBlip then
				createMapBlip(i, vector3(garage.blipX, garage.blipY, garage.blipZ), 357, 4,
					0.6, 64, true, "Parking Lot")
			end
		end
	end

	if garage.blockTow then
		BLOCKED_GARAGES[garage.location] = true
	end
end


RegisterDataSyncHandler("garages", function(data)
	for _, marker in ipairs(garage_markers) do
		RemoveInteractableMarker(marker)
	end
	garage_markers = {}



	GARAGES = data
	for i, garage in pairs(GARAGES) do
		local jobWhiteList = json.decode(garage.jobWhitelist)

		if jobWhiteList and next(jobWhiteList) then
			for _, job in ipairs(jobWhiteList) do
				if DoesPlayerHaveOrg(job) then
					createGarage(i, garage)
				end
			end
		else
			createGarage(i, garage)
		end
	end
end)

RegisterDataSyncHandler("garages_shared", function(data)
	for _, marker in ipairs(garages_shared_markers) do
		RemoveInteractableMarker(marker)
	end
	garages_shared_markers = {}

	GARAGES_SHARED = data

	for _, garage in pairs(GARAGES_SHARED) do
		local jobWhiteList = json.decode(garage.jobWhitelist)

		if jobWhiteList and next(jobWhiteList) then
			for _, job in ipairs(jobWhiteList) do
				if DoesPlayerHaveOrgPermission("sharedgarage", job) then
					local blipMarker = CreateInteractableMarker(vector3(garage.blipX, garage.blipY, garage.blipZ),
						function()
							OpenGarageMenu({
									x = garage.spawnX,
									y = garage.spawnY,
									z = garage.spawnZ,
									r = garage.spawnR + 0.0,
									location = garage.location,
									locationLabel = garage.locationLabel,
									heliGarage = garage.heliGarage
								},
								garage.locationLabel,
								nil, nil, nil, nil, true, job
							)
						end,
						{ text = "Shared Garage" },
						{ color = { r = 255, g = 255, b = 0, a = 200 } }
					)

					local deleteMarker = CreateInteractableMarker(
						vector3(garage.deleteX, garage.deleteY, garage.deleteZ), function()
							StoreVehicle(garage)
						end,
						{ text = "Store Vehicle", vehicleOnly = true, interactionDist = 4.0 },
						{ color = { r = 255, g = 0, b = 0, a = 200 } }
					)

					table.insert(garages_shared_markers, blipMarker)
					table.insert(garages_shared_markers, deleteMarker)

					break
				end
			end
		end
	end
end)

RegisterNetEvent("core_vehicles:placeStaffParkBlip")
AddEventHandler("core_vehicles:placeStaffParkBlip", function()
	local Event = true
	local stage = 1
	Wait(1000)
	TriggerEvent("fdg_ui:SendNotification",
		"Press <font color=\"green\">G</font> to place pull out blip or <font color=\"red\">E</font> to cancel")
	Citizen.CreateThread(function()
		local spawn, access, delete
		local pressed = false
		local playerPed = GetPlayerPed(-1)
		while Event do
			Citizen.Wait(0)
			if IsControlPressed(0, 38) then
				TriggerEvent("fdg_ui:SendNotification", "Blip placement <font color=\"red\">cancelled</font>")
				return
			end
			if IsControlPressed(0, 58) and stage == 1 and pressed == false then
				pressed = true
				access = GetEntityCoords(playerPed)
				TriggerEvent("fdg_ui:SendNotification",
					"Press <font color=\"green\">G</font> to place car spawn location")
				Wait(1000)
				stage = stage + 1
				pressed = false
			end
			if IsControlPressed(0, 58) and stage == 2 and pressed == false then
				pressed = true
				spawn = GetEntityCoords(playerPed)
				stage = stage + 1
				TriggerEvent("fdg_ui:SendNotification",
					"Press <font color=\"green\">G</font> to place car store location")
				Wait(1000)
				pressed = false
			end
			Wait(10)
			if IsControlPressed(0, 58) and stage == 3 and pressed == false then
				pressed = true
				delete = GetEntityCoords(playerPed)
				stage = stage + 1
				TriggerEvent("fdg_ui:SendNotification",
					"Press <font color=\"green\">G</font> to finish or <font color=\"red\">E</font> to cancel")
				Wait(1000)
				pressed = false
			end
			Wait(20)
			if IsControlPressed(0, 58) and stage == 4 then
				TriggerEvent("fdg_ui:SendNotification", "Parking blip <font color=\"green\">created</font>")
				Event = false
			end
		end
		local data = { spawnData = access, accessData = spawn, deleteData = delete }
		TriggerServerEvent("core_vehicles:addParkBlip", data)
	end)
end)

--get the adhoc garages incase they were not logged in when told to make it
onBaseReady(function()
	TriggerServerCallback("garage_get_adhoc_parking", function(adhoc_blips)
		for i, garage in pairs(adhoc_blips) do
			-- add all blips and make a marker
			local spawn = garage.spawnData
			local access = garage.accessData
			local delete = garage.deleteData
			local id = garage.id
			Base.Markers.Create("core_vehicles_parking_spawn:" .. id, { x = spawn.x, y = spawn.y, z = spawn.z },
				{ r = 0, g = 255, b = 255 }, "Access Parking",
				{
					x = access.x,
					y = access.y,
					z = access.z,
					r = 0.00,
					location = "TestLocation",
					locationLabel =
					"testttt"
				}, OpenParkMenu)
			Base.Markers.Create("core_vehicles_parking_delete:" .. id, { x = delete.x, y = delete.y, z = delete.z },
				{ r = 255, g = 0, b = 0 }, "Park Vehicle",
				{ location = "TestLocation" }, ParkVehicle, true)
		end
	end)
end)

RegisterNetEvent("core_vehicles:addParkBlipClient")
AddEventHandler("core_vehicles:addParkBlipClient", function(data)
	local spawn = data.spawnData
	local access = data.accessData
	local delete = data.deleteData
	local id = data.id
	Base.Markers.Create("core_vehicles_parking_spawn:" .. id, { x = spawn.x, y = spawn.y, z = spawn.z },
		{ r = 0, g = 255, b = 255 }, "Access Parking",
		{ x = access.x, y = access.y, z = access.z, r = 0.00, location = "TestLocation", locationLabel = "testttt" },
		OpenParkMenu)
	Base.Markers.Create("core_vehicles_parking_delete:" .. id, { x = delete.x, y = delete.y, z = delete.z },
		{ r = 255, g = 0, b = 0 }, "Park Vehicle",
		{ location = "TestLocation" }, ParkVehicle, true)
end)

RegisterDataSyncHandler("vehicle_tows", function(data)
	BLOCKED_VEHICLES = data
end)

RegisterDataSyncHandler("towBlockedItems", function(data)
	TOW_BLOCKED_ITEMS = data
end)

function OpenNewShareVehicleMenu(shareId, spawnPoint)
	TriggerServerCallback("core_vehicles:getOwned", function(ownedVehicles)
		local elements = {}
		local vehicles = {}

		if (#ownedVehicles == 0) then
			TriggerEvent("fdg_ui:SendNotification", "You have no vehicles")
			return
		end

		for _, vehicle in ipairs(ownedVehicles) do
			if GetVehicleClassFromName(vehicle.model) == 14 and spawnPoint and not spawnPoint.boatGarage then
				goto continue
			end
			if not vehicle.hidden and not vehicle.permaSeized then
				local displayState = (vehicle.shared == nil and "Not Shared" or vehicle.shared)
				local label = Base.Vehicles.GetLabel(vehicle.model) ..
					" | " .. vehicle.plate .. " [Shared: " .. displayState .. "]"
				vehicles["id_" .. vehicle.id] = vehicle
				table.insert(elements, { label = label, value = "id_" .. vehicle.id })
			end
			::continue::
		end

		table.sort(elements, function(a, b) return string.lower(a.label) < string.lower(b.label) end)

		Base.UI.Menu.Open("default", GetCurrentResourceName(), "garage_vehiclelist",
			{
				title = "All Vehicles",
				align = "bottom-right",
				elements = elements,
			},

			function(data, menu)
				menu.close()
				local vehicle = vehicles[data.current.value]
				TriggerServerCallback("core_vehicles:toggleShare", OpenNewShareVehicleMenu, shareId, vehicle)
			end,

			function(data, menu)
				menu.close()
				OpenGarageMenu(spawnPoint, shareId)
			end
		)
	end)
end

function OpenRaidingGarageMenu(spawnPoint, location, searchElements, searchVehicles)
	TriggerServerCallback("core_vehicles:getPhysicalVehicles", function(_vehicles, missing_fee, current_time)
		local elements = searchElements or {}
		local vehicles = searchVehicles or {}

		if searchElements == nil or searchVehicles == nil then
			for _, vehicle in ipairs(_vehicles) do
				if (
						not spawnPoint.boatGarage and not spawnPoint.heliGarage and not vehicle.hidden and not vehicle.permaSeized and GetVehicleClassFromName(vehicle.model) ~= 14 or spawnPoint.boatGarage and GetVehicleClassFromName(vehicle.model) == 14 or
						(spawnPoint.heliGarage and (GetVehicleClassFromName(vehicle.model) == 15 or GetVehicleClassFromName(vehicle.model) == 16))) then
					vehicle.vehicleProps = json.decode(vehicle.vehicle)
					vehicle.damage = json.decode(vehicle.damage or "[]")

					local engineHealth = (vehicle.damage and (vehicle.damage.engine and math.ceil((vehicle.damage.engine / 1000) * 100) or 100) or 100)
					local displayState = STATE_LABELS[vehicle.state]

					if (vehicle.model == GetHashKey("cartr16")) then
						vehicle.state = 1
						displayState = "UTILITY"
					end

					local label = vehicle.icname ..
						" | " .. Base.Vehicles.GetLabel(vehicle.model) .. " | " .. vehicle.plate .. ""
					vehicles["id_" .. vehicle.id] = vehicle

					if (vehicle.isSeized) then
						label = Base.Vehicles.GetLabel(vehicle.model) .. " | " .. vehicle.plate .. " - [SEIZED]"
					end

					table.insert(elements, { label = label, value = "id_" .. vehicle.id })
				end
			end
		end

		if (#elements == 0) then
			TriggerEvent("fdg_ui:SendNotification", "No vehicles on site")
			return
		end

		table.sort(elements, function(a, b) return string.lower(a.label) < string.lower(b.label) end)

		table.insert(elements, 1, { label = "Search", value = "search_vehicle" })

		if (not Base.Camera.exists("garage_cam")) then
			Base.Camera.create("garage_cam", 1,
				GetObjectOffsetFromCoords(spawnPoint.x, spawnPoint.y, spawnPoint.z, spawnPoint.r + 0.0, -3.5, 4.0, 2.0),
				vector3(-20, 0, spawnPoint.r + 225), 70.0)
			Base.Camera.render("garage_cam", true, 0)
		end

		if (vehicles[elements[1].value] ~= nil) then
			createGhostVehicle(vehicles[elements[1].value], vector3(spawnPoint.x, spawnPoint.y, spawnPoint.z),
				spawnPoint.r)
		end

		Citizen.CreateThread(function()
			menu_check()
		end)

		Base.UI.Menu.Open("default", GetCurrentResourceName(), "garage_vehiclelist",
			{
				title = "Stored Vehicles",
				align = "bottom-right",
				elements = elements,
			},

			function(data, menu)
				local vehicle = vehicles[data.current.value]

				if (data.current.value == "search_vehicle") then
					local searchedElements = {}
					OpenDialog("Search by vehicle name", function(input)
						for k, v in pairs(elements) do
							if string.find(string.lower(v.label), string.lower(input)) then
								table.insert(searchedElements, 1, v)
							end
						end
						OpenRaidingGarageMenu(spawnPoint, shared, searchedElements, vehicles)
						return
					end)
				end

				if (vehicle.isSeized) then
					TriggerEvent("fdg_ui:SendNotification",
						"This vehicle is still Seized for " .. getTimeLabel(tonumber(vehicle.seizedUntil), current_time))
					return
				end

				menu.close()
				clearGhostVehicles()
				createOwnedVehicle(vehicle.id, vector3(spawnPoint.x, spawnPoint.y, spawnPoint.z), spawnPoint.r,
					function(spawnedVehicle)
						Wait(500)
						TriggerServerEvent("core_vehicles:updateVehicle", NetworkGetNetworkIdFromEntity(spawnedVehicle),
							2, false, false, false, true)
						lockVehicle(spawnedVehicle)
						SetPedIntoVehicle(GetPlayerPed(-1), spawnedVehicle, -1)
					end)
			end,

			function(data, menu)
				menu.close()
			end,

			function(data, menu)
				local vehicle = vehicles[data.current.value]
				if (vehicle) then
					createGhostVehicle(vehicle, vector3(spawnPoint.x, spawnPoint.y, spawnPoint.z), spawnPoint.r)
				else
					clearGhostVehicles()
				end
			end
		)
	end, location)
end

function OpenGarageMenu(spawnPoint, shared, searchElements, searchVehicles, storedVehicles, header, orgSharedGarage, org)
	TriggerServerCallback((shared == nil and "core_vehicles:getOwned" or "core_vehicles:getShared"),
		function(_vehicles, missing_fee, current_time)
			local elements = searchElements or {}
			local vehicles = searchVehicles or {}

			local timeLeft = 0
			if (isDead) then
				TriggerEvent("fdg_ui:SendNotification", "You cannot retrieve a vehicle while dead.")
				return
			end

			if not spawnPoint then return end
			if searchElements == nil or searchVehicles == nil then
				for _, vehicle in ipairs(_vehicles) do
					if (
							(not spawnPoint.boatGarage and not spawnPoint.heliGarage and (GetVehicleClassFromName(vehicle.model) ~= 14 and GetVehicleClassFromName(vehicle.model) ~= 15 and GetVehicleClassFromName(vehicle.model) ~= 16)) or
							(spawnPoint.boatGarage and GetVehicleClassFromName(vehicle.model) == 14) or
							(spawnPoint.heliGarage and (GetVehicleClassFromName(vehicle.model) == 15 or GetVehicleClassFromName(vehicle.model) == 16))) and
						(not vehicle.hidden and not vehicle.permaSeized) then
						vehicle.vehicleProps = json.decode(vehicle.vehicle)
						vehicle.damage = json.decode(vehicle.damage or "[]")

						local engineHealth = (vehicle.damage and (vehicle.damage.engine and math.ceil((vehicle.damage.engine / 1000) * 100) or 100) or 100)
						local displayState = STATE_LABELS[vehicle.state]

						if (vehicle.model == GetHashKey("cartr16")) or (vehicle.model == GetHashKey("trailersmall")) or (vehicle.model == GetHashKey("trailers2")) then
							vehicle.state = 1
							displayState = "UTILITY"
						end

						local label = Base.Vehicles.GetLabel(vehicle.model) ..
							" | " .. vehicle.plate .. " [" .. displayState .. "] [Health: " .. engineHealth .. "%]"
						vehicles["id_" .. vehicle.id] = vehicle


						if vehicle.location ~= nil and (spawnPoint.location ~= nil or vehicle.state == 3) then
							local showVeh = true

							if storedVehicles then
								if spawnPoint.location == vehicle.location then
									showVeh = true
								else
									showVeh = false
								end
							end

							if showVeh then
								if spawnPoint.location == vehicle.location and vehicle.state == 1 then
									label = Base.Vehicles.GetLabel(vehicle.model) ..
										" | " .. vehicle.plate .. " - [GARAGE] " .. "[Health: " .. engineHealth .. "%]"
								elseif spawnPoint.location == vehicle.location and vehicle.state == 2 then
									label = Base.Vehicles.GetLabel(vehicle.model) .. " | " .. vehicle.plate .. " - [OUT]"
								else
									label = Base.Vehicles.GetLabel(vehicle.model) ..
										" | " ..
										vehicle.plate ..
										" - [" ..
										(vehicle.state == 3 and "IMPOUND" or "OTHER GARAGE") ..
										"] " .. (vehicle.locationLabel or vehicle.location)
								end

								if BLOCKED_GARAGES[vehicle.location] and spawnPoint.location ~= vehicle.location then
									label = Base.Vehicles.GetLabel(vehicle.model) ..
										" | " ..
										vehicle.plate ..
										" - [OTHER GARAGE] " .. (vehicle.locationLabel or vehicle.location)
								end

								if (vehicle.isSeized) then
									label = Base.Vehicles.GetLabel(vehicle.model) ..
										" | " .. vehicle.plate .. " - [SEIZED]"
								end

								if (vehicle.state == 4) then
									label = Base.Vehicles.GetLabel(vehicle.model) ..
										" | " .. vehicle.plate .. " - [BEING TOWED]"
								end

								if (vehicle.ownerBanned == 1) then
									label = Base.Vehicles.GetLabel(vehicle.model) ..
										" | " .. vehicle.plate .. " - [LOCKED]"
								end

								if vehicle.disableSpawn then
									label = Base.Vehicles.GetLabel(vehicle.model) ..
										" | " .. vehicle.plate .. " - [DISABLED]"
								end

								table.insert(elements, { label = label, value = "id_" .. vehicle.id })
							end
						elseif vehicle.location == nil then
							if vehicle.state == 1 then
								label = Base.Vehicles.GetLabel(vehicle.model) ..
									" | " .. vehicle.plate .. " - [GARAGE] " .. "[Health: " .. engineHealth .. "%]"
							elseif vehicle.state == 2 then
								label = Base.Vehicles.GetLabel(vehicle.model) .. " | " .. vehicle.plate .. " - [OUT]"
							end

							if vehicle.disableSpawn then
								label = Base.Vehicles.GetLabel(vehicle.model) ..
									" | " .. vehicle.plate .. " - [DISABLED]"
							end

							table.insert(elements, { label = label, value = "id_" .. vehicle.id })
						end
					end
				end
			end

			if (#elements == 0 and not shared) then
				TriggerEvent("fdg_ui:SendNotification", "You have no vehicles stored here.")
				return
			end

			table.sort(elements, function(a, b) return string.lower(a.label) < string.lower(b.label) end)

			table.insert(elements, 1, { label = "Search", value = "search_vehicle" })

			if (shared) then
				table.insert(elements, 2, { label = "Share New Vehicle..", value = "share_vehicle" })
			end

			if (not Base.Camera.exists("garage_cam")) then
				Base.Camera.create("garage_cam", 1,
					GetObjectOffsetFromCoords(spawnPoint.x, spawnPoint.y, spawnPoint.z, spawnPoint.r, -3.5, 4.0, 2.0),
					vector3(-20, 0, spawnPoint.r + 225), 70.0)
				Base.Camera.render("garage_cam", true, 0)
			end

			if (vehicles[elements[1].value] ~= nil) then
				createGhostVehicle(vehicles[elements[1].value], vector3(spawnPoint.x, spawnPoint.y, spawnPoint.z),
					spawnPoint.r)
			end

			Citizen.CreateThread(function()
				menu_check()
			end)

			Base.UI.Menu.Open("default", GetCurrentResourceName(), "garage_vehiclelist",
				{
					title = header,
					align = "bottom-right",
					elements = elements,
				},

				function(data, menu)
					if (data.current.value == "search_vehicle") then
						local searchedElements = {}
						OpenDialog("Search by vehicle name", function(input)
							for k, v in pairs(elements) do
								if string.find(string.lower(v.label), string.lower(input)) then
									table.insert(searchedElements, 1, v)
								end
							end
							OpenGarageMenu(spawnPoint, shared, searchedElements, vehicles)
							return
						end)
					end
					if (data.current.value == "share_vehicle") then
						menu.close()
						OpenNewShareVehicleMenu(shared, spawnPoint)
						return
					end

					local vehicle = vehicles[data.current.value]

					if vehicle.disableSpawn then
						TriggerEvent("fdg_ui:SendNotification", "This vehicle has been disabled")
						return
					end

					if (vehicle.isSeized) then
						TriggerEvent("fdg_ui:SendNotification",
							"This vehicle is still Seized for " ..
							getTimeLabel(tonumber(vehicle.seizedUntil), current_time))
						return
					end

					if (vehicle.isStageWB) then
						TriggerEvent("fdg_ui:SendNotification",
							"This vehicle is still being built for " ..
							getTimeLabel(tonumber(vehicle.mechUntil), current_time))
						return
					end

					if (vehicle.isRaiding) then
						TriggerEvent("fdg_ui:SendNotification", "This vehicle is currently being raided by the police!")
						return
					end

					if (vehicle.ownerBanned == 1) then
						TriggerEvent("fdg_ui:SendNotification",
							"This vehicle is owned by a player who is currently deported. You won't be able to use it until they can return.")
						return
					end

					if vehicle.state == 3 then
						TriggerEvent("fdg_ui:SendNotification",
							"This vehicle is in the impound. Go to the marked impound lot to get it")
						for _, garage in pairs(GARAGES) do
							if vehicle.locationLabel == garage.locationLabel then
								SetNewWaypoint(garage.blipX, garage.blipY)
							end
						end
						return
					end

					if vehicle.state == 4 then
						if tonumber(vehicle.towTimer) - current_time < 0 then
							TriggerServerEvent("core_vehicles:overdueTow", vehicle)
						else
							TriggerEvent("fdg_ui:SendNotification",
								"This vehicle is being towed to " ..
								(vehicle.locationLabel or vehicle.location) ..
								". It will be ready in " .. getTimeLabel(tonumber(vehicle.towTimer), current_time))
							return
						end
					end

					if BLOCKED_GARAGES[spawnPoint.location] and spawnPoint.location ~= vehicle.location then
						Base.Notification("You can't tow vehicles to this garage!")
						return
					end

					if BLOCKED_GARAGES[vehicle.location] and spawnPoint.location ~= vehicle.location then
						Base.Notification("You can't tow vehicles from this garage!")
						return
					end

					if (vehicle.location ~= nil and spawnPoint.location ~= nil) and (spawnPoint.location ~= vehicle.location) then
						local _hasKeys = exports["main_roleplay"]:hasKeyToProperty(vehicle.location)
						local g = string.match(vehicle.location or "", "car_")
						local g2 = string.match(vehicle.location or "", "gang_")
						if (_hasKeys or g or g2) and (vehicle.state == 1 or vehicle.state == 2) and json.decode(vehicle.inventory) ~= nil then
							for k, v in pairs(json.decode(vehicle.inventory)) do
								if TOW_BLOCKED_ITEMS[v.name] then
									Base.Notification("This vehicle's inventory contains an untowable item!")
									return
								end
							end
						end
					end

					if vehicle.location ~= nil and spawnPoint.location ~= nil then
						if spawnPoint.location ~= vehicle.location then
							Base.UI.Menu.Open("default", GetCurrentResourceName(), "garage_missing_tow",
								{
									title = "Tow Vehicle",
									align = "bottom-right",
									elements = {
										{ label = "Tow here ($" .. missing_fee .. ")", value = 'tow' },
										{ label = "Set GPS to this Garage",            value = 'gps' },
										{ label = "Cancel",                            value = false }
									},
								},

								function(data5, menu5)
									if data5.current.value == 'tow' then
										TriggerServerEvent("core_vehicles:towVehicle", vehicle, spawnPoint.location)
										Base.UI.Menu.CloseAll()
									elseif data5.current.value == 'gps' then
										for _, garage in pairs(GARAGES) do
											if vehicle.locationLabel == garage.locationLabel then
												SetNewWaypoint(garage.blipX, garage.blipY)
												Base.UI.Menu.CloseAll()
											end
										end
									else
										menu5.close()
									end
								end,
								function(data5, menu5)
									menu5.close()
								end
							)

							return
						end
					end

					if vehicle.state == 2 then
						Base.UI.Menu.Open("default", GetCurrentResourceName(), "garage_towback",
							{
								title = "Tow Back Vehicle",
								align = "bottom-right",
								elements = {
									{ label = "Tow Back ($2000)", value = true },
									{ label = "Cancel",           value = false }
								},
							},

							function(data2, menu2)
								if data2.current.value then
									menu2.close()
									menu.close()
									clearGhostVehicles()
									createOwnedVehicle(vehicle.id, vector3(spawnPoint.x, spawnPoint.y, spawnPoint.z),
										spawnPoint.r, function(spawnedVehicle)
											if orgSharedGarage then
												Entity(spawnedVehicle).state:set('job', org, true)
											end
											Wait(500)
											TriggerServerEvent("core_vehicles:updateVehicle",
												NetworkGetNetworkIdFromEntity(spawnedVehicle), 2, false, false, false,
												true)
											lockVehicle(spawnedVehicle)
											lockVehicleBoot(spawnedVehicle, true)
											SetPedIntoVehicle(GetPlayerPed(-1), spawnedVehicle, -1)
											TriggerServerEvent("core_vehicles:payVehicleFee", 2000)
										end)
								else
									menu2.close()
								end
							end,

							function(data2, menu2)
								menu2.close()
							end
						)
					else
						menu.close()
						clearGhostVehicles()
						createOwnedVehicle(vehicle.id, vector3(spawnPoint.x, spawnPoint.y, spawnPoint.z), spawnPoint.r,
							function(spawnedVehicle)
								if orgSharedGarage then
									Entity(spawnedVehicle).state:set('job', org, true)
								end
								Wait(500)
								TriggerServerEvent("core_vehicles:updateVehicle",
									NetworkGetNetworkIdFromEntity(spawnedVehicle), 2, false, false, false, true)
								lockVehicle(spawnedVehicle)
								lockVehicleBoot(spawnedVehicle, true)
								SetPedIntoVehicle(GetPlayerPed(-1), spawnedVehicle, -1)
							end)
					end
				end,

				function(data, menu)
					menu.close()
					Base.Camera.destroy("garage_cam", 0)
					clearGhostVehicles()
				end,

				function(data, menu)
					local vehicle = vehicles[data.current.value]
					if (vehicle) then
						createGhostVehicle(vehicle, vector3(spawnPoint.x, spawnPoint.y, spawnPoint.z), spawnPoint.r)
					else
						clearGhostVehicles()
					end
				end
			)
		end, shared)
end

function OpenPoundMenu(spawnPoint)
	TriggerServerCallback("core_vehicles:getOwned", function(_vehicles, missing_fee, current_time)
		local elements = {}
		local vehicles = {}

		for _, vehicle in ipairs(_vehicles) do
			if vehicle.state == 3 or vehicle.isSeized then
				if (vehicle.locationLabel == nil or vehicle.location == spawnPoint.location) then
					vehicle.vehicleProps = json.decode(vehicle.vehicle)
					vehicle.damage = json.decode(vehicle.damage)

					local label = vehicle.modelName ..
						" | " ..
						vehicle.plate ..
						" [Health: " .. math.ceil(((vehicle.damage['engine'] or 1000) / 1000) * 100) .. "%]"
					vehicles["id_" .. vehicle.id] = vehicle
					table.insert(elements,
						{ label = label, value = "id_" .. vehicle.id, seizedUntil = vehicle.seizedUntil })
				else
					local label = vehicle.modelName ..
						" | " .. vehicle.plate .. " [OTHER POUND: " .. vehicle.locationLabel .. "]"
					table.insert(elements, { label = label, value = 'other' })
				end
			end
		end

		Base.UI.Menu.Open("default", GetCurrentResourceName(), "garage_vehiclelist",
			{
				title = "Impounded Vehicles ($" .. 5000 .. ")",
				align = "bottom-right",
				elements = elements,
			},

			function(data, menu)
				if (data.current.seizedUntil and tonumber(data.current.seizedUntil) > current_time) then
					TriggerEvent("fdg_ui:SendNotification",
						"This vehicle is still seized for " ..
						getTimeLabel(tonumber(data.current.seizedUntil), current_time))
					return
				end

				if (data.current.value == 'other') then
					TriggerEvent("fdg_ui:SendNotification", "Vehicle at another Garage!")
					return
				end

				local vehicle = vehicles[data.current.value]
				createOwnedVehicle(vehicle.id, vector3(spawnPoint.x, spawnPoint.y, spawnPoint.z), spawnPoint.r,
					function(spawnedVehicle)
						SetPedIntoVehicle(GetPlayerPed(-1), spawnedVehicle, -1)
						lockVehicle(spawnedVehicle)
						lockVehicleBoot(spawnedVehicle, true)
						TriggerServerEvent("core_vehicles:claimImpoundedVehicle", vehicle)
					end)
				menu.close()
			end,

			function(data, menu)
				menu.close()
			end
		)
	end)
end

function StoreVehicle(selectedGarage)
	local ped = PlayerPedId()
	local veh = GetVehiclePedIsUsing(ped)
	local class = GetVehicleClass(veh)


	--Determine if player sold drugs within 5 minutes
	if (LocalPlayer.state.last_sold and tonumber(LocalPlayer.state.last_sold) + 300000 > tonumber(GetGameTimer())) then
		--Calculate the time left until player can pull car
		timeLeft = (300000 - (tonumber(GetGameTimer()) - LocalPlayer.state.last_sold)) / 1000
		--Send player message regarding error
		TriggerEvent("fdg_ui:SendNotification",
			"You cannot store a vehicle so soon after selling drugs. Please wait " .. math.floor(timeLeft) .. " seconds.")
		return
	end




	if (selectedGarage.boatGarage and class ~= 14) then
		TriggerEvent("fdg_ui:SendNotification", "Cannot store this vehicle here.")
		return
	end

	if (selectedGarage.heliGarage and (class ~= 15 and class ~= 16)) then
		TriggerEvent("fdg_ui:SendNotification", "Cannot store this vehicle here.")
		return
	end

	if ((not selectedGarage.heliGarage and not selectedGarage.boatGarage) and (class == 14 or class == 15 or class == 16)) then
		TriggerEvent("fdg_ui:SendNotification", "Cannot store this vehicle here.")
		return
	end

	exports["base_vehicle"]:isOwner(veh, function(isOwner, hasKeys)
		if (isOwner or hasKeys) then
			local props = Base.Vehicles.Props.Get(veh)
			local damage = Base.Vehicles.Damage.Get(veh)
			local location = selectedGarage.location
			local hasTrailer, trailer = GetVehicleTrailerVehicle(veh)

			if hasTrailer then
				local tProps = Base.Vehicles.Props.Get(trailer)
				local tDamage = Base.Vehicles.Damage.Get(trailer)
				TriggerServerEvent("core_vehicles:storeVehicle", NetworkGetNetworkIdFromEntity(trailer), tProps, tDamage,
					location)
			end
			TriggerEvent("fdg_ui:SendNotification", "You have stored: " .. GetVehicleNumberPlateText(veh))
			TriggerServerEvent("core_vehicles:storeVehicle", NetworkGetNetworkIdFromEntity(veh), props, damage, location)
		else
			TriggerEvent("fdg_ui:SendNotification", "You don't own this vehicle")
		end
	end)
end

function ImpoundVehicle(garage)
	if CurrentlyTowedVehicle then
		local damage = Base.Vehicles.Damage.Get(CurrentlyTowedVehicle)
		TriggerServerEvent("core_vehicles:claimMarkedVehicle", NetworkGetNetworkIdFromEntity(CurrentlyTowedVehicle),
			false, damage, garage.location)
		CurrentlyTowedVehicle = nil
	end
end

function ParkVehicle(garage)
	--Determine if player sold drugs within 5 minutes
	if (LocalPlayer.state.last_sold and tonumber(LocalPlayer.state.last_sold) + 300000 > tonumber(GetNetworkTime())) then
		--Calculate the time left until player can pull car
		timeLeft = (300000 - (tonumber(GetNetworkTime()) - LocalPlayer.state.last_sold)) / 1000
		--Send player message regarding error
		TriggerEvent("fdg_ui:SendNotification",
			"You cannot store a vehicle so soon after selling drugs. Please wait " .. math.floor(timeLeft) .. " seconds.")
		return
	end

	local ped = PlayerPedId()
	local veh = GetVehiclePedIsIn(ped, false)
	local model = GetEntityModel(veh)
	local props = Base.Vehicles.Props.Get(veh)
	local damage = Base.Vehicles.Damage.Get(veh)
	exports["base_vehicle"]:isOwner(veh, function(isOwner, hasKeys)
		if (isOwner or hasKeys) then
			TriggerEvent("fdg_ui:SendNotification", "You have stored: " .. props.plate)
			TriggerServerEvent("core_vehicles:addParkedVehicle", NetworkGetNetworkIdFromEntity(veh), model, props, damage,
				garage.location)
		else
			TriggerEvent("fdg_ui:SendNotification", "You don't own this vehicle")
		end
	end)
end

function OpenParkMenu(spawnPoint)
	TriggerServerCallback("core_vehicles:getParkedVehicles", function(_vehicles)
		local elements = {}
		local vehicles = {}

		local timeLeft = 0

		if (isDead) then
			TriggerEvent("fdg_ui:SendNotification", "You cannot retrieve a vehicle while dead.")
			return
		end


		for _, vehicle in pairs(_vehicles) do
			if (spawnPoint.location == vehicle.location) then
				table.insert(elements, { label = Base.Vehicles.GetLabel(vehicle.model), value = "id_" .. vehicle.id })
				vehicles["id_" .. vehicle.id] = vehicle
			end
		end

		if (#elements == 0) then
			TriggerEvent("fdg_ui:SendNotification", "You have no parked vehicles")
			return
		end

		Base.UI.Menu.Open("default", GetCurrentResourceName(), "parking_vehlist",
			{
				title = "Vehicles",
				align = "bottom-right",
				elements = elements,
			},
			function(data, menu)
				menu.close()
				local vehicle = vehicles[data.current.value]
				createOwnedVehicle(vehicle.id, vector3(spawnPoint.x, spawnPoint.y, spawnPoint.z), spawnPoint.r,
					function(spawned)
						SetPedIntoVehicle(PlayerPedId(), spawned, -1)
						lockVehicle(spawned)
						lockVehicleBoot(spawned, true)
						TriggerServerEvent("core_vehicles:retrieveParkedCar", vehicle.id)
					end)
			end,
			function(data, menu)
				menu.close()
			end
		)
	end)
end

function openGarageMenuSelection(spawnPoint, location, searchElements, searchVehicles)
	Base.UI.Menu.Open('default', GetCurrentResourceName(), 'garage_selector',
		{
			title    = 'Garage Options',
			align    = 'bottom-right',
			elements = { { label = "Stored Vehicles", value = "storedVeh" }, { label = "All Vehicles", value = "allVeh" } },
		},
		function(data, menu)
			if data.current.value == "storedVeh" then
				OpenGarageMenu(spawnPoint, location, searchElements, searchVehicles, true, "Stored Vehicles")
			end
			if data.current.value == "allVeh" then
				OpenGarageMenu(spawnPoint, location, searchElements, searchVehicles, nil, "All Vehicles")
			end

			menu.close()
		end,
		function(data, menu)
			menu.close()
		end
	)
end
