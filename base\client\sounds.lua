local function generateUuid()
    local template = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'

    return string.gsub(template, '[xy]', function(c)
        local v = (c == 'x') and math.random(0, 0xf) or math.random(8, 0xb)
        return string.format('%x', v)
    end)
end

---@class SoundOptions
---@field isLooped boolean?
---@field volume number?

---Plays a custom sound effect stored in base_sounds, eminating from the specified entity.
---@param entity number
---@param soundName string
---@param isLooped boolean?
---@param maxDistance number?
---@param targetId number?
---@param volumeMult number?
---@return string
function PlaySoundEffectOnEntity(entity, soundName, isLooped, maxDistance, targetId, volumeMult)
    local entityNetId = NetworkGetNetworkIdFromEntity(entity)
    local soundId = generateUuid()

    TriggerServerEvent("base_sounds:playOnEntity", entityNetId, soundId, soundName, isLooped, maxDistance, targetId, volumeMult)

    return soundId
end

---Plays a custom sound effect stored in base_sounds, eminating from the specified location.
---@param location vector3|string Either coords or a preset location defined in base_sounds.
---@param soundName string
---@param isLooped boolean?
---@param maxDistance number?
---@param targetId number?
---@param volumeMult number?
---@return string
function PlaySoundEffectAtLocation(location, soundName, isLooped, maxDistance, targetId, volumeMult)
    local soundId = generateUuid()

    TriggerServerEvent("base_sounds:play", soundId, soundName, isLooped, location, maxDistance, targetId, volumeMult)

    return soundId
end

---Stops a custom sound effect stored in base_sounds.
---@param soundId string
---@param targetId number?
function StopSoundEffect(soundId, targetId)
    TriggerServerEvent("base_sounds:stop", soundId, targetId)
end
