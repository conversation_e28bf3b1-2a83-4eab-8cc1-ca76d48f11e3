-- Static Inventory

animations = {}
proximityItems = {}
closeItems = {}
closeObjects = {}
inventoryOpen = false
usingItem = false
isCrafting = false

clothingOptions = {
	{ label = "Shirt",   anim = "shirt",   index = 6 },
	{ label = "Pants",   anim = "pants",   index = 7 },
	{ label = "Shoes",   anim = "shoes",   index = 8 },
	{ label = "Hat",     anim = "hat",     index = 1 },
	{ label = "Gloves",  anim = "gloves",  index = 9 },
	{ label = "Bag",     anim = "bag",     index = 10 },
	{ label = "Vest",    anim = "vest",    index = 5 },
	{ label = "Mask",    anim = "mask",    index = 2 },
	{ label = "Glasses", anim = "glasses", index = 3 },
	{ label = "Chain",   anim = "chain",   index = 4 },
}

function getInstance()
	return LocalPlayer.state.instanceID or "global"
end

CreateThread(function()
	RegisterDataSyncHandler("animations", function(animData)
		animations = {}

		for _, v in pairs(animData) do
			if animations[v.category] == nil then
				animations[v.category] = {}
			end
			animations[v.category][v.label] = v
		end

		for _, v in pairs(clothingOptions) do
			if animations["Clothing"] == nil then
				animations["Clothing"] = {}
			end
			animations["Clothing"][v.label] = v
		end

		SendNUIMessage({ animations = animations })
	end, "main_roleplay")
end)

onBaseReady(function()
	TriggerServerEvent("inventory:syncDroppedItems")

	Wait(500)

	PlayerData = Base.GetPlayerData()
	refreshPlayerData()
	SendNUIMessage({ inventory = "player-inventory", items = PlayerData.inventory })

	for _, account in ipairs(PlayerData.accounts) do
		if account.name == "money" then
			SendNUIMessage({ inventory = "player-inventory", money = account.money })
		elseif account.name == "black_money" then
			SendNUIMessage({ inventory = "player-inventory", dirty = account.money })
		end
	end
end)

getWeapons = function()
	local weapons = {}

	PlayerData = Base.GetPlayerData()
	for _, item in ipairs(PlayerData.inventory) do
		if item.type == "weapon" then
			table.insert(weapons, item)
		end
	end

	return weapons
end

getItems = function()
	local items = {}
	PlayerData = Base.GetPlayerData()

	for _, item in ipairs(PlayerData.inventory) do
		if item.type == "item" then
			table.insert(items, item)
		end
	end

	return items
end

getItem = function(name)
	PlayerData = Base.GetPlayerData()

	if type(name) == "number" then
		return PlayerData.inventory[name], name
	else
		for i, item in ipairs(PlayerData.inventory or {}) do
			if item.name == name then
				return item, i
			end
		end
	end
	return false
end

-- use items

local eatingAnimation = function()
	local ped = PlayerPedId()
	Base.Animation.LoadDict("mp_player_inteat@pnq")
	Base.Animation.LoadDict("facials@gen_female@base")
	TaskPlayAnim(ped, "mp_player_inteat@pnq", "intro", 15.0, -0.0, -1, 48, 0.0, 0, 0, 0)
	while GetEntityAnimCurrentTime(ped, "mp_player_inteat@pnq", "intro") < 0.90 do
		Wait(0)
		if GetEntityAnimCurrentTime(ped, "mp_player_inteat@pnq", "intro") == 0.0 then
			return
		end
	end
	PlayFacialAnim(ped, "eating_1", "facials@gen_female@base")
	TaskPlayAnim(ped, "mp_player_inteat@pnq", "outro", 1.0, -15.0, -1, 48, 0.0, 0, 0, 0)
end

local useItemNoAnim = function(text, time, onComplete)
	local total = time * 1000
	local start = GetGameTimer()
	exports["main_progressbar"]:start(total, text)
	while (GetGameTimer() - start < total) do
		Wait(0)
	end
	if (onComplete) then
		onComplete()
	end
end

local useItemAnim = function(text, lib, anim, repeating, frozen, upper, time, onComplete, onCancel)
	exports["main_progressbar"]:start((time * 1000), text)
	local action = Base.Animation.ActionAnimation(lib, anim, repeating, frozen, upper, time, onComplete,
		function()
			exports["main_progressbar"]:stop()
			if (onCancel) then
				onCancel()
			end
		end
	)
	if action == "fail" then
		exports["main_progressbar"]:stop()
		isCrafting = false
		usingItem = false
		return
	end
end

useItem = function(slot) -- this is full aids, but i full don't care :D
	if isDead or usingItem then return end
	local invItem = PlayerData.inventory[tonumber(slot)]
	if (invItem) then
		usingItem = true
		-- effect check
		if (invItem.effect and invItem.effectTime) then
			if (Base.Effects.isEffectMaxed(invItem.effect)) then
				usingItem = false
				TriggerEvent("fdg_ui:SendNotification", "You cannot take any more.")
				return
			end
		end

		if (invItem.type == "item" and invItem.useTime) then
			if (invItem.animationName and invItem.animationLibrary and invItem.animationLibrary ~= "prop") then
				useItemAnim("Using " .. invItem.label, invItem.animationLibrary, invItem.animationName, invItem.animationRepeat or false,
					invItem.animationFrozen or false, invItem.animationUpperBody or false, invItem.useTime, function()
						TriggerServerEvent("inventory:useItem", false, tonumber(slot))
						usingItem = false
						if (invItem.animationConsume) then -- facial animation
							Base.Animation.LoadDict("facials@gen_female@base")
							PlayFacialAnim(ped, "eating_1", "facials@gen_female@base")
							RemoveAnimDict("facials@gen_female@base")
						end
					end,
					function()
						usingItem = false
					end)
			elseif (invItem.label == "Joint") then
				-- TODO make the joint prop, appear to a convert from useitemanim for the base animations i.e smoke3
				useItemAnim("Using " .. invItem.label, "amb@world_human_aa_smoke@male@idle_a", "idle_c", invItem.animationRepeat or false,
					invItem.animationFrozen or false, invItem.animationUpperBody or false, invItem.useTime, function()
						TriggerServerEvent("inventory:useItem", false, tonumber(slot))
						usingItem = false
					end,
					function()
						usingItem = false
					end)
			else
				if (invItem.animationLibrary == "prop") then
					exports["main_roleplay"]:PlayAnim(invItem.animationName, true)
					-- Citizen.Wait(1000)
					-- print(IsEntityPlayingAnim(GetPlayerPed(PlayerId()), "amb@world_human_aa_smoke@male@idle_a", "idle_c", 3))
				end
				useItemNoAnim("Using " .. invItem.label, invItem.useTime, function()
					ClearPedSecondaryTask(PlayerPedId())
					TriggerServerEvent("inventory:useItem", false, tonumber(slot))
					usingItem = false
				end)
			end
		else
			if isCuffed then
				usingItem = false
			else
				if (invItem.food or invItem.water) and (IsPedRagdoll(GetPlayerPed(-1))) then
					usingItem = false
					TriggerEvent("fdg_ui:SendNotification", "You cannot use this right now.")
					return
				end

				if (invItem.type) == "weapon" and LocalPlayer.state.disarmed == true then
					usingItem = false
					TriggerEvent("fdg_ui:SendNotification", "You cannot use weapons whilst disarmed")
					TriggerEvent("fdg_ui:SendNotification", "You can rearm at a vehicle you have keys for, or by entering a property")
					return
				end
				usingItem = false
				TriggerServerEvent("inventory:useItem", false, tonumber(slot))
			end
		end
	end
end

showLicense = function(slot)
	if isDead or usingItem then return end
	local invItem = PlayerData.inventory[tonumber(slot)]
	if (invItem) then
		TriggerEvent("docs:showDocument", invItem)
	end
end


local sendToExternal = false
local pullFromExternal = false
local closeExternal = false

showInventory = function()
	if (not LocalPlayer.state.unconscious) then
		PlayerData = Base.GetPlayerData()
		inventoryOpen = true
		TriggerScreenblurFadeIn(1000)
		SetNuiFocus(true, true)
		SetNuiFocusKeepInput(true)
		SetCursorLocation(0.5, 0.5)

		if not (sendToExternal or pullFromExternal) then
			refreshProximityInventory()
		end

		SendNUIMessage({ showInventory = true })
		TriggerEvent("onInventoryOpen")
		QUI.TriggerEvent("toggleEffectMini", false)
	end
end

hideInventory = function()
	inventoryOpen = false
	Base.UI.Menu.CloseAll()
	TriggerScreenblurFadeOut(1000)
	SetNuiFocus(false, false)
	SetNuiFocusKeepInput(false)
	SendNUIMessage({ showInventory = false })

	sendToExternal = false
	pullFromExternal = false

	if closeExternal then
		closeExternal()
		closeExternal = false
	end

	SendNUIMessage({ inventory = "external-inventory", label = "Proximity", money = 0, dirty = 0, items = {}, showIndividualWeight = false })
	TriggerEvent("onInventoryClose")
	QUI.TriggerEvent("toggleEffectMini", true)
end

loadExternalInventory = function(label, items, money, dirty, capacity, sendCb, pullCb, closeCb, showIndividualWeight)
	local showCash = false
	if not money and not dirty then showCash = true end

	SendNUIMessage({ inventory = "external-inventory", label = label, money = money, dirty = dirty, items = items, capacity = capacity, isProximity = showCash, showIndividualWeight =
	showIndividualWeight })

	sendToExternal = sendCb
	pullFromExternal = pullCb
	closeExternal = closeCb

	if not inventoryOpen then showInventory() end
end

RegisterNUICallback("transferInvItem", function(data)
	local itemIndex = tonumber(data.index) or data.index
	local amount = tonumber(data.count)

	if LocalPlayer.state.unconscious then return end
	if LocalPlayer.state.carrying then return end

	if type(itemIndex) == "number" then itemIndex = itemIndex + 1 end

	if (data.newInventory == "player-inventory" and data.oldInventory == "player-inventory") then
		TriggerServerEvent("inventory:combineItems", tonumber(data.index) + 1 or data.index, tonumber(data.newIndex) + 1 or data.newIndex)
	else
		if data.newInventory == "external-inventory" then
			-- Don't let people drop weapons they are using
			local item = getItem(itemIndex)
			if item and selectedWeapon and item.name == selectedWeapon then
				Base.Notification("You can't drop a weapon you are using")
				return false
			end

			if sendToExternal then
				sendToExternal(itemIndex, amount)
			else
				if isCuffed then
					return
				end

				Base.Animation.StartForcedAnim(PlayerPedId(), "pickup_object", "putdown_low", false, false, true, false)

				-- Don't let people drop from cars
				if IsPedInAnyVehicle(PlayerPedId(), true) then
					Base.Notification("You can't drop items from a vehicle")
					return false
				end


				TriggerServerEvent("inventory:dropItem", getInstance(), itemIndex, amount)
				resyncEquipedWeapon()

				if isCrafting then
					Base.Animation.ForceCancelOutcome(PlayerPedId())
				end

				Wait(1500)
				ClearPedSecondaryTask(PlayerPedId())
			end
		elseif data.oldInventory == "external-inventory" then
			if pullFromExternal then
				pullFromExternal(itemIndex, amount)
			else
				Base.Animation.StartForcedAnim(PlayerPedId(), "pickup_object", "putdown_low", false, false, true, false)
				TriggerServerEvent("inventory:pickupItem", getInstance(), data.proxIndex, amount)
				Wait(1500)
				ClearPedSecondaryTask(PlayerPedId())
			end
		end
	end
end)

RegisterNUICallback("labelItem", function(data)
	TriggerServerEvent("inventory:labelItem", tonumber(data.itemIndex) + 1, data.text)
end)

RegisterNUICallback("useItem", function(item)
	useItem(tonumber(item) + 1)
end)

RegisterNUICallback("showLicense", function(item)
	showLicense(tonumber(item) + 1)
end)

RegisterNUICallback("removeAttachment", function(data)
	if isDead then return end
	TriggerServerEvent("inventory:removeAttachment", data.item + 1, data.attachment)
end)

Citizen.CreateThread(function()
	while true do
		Wait(0)
		if inventoryOpen then
			Base.Player.blockActions(true)
			DisableControlAction(1, 1, true)
			DisableControlAction(1, 2, true)
			DisableControlAction(1, 24, true)
			DisableControlAction(1, 38, true)
			DisableControlAction(1, 69, true)
			DisableControlAction(1, 92, true)
			DisableControlAction(1, 257, true)
			DisableControlAction(1, 142, true)
			DisableControlAction(1, 245, true)
		end

		if LocalPlayer.state.unconscious and inventoryOpen then
			hideInventory()
		end
	end
end)

RegisterNetEvent("inventory:clientSync")
AddEventHandler('inventory:clientSync', function(name, inv, money, dirty, capacity)
	if name == "playerinventory-" .. GetPlayerServerId(PlayerId()) then
		Base.SetPlayerData("inventory", inv)

		SendNUIMessage({ inventory = "player-inventory", items = inv, money = money, dirty = dirty, capacity = capacity })

		-- Remove active weapon if no longer in inventory
		if selectedWeapon and not getItem(selectedWeapon) then
			playerPed = PlayerPedId()
			RemoveAllPedWeapons(playerPed, true)
			GiveWeaponToPed(playerPed, "WEAPON_UNARMED", 1, false, false)
		end
	end
end)

RegisterNetEvent("inventory:showInventory")
AddEventHandler('inventory:showInventory', function()
	showInventory()
end)

RegisterNetEvent("inventory:hideInventory")
AddEventHandler('inventory:hideInventory', function()
	hideInventory()
end)

RegisterNetEvent("esx:setAccountMoney")
AddEventHandler('esx:setAccountMoney', function(account)
	if account.name == "money" then
		SendNUIMessage({ inventory = "player-inventory", money = account.money })
	elseif account.name == "black_money" then
		SendNUIMessage({ inventory = "player-inventory", dirty = account.money })
	end
end)

-- Proximity Items
RegisterNetEvent("inventory:syncDroppedItem")
AddEventHandler("inventory:syncDroppedItem", function(instance, key, item)
	if not proximityItems[instance] then proximityItems[instance] = {} end
	proximityItems[instance][key] = item
	refreshProximityInventory()
end)

RegisterNetEvent("inventory:syncDroppedItems")
AddEventHandler("inventory:syncDroppedItems", function(items)
	proximityItems = items
	refreshProximityInventory()
end)

refreshProximityInventory = function()
	closeItems = {}

	local coords = GetEntityCoords(PlayerPedId())
	for i, droppedItem in pairs(proximityItems[getInstance()] or {}) do
		if droppedItem.coords then
			if #(coords - droppedItem.coords) < 2.0 then
				droppedItem.item.proxIndex = i
				table.insert(closeItems, droppedItem.item)
			end
		end
	end

	if not sendToExternal and not pullFromExternal then
		SendNUIMessage({ inventory = "external-inventory", label = "Proximity", items = closeItems, isProximity = true })
	end

	refreshProximityModels()
end

refreshProximityModels = function()
	for _, obj in ipairs(closeObjects) do
		DeleteEntity(obj)
	end

	local coords = GetEntityCoords(PlayerPedId())
	for _, droppedItem in pairs(proximityItems[getInstance()] or {}) do
		if droppedItem and droppedItem.coords and coords then
			if #(coords - droppedItem.coords) < 50.0 then
				local propmodel = droppedItem.item.model
				local obj = CreateObject((propmodel ~= nil and GetHashKey(propmodel) or GetHashKey("hei_prop_hei_paper_bag")), droppedItem.coords.x,
					droppedItem.coords.y, droppedItem.coords.z - 0.95, false, false, false)
				FreezeEntityPosition(obj, true)
				SetEntityCollision(obj, false, false)

				if droppedItem.rotation then
					SetEntityHeading(obj, droppedItem.rotation)
				end

				PlaceObjectOnGroundProperly(obj)

				table.insert(closeObjects, obj)
			end
		end
	end
end

RegisterNetEvent("inventory:craftItem")
AddEventHandler("inventory:craftItem", function(id, time, label, anim, dict)
	local complete = false
	local cancelled = false
	if isCrafting then
		Base.Notification("You are already crafting an item!")
		return
	end
	isCrafting = true

	useItemAnim(label, anim, dict, true, false, true, time,
		function()
			complete = true
		end,

		function()
			cancelled = true
		end
	)

	while not complete and not cancelled do
		Wait(100)
	end

	if not cancelled and complete then
		TriggerServerEvent("inventory:craftItem", id)
	end

	isCrafting = false
end)

onBaseReady(function()
	RegisterClientCallback("core_inventory:craftingVehicleCheck", function(cb)
		local veh = GetVehiclePedIsIn(PlayerPedId())
		if veh then
			local ped = GetPedInVehicleSeat(veh, -1)
			cb(ped == PlayerPedId())
		else
			cb(true)
		end
	end)
end)

AddEventHandler("onResourceStop", function(resourcename)
	if resourcename == GetCurrentResourceName() then
		for _, obj in ipairs(closeObjects) do
			DeleteEntity(obj)
		end
	end
end)

exports("isInventoryOpen", function()
	return inventoryOpen
end)

exports("isCrafting", function()
	return isCrafting
end)
