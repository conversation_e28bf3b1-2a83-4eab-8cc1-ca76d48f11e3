@font-face  {
    font-family: arimo;
    src: url(font/Arimo-Regular.ttf);
    font-weight: normal;
}

@font-face  {
    font-family: arimo;
    src: url(font/Arimo-Bold.ttf);
    font-weight: bold;
}

*   {
    font-family: arimo;
    font-weight: normal;
    -webkit-user-select: none; /* Chrome/Safari */        
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE10+ */

    /* Rules below not implemented in browsers yet */
    -o-user-select: none;
    user-select: none;
}

body    {
    display: none;
    font-size: 100%;
}

.light-blue  {
    background-color: #157dbe;
}

.blue  {
    background-color: #006bb5;
}

.red  {
    background-color: #eb212e;
}

.pink   {
    background-color:#e81583;
}

.purple {
    background-color: #4d3895;
}

.hot {
    background-color: rgb(255, 175, 71);
}

.cold {
    background-color: rgb(64, 160, 224);
}

body{
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
}

#viewport-container {
    width: 95%;
    height: 95%;
    background-color: whitesmoke;
    margin: auto;
    border: rgb(90, 87, 87) 5px solid;
    border-radius: 10px;
}

.content-container  {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

#kb-container {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

#kb-container header{
    display: flex;
    flex-direction: flex-start;
    height: 12vh;
    flex-grow: 0;
    align-items: center;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.23), 0 3px 6px rgba(0, 0, 0, 0.23);
}

.header-element {
    width: 10vw;
    height: 10vh;
    border-radius: 25px;
    margin: 5px 10px 5px 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    overflow: hidden;
    background-color: whitesmoke;
}

.header-element label{
    color: #006bb5;
    font-size: 1.8vmin;
}

.header-element span{
    color: #006bb5;
    font-size: 5vmin;
}

#header-logo    {
    max-height: 160%;
    width: auto;
    margin-bottom: 3vh;
}

#last-right-element {
    margin-left: auto;
}

#kb-container main{
    flex-grow: 1;
    display: flex;
    flex-flow: row nowrap;
    max-height: 100%;
    margin:10px;
    overflow: hidden;

}

#kb-container footer{
    display: flex;
    flex-direction: row;
    height: 18vh;
    margin:10px;
    padding-bottom: 30px;
}

.main-element   {
    flex-grow: 1;
    margin: 10px;
    display: flex;
    flex-direction: column;
    width: 95%;
    justify-content: flex-start;
    align-items: center;
    justify-items: center;
    overflow: hidden;
}

#sides-container-controls {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.main-element-title {
    color: #006bb5;
    font-size: 2.4vmin;
    font-weight: bold;
    margin-bottom:5px;
}

.descriptor {
    font-size: 1.5vmin;
    margin-bottom: 10px;
}

#numbers-grid-container {
    display: grid;
    grid-gap: 0.5vw 0.5vw;
    grid-template-columns: repeat(10, 1fr);
    width: 55%;
    justify-content: space-evenly;
    flex-grow: 1;
    overflow: hidden;
    
}

.grid-element   {
    width: 3vw;
    height: 3vw;
    text-align: center;
    line-height: 3vw;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    font-size: 1vw;
}

.grid-element.heads-unselected   {
    background-color: #eb212e4f;
}

.grid-element.tails-unselected   {
    background-color: #157dbe5e;
}

.grid-element.heads-unselected:hover   {
    background-color: #eb212ea9;
}

.grid-element.tails-unselected:hover   {
    background-color: #157dbead;
}

.grid-element.heads-selected  {
    background-color: #eb212e;
}

.grid-element.tails-selected   {
    background-color: #157dbe;
}

#sides-descriptor   {
    margin-top:auto;
}

#sides-container {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-around;
    align-items: flex-end;
    width: 100%;
}

.side-option    {
    width: 30%;
    height: 6vh;
    border-radius: 25px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    align-content: center;
    color:white;
    
}

.side-option label{
    font-size: 1.2vw;
    font-weight: bold;
    text-align: center;
}

.side-option span{
    font-size: 0.8vw;
    text-align: center;
}

.side-option.heads   {
    background-color: #eb212e4f;
}

.side-option.tails   {
    background-color: #157dbe5e;
}

.side-option.draw   {
    background-color: #4e38954f;
}

.side-option.heads:hover   {
    background-color: #eb212ea9;
}

.side-option.tails:hover   {
    background-color: #157dbead;
}

.side-option.draw:hover   {
    background-color: #4e3895af;
}

.side-option.heads.selected   {
    background-color: #eb212e;
}

.side-option.tails.selected  {
    background-color: #157dbe;
}

.side-option.draw.selected   {
    background-color: #4d3895;
}

#confirm-betting    {
    width: 35vw;
    display: flex;
    flex-direction: column ;
    align-content: center;
    max-height: 100%;

}

#confirm-betting label  {

}

#bet-slip-contents {
    width: 95%;
    flex-grow: 1;
    background-color: whitesmoke;
    padding:5px;
}

.bs-item {
    display: flex;
    flex-direction: column;
    padding:20px;
    border-left: 5px #006bb5 solid;

    border-radius: 10px;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.23), 0 3px 6px rgba(0, 0, 0, 0.23);
    margin-top: 20px;
    margin-bottom: 20px;
    background-color: white;
}

.bs-title   {
    font-weight: bold;
    font-size: 1.5vw;
    color: #006bb5;
}

.bs-picks   {
    display: flex;
    flex-direction: column;
}

#bs-numbers {
    font-size: 1vw;
    margin: 10px;
}

#bs-sides {
    font-size: 1vw;
    margin: 10px;
}

#bs-amount-title   {
    margin-bottom: 20px;
}

.bs-input   {
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-evenly;
    height: 4vh;
}

.bs-amount-input    {
    border: 2px #006bb5 solid;
    border-radius: 5px;
    font-size: 1vw;
    width: 50%;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    background-color: white;

}

.bs-amount-input label{
    padding:4px;
    background-color: white;
}

.bs-amount-input input{
    border: none;
    height: 100%;
    font-size: 1vw;
    width: 100%;
}

.bs-potential-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 50%;
}

.bs-mpw-title   {
    color: #006bb5;  
    font-size: 0.5vw;
}

.bs-mpw-calc   {
    font-size: 1.5vw;
    color: #006bb5; 
    font-weight: bold;
}

.error-message  {
    border-radius: 5px;
    border:3px red solid;
    display: flex;
    flex-direction: column;
    margin-top: 10px;
    margin-bottom: 10px;
    padding:5px;

}

.bs-item-header {
    display: flex;
    flex-flow: row nowrap;
    justify-content: flex-start;
    align-items: center;
}

.new-place-button   {
    border: none;
    border-radius: 5px;
    height: 4vh;
    margin: 10px;
    width: 100%;
    color: white;
    font-size: 1vw;
    align-self: center;
    margin-top: 15px;
}

.input-open {
    background-color: #30852d;

}

.input-closed   {
    cursor: not-allowed;
    pointer-events: none;
    /*Button disabled - CSS color class*/
    color: #c0c0c0;
    background-color: #ffffff;
}

.new-clear-button   {
    margin-left:auto;
    border: none;
    width:3vw;
    height: 2vh;

}

.error-message label {
    font-size: 0.8vw;
    font-weight: bold;
    color:red;
}

.error-message span {
    font-size: 0.7vw;
    color:red;
}


/* #bet-slip-controls  {
    width: 100%;
    height: 15%;
    border-radius: 5px;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: center;
}

#bet-button {
    width: 40%;
    height: 100%;
    border-radius: 5px;
    background-color: green;
    color: whitesmoke;
    font-family: arimo;
    font-size: 1vw;
}

#clear-container    {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.clear-button   {
    height: 50%;
    background-color: red;
    color: whitesmoke;
    font-size: 1vw;
    border-radius: 5px;

}

.slip-button    {
    margin: 5px 5px 5px 5px;
    border: none;
}*/

#disclaimer {
    color: #eb212e;
    font-weight: bold;
    font-size: 0.6vw;
}

#seperator  {
    background-color: rgb(163, 158, 158);
    height: 4px;
    border-radius: 5px;
    width: 100%;
    margin-top: 5px;
    margin-bottom: 5px;
}

#close-terminal {
    background-color: #eb212e;
    color: whitesmoke;
    width: 10vw;
    height: 10vh;
    border-radius: 25px;
    margin: 5px 10px 5px 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    overflow: hidden;
    font-size: 2vmin;
}

#betting-header-container   {
    display: flex;
    flex-direction: column;
    align-items: center;
    /* background-color: turquoise; */
    height: 7vh;
}

#betting-footer-container   {
    display: flex;
    flex-direction: column;
    align-items: center;
    /* background-color: turquoise; */
    height: 15vh;
    width: 100%;
    margin-top: auto;

}