-- attachment shit
local WEAPON_ATTACHMENTS = {

	-- pistols
	["weapon_revolver"] = {
		["attachment_engrave"] = GetH<PERSON><PERSON><PERSON>("COMPONENT_REVOLVER_VARMOD_BOSS")
		-- COMPONENT_REVOLVER_VARMOD_GOON || ALT
	},

	["weapon_revolver_mk2"] = {
		["attachment_reddot"]      = GetHash<PERSON><PERSON>("COMPONENT_AT_SIGHTS"),
		["attachment_scope"]       = Get<PERSON><PERSON><PERSON><PERSON>("COMPONENT_AT_SCOPE_MACRO_MK2"),
		["attachment_flashlight"]  = GetHash<PERSON><PERSON>("COMPONENT_AT_PI_FLSH"),
		["attachment_muzzleboost"] = GetHashKey("COMPONENT_AT_PI_COMP_03"),
		["attachment_engrave"]     = GetHashKey("COMPONENT_REVOLVER_MK2_CAMO_10")
	},

	["weapon_snspistol"] = { -- SNS <PERSON>stol
		["attachment_extendedmag"] = GetH<PERSON><PERSON><PERSON>("COMPONENT_SNSPISTOL_CLIP_02"),
		["attachment_engrave"]     = <PERSON><PERSON><PERSON><PERSON><PERSON>("COMPONENT_SNSPISTOL_VARMOD_LOWRIDER"),
	},

	["weapon_snspistol_mk2"] = { -- SNS Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_SNSPISTOL_MK2_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_SUPP_02"),
		["attachment_reddot"]      = GetHashKey("COMPONENT_AT_PI_RAIL_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH_03"),
		["attachment_muzzleboost"] = GetHashKey("COMPONENT_AT_PI_COMP_02"),
		["attachment_engrave"]     = GetHashKey("COMPONENT_SNSPISTOL_MK2_CAMO_10")
	},

	["weapon_vintagepistol"] = { -- SNS Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_VINTAGEPISTOL_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_SUPP")
	},

	["weapon_pistol"] = { -- Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_PISTOL_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_SUPP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH"),
		["attachment_engrave"]     = GetHashKey("COMPONENT_PISTOL_VARMOD_LUXE")
	},

	["weapon_combatpistol"] = { -- Combat Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_COMBATPISTOL_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_SUPP"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH"),
		["attachment_engrave"]     = GetHashKey("COMPONENT_COMBATPISTOL_VARMOD_LOWRIDER")
	},

	["weapon_heavypistol"] = { -- Heavy Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_HEAVYPISTOL_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_SUPP"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH"),
		["attachment_engrave"]     = GetHashKey("COMPONENT_HEAVYPISTOL_VARMOD_LUXE")
	},

	["weapon_pistol50"] = { -- 50 Cal Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_PISTOL50_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH"),
		["attachment_engrave"]     = GetHashKey("COMPONENT_PISTOL50_VARMOD_LUXE")
	},

	["weapon_pdpistol50"] = { -- 50 Cal Pistol
		["attachment_flashlight"] = GetHashKey("COMPONENT_AT_PI_FLSH"),
	},

	["weapon_xmas1pistol50"] = { -- 50 Cal Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_XMAS1PISTOL50_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH"),
	},

	["weapon_xmas2pistol50"] = { -- 50 Cal Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_XMAS2PISTOL50_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH"),
	},

	["weapon_appistol"] = { -- AP Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_APPISTOL_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_SUPP"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH"),
		["attachment_engrave"]     = GetHashKey("COMPONENT_APPISTOL_VARMOD_LUXE")
	},

	["weapon_akuraappistol"] = { -- AP Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_AKURAAPPISTOL_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_AKURASUPP"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH")
	},

	["weapon_celesteappistol"] = { -- AP Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_CELESTAPPISTOL_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_CELESTE_SUPP"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH")
	},

	["weapon_dragonscaleappistol"] = { -- AP Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_DSAPPISTOL_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_DS_SUPP"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH")
	},

	["weapon_paddlepopappistol"] = { -- AP Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_APPISTOL_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_SUPP"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH")
	},

	["weapon_reaperappistol"] = { -- AP Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_REAPERAPPISTOL_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_REAPER_SUPP"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH")
	},

	["weapon_royaltyappistol"] = { -- AP Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_ROYALTYAPPISTOL_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_ROYALTY_SUPP"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH")
	},

	["weapon_impactglock"] = { -- Impact Glock
		["attachment_extendedmag"] = GetHashKey("COMPONENT_IMPACTGLOCK_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_GLOCKSUPP_02 "),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_GLOCKFLSH_02"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_PI_GLOCKSIGHT")
	},

	["weapon_rubberm4a1"] = { -- M4A1 (Rubber)
		["attachment_extendedmag"] = GetHashKey("COMPONENT_RUBBERM4A1_CLIP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AR_RUBBERM4A1_SUPP"),
	},

	["weapon_tecpistol"] = { -- Tactical Pistol
		["attachment_extendedmag"] = GetHashKey("COMPONENT_TECPISTOL_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_SCOPE_MACRO")
	},

	["weapon_machinepistol"] = {
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_MACHINEPISTOL_CLIP_03"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_PI_SUPP"),
	},

	["weapon_pistol_mk2"] = {
		["attachment_extendedmag"] = GetHashKey("COMPONENT_PISTOL_MK2_CLIP_02"),
		["attachment_muzzleboost"] = GetHashKey("COMPONENT_AT_PI_COMP"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_SUPP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH_02"),
		["attachment_engrave"]     = GetHashKey("COMPONENT_PISTOL_MK2_CAMO_10"),
		["attachment_reddot"]      = GetHashKey("COMPONENT_AT_PI_RAIL")
	},

	["weapon_umbrellapistol"] = {
		["attachment_extendedmag"] = GetHashKey("COMPONENT_UMBRELLAPISTOL_CLIP_02"),
		["attachment_muzzleboost"] = GetHashKey("COMPONENT_AT_PI_COMP"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_SUPP"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH"),
	},

	-- smgs
	["weapon_minismg"] = {
		["attachment_extendedmag"] = GetHashKey("COMPONENT_MINISMG_CLIP_02"),
	},

	["weapon_gusenberg"] = {
		["attachment_extendedmag"] = GetHashKey("COMPONENT_GUSENBERG_CLIP_02"),
	},

	["weapon_combatpdw"] = {
		["attachment_extendedmag"]  = GetHashKey("COMPONENT_COMBATPDW_CLIP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_COMBATPDW_CLIP_03"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_grip"]         = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
		["attachment_scope"]        = GetHashKey("COMPONENT_AT_SCOPE_SMALL"),
	},

	["weapon_assaultsmg"] = {
		["attachment_extendedmag"] = GetHashKey("COMPONENT_ASSAULTSMG_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_engrave"]     = GetHashKey("COMPONENT_ASSAULTSMG_VARMOD_LOWRIDER"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_SCOPE_MACRO"),
	},

	["weapon_smg"] = {
		["attachment_extendedmag"]  = GetHashKey("COMPONENT_SMG_CLIP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_SMG_CLIP_03"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_PI_SUPP"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_engrave"]      = GetHashKey("COMPONENT_SMG_VARMOD_LUXE"),
		["attachment_scope"]        = GetHashKey("COMPONENT_AT_SCOPE_MACRO_02"),
	},

	["weapon_smg_mk2"] = {
		["attachment_extendedmag"] = GetHashKey("COMPONENT_SMG_MK2_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_PI_SUPP"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_SCOPE_MACRO_02_SMG_MK2"),
		["attachment_scope2"]      = GetHashKey("COMPONENT_AT_SCOPE_SMALL_SMG_MK2"),
		["attachment_reddot"]      = GetHashKey("COMPONENT_AT_SIGHTS_SMG"),
		["attachment_muzzleboost"] = GetHashKey("COMPONENT_AT_MUZZLE_07"),
		["attachment_heavybarrel"] = GetHashKey("COMPONENT_AT_SB_BARREL_02"),
		["attachment_engrave"]     = GetHashKey("COMPONENT_SMG_MK2_CAMO_10")
	},

	["weapon_microsmg"] = { -- Micro SMG (Uzi)
		["attachment_extendedmag"] = GetHashKey("COMPONENT_MICROSMG_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_PI_FLSH"),
		["attachment_engrave"]     = GetHashKey("COMPONENT_MICROSMG_VARMOD_LUXE"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_SCOPE_MACRO"),
	},

	["weapon_pp91"] = { -- PP-91 (Uzi)
		["attachment_extendedmag"] = GetHashKey("COMPONENT_PP91_CLIP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_PP91_CLIP_03"),
	},

	-- rifles
	["weapon_specialcarbine"] = {
		["attachment_extendedmag"]  = GetHashKey("COMPONENT_SPECIALCARBINE_CLIP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_SPECIALCARBINE_CLIP_03"),
		["attachment_grip"]         = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_scope"]        = GetHashKey("COMPONENT_AT_SCOPE_MEDIUM"),
	},

	["weapon_compactrifle"] = { -- Compact Rifle (AK)
		["attachment_extendedmag"] = GetHashKey("COMPONENT_COMPACTRIFLE_CLIP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_COMPACTRIFLE_CLIP_03"),
	},

	["weapon_carbinerifle"] = { -- Carbine Rifle (AK)
		["attachment_extendedmag"]  = GetHashKey("COMPONENT_CARBINERIFLE_CLIP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_CARBINERIFLE_CLIP_03"),
		["attachment_grip"]         = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_AR_SUPP"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_engrave"]      = GetHashKey("COMPONENT_CARBINERIFLE_VARMOD_LUXE"),
		["attachment_scope"]        = GetHashKey("COMPONENT_AT_SCOPE_MEDIUM"),
	},

	["weapon_m4a1"] = { -- M4A1 (AK)
		["attachment_extendedmag"] = GetHashKey("COMPONENT_M4A1_CLIP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AR_M4A1_SUPP"),
	},

	["weapon_mk47"] = { -- MK47
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_MK47_CLIP_02"),
		["attachment_scope"]        = GetHashKey("COMPONENT_AT_SCOPE_MEDIUM"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_AR_SUPP"),
		["attachment_grip"]         = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
	},

	["weapon_hk416"] = { -- HK 416
		["attachment_scope"]      = GetHashKey("COMPONENT_HK416_AT_SCOPE_MEDIUM"),
		["attachment_flashlight"] = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_suppressor"] = GetHashKey("COMPONENT_AT_AR_SUPP"),
		["attachment_grip"]       = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
	},

	["weapon_ar15"] = { -- AR15
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_AR15_CLIP_02"),
	},

	["weapon_m6ic"] = { -- AR15
		["attachment_suppressor"] = GetHashKey("COMPONENT_M6IC_SUPP_01"),
		["attachment_reddot"] = GetHashKey("COMPONENT_M6IC_SCOPE_06"),
		["attachment_scope2"] = GetHashKey("COMPONENT_M6IC_SCOPE_10"),
	},

	["weapon_militaryrifle"] = { -- Military Rifle
		["attachment_extendedmag"] = GetHashKey("COMPONENT_MILITARYRIFLE_CLIP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_SCOPE_SMALL"),
	},

	["weapon_scar"] = { -- SCAR
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_SCAR_CLIP_02"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_grip"]         = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
	},

	["weapon_tacticalrifle"] = { -- Tactical Rifle
		["attachment_extendedmag"] = GetHashKey("COMPONENT_TACTICALRIFLE_CLIP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_AR_FLSH_REH"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_grip"]        = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
	},

	["weapon_dd16"] = { -- DD 16
		["attachment_extendedmag"]  = GetHashKey("COMPONENT_PMAG40_B"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_PMAGLINK_B"),
		["attachment_scope"]        = GetHashKey("COMPONENT_VUDU_1X"),
		["attachment_grip"]         = GetHashKey("COMPONENT_BCM_OD"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_PEQ16_B"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_SOCOMSUP16_B"),
	},

	["weapon_bullpuprifle"] = { -- Bullpup rifle
		["attachment_grip"]       = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
		["attachment_suppressor"] = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_flashlight"] = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_scope"]      = GetHashKey("COMPONENT_AT_SCOPE_SMALL"),
	},

	["weapon_bullpuprifle_mk2"] = { -- Bullpup rifle mk2
		["attachment_extendedmag"] = GetHashKey("COMPONENT_BULLPUPRIFLE_MK2_CLIP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_reddot"]      = GetHashKey("COMPONENT_AT_SIGHTS"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_SCOPE_MACRO_02_MK2"),
		["attachment_scope2"]      = GetHashKey("COMPONENT_AT_SCOPE_SMALL_MK2"),
		["attachment_heavybarrel"] = GetHashKey("COMPONENT_AT_BP_BARREL_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP"),
		["attachment_grip"]        = GetHashKey("COMPONENT_AT_AR_AFGRIP_02"),
	},

	["weapon_assaultrifle"] = { -- Assault Rifle (AK)
		["attachment_extendedmag"]  = GetHashKey("COMPONENT_ASSAULTRIFLE_CLIP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_ASSAULTRIFLE_CLIP_03"),
		["attachment_grip"]         = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_engrave"]      = GetHashKey("COMPONENT_ASSAULTRIFLE_VARMOD_LUXE"),
		["attachment_scope"]        = GetHashKey("COMPONENT_AT_SCOPE_MACRO"),
	},

	["weapon_blackdragonak"] = { -- Assault Rifle (AK)
		["attachment_extendedmag"]  = GetHashKey("COMPONENT_BLACKDRAGONAK_CLIP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_ASSAULTRIFLE_CLIP_03"),
		["attachment_grip"]         = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_scope"]        = GetHashKey("COMPONENT_AT_SCOPE_MACRO"),
	},

	["weapon_royalgoldak"] = { -- Assault Rifle (AK)
		["attachment_extendedmag"]  = GetHashKey("COMPONENT_ROYALGOLDAK_CLIP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_ROYALGOLDAK_CLIP_03"),
		["attachment_grip"]         = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_AR_RGAKSUPP_02"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_scope"]        = GetHashKey("COMPONENT_AT_SCOPE_MACRO"),
	},

	["weapon_royaljadeak"] = { -- Assault Rifle (AK)
		["attachment_extendedmag"]  = GetHashKey("COMPONENT_ROYALJADEAK_CLIP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_ROYALJADEAK_CLIP_03"),
		["attachment_grip"]         = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_AR_RJAKSUPP_02"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_scope"]        = GetHashKey("COMPONENT_AT_SCOPE_MACRO"),
	},

	["weapon_royalcrimsonak"] = { -- Assault Rifle (AK)
		["attachment_extendedmag"]  = GetHashKey("COMPONENT_CRIMSONAK_CLIP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_CRIMSONAK_CLIP_03"),
		["attachment_grip"]         = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_AR_RCAKSUPP_02"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_scope"]        = GetHashKey("COMPONENT_AT_SCOPE_MACRO"),
	},

	["weapon_assaultrifle_mk2"] = { -- Assault Rifle (AK)
		["attachment_extendedmag"] = GetHashKey("COMPONENT_ASSAULTRIFLE_MK2_CLIP_02"),
		["attachment_grip"]        = GetHashKey("COMPONENT_AT_AR_AFGRIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_engrave"]     = GetHashKey("COMPONENT_ASSAULTRIFLE_MK2_CAMO_10"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_SCOPE_MACRO_MK2"),
		["attachment_scope2"]      = GetHashKey("COMPONENT_AT_SCOPE_MEDIUM_MK2"),
		["attachment_reddot"]      = GetHashKey("COMPONENT_AT_SIGHTS"),
		["attachment_muzzleboost"] = GetHashKey("COMPONENT_AT_MUZZLE_07"),
		["attachment_heavybarrel"] = GetHashKey("COMPONENT_AT_AR_BARREL_02"),
	},

	["weapon_advancedrifle"] = { -- Assault Rifle (AK)
		["attachment_extendedmag"] = GetHashKey("COMPONENT_ADVANCEDRIFLE_CLIP_02"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_engrave"]     = GetHashKey("COMPONENT_ADVANCEDRIFLE_VARMOD_LUXE"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_SCOPE_SMALL"),
	},

	["weapon_mm4"] = { -- MM4
		["attachment_extendedmag"] = GetHashKey("COMPONENT_MM4_CLIP_02"),
		["attachment_scope2"] = GetHashKey("COMPONENT_AT_SIGHTS"),
		["attachment_flashlight"] = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_grip"] = GetHashKey("COMPONENT_AT_AR_AFGRIP_02"),
	},

	["weapon_carbinerifle_mk2"] = { -- Carbine Rifle MK2 (AK)
		["attachment_extendedmag"]  = GetHashKey("COMPONENT_CARBINERIFLE_MK2_CLIP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_CARBINERIFLE_MK2_CLIP_03"),
		["attachment_grip"]         = GetHashKey("COMPONENT_AT_AR_AFGRIP_02"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_AR_SUPP"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_scope"]        = GetHashKey("COMPONENT_AT_SCOPE_MACRO_MK2"),
		["attachment_scope2"]       = GetHashKey("COMPONENT_AT_SCOPE_MEDIUM_MK2"),
		["attachment_reddot"]       = GetHashKey("COMPONENT_AT_SIGHTS"),
		["attachment_heavybarrel"]  = GetHashKey("COMPONENT_AT_CR_BARREL_02"),
		["attachment_muzzleboost"]  = GetHashKey("COMPONENT_AT_MUZZLE_01"),
		["attachment_engrave"]      = GetHashKey("COMPONENT_CARBINERIFLE_MK2_CAMO_10"),
	},                             -- need to setup muzzles, fuck that tho

	["weapon_specialcarbine_mk2"] = { -- Carbine Rifle MK2 (AK)
		["attachment_extendedmag"]  = GetHashKey("COMPONENT_SPECIALCARBINE_MK2_CLIP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_SPECIALCARBINE_MK2_CLIP_03"),
		["attachment_grip"]         = GetHashKey("COMPONENT_AT_AR_AFGRIP_02"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_AR_SUPP"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_scope"]        = GetHashKey("COMPONENT_AT_SCOPE_MACRO_MK2"),
		["attachment_scope2"]       = GetHashKey("COMPONENT_AT_SCOPE_MEDIUM_MK2"),
		["attachment_reddot"]       = GetHashKey("COMPONENT_AT_SIGHTS"),
		["attachment_heavybarrel"]  = GetHashKey("COMPONENT_AT_CR_BARREL_02"),
		["attachment_muzzleboost"]  = GetHashKey("COMPONENT_AT_MUZZLE_01"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
	}, --

	-- shotguns
	["weapon_sawnoffshotgun"] = {
		["attachment_engrave"] = GetHashKey("COMPONENT_SAWNOFFSHOTGUN_VARMOD_LUXE")
	},

	["weapon_pumpshotgun"] = {
		["attachment_suppressor"] = GetHashKey("COMPONENT_AT_SR_SUPP"),
		["attachment_flashlight"] = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_engrave"]    = GetHashKey("COMPONENT_PUMPSHOTGUN_VARMOD_LOWRIDER"),
	},

	["weapon_mossberg"] = {
		["attachment_flashlight"] = GetHashKey("COMPONENT_AT_AR_FLSH"),
	},

	["weapon_bbshotgun"] = {
		["attachment_flashlight"] = GetHashKey("COMPONENT_AT_AR_FLSH"),
	},


	["weapon_heavyshotgun"] = {
		["attachment_extendedmag"]  = GetHashKey("COMPONENT_HEAVYSHOTGUN_CLIP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_HEAVYSHOTGUN_CLIP_03"),
		["attachment_suppressor"]   = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_flashlight"]   = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_grip"]         = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
	},

	["weapon_bullpupshotgun"] = {
		["attachment_grip"]       = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
		["attachment_suppressor"] = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_flashlight"] = GetHashKey("COMPONENT_AT_AR_FLSH"),
	},

	["weapon_assaultshotgun"] = {
		["attachment_extendedmag"] = GetHashKey("COMPONENT_ASSAULTSHOTGUN_CLIP_02"),
		["attachment_grip"]        = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_AR_FLSH"),
	},

	-- snipers
	["weapon_sniperrifle"] = {
		["attachment_suppressor"] = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_engrave"]    = GetHashKey("COMPONENT_SNIPERRIFLE_VARMOD_LUXE"),
		["attachment_scope2"]     = GetHashKey("COMPONENT_AT_SCOPE_MAX"),
	},

	["weapon_marksmanrifle_mk2"] = {
		["attachment_reddot"] = GetHashKey("COMPONENT_AT_SIGHTS"),
	},

	--mp5
	["weapon_mp5"] = {
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_MP5_SUPP"),
		["attachment_extendedmag"] = GetHashKey("COMPONENT_MP5_CLIP_01"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_SCOPE_MP5"),
	},

	--mp7
	["weapon_mp7"] = {
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_extendedmag"] = GetHashKey("COMPONENT_MP7_CLIP_02"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_SCOPE_MACRO"),
	},

	["weapon_mpx"] = {
		["attachment_suppressor"] = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_extendedmag2"] = GetHashKey("COMPONENT_MPX_CLIP_02"),
		["attachment_scope"] = GetHashKey("COMPONENT_AT_SCOPE_MEDIUM"),
		["attachment_grip"] = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
		["attachment_flashlight"] = GetHashKey("COMPONENT_AT_AR_FLSH"),
	},

	["weapon_famas"] = {
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP"),
		["attachment_extendedmag"] = GetHashKey("COMPONENT_BULLPUPRIFLE_CLIP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_SCOPE_SMALL"),
		["attachment_grip"]        = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
		--["attachment_engrave"] = GetHashKey("COMPONENT_BULLPUPRIFLE_VARMOD_LOW"), !!NEED TO FIX TO BEING FAMAS
	},

	["weapon_battlerifle"] = {
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP"),
		["attachment_extendedmag"] = GetHashKey("COMPONENT_BATTLERIFLE_CLIP_02"),
	},

	["weapon_jericho"] = {
		["attachment_flashlight"] = GetHashKey("COMPONENT_AT_PI_FLSH"),
	},

	["weapon_dp12"] = {
		["attachment_suppressor"] = GetHashKey("COMPONENT_AT_AR_SUPP_02"),
		["attachment_grip"]       = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
		["attachment_flashlight"] = GetHashKey("COMPONENT_AT_AR_FLSH"),
	},

	["weapon_groza"] = {
		--["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_AR_SUPP"), DISABLED
		["attachment_extendedmag"] = GetHashKey("COMPONENT_BULLPUPRIFLE_CLIP_02"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_SCOPE_SMALL"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_engrave"]     = GetHashKey("COMPONENT_GROZA_VARMOD_LUXE"),
	},

	["weapon_ump45"] = {
		["attachment_reddot"] = GetHashKey("COMPONENT_AT_SIGHTS_SMG"),
		["attachment_flashlight"] = GetHashKey("COMPONENT_AT_AR_FLSH"),
	},

	["weapon_nsr9"] = {
		["attachment_extendedmag"] = GetHashKey("COMPONENT_NSR9_CLIP_02"),
		["attachment_flashlight"] = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_reddot"] = GetHashKey("COMPONENT_AT_SIGHTS"),
		["attachment_scope"] = GetHashKey("COMPONENT_AT_SCOPE_MACRO"),
		["attachment_scope2"] = GetHashKey("COMPONENT_AT_SCOPE_MEDIUM"),
		["attachment_suppressor"] = GetHashKey("COMPONENT_AR_NSR9_SUPP"),
		["attachment_muzzleboost"] = GetHashKey("COMPONENT_AT_MUZZLE_02"),
		["attachment_heavybarrel"] = GetHashKey("COMPONENT_AT_AR_BARREL_02"),
	},

	["weapon_mcxspear"] = {
		["attachment_extendedmag"] = GetHashKey("COMPONENT_MCXSPEAR_CLIP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_SIGHTS"),
		["attachment_scope2"]      = GetHashKey("COMPONENT_AT_SCOPE_MEDIUM_MCX"),
		["attachment_suppressor"]  = GetHashKey("COMPONENT_AT_SPEAR_SUPP_02"),
		["attachment_muzzleboost"] = GetHashKey("COMPONENT_AT_MUZZLE_01"),
		["attachment_grip"]        = GetHashKey("COMPONENT_AT_AR_AFGRIP_02"),
		["attachment_heavybarrel"] = GetHashKey("COMPONENT_AT_CR_BARREL_02"),

	},

	["weapon_catrifle"] = { -- Cat rifle
		["attachment_extendedmag"] = GetHashKey("COMPONENT_CATRIFLE_CLIP_02"),
		["attachment_flashlight"]  = GetHashKey("COMPONENT_AT_AR_FLSH"),
		["attachment_scope2"]      = GetHashKey("COMPONENT_AT_SCOPE_MEDIUM"),
		["attachment_grip"]        = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
	},

	["weapon_mg"] = {
		["attachment_extendedmag"] = GetHashKey("COMPONENT_MG_CLIP_02"),
		["attachment_scope"]       = GetHashKey("COMPONENT_AT_SCOPE_SMALL_02"),
	},

	["weapon_combatmg"] = {
		["attachment_extendedmag"] = GetHashKey("COMPONENT_COMBATMG_CLIP_02"),
		["attachment_scope2"]      = GetHashKey("COMPONENT_AT_SCOPE_MEDIUM"),
		["attachment_grip"]        = GetHashKey("COMPONENT_AT_AR_AFGRIP"),
	},
}

function applyAttachments(weapon)
	local name = weapon.name
	local attachments = weapon.data
	if (WEAPON_ATTACHMENTS[name]) then
		for attachment, active in pairs(attachments) do
			if (WEAPON_ATTACHMENTS[name][attachment]) then
				local enabled = HasPedGotWeaponComponent(PlayerPedId(), GetHashKey(name),
					WEAPON_ATTACHMENTS[name][attachment])
				if (active and not enabled) then
					GiveWeaponComponentToPed(PlayerPedId(), GetHashKey(name), WEAPON_ATTACHMENTS[name][attachment])
				elseif (not active and enabled) then
					RemoveWeaponComponentFromPed(PlayerPedId(), GetHashKey(name), WEAPON_ATTACHMENTS[name][attachment])
				end
			end
		end
	end
end

function getWeaponAttachment(weapon, attachment)
	if (WEAPON_ATTACHMENTS[weapon] == nil) then return nil end
	return WEAPON_ATTACHMENTS[weapon][attachment]
end

local deagleElements = {
	{ label = "Asiimov",        item = "weapon_asiimovpistol50" },
	{ label = "Bang!",          item = "weapon_bangpistol50" },
	{ label = "Black Anime",    item = "weapon_blackanimepistol50" },
	{ label = "Black Ice",      item = "weapon_blackicepistol50" },
	{ label = "Blaze It",       item = "weapon_blazeitpistol50" },
	{ label = "Blaze",          item = "weapon_blazepistol50" },
	{ label = "Bloodline",      item = "weapon_bloodlinepistol50" },
	{ label = "Kumicho Dragon", item = "weapon_dragonpistol50" },
	{ label = "Fat Duck",       item = "weapon_fatduckpistol50" },
	{ label = "Hot & Cold",     item = "weapon_hotcoldpistol50" },
	{ label = "Orange",         item = "weapon_orangepistol50" },
	{ label = "PixelD",         item = "weapon_pixeldpistol50" },
	{ label = "Printstream",    item = "weapon_printstreampistol50" },
	{ label = "Red Dragon",     item = "weapon_reddragonpistol50" },
	{ label = "Rip Tide",       item = "weapon_riptidepistol50" },
	{ label = "Shenlong",       item = "weapon_shenlongpistol50" },
	{ label = "Special Forces", item = "weapon_specialforcespistol50" },
	{ label = "Sultan",         item = "weapon_sultanpistol50" },
	{ label = "Waifu",          item = "weapon_waifupistol50" },
}

local akElements = {
	{ label = "Black Dragon",  item = "weapon_blackdragonak" },
	{ label = "Royal Gold",    item = "weapon_royalgoldak" },
	{ label = "Royal Jade",    item = "weapon_royaljadeak" },
	{ label = "Royal Crimson", item = "weapon_royalcrimsonak" },
}

local apElements = {
	{ label = "Akura",        item = "weapon_akuraappistol" },
	{ label = "Celeste",      item = "weapon_celesteappistol" },
	{ label = "Dragon Scale", item = "weapon_dragonscaleappistol" },
	{ label = "Paddle Pop",   item = "weapon_paddlepopappistol" },
	{ label = "Reaper",       item = "weapon_reaperappistol" },
	{ label = "Royalty",      item = "weapon_royaltyappistol" },
}

function openDeagleMenu()
	Base.UI.Menu.Open(
		'default', GetCurrentResourceName(), 'deagle_menu',
		{
			title    = 'Engraving Select',
			align    = 'bottom-right',
			elements = deagleElements,
		},
		function(d, m)
			m.close()

			TriggerServerEvent("inventory:deagleEngraving", d.current.item)
		end,
		function(d, m)
			m.close()
		end
	)
end

function OpenAkMenu()
	Base.UI.Menu.Open(
		'default', GetCurrentResourceName(), 'ak_menu',
		{
			title    = 'Engraving Select',
			align    = 'bottom-right',
			elements = akElements,
		},
		function(d, m)
			m.close()

			TriggerServerEvent("inventory:akEngraving", d.current.item)
		end,
		function(d, m)
			m.close()
		end
	)
end

function OpenApMenu()
	Base.UI.Menu.Open(
		'default', GetCurrentResourceName(), 'ap_menu',
		{
			title    = 'Engraving Select',
			align    = 'bottom-right',
			elements = apElements,
		},
		function(d, m)
			m.close()

			TriggerServerEvent("inventory:apEngraving", d.current.item)
		end,
		function(d, m)
			m.close()
		end
	)
end
