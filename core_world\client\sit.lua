local sitable = {}

local function isObjectSittable(model)
    if sitable[tostring(model)] then
        return sitable[tostring(model)]
    end
end

RegisterInteraction("object", "sit", { label = "Sit" }, function(object)
    sitDown(object)
end, function(object)
    return isObjectSittable(GetEntityModel(object))
end)

RegisterDataSyncHandler("sittable", function(data)
    for i, v in ipairs(data) do
        local model = GetHashKey(v.name)
        sitable[tostring(model)] = { scenario = v.scenario, xOffset = v.xOffset, yOffset = v.yOffset, zOffset = v.zOffset }
    end
end)

function sitDown(object)
    local data = sitable[tostring(GetEntityModel(object))]
    local ped = PlayerPedId()
    local objPos = GetOffsetFromEntityInWorldCoords(object, 0.0 + data.xOffset, 0.0 + -data.yOffset, 0.0 + -data.zOffset)
    FreezeEntityPosition(object, true)
    TaskStartScenarioAtPosition(ped, data.scenario, objPos.x, objPos.y, objPos.z, GetEntityHeading(object) + 180.0, -1, (string.upper(data.scenario) == "PROP_HUMAN_SEAT_BENCH"), true)
end
