function GetPlayerSkill(skillName)
    local skill = LocalPlayer.state["skill_" .. skillName]

    if skill then
        return skill
    else
        return { level = 0, xp = 0, nextLevelXp = 1000, lastLevelXp = 0 }
    end
end


-----------------------
--- Setup Milestones---
-----------------------

local drugs = {
    10,
    20,
    30,
    40,
    50, 
    60,
    70,
    80,
    90,
}

local fishing = {
    10,
    20,
    35,
    50,
    95,
}

local trucking = {
    45,
}



----------------------
--- Set UI & Sounds---
----------------------

-- Function to check if a table contains a value
local function tableContains(table, value)
    for _, v in ipairs(table) do
        if v == value then
            return true
        end
    end
    return false
end

local function checkMilestones(skill, level)
    if tableContains(drugs, level) and skill == "drugs" then
        return true
    elseif tableContains(fishing, level) and skill == "fishing" then
        return true
    elseif tableContains(trucking, level) and skill == "trucking" then
        return true
    end
    return false
end

RegisterNetEvent("base:level-up-ui")
AddEventHandler("base:level-up-ui", function(skill, level)
    local coords = GetEntityCoords(PlayerPedId())
    SendNUIMessage({ type = "skillLevelUp", skill = skill, level = level })
    if checkMilestones(skill, level) then
        PlaySoundEffectAtLocation(coords, "level-up-milestone", false, 15.0)
    else
        PlaySoundEffectAtLocation(coords, "level-up-normal", false, 15.0)
    end
end)
