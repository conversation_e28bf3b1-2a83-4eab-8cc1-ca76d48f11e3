<?xml version="1.0" encoding="UTF-8"?>
<CVehicleModelInfoVarGlobal>

<Lights />
	<Sirens>
	<Item>
      <id value="11076123111"/>
      <name>ppgjv</name>
      <timeMultiplier value="1.00000000"/>
      <lightFalloffMax value="100.00000000"/>
      <lightFalloffExponent value="100.00000000"/>
      <lightInnerConeAngle value="2.29061000"/>
      <lightOuterConeAngle value="70.00000000"/>
      <lightOffset value="0.00000000"/>
      <textureName>VehicleLight_sirenlight</textureName>
      <sequencerBpm value="800"/>
      <leftHeadLight>
        <sequencer value="0"/>
      </leftHeadLight>
      <rightHeadLight>
        <sequencer value="0"/>
      </rightHeadLight>
      <leftTailLight>
        <sequencer value="0"/>
      </leftTailLight>
      <rightTailLight>
        <sequencer value="0"/>
      </rightTailLight>
      <leftHeadLightMultiples value="1"/>
      <rightHeadLightMultiples value="1"/>
      <leftTailLightMultiples value="1"/>
      <rightTailLightMultiples value="1"/>
      <useRealLights value="true"/>
      <sirens>
      <Item>	<!--siren1-->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.820305"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="3772834016"/>
            <multiples value="2"/>
            <direction value="true"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.03000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>	<!--siren2-->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.03000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>	<!--siren3-->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="3772834016"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.03000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>	<!--siren4-->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159300"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.03000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>	<!--siren5-->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="3772834016"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.03000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <Item>	<!--siren6-->
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-0.820305"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.03000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren7 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="2.32129"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="3772834016"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.03000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <!--siren8-->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="-2.32129"/>
            <start value="0.00000000"/>
            <speed value="150.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.03000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="100"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
         <!--siren9-->
		<Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="3"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
          <!--siren10-->
		<Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="3772834016"/>
            <multiples value="3"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <!--siren11-->
		<Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="3"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
          <!--siren12-->
		<Item>
          <rotation>
            <delta value="-0.01000000"/>
            <start value="0.00000000"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="3.14159265"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="3772834016"/>
            <multiples value="3"/>
            <direction value="false"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.00000000"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
          <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="4"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        		<!-- siren13 -->
        <Item>
          <rotation>
            <delta value="0"/>
            <start value="0"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0"/>
            <start value="0"/>
            <speed value="150.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
        <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren14 -->
        <Item>
          <rotation>
            <delta value="0"/>
            <start value="0"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0"/>
            <start value="0"/>
            <speed value="150.00000000"/>
            <sequencer value="3772834016"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
        <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
        <!-- siren15 -->
        <Item>
          <rotation>
            <delta value="0"/>
            <start value="0"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0"/>
            <start value="0"/>
            <speed value="150.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
        <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren16 -->
        <Item>
          <rotation>
            <delta value="0"/>
            <start value="0"/>
            <speed value="3.00000000"/>
            <sequencer value="4294967295"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0"/>
            <start value="0"/>
            <speed value="150.00000000"/>
            <sequencer value="3772834016"/>
            <multiples value="1"/>
            <direction value="true"/>
            <syncToBpm value="false"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="1.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF000AFF"/>
        <intensity value="1.00000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="true"/>
          <castShadows value="false"/>
        </Item>
<!-- siren17 -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="4"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="11141290"/>
            <multiples value="4"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFF0000FF"/>
        <intensity value="0.25000000"/>
          <lightGroup value="1"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="true"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>
		<!-- siren18 white -->
        <Item>
          <rotation>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="18876123555"/>
            <multiples value="4"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </rotation>
          <flashiness>
            <delta value="0.00000000"/>
            <start value="0.00000000"/>
            <speed value="0.00000000"/>
            <sequencer value="18876123555"/>
            <multiples value="4"/>
            <direction value="true"/>
            <syncToBpm value="true"/>
          </flashiness>
          <corona>
            <intensity value="50.00000000"/>
            <size value="0.00000000"/>
            <pull value="0.00000001"/>
            <faceCamera value="false"/>
          </corona>
          <color value="0xFFFFFFFF"/>
        <intensity value="0.50000000"/>
          <lightGroup value="0"/>
          <rotate value="false"/>
          <scale value="true"/>
          <scaleFactor value="10"/>
          <flash value="true"/>
          <light value="false"/>
          <spotLight value="false"/>
          <castShadows value="false"/>
        </Item>

		<!--Siren 19 : Amber : Back-->
                <Item>
                    <rotation>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294901760"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="4294901760"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="40.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFFD700"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>                

                <!--Siren 20 : Amber : Back-->
                <Item>
                    <rotation>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="65535"/>
                        <multiples value="1"/>
                        <direction value="false"/>
                        <syncToBpm value="true"/>
                    </rotation>
                    <flashiness>
                        <delta value="3.14159265"/>
                        <start value="0.00000000"/>
                        <speed value="1.00000000"/>
                        <sequencer value="65535"/>
                        <multiples value="1"/>
                        <direction value="true"/>
                        <syncToBpm value="true"/>
                    </flashiness>
                    <corona>
                        <intensity value="40.00000000"/>
                        <size value="0.60000000"/>
                        <pull value="0.01500000"/>
                        <faceCamera value="false"/>
                    </corona>
                    <color value="0xFFFFD700"/>
                    <intensity value="1.00000000"/>
                    <lightGroup value="0"/>
                    <rotate value="false"/>
                    <scale value="true"/>
                    <scaleFactor value="100"/>
                    <flash value="true"/>
                    <light value="true"/>
                    <spotLight value="true"/>
                    <castShadows value="false"/>
                </Item>     
      </sirens>
    </Item>
  </Sirens>

	<Kits>
		<Item>
			<kitName>882_pvsjv_modkit</kitName>
			<id value="882"/>
			<kitType>MKT_SPORT</kitType>
			<visibleMods>
				<Item>
					<modelName>pvsjv_callsign_a0</modelName>
					<modShopLabel>CALLSIGN_A0</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_L</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_a1</modelName>
					<modShopLabel>CALLSIGN_A1</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_L</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_a2</modelName>
					<modShopLabel>CALLSIGN_A2</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_L</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_a3</modelName>
					<modShopLabel>CALLSIGN_A3</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_L</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_a4</modelName>
					<modShopLabel>CALLSIGN_A4</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_L</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_a5</modelName>
					<modShopLabel>CALLSIGN_A5</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_L</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_a6</modelName>
					<modShopLabel>CALLSIGN_A6</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_L</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_a7</modelName>
					<modShopLabel>CALLSIGN_A7</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_L</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_a8</modelName>
					<modShopLabel>CALLSIGN_A8</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_L</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_a9</modelName>
					<modShopLabel>CALLSIGN_A9</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_L</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_b0</modelName>
					<modShopLabel>CALLSIGN_B0</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_R</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_b1</modelName>
					<modShopLabel>CALLSIGN_B1</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_R</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_b2</modelName>
					<modShopLabel>CALLSIGN_B2</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_R</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_b3</modelName>
					<modShopLabel>CALLSIGN_B3</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_R</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_b4</modelName>
					<modShopLabel>CALLSIGN_B4</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_R</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_b5</modelName>
					<modShopLabel>CALLSIGN_B5</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_R</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_b6</modelName>
					<modShopLabel>CALLSIGN_B6</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_R</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_b7</modelName>
					<modShopLabel>CALLSIGN_B7</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_R</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_b8</modelName>
					<modShopLabel>CALLSIGN_B8</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_R</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_b9</modelName>
					<modShopLabel>CALLSIGN_B9</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_WING_R</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_c0</modelName>
					<modShopLabel>CALLSIGN_C0</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_ROOF</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_c1</modelName>
					<modShopLabel>CALLSIGN_C1</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_ROOF</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_c2</modelName>
					<modShopLabel>CALLSIGN_C2</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_ROOF</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_c3</modelName>
					<modShopLabel>CALLSIGN_C3</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_ROOF</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_c4</modelName>
					<modShopLabel>CALLSIGN_C4</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_ROOF</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_c5</modelName>
					<modShopLabel>CALLSIGN_C5</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_ROOF</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_c6</modelName>
					<modShopLabel>CALLSIGN_C6</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_ROOF</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_c7</modelName>
					<modShopLabel>CALLSIGN_C7</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_ROOF</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_c8</modelName>
					<modShopLabel>CALLSIGN_C8</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_ROOF</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
				<Item>
					<modelName>pvsjv_callsign_c9</modelName>
					<modShopLabel>CALLSIGN_C9</modShopLabel>
					<linkedModels />
					<turnOffBones />
					<type>VMT_ROOF</type>
					<bone>chassis</bone>
					<collisionBone>chassis</collisionBone>
					<cameraPos>VMCP_DEFAULT</cameraPos>
					<audioApply value="1.000000" />
					<weight value="20" />
					<turnOffExtra value="false" />
					<disableBonnetCamera value="false" />
					<allowBonnetSlide value="true" />
				</Item>
			</visibleMods>
			<linkMods/>
			<statMods>
				<Item>
					<identifier/>
					<modifier value="50"/>
					<audioApply value="1.00000000"/>
					<weight value="20"/>
					<type>VMT_ENGINE</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="100"/>
					<audioApply value="1.00000000"/>
					<weight value="20"/>
					<type>VMT_ENGINE</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="150"/>
					<audioApply value="1.00000000"/>
					<weight value="20"/>
					<type>VMT_ENGINE</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="200"/>
					<audioApply value="1.00000000"/>
					<weight value="20"/>
					<type>VMT_ENGINE</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="235"/>
					<audioApply value="1.00000000"/>
					<weight value="20"/>
					<type>VMT_ENGINE</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="25"/>
					<audioApply value="1.00000000"/>
					<weight value="5"/>
					<type>VMT_BRAKES</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="50"/>
					<audioApply value="1.00000000"/>
					<weight value="5"/>
					<type>VMT_BRAKES</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="100"/>
					<audioApply value="1.00000000"/>
					<weight value="5"/>
					<type>VMT_BRAKES</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="120"/>
					<audioApply value="1.00000000"/>
					<weight value="5"/>
					<type>VMT_BRAKES</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="25"/>
					<audioApply value="1.00000000"/>
					<weight value="5"/>
					<type>VMT_GEARBOX</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="50"/>
					<audioApply value="1.00000000"/>
					<weight value="5"/>
					<type>VMT_GEARBOX</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="100"/>
					<audioApply value="1.00000000"/>
					<weight value="5"/>
					<type>VMT_GEARBOX</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="120"/>
					<audioApply value="1.00000000"/>
					<weight value="5"/>
					<type>VMT_GEARBOX</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="20"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_ARMOUR</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="40"/>
					<audioApply value="1.00000000"/>
					<weight value="10"/>
					<type>VMT_ARMOUR</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="60"/>
					<audioApply value="1.00000000"/>
					<weight value="20"/>
					<type>VMT_ARMOUR</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="80"/>
					<audioApply value="1.00000000"/>
					<weight value="30"/>
					<type>VMT_ARMOUR</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="100"/>
					<audioApply value="1.00000000"/>
					<weight value="40"/>
					<type>VMT_ARMOUR</type>
				</Item>
				<Item>
					<identifier>HORN_TRUCK</identifier>
					<modifier value="1766676233"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>HORN_COP</identifier>
					<modifier value="2904189469"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>HORN_CLOWN</identifier>
					<modifier value="2543206147"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>HORN_MUSICAL_1</identifier>
					<modifier value="1732399718"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>HORN_MUSICAL_2</identifier>
					<modifier value="2046162893"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>HORN_MUSICAL_3</identifier>
					<modifier value="2194999691"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>HORN_MUSICAL_4</identifier>
					<modifier value="2508304100"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>HORN_MUSICAL_5</identifier>
					<modifier value="3707223535"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>HORN_SAD_TROMBONE</identifier>
					<modifier value="632950117"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="5"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_SUSPENSION</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="10"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_SUSPENSION</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="15"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_SUSPENSION</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="20"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_SUSPENSION</type>
				</Item>
				<Item>
					<identifier/>
					<modifier value="22"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_SUSPENSION</type>
				</Item>
				<Item>
					<identifier>MUSICAL_HORN_BUSINESS_1</identifier>
					<modifier value="3628534289"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>MUSICAL_HORN_BUSINESS_2</identifier>
					<modifier value="3892554122"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>MUSICAL_HORN_BUSINESS_3</identifier>
					<modifier value="4112892878"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>MUSICAL_HORN_BUSINESS_4</identifier>
					<modifier value="116877169"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>MUSICAL_HORN_BUSINESS_5</identifier>
					<modifier value="2684983719"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>MUSICAL_HORN_BUSINESS_6</identifier>
					<modifier value="2982690084"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>MUSICAL_HORN_BUSINESS_7</identifier>
					<modifier value="3203290992"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>DLC_BUSI2_C_MAJOR_NOTES_C0</identifier>
					<modifier value="771284519"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>DLC_BUSI2_C_MAJOR_NOTES_D0</identifier>
					<modifier value="2586621229"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>DLC_BUSI2_C_MAJOR_NOTES_E0</identifier>
					<modifier value="283386134"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>DLC_BUSI2_C_MAJOR_NOTES_F0</identifier>
					<modifier value="3884502400"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>DLC_BUSI2_C_MAJOR_NOTES_G0</identifier>
					<modifier value="265723083"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>DLC_BUSI2_C_MAJOR_NOTES_A0</identifier>
					<modifier value="1746883687"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>DLC_BUSI2_C_MAJOR_NOTES_B0</identifier>
					<modifier value="1919870950"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>DLC_BUSI2_C_MAJOR_NOTES_C1</identifier>
					<modifier value="1085277077"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>HIPSTER_HORN_1</identifier>
					<modifier value="444549672"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>HIPSTER_HORN_2</identifier>
					<modifier value="1603064898"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>HIPSTER_HORN_3</identifier>
					<modifier value="240366033"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>HIPSTER_HORN_4</identifier>
					<modifier value="960137118"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>INDEP_HORN_1</identifier>
					<modifier value="3572144790"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>INDEP_HORN_2</identifier>
					<modifier value="3801396714"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>INDEP_HORN_3</identifier>
					<modifier value="2843657151"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>INDEP_HORN_4</identifier>
					<modifier value="3341811489"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>LUXE_HORN_1</identifier>
					<modifier value="3199657341"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>LUXE_HORN_2</identifier>
					<modifier value="2900378064"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>LUXE_HORN_3</identifier>
					<modifier value="3956195248"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>LUXORY_HORN_1</identifier>
					<modifier value="676333254"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>LUXURY_HORN_2</identifier>
					<modifier value="2099578296"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>LUXURY_HORN_3</identifier>
					<modifier value="1373384483"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>ORGAN_HORN_LOOP_01</identifier>
					<modifier value="2916775806"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>ORGAN_HORN_LOOP_01_PREVIEW</identifier>
					<modifier value="3714706952"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>ORGAN_HORN_LOOP_02</identifier>
					<modifier value="2611860261"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>ORGAN_HORN_LOOP_02_PREVIEW</identifier>
					<modifier value="3206770359"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>LOWRIDER_HORN_1</identifier>
					<modifier value="310529291"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>LOWRIDER_HORN_1_PREVIEW</identifier>
					<modifier value="2965568987"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>LOWRIDER_HORN_2</identifier>
					<modifier value="55291550"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>LOWRIDER_HORN_2_PREVIEW</identifier>
					<modifier value="965054819"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>XM15_HORN_01</identifier>
					<modifier value="55862314"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>XM15_HORN_01_PREVIEW</identifier>
					<modifier value="2156743178"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>XM15_HORN_02</identifier>
					<modifier value="400002352"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>XM15_HORN_02_PREVIEW</identifier>
					<modifier value="897484282"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>XM15_HORN_03</identifier>
					<modifier value="560832604"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>XM15_HORN_03_PREVIEW</identifier>
					<modifier value="314232747"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>DLC_AW_Airhorn_01</identifier>
					<modifier value="3851180092"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>DLC_AW_Airhorn_01_Preview</identifier>
					<modifier value="246182814"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>DLC_AW_Airhorn_02</identifier>
					<modifier value="3412861948"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>DLC_AW_Airhorn_02_Preview</identifier>
					<modifier value="1804608241"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>DLC_AW_Airhorn_03</identifier>
					<modifier value="3374260066"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
				<Item>
					<identifier>DLC_AW_Airhorn_03_Preview</identifier>
					<modifier value="2798044638"/>
					<audioApply value="1.00000000"/>
					<weight value="0"/>
					<type>VMT_HORN</type>
				</Item>
			</statMods>
			<slotNames>
			</slotNames>
			<liveryNames/>
			<livery2Names/>
		</Item>
	</Kits>
</CVehicleModelInfoVarGlobal>