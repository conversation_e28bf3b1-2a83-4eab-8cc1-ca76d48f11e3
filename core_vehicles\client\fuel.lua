-----------------------
-- DISCLAIMER --
-- VEHICLE UNDRIVEABLE IS
-- SET IN OFFROAD SCRIPT 
-- ON LINE 123 --
-----------------------
local PRICE_PER_LITRE = 20 -- $
local lastUsage = 0
local closeDist = -1
local closePump = nil
local refueling = false
local fuelPumped = 0
local lastPump = 0
local fuelDecrement = 0

local FUEL_USAGE = {
    [1.0] = 1.9,
    [0.9] = 1.6,
    [0.8] = 1.3,
    [0.7] = 0.8,
    [0.6] = 0.7,
    [0.5] = 0.5,
    [0.4] = 0.4,
    [0.3] = 0.3,
    [0.2] = 0.2,
    [0.1] = 0.15,
    [0.0] = 0.1
}

-- List of helicopters and planes that will have a flight time of XX minutes
local flightTime45 = {"conada"}

-- List of helicopters and planes that will have a flight time of 25 minutes
local flightTime25 = {"polmav", "polair", "aw109", "annihilator2", "aw139", "frogger", "maverick", "swift", "swift2",
                      "volatus", "supervolito", "supervolito2", "dodo", "duster", "mammatus", "velum", "velum2",
                      "luxor2", "nimbus", "vestra"}

-- List of helicopters and planes that will have a flight time of 15 minutes
local flightTime15 = {"buzzard2", "seasparrow", "mh6m"}

-- List of helicopters and planes that will have a flight time of 10 minutes
local flightTime10 = {"havok", "besra"}

local VEHICLE_CLASSES = {
    [0] = 0.7, -- Compacts
    [1] = 0.8, -- Sedans
    [2] = 0.9, -- SUVs
    [3] = 1.0, -- Coupes
    [4] = 1.4, -- Muscle
    [5] = 1.3, -- Sports Classics
    [6] = 1.1, -- Sports
    [7] = 1.0, -- Super
    [8] = 2.0, -- Motorcycles
    [9] = 0.9, -- Off-road
    [10] = 1.0, -- Industrial
    [11] = 0.7, -- Utility
    [12] = 0.7, -- Vans
    [13] = 0.0, -- Cycles
    [14] = 1.0, -- Boats
    [15] = 0.9, -- Helicopters
    [16] = 1.0, -- Planes
    [17] = 1.0, -- Service
    [18] = 1.0, -- Emergency
    [19] = 1.0, -- Military
    [20] = 1.0, -- Commercial
    [21] = 1.0 -- Trains
}

local MAP_MARKERS = {vector3(49.4187, 2778.793, 58.043), vector3(263.894, 2606.463, 44.983),
                     vector3(1039.958, 2671.134, 39.550), vector3(1207.260, 2660.175, 37.899),
                     vector3(2539.685, 2594.192, 37.944), vector3(2679.858, 3263.946, 55.240),
                     vector3(2005.055, 3773.887, 32.403), vector3(1687.156, 4929.392, 42.078),
                     vector3(1701.314, 6416.028, 32.763), vector3(179.857, 6602.839, 31.868),
                     vector3(-94.4619, 6419.594, 31.489), vector3(-2554.996, 2334.40, 33.078),
                     vector3(-1800.375, 803.661, 138.651), vector3(-1437.622, -276.747, 46.207),
                     vector3(-2096.243, -320.286, 13.168), vector3(-724.619, -935.1631, 19.213),
                     vector3(-526.019, -1211.003, 18.184), vector3(-70.2148, -1761.792, 29.534),
                     vector3(265.648, -1261.309, 29.292), vector3(819.653, -1028.846, 26.403),
                     vector3(1208.951, -1402.567, 35.224), vector3(1181.381, -330.847, 69.316),
                     vector3(620.843, 269.100, 103.089), vector3(2581.321, 362.039, 108.468),
                     vector3(176.631, -1562.025, 29.263), vector3(176.631, -1562.025, 29.263),
                     vector3(-319.292, -1471.715, 30.549), vector3(1784.324, 3330.55, 41.253)}

local PUMP_MODELS = {-2007231801, 1339433404, 1694452750, 1933174915, -462817101, -469694731, -164877493, 1767019582}

function getVehicleFuel(veh)
    if not DoesEntityExist(veh) then
        return 0.0
    else
        return Entity(veh).state.fuelAmount or 0.0
    end
end

function setVehicleFuel(veh, value)
    if not DoesEntityExist(veh) then
        return
    end
    if (value > 100) then
        value = 100.0
    end
    if (value < 0) then
        value = 0
    end
    Entity(veh).state:set('fuelAmount', value, true)
end

function ManageFuel(veh)
    if DoesEntityExist(veh) and IsVehicleEngineOn(veh) then
        local fuel = getVehicleFuel(veh)

        if (fuel <= 5.0) then
            fuelFailure(veh, 500)
        elseif (fuel <= 4.0) then
            fuelFailure(veh, 1000)
        elseif (fuel <= 3.0) then
            fuelFailure(veh, 1500)
        elseif (fuel <= 2.0) then
            fuelFailure(veh, 2500)
        end

        local rpm = GetVehicleCurrentRpm(veh)
        if (rpm < 0.21) then
            rpm = rpm / 2
        end

        if GetVehicleClass(veh) == 15 or GetVehicleClass(veh) == 16 then -- Check if the vehicle is a helicopter or plane (Vehicle Class 15 or 16)

            if Entity(veh).state.fuelAmount < 1 then
                SetVehicleUndriveable(veh, true)
            end

            local vehicleModel = GetEntityModel(veh)

            for _, model in ipairs(flightTime10) do
                local vehHash = GetHashKey(model)
                if vehicleModel == vehHash then
                    fuelDecrement = 1.66
                    break
                end
            end

            if fuelDecrement == 0 then
                for _, model in ipairs(flightTime15) do
                    local vehHash = GetHashKey(model)
                    if vehicleModel == vehHash then
                        fuelDecrement = 1.11
                        break
                    end
                end
            end

            if fuelDecrement == 0 then
                for _, model in ipairs(flightTime25) do
                    local vehHash = GetHashKey(model)
                    if vehicleModel == vehHash then
                        fuelDecrement = 0.667
                        break
                    end
                end
            end

            if fuelDecrement == 0 then
                for _, model in ipairs(flightTime45) do
                    local vehHash = GetHashKey(model)
                    if vehicleModel == vehHash then
                        fuelDecrement = 0.37
                        break
                    end
                end
            end

        elseif GetVehicleClass(veh) ~= 15 then
            fuelDecrement = FUEL_USAGE[Round(rpm, 1)] * (VEHICLE_CLASSES[GetVehicleClass(veh)] or 1.0) / 8
        elseif GetVehicleClass(veh) == 8 and IsVehicleOffroadCapable(veh) then
            fuelDecrement = fuelDecrement * 3.5
        end

        setVehicleFuel(veh, getVehicleFuel(veh) - fuelDecrement)
    end

    fuelDecrement = 0
end

function Round(num, numDecimalPlaces)
    local mult = 10 ^ (numDecimalPlaces or 0)
    return math.floor(num * mult + 0.5) / mult
end

function RefuelingAnimation()
    Base.Animation.StartForcedAnim(PlayerPedId(), "timetable@gardener@filling_can", "gar_ig_5_filling_can", true, true,
        false)
end

function CheckVehicleFuel(veh)
    getVehicleFuel(veh)
    if not Entity(veh).state.fuelAmount and GetVehicleClass(veh) == 15 then
        setVehicleFuel(veh, 100)
    elseif not Entity(veh).state.fuelAmount then
        setVehicleFuel(veh, math.random(200, 800) / 10)
    end
end

function GetClosestFuelPump(coords)
    local closeObj = nil
    local closeDis = 4.0
    for _, v in pairs(PUMP_MODELS) do
        local obj = GetClosestObjectOfType(coords.x, coords.y, coords.z, 4.0, v, 0, 0, 0)
        if (DoesEntityExist(obj)) then
            local dist = #(coords - GetEntityCoords(obj))
            if (dist < closeDis) then
                closeObj = obj
                closeDis = dist
            end
        end
    end
    return closeObj
end

-- Event for syncing fuel setting
RegisterNetEvent("fuel:tryFuelVehicle")
AddEventHandler("fuel:tryFuelVehicle", function(vehNetId, totalFuel, plate)
    local vehicle = NetworkGetEntityFromNetworkId(vehNetId)
    setVehicleFuel(vehicle, totalFuel)
end)

Citizen.CreateThread(function()
    while true do
        Wait(2000)
        closePump = GetClosestFuelPump(GetEntityCoords(PlayerPedId()))
    end
end)

onBaseReady(function()
    while (true) do
        local ped = PlayerPedId()
        local sleep = 0
        local veh = GetVehiclePedIsIn(ped, false)
        local isEmergencyJob = DoesPlayerHaveOrg("police") or DoesPlayerHaveOrg("lses")

        if veh ~= 0 and ped == GetPedInVehicleSeat(veh, -1) then
            CheckVehicleFuel(veh)
        end

        if (veh ~= 0 and ped == GetPedInVehicleSeat(veh, -1) and
            (GetVehicleClass(veh) ~= 13 and GetVehicleClass(veh) ~= 21) and GetGameTimer() - lastUsage >= 10000) then
            lastUsage = GetGameTimer()
            ManageFuel(veh)
        end

        if veh == 0 then
            local coords = GetEntityCoords(PlayerPedId())
            local _, weapon = GetCurrentPedWeapon(ped, 1)
            local holdingJerry = (weapon == GetHashKey("weapon_petrolcan"))

            if holdingJerry and refueling and closePump == nil then
                local closeVehicle = Base.Vehicles.GetClosestVehicle(ped, 3.5, false)
                local current = GetAmmoInPedWeapon(PlayerPedId(), GetHashKey("weapon_petrolcan"))
                local jerryPerc = (current / 4500) * 100
                local jerryCoords = GetWorldPositionOfEntityBone(ped, GetPedBoneIndex(ped, 0xDEAD), 0.0, 0.0, 0.0)
                local vehCoords = GetEntityCoords(closeVehicle)
                local fuel = getVehicleFuel(closeVehicle)
                local fuelRemaining = 100 - fuel

                if (fuel + fuelPumped >= 100 and refueling and holdingJerry or not DoesEntityExist(closeVehicle)) then
                    refueling = false
                    Base.Notification("Fuel tank is Full!", {})
                    TriggerServerEvent("core_vehicles:buyFuel", NetworkGetNetworkIdFromEntity(closeVehicle), fuelPumped,
                        fuel + fuelPumped, true)
                    fuelPumped = 0
                    Base.Animation.StopForcedAnim(PlayerPedId())
                elseif jerryPerc == 0 and refueling or IsControlJustPressed(0, 38) then
                    refueling = false
                    TriggerServerEvent("core_vehicles:buyFuel", NetworkGetNetworkIdFromEntity(closeVehicle), fuelPumped,
                        fuel + fuelPumped, true)
                    fuelPumped = 0
                    Base.Animation.StopForcedAnim(PlayerPedId())
                end

                if GetGameTimer() - lastPump > 1000 then
                    lastPump = GetGameTimer()
                    fuelPumped = fuelPumped + 1
                end

                SetPedAmmo(ped, GetHashKey("weapon_petrolcan"), current - 1)

                Base.DrawText(jerryCoords.x, jerryCoords.y, jerryCoords.z - 0.2, math.floor(jerryPerc) .. "%")
                Base.DrawText(vehCoords.x, vehCoords.y, vehCoords.z + 1.0,
                    "[~r~E~s~] Filling Vehicle | " .. (math.floor(fuel + fuelPumped)) .. "/100")

            elseif holdingJerry and closePump == nil then
                local closeVehicle = Base.Vehicles.GetClosestVehicle(ped, 3.5, false)
                local vehCoords = GetEntityCoords(closeVehicle)
                local fuel = getVehicleFuel(closeVehicle)

                if DoesEntityExist(closeVehicle) and GetIsVehicleEngineRunning(closeVehicle) then
                    Base.DrawText(vehCoords.x, vehCoords.y, vehCoords.z + 1.10, "~r~TURN ENGINE OFF")
                end

                if DoesEntityExist(closeVehicle) and not refueling and not GetIsVehicleEngineRunning(closeVehicle) then
                    local current = GetAmmoInPedWeapon(PlayerPedId(), GetHashKey("weapon_petrolcan"))
                    local jerryPerc = (current / 4500) * 100
                    local jerryCoords = GetWorldPositionOfEntityBone(ped, GetPedBoneIndex(ped, 0xDEAD), 0.0, 0.0, 0.0)
                    local fuelRemaining = 100 - fuel

                    Base.DrawText(jerryCoords.x, jerryCoords.y, jerryCoords.z - 0.2, math.floor(jerryPerc) .. "%")
                    Base.DrawText(vehCoords.x, vehCoords.y, vehCoords.z + 1.0,
                        "[~g~G~s~] Fill Vehicle | " .. (math.floor(fuel + fuelPumped)) .. "/100")

                    if IsControlJustPressed(0, 47) and jerryPerc ~= 0 and fuelRemaining ~= 0 then
                        lastPump = GetGameTimer()
                        refueling = true
                        Base.Animation.StartForcedAnim(PlayerPedId(), "weapon@w_sp_jerrycan", "fire", true, true, true)
                    end
                end
            elseif closePump ~= nil and closeDist <= 4.0 then
                local closeVehicle = Base.Vehicles.GetClosestVehicle(ped, 3.5, false)

                if DoesEntityExist(closeVehicle) or holdingJerry then
                    local pumpCoords = GetEntityCoords(closePump)
                    local vehCoords = GetEntityCoords(closeVehicle)
                    local dist = #(coords - pumpCoords)
                    local fuel = getVehicleFuel(closeVehicle)
                    local fuelRemaining = 100 - fuel

                    if dist < 10.0 then
                        DrawMarker(20, vehCoords.x, vehCoords.y, vehCoords.z + 1.6, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.5,
                            0.5, -0.5, 0, 0, 150, 150, false, true, 2, 0.0, nil, nil, false)
                    end

                    if dist >= 4.0 and refueling and not holdingJerry and DoesEntityExist(closeVehicle) then
                        refueling = false
                        TriggerServerEvent("core_vehicles:buyFuel", NetworkGetNetworkIdFromEntity(closeVehicle),
                            fuelPumped, fuel + fuelPumped)
                        fuelPumped = 0
                        Base.Animation.StopForcedAnim(PlayerPedId())
                    end

                    if (fuel + fuelPumped >= 100 and refueling and not holdingJerry) then
                        refueling = false
                        Base.Notification("Fuel tank is Full!", {})
                        TriggerServerEvent("core_vehicles:buyFuel", NetworkGetNetworkIdFromEntity(closeVehicle),
                            fuelPumped, fuel + fuelPumped)
                        fuelPumped = 0
                        Base.Animation.StopForcedAnim(PlayerPedId())
                    elseif GetAmmoInPedWeapon(PlayerPedId(), GetHashKey("weapon_petrolcan")) >= 4500 and refueling and
                        holdingJerry then
                        refueling = false
                        Base.Notification("Jerry Can is Full!", {})
                        TriggerServerEvent("core_vehicles:buyFuel", nil, fuelPumped, fuel + fuelPumped)
                        fuelPumped = 0
                        Base.Animation.StopForcedAnim(PlayerPedId())
                    end

                    if holdingJerry and refueling and dist < 4.0 then
                        local current = GetAmmoInPedWeapon(PlayerPedId(), GetHashKey("weapon_petrolcan"))
                        local jerryPerc = (current / 4500) * 100
                        local jerryRemain = 4500 - current
                        local jerryCoords = GetWorldPositionOfEntityBone(ped, GetPedBoneIndex(ped, 0xDEAD), 0.0, 0.0,
                            0.0)

                        Base.DrawText(jerryCoords.x, jerryCoords.y, jerryCoords.z - 0.2, math.floor(jerryPerc) .. "%")
                        Base.DrawText(pumpCoords.x, pumpCoords.y, pumpCoords.z + 1.2, "Fill Price ~r~$" ..
                            math.ceil(jerryRemain / 255) * PRICE_PER_LITRE .. "~s~ | Per Litre ~g~$" .. PRICE_PER_LITRE)
                        Base.DrawText(pumpCoords.x, pumpCoords.y, pumpCoords.z + 1.0, "[~r~E~s~] Filling Jerry Can")

                        if GetGameTimer() - lastPump > 2000 then
                            local money = Base.GetPlayerData().money

                            if money - (math.floor(fuelPumped) * PRICE_PER_LITRE) <= 0 then
                                refueling = false
                                TriggerServerEvent("core_vehicles:buyFuel", nil, fuelPumped, fuel + fuelPumped)
                                fuelPumped = 0
                                Base.Animation.StopForcedAnim(PlayerPedId())
                            else
                                lastPump = GetGameTimer()
                                fuelPumped = fuelPumped + 1
                            end
                        end

                        AddAmmoToPed(ped, GetHashKey("weapon_petrolcan"), 1)

                        if IsControlJustPressed(0, 38) then
                            refueling = false
                            TriggerServerEvent("core_vehicles:buyFuel", nil, fuelPumped, fuel + fuelPumped)
                            fuelPumped = 0
                            Base.Animation.StopForcedAnim(PlayerPedId())
                        end
                    elseif holdingJerry and not refueling and dist < 4.0 then
                        local current = GetAmmoInPedWeapon(PlayerPedId(), GetHashKey("weapon_petrolcan"))
                        local jerryPerc = (current / 4500) * 100
                        local jerryRemain = 4500 - current
                        local jerryCoords = GetWorldPositionOfEntityBone(ped, GetPedBoneIndex(ped, 0xDEAD), 0.0, 0.0,
                            0.0)

                        Base.DrawText(jerryCoords.x, jerryCoords.y, jerryCoords.z - 0.2, math.floor(jerryPerc) .. "%")
                        Base.DrawText(pumpCoords.x, pumpCoords.y, pumpCoords.z + 1.2, "Fill Price ~r~$" ..
                            math.ceil(jerryRemain / 255) * PRICE_PER_LITRE .. "~s~ | Per Litre ~g~$" .. PRICE_PER_LITRE)
                        Base.DrawText(pumpCoords.x, pumpCoords.y, pumpCoords.z + 1.0, "[~g~G~s~] Fill Jerry Can")

                        local money = Base.GetPlayerData().money

                        if IsControlJustPressed(0, 47) and (money - (1 * PRICE_PER_LITRE) > 0) then
                            fuelPumped = 0
                            refueling = true
                            RefuelingAnimation()
                        elseif IsControlJustPressed(0, 47) and (money - (1 * PRICE_PER_LITRE) < 0) then
                            Base.Notification("You do not have enough cash to fill the Jerry Can!", {})
                        end
                    elseif GetIsVehicleEngineRunning(closeVehicle) and dist < 2.0 then
                        Base.DrawText(pumpCoords.x, pumpCoords.y, pumpCoords.z + 1.4, (math.floor(fuel)) .. "/100")
                        Base.DrawText(pumpCoords.x, pumpCoords.y, pumpCoords.z + 1.2, "Fill Price ~r~$" ..
                            math.floor(fuelRemaining) * PRICE_PER_LITRE .. "~s~ | Per Litre ~g~$" .. PRICE_PER_LITRE)
                        Base.DrawText(pumpCoords.x, pumpCoords.y, pumpCoords.z + 1.0, "~r~TURN ENGINE OFF")
                    elseif refueling then
                        Base.DrawText(pumpCoords.x, pumpCoords.y, pumpCoords.z + 1.4,
                            (math.floor(fuel + fuelPumped)) .. "/100")
                        Base.DrawText(pumpCoords.x, pumpCoords.y, pumpCoords.z + 1.2, "Current Price ~r~$" ..
                            math.floor(fuelPumped) * PRICE_PER_LITRE .. "~s~ | Per Litre ~g~$" .. PRICE_PER_LITRE)
                        Base.DrawText(pumpCoords.x, pumpCoords.y, pumpCoords.z + 1.0, "[~r~E~s~] Refueling Vehicle")

                        if GetGameTimer() - lastPump > 500 then
                            local money = Base.GetPlayerData().money

                            if not isEmergencyJob and money - (math.floor(fuelPumped) * PRICE_PER_LITRE) <= 0 then
                                refueling = false
                                TriggerServerEvent("core_vehicles:buyFuel", NetworkGetNetworkIdFromEntity(closeVehicle),
                                    fuelPumped, fuel + fuelPumped)
                                fuelPumped = 0
                                Base.Animation.StopForcedAnim(PlayerPedId())
                            else
                                lastPump = GetGameTimer()
                                fuelPumped = fuelPumped + 1
                            end
                        end

                        if IsControlJustPressed(0, 38) then
                            refueling = false
                            TriggerServerEvent("core_vehicles:buyFuel", NetworkGetNetworkIdFromEntity(closeVehicle),
                                fuelPumped, fuel + fuelPumped)
                            fuelPumped = 0
                            Base.Animation.StopForcedAnim(PlayerPedId())
                        end
                    elseif dist < 2.0 then
                        Base.DrawText(pumpCoords.x, pumpCoords.y, pumpCoords.z + 1.4, (math.floor(fuel)) .. "/100")
                        Base.DrawText(pumpCoords.x, pumpCoords.y, pumpCoords.z + 1.2, "Fill Price ~r~$" ..
                            math.floor(fuelRemaining) * PRICE_PER_LITRE .. "~s~ | Per Litre ~g~$" .. PRICE_PER_LITRE)
                        Base.DrawText(pumpCoords.x, pumpCoords.y, pumpCoords.z + 1.0, "[~g~G~s~] Refuel Vehicle")

                        local money = Base.GetPlayerData().money
                        local isEmergencyJob = DoesPlayerHaveOrg("police") or DoesPlayerHaveOrg("lses")
                        if IsControlJustPressed(0, 47) then
                            if isEmergencyJob or (money - (1 * PRICE_PER_LITRE) > 0) then
                                if DoesEntityExist(closeVehicle) then
                                    fuelPumped = 0
                                    refueling = true
                                    RefuelingAnimation()
                                end
                            else
                                Base.Notification("You do not have enough cash to buy fuel!", {})
                            end
                        end
                    elseif dist < 4.0 then
                        Base.DrawText(pumpCoords.x, pumpCoords.y, pumpCoords.z + 1.0, "Refuel Vehicle")
                    end
                elseif refueling then
                    refueling = false
                    TriggerServerEvent("core_vehicles:buyFuel", NetworkGetNetworkIdFromEntity(closeVehicle), fuelPumped,
                        fuel + fuelPumped)
                    fuelPumped = 0
                    Base.Animation.StopForcedAnim(PlayerPedId())
                end
            else
                if refueling then
                    refueling = false
                    TriggerServerEvent("core_vehicles:buyFuel", NetworkGetNetworkIdFromEntity(closeVehicle), fuelPumped,
                        fuel + fuelPumped)
                    fuelPumped = 0
                    Base.Animation.StopForcedAnim(PlayerPedId())
                end
                sleep = 2000
            end
        end

        Wait(sleep)
    end
end)
