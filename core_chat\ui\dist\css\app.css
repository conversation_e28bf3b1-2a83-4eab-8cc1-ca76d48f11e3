.messageTitle[data-v-0a7d4e20] {
    display: inline-block;
    font-weight: 700;
    margin-right: 0.5em;
}
.messageTitle[data-v-0a7d4e20] * {
    font-size: inherit;
}
.value[data-v-38ec8f4d] {
    color: #efefef;
    word-break: break-word;
    word-wrap: break-word;
}
.value[data-v-38ec8f4d] * {
    font-size: inherit;
}
.message-container[data-v-5ac944c3] {
    text-shadow: 1px 1px 2px #000;
}
.message-container.smallFont[data-v-5ac944c3] * {
    font-size: 1.1vh;
}
.messages-container[data-v-21f36f14] {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
    height: 100%;
    overflow-y: scroll;
    border-radius: 0.25em;
    border: 1px solid transparent;
    padding: 0.25em 0.5em;
}
.messages-container[data-v-21f36f14]::-webkit-scrollbar {
    display: none;
}
.messages-container.active[data-v-21f36f14] {
    border: 1px solid #214a6b;
    background-color: rgba(28, 28, 28, 0.447);
}
.messages-container.cursorActive[data-v-21f36f14] {
    border: 1px solid #6b2121;
    background-color: rgba(28, 16, 16, 0.447);
}
.input-container[data-v-09ce8b34] {
    width: 100%;
    padding: 0.5em;
    background-color: #10161c;
    border-radius: 0.25em;
    border: 1px solid #214a6b;
}
.input-container.cursorActive[data-v-09ce8b34] {
    border: 1px solid #6b2121;
    background-color: #1c1010;
}
.input[data-v-09ce8b34] {
    width: 100%;
    color: #fff;
}
.input[data-v-09ce8b34]:focus {
    outline: none;
    border: none;
    background-image: none;
    background-color: transparent;
    box-shadow: none;
}
.suggestion-container[data-v-21d6b7f4] {
    display: flex;
    flex-direction: column;
    gap: 0.1em;
    padding: 0.25em 0.5em;
    color: #fff;
}
.main-container[data-v-21d6b7f4] {
    display: flex;
    align-items: center;
    gap: 0.25em;
}
.main-container .selected[data-v-21d6b7f4] {
    color: #108bff;
}
.main-container .cursorActive[data-v-21d6b7f4] {
    color: #ff1010;
}
.params-container[data-v-21d6b7f4] {
    display: flex;
    align-items: center;
    gap: 0.25em;
}
.name[data-v-21d6b7f4] {
    font-weight: 500;
}
.param-help[data-v-21d6b7f4] {
    font-size: 0.9rem;
    color: #ffa200;
}
.help[data-v-21d6b7f4] {
    font-size: 0.9rem;
}
.suggestions-container[data-v-17e7621b] {
    display: flex;
    flex-direction: column;
    gap: 0.25em;
    background-color: rgba(28, 28, 28, 0.922);
    border-radius: 0.25em;
    border: 1px solid #4a4a4a;
}
.container[data-v-532e3371] {
    display: flex;
}
.chat-container[data-v-56d12976] {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 0.25em;
    height: 45vh;
    max-width: 1000px;
    opacity: 0;
    transition: opacity 0.2s ease;
    visibility: visible;
}
.chat-container.showing[data-v-56d12976] {
    opacity: 1;
}
.chat-container.hidden[data-v-56d12976] {
    visibility: hidden;
}
.settings-button-container[data-v-56d12976] {
    display: flex;
    justify-content: flex-end;
    position: absolute;
    top: -0.2em;
    width: 100%;
    transform: translateY(-100%);
}
.input-container[data-v-56d12976] {
    position: absolute;
    bottom: -0.1em;
    width: 100%;
    transform: translateY(100%);
}
.suggestions-container[data-v-56d12976] {
    position: absolute;
    bottom: -1.5em;
    width: 100%;
    transform: translateY(100%);
}
.chat-helper-container[data-v-6d9a26ec] {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 0.25em 0.5em;
    border-radius: 0.25em;
    border: 1px solid #214a6b;
    background-color: rgba(28, 28, 28, 0.447);
    color: #efefef;
    font-size: 1.3vh;
}
.chat-helper-container.cursorActive[data-v-6d9a26ec] {
    border: 1px solid #6b2121;
    background-color: rgba(28, 16, 16, 0.447);
}
.chat-helper-container .button-color[data-v-6d9a26ec] {
    color: #ff0;
}
* {
    font-size: 1.3vh;
}
:root {
    overflow: hidden;
}
body {
    margin: 0;
    box-sizing: border-box;
}
.chat-wrapper {
    position: absolute;
    left: 0.5em;
    top: 45%;
    transform: translateY(-50%);
}
.chat-helper-wrapper {
    position: absolute;
    left: 50%;
    top: 0.5em;
    transform: translate(-50%);
}
