exports("GetPrimaryFX", function()
    return GetResourceKvpString("voiceradio_fx1_" .. PlayerData.charid)
end)

-- Radio Channel(s)
Voice.Radio = {
    blocked = false,
    enabled = false,
    secondaryEnabled = false,
    activeChannel = 51,
    secondaryChannel = 52,
    activeChannelDirection = "C",
    secondaryChannelDirection = "C",
    usingRadio = false,
    primaryTransmission = true,
    configuring = false,
    radioEffects = true,
    radioFilter = true,
    radioVolume = 0.4,
    secondaryVolume = 0.4,
    hasRadio = false,
    radioSubmix = -1,
    primaryTargets = {},
    secondaryTargets = {},

    labels = {
        ["RADIO_CONFIGURE"] = {
            "COMM1: ~a~~w~ (~1~MHz) (~1~%)",
            "COMM2: ~a~~w~ (~1~MHz) (~1~%)",
            "Press ~INPUT_PICKUP~ to toggle radio",
            "Press ~INPUT_CELLPHONE_LEFT~ & ~INPUT_CELLPHONE_RIGHT~ to cycle channel",
            "Press ~INPUT_CELLPHONE_UP~ & ~INPUT_CELLPHONE_DOWN~ to adjust volume",
            "Press ~INPUT_DETONATE~ to ~a~~w~ radio beeps",
            "Press ~INPUT_VEH_HEADLIGHT~ to toggle radio filter",
            "Hold ~INPUT_SPRINT~ to modify COMM2",
        }
    }
}

local jobRadioFX = {}

Citizen.CreateThread(function()
    for k, v in pairs(Voice.Radio.labels) do AddTextEntry(k, table.concat(v, "~n~~w~")) end

    Voice.Radio.radioSubmix = CreateAudioSubmix("Radio")
    Voice.Radio.leftSubmix = CreateAudioSubmix("Left")
    Voice.Radio.rightSubmix = CreateAudioSubmix("Right")

    if (Voice.Radio.radioSubmix == -1) then
        print("Failed to create radio submix!")
    end

    if (Voice.Radio.leftSubmix == -1) then
        print("Failed to create left submix!")
    end

    if (Voice.Radio.rightSubmix == -1) then
        print("Failed to create right submix!")
    end

    SetAudioSubmixEffectRadioFx(Voice.Radio.radioSubmix, 0)
    SetAudioSubmixEffectParamInt(Voice.Radio.radioSubmix, 0, GetHashKey("default"), 1)
    AddAudioSubmixOutput(Voice.Radio.radioSubmix, 0)

    SetAudioSubmixEffectRadioFx(Voice.Radio.leftSubmix, 0)
    SetAudioSubmixEffectParamInt(Voice.Radio.leftSubmix, 0, GetHashKey("default"), 1)
    AddAudioSubmixOutput(Voice.Radio.leftSubmix, 0)

    SetAudioSubmixEffectRadioFx(Voice.Radio.rightSubmix, 0)
    SetAudioSubmixEffectParamInt(Voice.Radio.rightSubmix, 0, GetHashKey("default"), 1)
    AddAudioSubmixOutput(Voice.Radio.rightSubmix, 0)
end)

local radioObj = {
    model = GetHashKey("prop_cs_hand_radio"),
    bone = 18905,
    offset = vector3(0.1, 0.0, 0.03),
    rotation = vector3(-90.0, 45.0, 0.0),
    dict = "random@arrests",
    anim = "generic_radio_chatter",
    handles = {}
}

local radioConfigureObj = {
    model = GetHashKey("prop_cs_hand_radio"),
    bone = 28422,
    offset = vector3(0.0, 0.0, 0.00),
    rotation = vector3(0.0, 0.0, 0.0),
    dict = "cellphone@",
    anim = "cellphone_text_in",
    handles = {}
}

local radioObjHandles = {}

Voice.Radio.toggleRadio = function(secondary, state)
    if not Voice.Radio.blocked and Voice.Radio.hasRadio then
        if not secondary then
            if state ~= nil then
                Voice.Radio.enabled = state
            else
                Voice.Radio.enabled = not Voice.Radio.enabled
            end

            if Voice.Radio.enabled then
                Base.Notification("COMM 1 <font color='green'>Activated")

                if Voice.Radio.activeChannel then
                    Base.Notification("Radio Frequency: " .. Voice.Radio.activeChannel)
                end
            else
                Base.Notification("COMM 1 <font color='red'>Deactivated")
            end

            TriggerServerEvent("radio:toggleRadio", Voice.Radio.enabled, false, Voice.Radio.activeChannel)
            SendNUIEvent("setActive", { primary = true, toggle = Voice.Radio.enabled })
        else
            if state ~= nil then
                Voice.Radio.secondaryEnabled = state
            else
                Voice.Radio.secondaryEnabled = not Voice.Radio.secondaryEnabled
            end

            if Voice.Radio.secondaryEnabled then
                Base.Notification("COMM 2 <font color='green'>Activated")

                if Voice.Radio.secondaryChannel then
                    Base.Notification("Radio Frequency: " .. Voice.Radio.secondaryChannel)
                end
            else
                Base.Notification("COMM 2 <font color='red'>Deactivated")
            end

            TriggerServerEvent("radio:toggleRadio", Voice.Radio.secondaryEnabled, true, Voice.Radio.secondaryChannel)
            SendNUIEvent("setActive", { primary = false, toggle = Voice.Radio.secondaryEnabled })
        end
    end
end

Voice.Radio.setFrequency = function(fString, blockNotification, secondary)
    local channelNumber = tonumber(fString)
    if not channelNumber then
        return Base.Notification("Invalid Frequency")
    end
    local channel = math.floor(channelNumber)

    if channel and channel > 0 and channel < 99999999 then
        if Voice.Radio.isChannelRestricted(channel) then
            if not blockNotification then Base.Notification("Frequency Locked!") end
            return Voice.Radio.setFrequency(50, false, secondary)
        else
            if not secondary then
                -- Handle player leaving previous channel
                if Voice.Radio.activeChannel then
                    TriggerServerEvent("radio:leaveChannel", Voice.Radio.activeChannel)
                end

                Voice.Radio.activeChannel = channel
                if not blockNotification then Base.Notification("COMM1 Frequency: " .. Voice.Radio.activeChannel) end
                TriggerServerEvent("radio:joinChannel", Voice.Radio.activeChannel, secondary)
                SetResourceKvp("voiceradio_fx1_" .. PlayerData.charid, channel)
            else
                -- Handle player leaving previous channel
                if Voice.Radio.secondaryChannel then
                    TriggerServerEvent("radio:leaveChannel", Voice.Radio.secondaryChannel, secondary)
                end

                Voice.Radio.secondaryChannel = channel
                if not blockNotification then Base.Notification("COMM2 Frequency: " .. Voice.Radio.secondaryChannel) end
                TriggerServerEvent("radio:joinChannel", Voice.Radio.secondaryChannel, secondary)
                SetResourceKvp("voiceradio_fx2_" .. PlayerData.charid, channel)
            end
        end
    elseif not blockNotification then
        Base.Notification("Invalid Frequency!")
    end

    SendNUIEvent("setFrequency", { secondary = secondary, fx = channel })
end

Voice.Radio.setDirection = function(direction, primary)
    if not direction then return print("Invalid Direction") end

    if primary then
        Voice.Radio.activeChannelDirection = direction
        SetResourceKvp("voicedirection_fx1_" .. PlayerData.charid, direction)
    else
        Voice.Radio.secondaryChannelDirection = direction
        SetResourceKvp("voicedirection_fx2_" .. PlayerData.charid, direction)
    end
end

Voice.Radio.toggleRadioEffects = function()
    Voice.Radio.radioEffects = true
end

Voice.Radio.toggleRadioFilter = function()
    Voice.Radio.radioFilter = true
end

Voice.Radio.setVolume = function(volume, secondary)
    if not secondary then
        Voice.Radio.radioVolume = math.max(math.min(volume, 0.99), 0.0)
    else
        Voice.Radio.secondaryVolume = math.max(math.min(volume, 0.99), 0.0)
    end
end

Voice.Radio.blockRadio = function(state, notif)
    Voice.Radio.blocked = state

    if notif then
        if state then
            Base.Notification("Radio temporarily blocked!")
        else
            Base.Notification("Radio unblocked!")
        end
    end
end

Voice.Radio.configureRadio = function(state)
    if (state) and not IsPedSwimming(PlayerPedId()) then
        SetNuiFocus(true, true)
        SetNuiFocusKeepInput(true)
        SendNUIEvent("showDisplay", true)

        playRadioAnim(true, true)
        setRadioObject(true, true)

        CreateThread(function()
            while Voice.Radio.configuring do
                Wait(0)
                DisableControlAction(0, 1, true)
                DisableControlAction(0, 2, true)
                DisableControlAction(0, 200, true)
                DisablePlayerControls({ attack = true })

                if IsDisabledControlJustReleased(0, 200) or IsPedSwimming(PlayerPedId()) then
                    Voice.Radio.configureRadio(false)
                end
            end
        end)
    else
        SetNuiFocus(false, false)
        SetNuiFocusKeepInput(false)
        SendNUIEvent("showDisplay", false)
        playRadioAnim(false, true)
        Wait(200)
        setRadioObject(false, true)
    end

    Voice.Radio.configuring = state
end

Voice.Radio.isChannelRestricted = function(channel)
    if channel > 0 and channel < 50 and not DoesPlayerHaveOrgPermission("restrictedradio") then
        return true
    end
    for k, v in pairs(jobRadioFX) do
        if channel == v.radioFX then
            if not DoesPlayerHaveOrg(v.name) then
                return true
            end
        end
    end
    return false
end

RegisterNetEvent("radio:radioSpeak")
AddEventHandler("radio:radioSpeak", function(source, channel, state)
    if not Voice.Radio.blocked and Voice.Radio.hasRadio then
        -- COMM1
        if channel == Voice.Radio.activeChannel and Voice.Radio.enabled and not Voice.Radio.isChannelRestricted(Voice.Radio.activeChannel) then
            if state then
                local direction = Voice.Radio.activeChannelDirection
                if direction == "L" then
                    SetAudioSubmixOutputVolumes(Voice.Radio.leftSubmix, 0, 1.0, 0.0, 1.0, 0.0, 1.0, 1.0)
                    MumbleSetSubmixForServerId(source, Voice.Radio.leftSubmix)
                elseif direction == "R" then
                    SetAudioSubmixOutputVolumes(Voice.Radio.rightSubmix, 0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0)
                    MumbleSetSubmixForServerId(source, Voice.Radio.rightSubmix)
                else
                    MumbleSetSubmixForServerId(source, Voice.Radio.radioSubmix)
                end

                vlog("Receiving on COMM1 from " .. source)
                Voice.forcePlayerVolume(source, Voice.Radio.radioVolume or 0.4)

                if Voice.Radio.radioEffects then
                    vlog("Playing radio on effect")
                    SendNUIEvent("soundEffect", "radio_speak")
                end
            else
                if Voice.Radio.radioEffects then
                    vlog("Playing radio off effect")
                    SendNUIEvent("soundEffect", "radio_speak_finish")
                end
            end

            -- COMM2
        elseif channel == Voice.Radio.secondaryChannel and Voice.Radio.secondaryEnabled and not Voice.Radio.isChannelRestricted(Voice.Radio.secondaryChannel) then
            if state then
                local direction = Voice.Radio.secondaryChannelDirection
                if direction == "L" then
                    SetAudioSubmixOutputVolumes(Voice.Radio.leftSubmix, 0, 1.0, 0.0, 1.0, 0.0, 1.0, 1.0)
                    MumbleSetSubmixForServerId(source, Voice.Radio.leftSubmix)
                elseif direction == "R" then
                    SetAudioSubmixOutputVolumes(Voice.Radio.rightSubmix, 0, 0.0, 1.0, 0.0, 0.0, 1.0, 1.0)
                    MumbleSetSubmixForServerId(source, Voice.Radio.rightSubmix)
                else
                    MumbleSetSubmixForServerId(source, Voice.Radio.radioSubmix)
                end

                vlog("Receiving on COMM2 from " .. source)
                Voice.forcePlayerVolume(source, Voice.Radio.secondaryVolume or 0.4)

                if Voice.Radio.radioEffects then
                    vlog("Playing radio on aux effect")
                    SendNUIEvent("soundEffect", "radio_speak_alt")
                end
            else
                if Voice.Radio.radioEffects then
                    vlog("Playing radio off aux effect")
                    SendNUIEvent("soundEffect", "radio_speak_finish")
                end
            end
        end
    end

    if not state then
        Voice.forcePlayerVolume(source, -1.0)
        MumbleSetSubmixForServerId(source, -1)
    end
end)

RegisterNetEvent("radio:channelTargetsUpdated")
AddEventHandler("radio:channelTargetsUpdated", function(channel, players)
    if channel == Voice.Radio.activeChannel then
        -- Update the primary voice targets
        Voice.Radio.primaryTargets = players
        vlog("Updating primary channel targets: " .. json.encode(players))

        -- If the radio is active, apply the voice targets again
        if Voice.Radio.usingRadio and Voice.Radio.primaryTransmission then
            Voice.applyVoiceTargets(Voice.Radio.primaryTargets)
        end
    elseif channel == Voice.Radio.secondaryChannel then
        -- Update the primary voice targets
        Voice.Radio.secondaryTargets = players
        vlog("Updating secondary channel targets: " .. json.encode(players))

        -- If the radio is active, apply the voice targets again
        if Voice.Radio.usingRadio and not Voice.Radio.primaryTransmission then
            Voice.applyVoiceTargets(Voice.Radio.secondaryTargets)
        end
    end
end)

function canPlayerUsePrimaryRadio()
    if not Voice.Radio.enabled or Voice.Radio.blocked or not Voice.Radio.hasRadio then
        return false
    end

    if not Voice.Radio.activeChannel or Voice.Radio.isChannelRestricted(Voice.Radio.activeChannel) then
        return false
    end

    return true
end

function canPlayerUseSecondaryRadio()
    if not Voice.Radio.secondaryEnabled or Voice.Radio.blocked or not Voice.Radio.hasRadio then
        return false
    end

    if not Voice.Radio.secondaryChannel or Voice.Radio.isChannelRestricted(Voice.Radio.secondaryChannel) then
        return false
    end

    return true
end

-- Is the player in a condition to use the radio?
function canPedUseRadio()
    if isCuffed or IsPedBeingStunned(PlayerPedId(), 0) then
        return false
    end

    if LocalPlayer.state.unconscious or LocalPlayer.state.dead or LocalPlayer.state.wounded then
        return false
    end

    return true
end

onBaseReady(function()
    TriggerServerEvent("radio:requestJobFX")

    Base.Controls.register("capital", "Radio (Use)",
        function()
            print(canPedUseRadio(), canPlayerUsePrimaryRadio())
            if canPedUseRadio() and canPlayerUsePrimaryRadio() then
                Voice.Radio.usingRadio = true
                Voice.Radio.primaryTransmission = true
                TriggerServerEvent("radio:radioSpeak", Voice.Radio.activeChannel, true)
                vlog("primary", json.encode(Voice.Radio.primaryTargets))
                vlog("secondary", json.encode(Voice.Radio.secondaryTargets))
                Voice.applyVoiceTargets(Voice.Radio.primaryTargets)

                if not isPlayerWearingRadioClothing() then
                    if Voice.Radio.usingRadio then playRadioAnim(true) end
                    if Voice.Radio.usingRadio then setRadioObject(true) end
                end
            end
        end,

        function()
            if Voice.Radio.usingRadio then
                Voice.Radio.usingRadio = false
                TriggerServerEvent("radio:radioSpeak", Voice.Radio.activeChannel, false)
            end
            playRadioAnim(false)
            Wait(200)
            setRadioObject(false)
        end,

        function()
            if canPedUseRadio() then
                SetControlNormal(0, 249, 1.0)
                SetControlNormal(1, 249, 1.0)
                SetControlNormal(2, 249, 1.0)
            end
        end
    )

    Base.Controls.register("Y", "Radio (COMM2) (Use)",
        function()
            if canPedUseRadio() and canPlayerUseSecondaryRadio() then
                Voice.Radio.usingRadio = true
                Voice.Radio.primaryTransmission = false
                TriggerServerEvent("radio:radioSpeak", Voice.Radio.secondaryChannel, true)
                Voice.applyVoiceTargets(Voice.Radio.secondaryTargets)

                if not isPlayerWearingRadioClothing() then
                    if Voice.Radio.usingRadio and not IsPedInAnyVehicle(PlayerPedId(), true) then playRadioAnim(true) end
                    if Voice.Radio.usingRadio then setRadioObject(true) end
                end
            end
        end,

        function()
            if Voice.Radio.usingRadio then
                Voice.Radio.usingRadio = false
                TriggerServerEvent("radio:radioSpeak", Voice.Radio.secondaryChannel, false)
            end
            if not IsPedInAnyVehicle(PlayerPedId(), true) then playRadioAnim(false) end
            Wait(200)
            setRadioObject(false)
        end,

        function()
            if canPedUseRadio() then
                SetControlNormal(0, 249, 1.0)
                SetControlNormal(1, 249, 1.0)
                SetControlNormal(2, 249, 1.0)
            end
        end
    )

    Base.Controls.register("F2", "Radio (Toggle)", function()
        Voice.Radio.toggleRadio(IsControlPressed(1, 21))
    end)

    Base.Controls.register("F4", "Radio (Configure)", function()
        if Voice.Radio.hasRadio then
            Voice.Radio.configureRadio(not Voice.Radio.configuring)
        else
            Base.Notification("You do not have a radio!", { timeout = 800 })
        end
    end)

    Base.loadModule("Voice", Voice)

    Wait(500)
    Voice.Radio.hasRadio = exports["core_inventory"]:getItem("radio") ~= false
end)

RegisterNetEvent("inventory:clientSync")
AddEventHandler('inventory:clientSync', function(name, inv, money, dirty, capacity)
    if name == "playerinventory-" .. GetPlayerServerId(PlayerId()) then
        Wait(500)
        Voice.Radio.hasRadio = exports["core_inventory"]:getItem("radio") ~= false
    end
end)

function cleanupRadioObjects()
    --[[for i = #radioObjHandles, 1, -1 do
        local count = 0
        NetworkRequestControlOfEntity(radioObjHandles[i])
        while not NetworkHasControlOfEntity(radioObjHandles[i]) and count < 100 do
            Wait(0)
            count = count + 1
        end

        count = 0
        DetachEntity(radioObjHandles[i], true, false)
        while DoesEntityExist(radioObjHandles[i]) and count < 100 do
            Wait(0)
            DeleteEntity(radioObjHandles[i])
            count = count + 1
        end

        if (not DoesEntityExist(radioObjHandles[i])) then
            table.remove(radioObjHandles, i)
        end
    end]]
    local ped = PlayerPedId()
    local coords = GetEntityCoords(ped)
    local radio = GetClosestObjectOfType(coords.x, coords.y, coords.z, 3.0, radioObj.model, 0, 0, 0)
    if DoesEntityExist(radio) and GetEntityAttachedTo(radio) == PlayerPedId() then
        DeleteEntity(radio)
    end
end

function setRadioObject(state, configure)
    local _radioObj = radioObj
    if configure then _radioObj = radioConfigureObj end

    if state then
        cleanupRadioObjects()

        RequestModel(_radioObj.model)
        while not HasModelLoaded(_radioObj.model) do Citizen.Wait(150) end

        local playerPed = PlayerPedId()
        local coords = GetEntityCoords(playerPed)
        local bone = GetPedBoneIndex(playerPed, _radioObj.bone)

        if IsPedInAnyVehicle(playerPed, true) or IsPedFalling(playerPed) or IsPlayerParachuting() then
            return
        end

        local radio = CreateObject(_radioObj.model, coords.x, coords.y, coords.z, true, true, false)
        AttachEntityToEntity(radio, playerPed, bone, _radioObj.offset.x, _radioObj.offset.y, _radioObj.offset.z,
            _radioObj.rotation.x, _radioObj.rotation.y, _radioObj.rotation.z, true, false, false, false, 2, true)
        SetEntityCollision(radio, false, true)

        SetModelAsNoLongerNeeded(radio)
        --table.insert(radioObjHandles, radio)
    else
        cleanupRadioObjects()
    end
end

local radioClothing = {
    hat = {
        ["75"] = true,
        ["111"] = true,
        ["116"] = true,
        ["117"] = true,
        ["118"] = true,
        ["119"] = true,
        ["205"] = true,
        ["216"] = true,
        ["235"] = true,        
        ["240"] = true,
        ["241"] = true,
        ["242"] = true,
        ["246"] = true,
        ["250"] = true,
        ["255"] = true,
        ["256"] = true,
        ["257"] = true,
        ["258"] = true,
    }
}

function isPlayerWearingRadioClothing()
    local hat = GetPedPropIndex(PlayerPedId(), 0)
    return radioClothing.hat[tostring(hat)] or false
end

function IsPlayerParachuting()
    local playerPed = PlayerPedId()
    if GetPedParachuteState(playerPed) == 0 or
        GetPedParachuteState(playerPed) == 1 or
        GetPedParachuteState(playerPed) == 2 or
        GetPedParachuteState(playerPed) == 3 or
        IsPedInParachuteFreeFall(playerPed) then
        return true
    end
    return false
end

function playRadioAnim(state, configure)
    local _radioObj = radioObj
    if configure then _radioObj = radioConfigureObj end

    if state then
        Base.Animation.Start(PlayerPedId(), _radioObj.dict, _radioObj.anim, false, false, true, -8.0, 50)
    else
        if (IsEntityPlayingAnim(PlayerPedId(), _radioObj.dict, _radioObj.anim, 3)) then
            Base.Animation.Stop(PlayerPedId(), _radioObj.dict, _radioObj.anim)
        end
    end
end

RegisterNetEvent("radio:listChannel")
AddEventHandler("radio:listChannel", function(occupants, staff)
    local function notif(text)
        TriggerEvent('chat:addMessage', "RADIO", { 255, 0, 0 }, text)
    end

    if not occupants or #occupants < 1 then
        notif("No one on specificed channel")
    end

    for source, v in pairs(occupants) do
        if v.enabled then
            state = ''
        else
            state = " | Muted"
        end

        if staff then
            notif(v.name .. state)
        else
            notif((v.callsign or "NOCALL") .. " | " .. v.icname .. state)
        end
    end
end)

RegisterNetEvent("radio:joinChannel")
AddEventHandler("radio:joinChannel", function(channel, player)
    if channel == Voice.Radio.activeChannel and player.callsign then
        Base.Notification(player.callsign .. " joined frequency", { timeout = 800 })
    end
end)

RegisterNetEvent("radio:leaveChannel")
AddEventHandler("radio:leaveChannel", function(channel, player)
    if (player ~= nil) then
        if channel == Voice.Radio.activeChannel and player.callsign then
            Base.Notification(player.callsign .. " left frequency", { timeout = 800 })
        end
    end
end)

-- Restore Radio Frequencies
onBaseReady(function()
    if Base and Base.GetPlayerData then
        PlayerData = Base.GetPlayerData()
        if PlayerData and PlayerData.charid then
            local fx1 = GetResourceKvpString("voiceradio_fx1_" .. PlayerData.charid) or "51"
            local fx2 = GetResourceKvpString("voiceradio_fx2_" .. PlayerData.charid) or "52"

            Voice.Radio.activeChannelDirection = GetResourceKvpString("voicedirection_fx1_" .. PlayerData.charid) or "C"
            Voice.Radio.secondaryChannelDirection = GetResourceKvpString("voicedirection_fx2_" .. PlayerData.charid) or
            "C"
            SendNUIEvent("setDirection", { direction = Voice.Radio.activeChannelDirection, secondary = false })
            SendNUIEvent("setDirection", { direction = Voice.Radio.secondaryChannelDirection, secondary = true })

            Voice.Radio.setFrequency(fx1, true, false)
            Voice.Radio.setFrequency(fx2, true, true)
        end
    end
end)

-- Restore Radio Frequencies
RegisterNetEvent("base:characterLoaded", function()
    Wait(1000) -- wait jor job perms to load

    local fx1 = GetResourceKvpString("voiceradio_fx1_" .. PlayerData.charid) or "51"
    Voice.Radio.setFrequency(fx1, true, false)

    Wait(0)
    local fx2 = GetResourceKvpString("voiceradio_fx2_" .. PlayerData.charid) or "52"
    Voice.Radio.setFrequency(fx2, true, true)

    Voice.Radio.activeChannelDirection = GetResourceKvpString("voicedirection_fx1_" .. PlayerData.charid) or "C"
    Voice.Radio.secondaryChannelDirection = GetResourceKvpString("voicedirection_fx2_" .. PlayerData.charid) or "C"

    SendNUIEvent("setDirection", { direction = Voice.Radio.activeChannelDirection, secondary = false })
    SendNUIEvent("setDirection", { direction = Voice.Radio.secondaryChannelDirection, secondary = true })
end)

RegisterNetEvent("radio:requestJobFX")
AddEventHandler('radio:requestJobFX', function(result)
    jobRadioFX = result
end)

-- NUI Events
RegisterNUICallback("setRadio", function(data, cb)
    Voice.Radio.setFrequency(data.fx, true, not data.primary)
    cb(true)
end)

RegisterNUICallback("setVolume", function(data, cb)
    Voice.Radio.setVolume(data.volume, not data.primary)
    cb(true)
end)

RegisterNUICallback("setActive", function(data, cb)
    Voice.Radio.toggleRadio(not data.primary, data.toggle)
    cb(true)
end)

RegisterNUICallback("setDirection", function(data, cb)
    local direction = data.direction
    local radio = data.primary
    Voice.Radio.setDirection(direction, radio)
    cb(true)
end)
