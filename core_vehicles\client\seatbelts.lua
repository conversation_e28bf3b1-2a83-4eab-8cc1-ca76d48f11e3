local EXIT_SPEED = 28.0
local speedBuffer = {}
local velBuffer = {}
local beltOn = false

block_seatbelt_vehicles = {}

function FowardVector2(entity)
    local hr = GetEntityHeading(entity) + 90.0
    if hr < 0.0 then
        hr = 360.0 + hr
    end
    hr = hr * 0.0174533
    return { x = math.cos(hr) * 2.0, y = math.sin(hr) * 2.0 }
end

function IsPedInCar(veh)
    local vc = GetVehicleClass(veh)
    return (vc >= 0 and vc <= 7) or (vc >= 9 and vc <= 12) or (vc >= 17 and vc <= 20)
end

onBaseReady(function()
    TriggerServerCallback("core_vehicles:getBlockedSeatbeltVehicles", function(result)
        for k, veh in pairs(result) do
            if veh.blockSeatbelt then
                block_seatbelt_vehicles[GetHashKey(veh.model)] = true
            end
        end
    end)
end)

RegisterNetEvent("core_vehicles:yeet", function()
    Citizen.CreateThread(function()
        if not beltOn and (not PlayerData or PlayerData.identifier ~= 1) then
            local ped = PlayerPedId()
            local pos = GetEntityCoords(ped)
            local fwd = FowardVector2(ped)
            SetPedCanBeKnockedOffVehicle(ped, 0)
            SetEntityCoords(ped, pos.x + fwd.x, pos.y + fwd.y, pos.z + 1.0, true, true, true)
            SetEntityVelocity(ped, velBuffer[2].x * 2.5, velBuffer[2].y * 2.5, velBuffer[2].z * 2.5)
            Wait(1)
            SetPedToRagdoll(ped, math.random(4000, 9000), math.random(4000, 9000), 0, 0, 0, 0)
            Wait(math.random(1500, 3000))
            ShakeGameplayCam("DRUNK_SHAKE", 1.5)
            Wait(math.random(4000, 10000))
            StopGameplayCamShaking(true)
        end
    end)
end)

-- Add the following function into the driverTicks table.
-- This function will be called every frame that the player is in the driver seats (up to 500ms delay)
AddEventHandler("vehicleDriverTick", function(veh)
    local veh = GetVehiclePedIsIn(PlayerPedId(), false)
    local forward = (GetEntitySpeedVector(veh, true).y > 0.0)
    local speed = GetEntitySpeed(veh)
    speedBuffer[2] = speedBuffer[1]
    speedBuffer[1] = speed

    if (forward and speedBuffer[2] > EXIT_SPEED / 2 and
            (speedBuffer[2] - speedBuffer[1] > (speedBuffer[1] * 0.255)) and
            not IsPedOnAnyBike(PlayerPedId()) and
            IsPedInAnyVehicle(PlayerPedId()))
    then
        Blackout(veh, speedBuffer[2] * 3.6) -- mutliplied by 3.6 because the blackout script uses km/h
        if (forward and speedBuffer[2] > EXIT_SPEED and
                (speedBuffer[2] - speedBuffer[1] > (speedBuffer[1] * 0.255)))
        then
            local players = {}
            for i = -1, 5, 1 do
                local ped = GetPedInVehicleSeat(veh, i)
                if (DoesEntityExist(ped) and IsPedAPlayer(ped)) then
                    local index = NetworkGetPlayerIndexFromPed(ped)
                    if (index) then
                        table.insert(players, GetPlayerServerId(index))
                    end
                    TriggerServerEvent("core_vehicles:yeet", players)
                    speedBuffer[1] = 0.0
                    speedBuffer[2] = 0.0
                end
            end
        end
    end
    velBuffer[2] = velBuffer[1]
    velBuffer[1] = GetEntityVelocity(PlayerPedId())
end
)

AddEventHandler("vehicleEnter", function()
    speedBuffer[1], speedBuffer[2] = 0.0, 0.0
    if (beltOn) then
        TriggerEvent("core_hud:belt", false)
        beltOn = false
    end
end)

Citizen.CreateThread(
    function()
        while true do
            local ped = PlayerPedId()
            local inVeh = IsPedInAnyVehicle(ped, false)
            if (IsPedInCar(ped, false) and inVeh) and beltOn then
                DisableControlAction(0, 75, true)  -- Disable exit vehicle when stop
                DisableControlAction(27, 75, true) -- Disable exit vehicle when Driving
            end

            if (not inVeh and beltOn) then
                setSeatbelt(false, true)
            end

            Wait(1)
        end
    end
)

function toggleSeatbelt()
    if IsPedInAnyVehicle(PlayerPedId(), false) then
        if (IsPedOnAnyBike(PlayerPedId())) or block_seatbelt_vehicles[GetEntityModel(GetVehiclePedIsIn(PlayerPedId()))] then
            return
        end
        if (isCuffed) then
            return
        end
        beltOn = not beltOn
        if beltOn then
            TriggerEvent("fdg_ui:SendNotification", "Seatbelt On")
            TriggerEvent("core_hud:belt", true)
            isUiOpen = true
        else
            TriggerEvent("fdg_ui:SendNotification", "Seatbelt Off")
            TriggerEvent("core_hud:belt", false)
            isUiOpen = true
        end
    end
end

function setSeatbelt(state, ignoreNoti)
    beltOn = state
    if beltOn then
        if (not ignoreNoti) then
            TriggerEvent("fdg_ui:SendNotification", "Seatbelt On")
        end
        TriggerEvent("core_hud:belt", true)
        isUiOpen = true
    else
        if (not ignoreNoti) then
            TriggerEvent("fdg_ui:SendNotification", "Seatbelt Off")
        end
        TriggerEvent("core_hud:belt", false)
        isUiOpen = true
    end
end

onBaseReady(
    function()
        Base.Controls.register("u", "Toggle Seatbelt", toggleSeatbelt)
    end
)
