local nextMarkerId = 1
local markers = {}
local nearbyMarkers = {}

local defaultSize = 1.2

local function generateMarkerOptions(options)
    if not options then options = {} end

    options = table.assign({
        type = 25,
        scale = vector3(defaultSize, defaultSize, defaultSize),
        offset = vector3(0.0, 0.0, -0.99),
        color = { r = 57, g = 154, b = 247, a = 200 },
        bob = false,
        faceCamera = false,
        heightOffset = 0.0,
        instance = options.instance or nil
    }, options)

    if type(options.scale) == "number" then
        options.scale = vector3(options.scale, options.scale, options.scale)
    end

    return options
end

local function generateInteractionOptions(options)
    interactionOptions = table.assign({
        text = nil,
        textRenderDist = 10.0,
        interactionDist = 1.5,
        vehicleOnly = false,
    }, options or {})

    return interactionOptions
end

---@class MarkerOptions
---@field type number? The marker type according to FiveM docs
---@field scale vector3|number? The scale of the marker, either as XYZ or a single number used for all axes
---@field color {r: number, g: number, b: number, a: number}? The color of the marker
---@field bob boolean? Should the marker bob up and down?
---@field faceCamera boolean? Should the marker face the camera?
---@field heightOffset number? The height offset of the marker
---@field instance string? Which instance this marker belongs to. Marker is only visible to players in this instance. All instances if nil.

---@class InteractionOptions
---@field text string? The text to display when the player is within the interaction distance.
---@field textRenderDist number? The distance to render the text at. Defaults to 10.0
---@field interactionDist number? The distance to interact with the marker at. Defaults to 1.5

---Creates a marker at a set of coordinates
---@param coords vector3 The coordinates to create the marker at
---@param handler function The handler to run when the player interacts with the marker
---@param interactionOptions InteractionOptions? The options for the interaction, optional
---@param markerOptions MarkerOptions? The options for the marker, optional
---@return string markerId The ID of the created marker, a number converted to a string
function CreateInteractableMarker(coords, handler, interactionOptions, markerOptions)
    if type(coords) == "table" and coords.x and coords.y and coords.z then
        coords = vector3(coords.x, coords.y, coords.z)
    elseif type(coords) ~= "vector3" then
        error("Invalid coords for marker")
    end

    local marker = {
        coords = coords,
        handler = handler,
        renderDistance = 100.0,
        interactionOptions = generateInteractionOptions(interactionOptions),
        markerOptions = generateMarkerOptions(markerOptions)
    }

    local markerId = tostring(nextMarkerId)
    nextMarkerId = nextMarkerId + 1
    markers[markerId] = marker

    return markerId
end

---Removes an interactable marker
---@param markerId string The ID of the marker to remove
function RemoveInteractableMarker(markerId)
    markers[markerId] = nil
end

-- Nearby marker detection thread
CreateThread(function()
    while true do
        Wait(0)

        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)
        local processCount = 0
        local instance = LocalPlayer.state.instanceID

        -- Delay if there are no markers
        if not next(markers) then
            Wait(1000)
        end

        local loopMarkers = table.assign({}, markers)

        -- Loop through each registered marker
        for id, marker in pairs(loopMarkers) do
            if marker.interactionOptions.vehicleOnly then
                local vehicle = GetVehiclePedIsIn(playerPed, false)
                if not DoesEntityExist(vehicle) then
                    nearbyMarkers[id] = nil
                    goto continue
                end
            end

            if marker.markerOptions.instance and marker.markerOptions.instance ~= instance then
                nearbyMarkers[id] = nil
                goto continue
            end

            -- Store nearby markers
            local dist = #(playerCoords - marker.coords)
            if dist <= marker.renderDistance then
                nearbyMarkers[id] = { marker = marker, dist = dist }
            else
                nearbyMarkers[id] = nil
            end

            -- Only process 5 markers per frame
            processCount = processCount + 1
            if processCount >= 5 then
                Wait(0)
                processCount = 0
            end

            ::continue::
        end

        -- Remove markers that are no registered
        for id in pairs(nearbyMarkers) do
            if not markers[id] then
                nearbyMarkers[id] = nil
            end
        end
    end
end)

local losChecks = {}

---@class DrawTextOptions
---@field size number? The size of the text, defaults to 0.35
---@field color {r: number, g: number, b: number, a: number}? The color of the text, defaults to white
---@field los boolean? Should the text be hidden if there is an object between the player and the text? Defaults to false. May have a perf impact. Note: Use of LOS may cause slight issues when dealing with multiple text with the same content

---Draws text on the screen in 3D space
---@param coords vector3
---@param text string
---@param options {size: number?, color: {r: number, g: number, b: number, a: number}?, los: boolean?}?
function DrawTextAtCoords(coords, text, options)
    options = table.assign({
        size = 0.35,
        color = { r = 255, g = 255, b = 255, a = 215 },
        los = false
    }, options or {})

    local shouldRender, _x, _y = World3dToScreen2d(coords.x, coords.y, coords.z)

    if options.los then
        local lastCheck = losChecks[text]

        if not lastCheck or GetGameTimer() > lastCheck.nextCheckTime then
            local playerCoords = GetEntityCoords(PlayerPedId())
            local shapeTest = StartExpensiveSynchronousShapeTestLosProbe(playerCoords.x, playerCoords.y, playerCoords.z, coords.x, coords.y, coords.z, 1,
                PlayerPedId(), 7)
            local _, hit = GetShapeTestResult(shapeTest)

            losChecks[text] = {
                nextCheckTime = GetGameTimer() + 1000,
                hit = hit ~= 0
            }
        end

        if shouldRender and losChecks[text].hit then
            shouldRender = false
        end
    end

    if not shouldRender then
        return
    end

    SetTextScale(options.size, options.size)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(options.color.r, options.color.g, options.color.b, options.color.a)
    SetTextEdge(3, 0, 0, 0, 255)
    SetTextOutline()
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x, _y)
end

-- Marker rendering thread
CreateThread(function()
    while true do
        Wait(0)

        local closestMarker = nil
        local closestDist = 999999
        local playerPed = PlayerPedId()
        local playerCoords = GetEntityCoords(playerPed)

        -- Loop through each nearby marker
        for id, markerData in pairs(nearbyMarkers) do
            local coords = markerData.marker.coords + markerData.marker.markerOptions.offset
            local marker = markerData.marker.markerOptions
            local interaction = markerData.marker.interactionOptions

            -- Draw the marker
            DrawMarker(marker.type, coords.x, coords.y, coords.z, 0, 0, 0, 0, 0, 0, marker.scale.x, marker.scale.y, marker.scale.z, marker.color.r,
                marker.color.g, marker.color.b, marker.color.a, marker.bob, marker.faceCamera, 2, false, false, false, false)

            -- Check if the player is within the interaction distance
            local dist = #(playerCoords - markerData.marker.coords)
            local canInteract = dist <= interaction.interactionDist / 2

            -- Draw the marker text
            if interaction.text and dist <= interaction.textRenderDist / 2 then
                local textCoords = coords + vector3(0.0, 0.0, interaction.textOffset or 1.0)

                if canInteract then
                    DrawTextAtCoords(textCoords, "[~b~G~w~] " .. interaction.text, { los = true })
                else
                    DrawTextAtCoords(textCoords, interaction.text, { los = true })
                end
            end

            -- Check if the player is interacting with the marker
            if canInteract and dist < closestDist then
                closestMarker = markerData.marker
                closestDist = dist
            end
        end

        -- Check if the player is interacting with a marker
        if closestMarker then
            if IsControlJustPressed(0, 47) then
                CreateThread(closestMarker.handler)
            end
        end
    end
end)
