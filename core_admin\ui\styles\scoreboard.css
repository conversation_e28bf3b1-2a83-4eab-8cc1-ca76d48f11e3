@import url('https://fonts.googleapis.com/css?family=Roboto&display=swap');

img.imglogo {
margin-left:9.6vw;
width:7vw; 
height:3.7vw;
margin-bottom:5px;
}

#wrap {
	width: 90%;
	min-height: 185px;
	margin-top: 1%;
	margin-left: auto;
	margin-right: auto;
	background-color: rgb(46, 46, 46, 0.85);
	box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.5), 0px 2px 10px 0px rgba(0, 0, 0, 0.5);
	color: rgba(255, 255, 255, 0.9);
	border-radius: 2px;
	font-family: 'Roboto', sans-serif;
}

p {
	float: center;
	margin-left: 15px;
	padding-left: 10px;
	font-size: 1.05vw;
}

.jobs {
	display: table;
	width: 100%;	
	padding-top: 10px;
	background-color: #000000;
	height: 3vw;
	text-align: center;
	box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.3), 0px 1px 1px 0px rgba(0, 0, 0, 0.3);
	float: center;

}

.jobs p {
	display: table-cell;
	/*width: 100%;*/
}

table {
	text-align: left;
	color: white;
}

th, td {
	padding-left: 25px;
}


th {
	padding-top: 10px;
	height: 40px;
}

tr {
	font-size: 0.85vw;
	text-shadow: 1px 1px 1px rgba(26, 16, 16, 0.5);
}

tr.heading {
	font-size: 1vw;
	color: rgba(255, 255, 255, 0.9);
}

.serverinfo {
	font-size: 0.8vw;
	text-align: center;
	padding-top: 2%
}