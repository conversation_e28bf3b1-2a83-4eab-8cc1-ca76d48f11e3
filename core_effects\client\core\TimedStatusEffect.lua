---@class TimedEffectOptions
---@field maxTime integer Maximum time the effect can last


---Status Effect which lasts for a period of time
---@param effect StatusEffect
---@param options TimedEffectOptions
---@return any
function TimedStatusEffect(effect, options)
    local self = {}

    self.effect = effect
    self.active = false
    self.remainingTime = 0
    self.endTimestamp = 0
    self.maxTime = options.maxTime or -1

    self.addTime = function(time)
        self.setTime(self.remainingTime + time)
    end

    self.subtractTime = function(time)
        self.setTime(self.remainingTime - time)
    end

    self.setTime = function(time)
        self.remainingTime = math.max(math.min(time, self.maxTime), 0)

        QUI.TriggerEvent("updateEffectTime", effect.name, self.remainingTime)
    end

    self.start = function()
        self.active = true
        self.effect.start({remainingTime = self.remainingTime})
    end

    self.stop = function()
        self.active = false
        self.effect.stop()
    end


    Citizen.CreateThread(function()
        while true do
            Wait(1000)


            if self.active then
                self.subtractTime(1)

                if self.remainingTime <= 0 then
                    self.stop()
                end


            elseif self.remainingTime > 0 then
                self.start()
            end
        end
    end)

    return self
end