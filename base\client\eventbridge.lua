--[====[
    @filename UI Core
    @filebrief Interface to use the global UI vue project
--]====]

RegisterNUICallback(
    "triggerClientEvent",
    function(data, cb)
        TriggerEvent(data.eventName, table.unpack(data.data or {}))
        cb("OK")
    end
)

RegisterNUICallback(
    "triggerServerEvent",
    function(data, cb)
        TriggerServerEvent(data.eventName, table.unpack(data.data or {}))
        cb("OK")
    end
)

RegisterNUICallback(
    "triggerServerCallback",
    function(data, cb)
        TriggerServerCallback(
            data.eventName,
            function(...)
                if ... then
                    cb(json.encode(table.pack(...)))
                else
                    cb("OK")
                end
            end,
            table.unpack(data.data or {})
        )
    end
)

local netEvents = {}

RegisterNUICallback(
    "registerNetEvent",
    function(data, cb)
        if netEvents[data.eventName] then
            RemoveEventHandler(netEvents[data.eventName])
        end

        netEvents[data.eventName] = RegisterNetEvent(
            data.eventName,
            function(...)
                if ... then
                    SendNUIMessage({ netEvent = data.eventName, data = table.pack(...) })
                else
                    SendNUIMessage({ netEvent = data.eventName, data = {} })
                end
            end,
            table.unpack(data.data or {})
        )

        cb("OK")
    end
)

---Sends an event to an eventbridge enabled UI
---@param eventName string
---@param ... any additional data parameters
function SendNUIEvent(eventName, ...)
    SendNUIMessage({ nuiEvent = eventName, data = table.pack(...) })
end

--[====[
    @type func
    @name SendNuiEvent(eventName, ...)
    @param eventName: string - The name of the event
    @param ...: any[] - Any other data parameters
    @brief Sends an NUI event to the core UI controller
--]====]

-- NOT IMPLEMENTED YET
-- function SendNuiEvent(eventName, ...)
--     exports["base_ui"]:sendQUIEvent(eventName, ...)
-- end

-- function ToggleNuiState(keyboardFocus, mouseInput, passthrough)
--     exports["base_ui"]:toggleNUIState(keyboardFocus, mouseInput, passthrough)
-- end
