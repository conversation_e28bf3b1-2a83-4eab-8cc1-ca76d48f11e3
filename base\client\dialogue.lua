local spawnedPeds = {}
local events = {}

---@class DialogueOptions
---@field characterText string The text the character says
---@field responses ResponseOptions[]? The responses the player can choose from

---@class ResponseOptions
---@field responseText string The text the player can select
---@field responses DialogueOptions[]? The responses that the character can say (selected at random)
---@field event string? The event to trigger when the player selects this option

---Opens the dialogue menu focussing on a ped
---@param characterName string Name of the character for the UI
---@param ped string Handle of the ped
---@param dialogue DialogueOptions[] The dialogue options
---@param characterDetails? {role: string, rep: number} Details about the character for UI
function OpenDialogueWithPed(characterName, ped, dialogue, characterDetails)
    if not characterDetails then
        characterDetails = {}
    end

    local camCoords = GetOffsetFromEntityInWorldCoords(ped, -2.0, 3.0, 1.2)
    Base.Camera.create("ped_dialogue_cam", 1, camCoords, vector3(-20.0, 0, GetEntityHeading(ped) + 210.0), 50.0)
    Base.Camera.render("ped_dialogue_cam", true, 650)

    exports.core_hud:openDialogue({
        characterName = characterName,
        ped = ped,
        dialogue = dialogue,
        characterRole = characterDetails.role,
        characterRep = characterDetails.rep,
    })
end

---Registers an action which occurs when the player selects a specific dialogue option
---@param actionIdentifier string The identifier of the action
---@param cb function The callback to trigger when the action is selected
function RegisterDialogueAction(actionIdentifier, cb)
    local event = AddEventHandler("charDialog:" .. actionIdentifier, cb)
    events[actionIdentifier] = event
end

function UnregisterDialogueAction(actionIdentifier)
    if events[actionIdentifier] then
        RemoveEventHandler(events[actionIdentifier])
        events[actionIdentifier] = nil
    end
end

---@class InteractablePedOptions
---@field allowMovement boolean? Whether the ped can move, defaults to false
---@field skin string? The skin to apply to the ped
---@field anim {dict:string, name:string}? The animation to play on the ped
---@field weapon string? The weapon to give to the ped
---@field attackCb function? The callback to trigger when the ped is attacked

---Creates a ped with specific option, spawning a local copy on each client at given coords with some preset options
---@param identifier string A unique identifier for the ped
---@param model string The model of the ped
---@param location {coords:vector3, heading:number} The location to spawn the ped
---@param interactCb function? The callback to trigger when the player interacts with the ped
---@param options InteractablePedOptions? The options for the ped
---@return number pedHandle The handle of the ped
function CreateInteractablePed(identifier, model, location, interactCb, options)
    if spawnedPeds[identifier] then
        DeleteEntity(ped)
    end

    if not options then
        options = {}
    end

    RequestModel(model)
    while not HasModelLoaded(model) do
        Wait(0)
    end

    local ped = CreatePed(4, model, location.coords.x, location.coords.y, location.coords.z, location.heading, false, true)
    SetEntityAsMissionEntity(ped, true, true)
    SetModelAsNoLongerNeeded(model)
    SetEntityInvincible(ped, true)

    Entity(ped).state:set("interactableped", identifier)

    if not options.allowMovement then
        FreezeEntityPosition(ped, true)
        TaskSetBlockingOfNonTemporaryEvents(ped, true)
        SetBlockingOfNonTemporaryEvents(ped, true)
    end

    if options.skin then
        Base.Skin.setSkin(options.skin, false, ped)
    end

    if options.anim then
        RequestAnimDict(options.anim.dict)
        while not HasAnimDictLoaded(options.anim.dict) do
            Wait(0)
        end

        TaskPlayAnim(ped, options.anim.dict, options.anim.name, 8.0, 0.0, -1, 3, 0, 0, 0, 0)
    end

    if options.weapon then
        CreateThread(function()
            Wait(500)
            GiveWeaponToPed(ped, GetHashKey(options.weapon), 999, false, true)
            SetCurrentPedWeapon(ped, GetHashKey(options.weapon), true)
            SetPedCanSwitchWeapon(ped, false)
        end)
    end

    if interactCb then
        Entity(ped).state:set("dialog", identifier)
        AddEventHandler("interactPedCharacter:" .. identifier, interactCb)
    end

    if options.attackCb then
        CreateThread(function()
            while true do
                Wait(0)
                if #(GetEntityCoords(PlayerPedId()) - GetEntityCoords(ped)) > 10.0 then
                    Wait(1000)
                else
                    local targetPed = GetMeleeTargetForPed(PlayerPedId())
                    if targetPed == ped then
                        options.attackCb(ped, identifier)
                    end

                    while GetMeleeTargetForPed(PlayerPedId()) == ped do
                        Wait(0)
                    end
                end
            end
        end)
    end

    spawnedPeds[identifier] = ped

    return ped
end

---Registers an event which occurs on a network request (we should probably use networked peds rather than this, but it works)
---@param eventIdentifier string The identifier of the event
---@param cb function The callback to trigger when the event is received
function RegisterPedEvent(eventIdentifier, cb)
    print("registering", eventIdentifier)
    RegisterNetEvent("pedEvent:" .. eventIdentifier, function(pedIdentifier, ...)
        print("receive", eventIdentifier, "on", pedIdentifier)
        local ped = spawnedPeds[pedIdentifier]

        print("ped", pedIdentifier, ped)

        if ped then
            print("triggering", eventIdentifier, "on", pedIdentifier)
            cb(ped, ...)
        end
    end)
end

---Triggers an event on a ped
---@param eventIdentifier string The identifier of the event
---@param pedIdentifier string The identifier of the ped
---@vararg any The arguments to pass to the event
function TriggerPedEvent(eventIdentifier, pedIdentifier, ...)
    print("sending", eventIdentifier, "on", pedIdentifier)
    TriggerServerEvent("pedEvent", eventIdentifier, pedIdentifier, ...)
end

AddEventHandler("onResourceStop", function(resourceName)
    if resourceName ~= GetCurrentResourceName() then
        return
    end

    Base.Camera.destroy("ped_dialogue_cam", 650)

    for id, ped in pairs(spawnedPeds) do
        DeleteEntity(ped)
    end
end)
