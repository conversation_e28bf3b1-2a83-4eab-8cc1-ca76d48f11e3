moto                 = moto or {}
moto.Eject           = true      -- This value determines whether the player will be ejected if they land while doing a trick or not (The default value is true)
moto.InstantAnimStop = true      -- This value determines whether the anim should instantly stopped, otherwise it will just go back to the sitting position gradually (The default value is true)

moto.SpecificVehicle = true      -- If set to true, you will be able to do tricks only on some specific bikes from the 'Config.Vehicles' list
moto.Vehicles        = {         -- We use model hash now as we are not stupid.
    "enduro",
    "esskey",
    "manchez",
    "sanchez",
    "sanchez2",
    "sxf450",
    "exc530",
    "bf400",
    "bmxW",
    "brt600",
    "manchez2"
}

moto.SimpleKey       = 76      -- Set the key used for the simple tricks (76 is 'INPUT_VEH_HANDBRAKE' aka SPACEBAR per default)
-- You can find those value here: https://docs.fivem.net/docs/game-references/controls/


local animDict = 'rcmextreme2atv'
local inTricks = false
local animParams = {
    ['stunt1'] = { 'idle_b', 0.28, 0.5, 0.54, 0.50 },
    ['stunt2'] = { 'idle_c', 0.15, 0.44, 0.52, 0.46 },
    ['stunt3'] = { 'idle_d', 0.18, 0.69, 0.71, 0.20 },
    ['stunt4'] = { 'idle_e', 0.15, 0.35, 0.37, 0.35 },
}

local buttons = {
    ['stunt1'] = 'MOUSE_LEFT',
    ['stunt2'] = 'MOUSE_RIGHT',
    ['stunt3'] = 'MOUSE_EXTRABTN1',
    ['stunt4'] = 'MOUSE_EXTRABTN2',
}

for stunt, button in pairs(buttons) do
    RegisterKeyMapping(stunt, 'Stunt ' .. stunt:sub(6), 'MOUSE_BUTTON', button)
end

function PerformStunt(stunt)
    local xPlayer = PlayerPedId(source)
    local Veh = GetVehiclePedIsIn(xPlayer, false)
    local vehicleModel = GetEntityModel(Veh)


    if not SpecificVehicle(vehicleModel) then return end



    local animName, Start, Mid1, Mid2, Loop = table.unpack(animParams[stunt])

    if not HasAnimDictLoaded(animDict) then
        RequestAnimDict(animDict)
        while not HasAnimDictLoaded(animDict) do Wait(50) end
    end

    if IsEntityInAir(Veh) then
        inTricks = false
        Wait(50)
        inTricks = true

        while IsControlPressed(0, moto.SimpleKey) and inTricks do
            if not IsEntityPlayingAnim(xPlayer, animDict, animName, 3) then
                TaskPlayAnimAdvanced(xPlayer, animDict, animName, 0, 0, 0, 0, 0, 0, 8.0, 8.0, -1, 0, Start, false, false)
            elseif GetEntityAnimCurrentTime(xPlayer, animDict, animName) >= Mid1 and GetEntityAnimCurrentTime(xPlayer, animDict, animName) < Mid2 and IsEntityInAir(Veh) then
                TaskPlayAnimAdvanced(xPlayer, animDict, animName, 0, 0, 0, 0, 0, 0, 8.0, 8.0, -1, 0, Loop, false, false)
            elseif not IsEntityInAir(Veh) then
                if moto.Eject then
                    Eject(xPlayer, Veh)
                else
                    if moto.InstantAnimStop then ClearPedTasks(xPlayer) else StopAnimTask(xPlayer, animDict, animName,
                            1.0) end
                end
                return
            end
            Wait(50)
        end
        if moto.InstantAnimStop then ClearPedTasks(xPlayer) else StopAnimTask(xPlayer, animDict, animName, 1.0) end
    end
end

for stunt, _ in pairs(animParams) do
    RegisterCommand(stunt, function() PerformStunt(stunt) end)
end

function Eject(xPlayer, Veh)
    local VehV = GetEntityVelocity(Veh)
    SetEntityCoords(xPlayer, GetEntityCoords(Veh, 0.0, 0.0, -0.7))
    SetPedToRagdoll(xPlayer, 2800, 2800, 0, 0, 0, 0)
    SetEntityVelocity(xPlayer, VehV.x * 1.5, VehV.y * 1.5, VehV.z)
end

function SpecificVehicle(table, val)
    if moto.SpecificVehicle then
        local xPlayer = PlayerPedId(source)
        local Veh = GetVehiclePedIsIn(xPlayer, false)
        local vehicleModel = GetEntityModel(Veh)

        for _, model in ipairs(moto.Vehicles) do
            local vehHash = GetHashKey(model)
            if vehicleModel == vehHash then
                return true
            end
        end
        return false
    end
    return true
end

exports("IsDoingTricks", function()
    return inTricks
end)
