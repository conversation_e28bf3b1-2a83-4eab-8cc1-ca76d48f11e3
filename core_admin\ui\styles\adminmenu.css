@import url('https://fonts.googleapis.com/css?family=Roboto&display=swap');

body, html {
	overflow-y: hidden;
}

#admin {
	display: none;
	font-family: 'Roboto', sans-serif;
	height: 95%;
	background: rgb(3, 12, 23);
	color: white;
	width: 90%;
	position: absolute;
	margin: auto;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
}

#exist {
	display: none; 
}

#main {
	height: 640px;
}

#tabs {
	height: 30px;
	border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}

.reasonstyle {
	width: 400px;
    display: inline-block;
}

.reasonstyle button {
	width: 50%;
}

.tab {
	width: 100px;
	height: 30px;
	float: left;
	border: none;
	font-weight: bold;
	color: white;
	background: rgb(3, 12, 23);
	border: 1px solid rgb(9, 88, 149);;
}

.tab.selected {
	background: rgb(9, 88, 149);
	color: yellow;
}

.tab-search {
	height: 30px;
	float: left;
	/*font-weight: bold;*/
	color: white;
	background: rgb(3, 12, 23);
	width: 235px; text-align: center; 
	border: 1px solid rgb(9, 88, 149);;
}

#main {
	height: 100%;
	width: 100%;
}


#list {
	display: inline-block;
	width: 25%;
	height: 90%;
	border: 1px solid rgb(9, 88, 149);;
	overflow-x: hidden;
	overflow-y: auto;
	-webkit-box-shadow: 0 6px 20px rgba(0, 0, 0, 0.19);
			box-shadow: 0 6px 20px rgba(0, 0, 0, 0.19);
}

#selected h3 {
	margin-left: 5px;
}

.quick {
	margin-left: 9px;
	width: 23%;
	background: rgb(13, 26, 65);
	border: none;
	padding-top: 5px;
	padding-bottom: 5px;
	margin-top: 5px;
	color: white;
	font-size: 17px;
	border: 1px solid rgb(9, 88, 149);;
}

#selected {
	overflow-y: scroll;
}

.quick:hover {
	background-color: rgb(9, 88, 149);
	color: yellow;
}

.group_set {
	display: inline-block;
	float: left;
	padding: 10px;
	width: 260px;
}

.group_set button { 
	padding-left: 25px;
	padding-right: 25px;
	border: 1px solid rgb(9, 88, 149);;
	width: 100%;
	margin-top: 4px;
	padding-top: 5px;
	padding-bottom: 5px;
	background: rgb(13, 26, 65);
	color: white;
	font-weight: bold;
}

.group_set button:hover {
	background-color: rgb(9, 88, 149);
	color: yellow;
}

.group_set input {
	width: 100%;
	padding-top: 3px;
	padding-bottom: 3px;
}

.group_set select {
	width: 49%;
	padding-top: 3px;
	padding-bottom: 3px;
}

.group_set label {
	display: block;
}

.group_set_2 {
	display: inline-block;
	float: left;
	padding: 10px;
	width: 576px;
}

.group_set_2 textarea {
	width: 576px;
	height:200px;
	resize: none;
}
.group_set_2 input {
	width: 10%;
	padding-top: 3px;
	padding-bottom: 3px;
}

.group_set_2 label {
	display: block;
}


.group_set_2 button {
	padding-left: 25px;
	padding-right: 25px;
	border: none;
	width: 49%;
	float: left;
	margin-top: 4px;
	margin-left: 4px;
	padding-top: 5px;
	padding-bottom: 5px;
	background: rgb(151, 66, 66);
	color: white;
	font-weight: bold;
}

.group_set_2.1 button {
	border-right: 1px #000 solid;
}
.group_set_2.2 button {
	border-left: 1px #000 solid;
}
.group_set_2 label {
	display: block;
}

#selected {
	position: absolute;
	display: inline-block;
	width: 75%;
	height: 90%;
}

#selected h1 {
	margin-left: 20px;
}

.player {
	width: 100%;
	text-weight: bold;
	text-indent: 10px;
	padding: 5px;
	color:white;
}

.player:nth-child(odd) {
	background: -webkit-linear-gradient(right, transparent 0%, rgb(85, 85, 85) 100%);
}

.player.selected {
	background: -webkit-linear-gradient(right, transparent 0%, rgb(9, 88, 149) 100%);
	color: yellow;
}

.topbar {
	height: 50px;
	font-weight: bold;
	font-size: 26px;
	background: rgb(3, 12, 23);
	border: 1px solid rgb(9, 88, 149);
}

.body {
	height: 650px;
}

.topbar p {
	top: 0;
	margin-top: 0;
	padding: 10px;
	display: inline-block;
	color: yellow;
}

#close {
	display: inline-block;
	float: right;
	padding: 5px;
	padding-right: 10px;
	color: rgb(255, 0, 0);
}

#stopSpectating {
	display: none;
	float: right;
	margin-left: 9px;
	width: 31%;
	background: rgb(151, 66, 66);
	border: none;
	padding-top: 5px;
	padding-bottom: 5px;
	margin-top: 10px;
	margin-right: 15px;
	color: white;
	font-size: 17px;
}

#reason .for {
	width: 556px;
}

#historyList {
	width: 100%;
	overflow-y: hidden;
	overflow-x: hidden;
}

#historyList td {
	border-bottom: 1px solid rgb(9, 88, 149);
}

#historyList h2{
	width: 100%;
	padding-left: 50px;
	margin-top: 5px;
	margin-bottom: 5px;
}

#buttonDiv{
	height: 10%;
	width: 90%;
	margin-left: 30%;
}

#hisList{
	height: 90%;
	overflow-y: scroll;
}

#hisTd {
	padding: 5px;
	float:	left;
	overflow: hidden;
}

#hisType{
	width: 75px;
}

#hisStaffName{
	width: 115px
}

#hisTimeDate{
	width: 100px
}

#hisReason{
	width: 335px;
	overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical; 
}

#hisBanExpire{
	width: 100px;
}