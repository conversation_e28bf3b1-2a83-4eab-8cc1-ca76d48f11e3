-- Interfaces to stop carrying (move server side)
-- local function requestCarryStop()
--     if LocalPlayer.state.carrying then
--         TriggerServerEvent("carry:stop")
--     end
-- end


local function carrierThread()
    print("start carry thread")
    while LocalPlayer.state.carrying do
        Wait(0)

        -- Disable sprinting
        DisableControlAction(2, 21, true)

        -- Drop the player when they press E or X
        if IsDisabledControlJustPressed(0, 38) or IsDisabledControlJustPressed(0, 73) then
            print("Carry Stop. Key pressed")
            TriggerServerEvent("carry:stop")
            break
        end

        -- Drop the player if they no longer exist
        local player = GetPlayerFromServerId(LocalPlayer.state.carrying)
        if not player or player == -1 then
            print("Carry Stop. Player not found")
            TriggerServerEvent("carry:stop")
            break
        end

        -- Drop the player if the ped no longer exists
        local ped = GetPlayerPed(player)
        if not DoesEntityExist(ped) then
            print("Carry Stop. Ped not found")
            TriggerServerEvent("carry:stop")
            break
        end

        -- Enable handcuffs if the player is not in water
        if not IsEntityInWater(PlayerPedId()) then
            SetEnableHandcuffs(PlayerPedId(), true)
        else
            SetEnableHandcuffs(PlayerPedId(), false)
        end
    end
    print("end carry thread")
    -- Cleanup
    SetEnableHandcuffs(PlayerPedId(), false)
end


-- Start carry (for the carrier)
RegisterNetEvent("carry:start:source", function(target)
    CreateThread(carrierThread)
    Base.Animation.StartForcedAnim(PlayerPedId(), "missfinale_c2mcs_1", "fin_c2_mcs_1_camman", true, false, true)
end)

-- Start carry (for the target)
RegisterNetEvent("carry:start:target", function(source)
    local targetPly = GetPlayerFromServerId(source)
    if targetPly == -1 then
        return print("Player not found for source", source)
    end

    local targetPed = GetPlayerPed(targetPly)
    if not DoesEntityExist(targetPed) then
        return print("Ped not found for source", source)
    end

    AttachEntityToEntity(PlayerPedId(), targetPed, 0, -0.10, -0.15, 0.60, -90.0, -90.0, -0.0, false, false,
        false, true, 2, true)
    Base.Animation.StartForcedAnim(PlayerPedId(), "timetable@floyd@cryingonbed@base", "base", true, false, false)
end)

-- Stop carry (for the carrier)
RegisterNetEvent("carry:stop:source", function()
    Base.Animation.StopForcedAnim(PlayerPedId())
    ClearPedSecondaryTask(PlayerPedId())
end)

-- Stop carry (for the target)
RegisterNetEvent("carry:stop:target", function()
    DetachEntity(PlayerPedId(), true, false)

    local coords = GetOffsetFromEntityInWorldCoords(GetPlayerPed(GetPlayerFromServerId(target)), 0.0, 1.0, -0.95)
    SetEntityCoords(PlayerPedId(), coords, true, true, false, false)
    Base.Animation.StopForcedAnim(PlayerPedId())
end)

local function canPlayerCarry()
    return not LocalPlayer.state.wounded and not LocalPlayer.state.carrying and not LocalPlayer.state.cuff
end

local function canPlayerBeCarried(player)
    local target = GetPlayerServerId(player)
    if not target or target == -1 then return false end

    local targetState = Player(target).state
    return not IsPedInAnyVehicle(PlayerPedId(), true) and (targetState.sedated or targetState.wounded)
end

RegisterInteraction("player", "carry", { label = "Carry", emoji = "🔝", order = 2 }, function(player)
    if LocalPlayer.state.carrying then
        return TriggerServerEvent("carry:stop")
    else
        return TriggerServerEvent("carry:start", GetPlayerServerId(player))
    end
end, function(target)
    return canPlayerBeCarried(target) and canPlayerCarry()
end)

RegisterCommand("carry", function()
    if LocalPlayer.state.carrying then
        return TriggerServerEvent("carry:stop")
    end

    local clsPlr = Base.Player.getClosestPlayer(GetEntityCoords(PlayerPedId()), 3.0)

    if clsPlr == -1 then
        print("Carry Failed. No player found")
        return
    end

    if not canPlayerBeCarried(clsPlr) then
        print("Carry Failed. Player cannot be carried")
        return
    end

    if not canPlayerCarry() then
        print("Carry Failed. Player cannot carry")
        return
    end

    TriggerServerEvent("carry:start", GetPlayerServerId(clsPlr))
end, false)

function getCarrying()
    return LocalPlayer.state.carrying or false
end

exports("GetCarrying", getCarrying)
