--[[
core_inventory: inventory.lua

This file handles:
- Player inventory on player load
- Adding and removing items from inventory
]]
--

local inventories = {}

exports("_getInventoryByName", function(name)
	return inventories[name] or false
end)

local ITEMS = {}

RegisterDataSyncHandler("items", function(items)
	print(GetCurrentResourceName() .. " | Items Synced for xInventory")
	ITEMS = items
end)

local maxAllowedItems = 10000

function xInventory(name, source, capacity, saveLocation, playerItems, money, dirty, ignoreStacking)
	local self = {}

	self.name = name
	self.source = source
	self.items = {}
	self.capacity = capacity
	self.money = money or false
	self.dirty = dirty or false
	self.saveLocation = saveLocation
	self.clientSyncEnabled = false
	self.ignoreStacking = ignoreStacking or false
	self.cooldown = false

	self.setCooldown = function(state)
		self.cooldown = state
	end

	self.isCooldown = function()
		return self.cooldown
	end

	self.getClientInventory = function()
		return self.items
	end

	self.clientSync = function(src)
		if self.clientSyncEnabled then
			if src then
				TriggerClientEvent("inventory:clientSync", src, self.name, self.getClientInventory(), self.money, self.dirty, self.capacity)
			elseif self.source then
				if type(self.source) == "table" then
					for i, src in ipairs(self.source) do
						TriggerClientEvent("inventory:clientSync", src, self.name, self.getClientInventory(), self.money, self.dirty, self.capacity)
					end
				else
					TriggerClientEvent("inventory:clientSync", self.source, self.name, self.getClientInventory(), self.money, self.dirty, self.capacity)
				end
			end
		end
	end

	self.addSource = function(source)
		Player(source).state.externalInventoryResource = GetCurrentResourceName()
		Player(source).state.externalInventoryName = self.name

		local found = false
		for i, src in ipairs(self.source) do
			if src == source then found = true end
		end

		if not found then
			table.insert(self.source, source)
		end
	end

	self.destroy = function()
		self.saveInventory()
		self = nil
		inventories[name] = self
	end

	self.removeSource = function(source, destruct)
		for i, src in ipairs(self.source) do
			if src == source then
				table.remove(self.source, i)

				local player = Player(source)
				if player then
					player.state.externalInventoryResource = nil
					player.state.externalInventoryName = nil
				end
			end
		end

		if destruct and #self.source < 1 then
			self.destroy()
		end
	end

	self.hasSources = function()
		if type(self.source) == "table" and #self.source < 1 then
			return false
		end
		return true
	end

	-- Items
	self.getItem = function(name)
		if type(name) == "number" then
			return self.items[name], name
		elseif type(name) == "string" then
			name = string.lower(name)
			for i, item in ipairs(self.items) do
				if item.name == name then return item, i end
			end
			return false
		end
	end

	self.setItemData = function(name, key, value)
		if type(name) == "number" then
			if not self.items[name] then return end

			if not self.items[name].data then
				self.items[name].data = {}
			end

			self.items[name].data[key] = value
		elseif type(name) == "string" then
			name = string.lower(name)
			for i, item in ipairs(self.items) do
				if item.name == name then
					self.items[i].data[key] = value
					break
				end
			end
		end

		self.clientSync()
		self.saveInventory()
	end

	self.addItem = function(name, count, data, ignoreWeight, addMax, blockSave, loopNonStackableItems, blockSync)
		if count < 0 then return end

		local invItem = self.getItem(name)
		local template = ITEMS[name]

		if not template then
			-- print("[core_inventory] Failed to add " .. name .. ", item not registered in DB")
			return false
		end

		if data and data.weightOverride then
			template.weight = data.weightOverride
		end

		-- Ignore weight calculation on inventory load
		if self.capacity > 0 and not ignoreWeight and (self.getWeight() + (count * template.weight)) > self.capacity then
			if addMax then
				local remaining = self.capacity - self.getWeight()
				count = math.floor(remaining / template.weight)
			else
				--print("[core_inventory] Failed to add "..name..", item too heavy")
				return false
			end
		end

		if count <= 0 then
			--print("[core_inventory] Failed to add "..name..", item too heavy")
			return false
		end


		if (not template.canStack and not self.ignoreStacking) then
			if #self.items > maxAllowedItems then
				--print("[core_inventory] Failed to add "..name..", item count > "..maxAllowedItems)
				return false
			end

			if not loopNonStackableItems then
				table.insert(self.items, initItem(template, 1, data))
			else
				for i = 1, count do
					table.insert(self.items, initItem(template, 1, data))
				end
			end
		elseif (not invItem) then
			if #self.items > maxAllowedItems then
				--print("[core_inventory] Failed to add "..name..", item count > "..maxAllowedItems)
				return false
			end

			table.insert(self.items, initItem(template, count, data))
		else
			-- Ammend the count
			invItem.count = invItem.count + count
		end

		if not blockSync then
			self.clientSync()
		end

		if not blockSave then
			self.saveInventory()
		end

		return count
	end

	self.removeItem = function(name, count, takeMax)
		if count < 0 then return end

		local invItem, index = self.getItem(name)

		-- Make sure the player has the item
		if not invItem then
			--print("[core_inventory] Failed to remove, item doesn't exist")
			return false, "item_not_exist"
		end

		if count > invItem.count then
			if takeMax then
				count = invItem.count
			else
				return false, "item_not_enough"
			end
		end

		if not (ignoreWeight and blockSave) then
			-- GeneralLog("Item Removed", "Item Removed | InvName: "..self.name.." | ItemName: "..name.." x "..count, "red", "debug-inventory")
		end

		invItem.count = invItem.count - count

		if invItem.count <= 0 then
			table.remove(self.items, index)
		end

		self.clientSync()
		self.saveInventory()

		return invItem
	end

	-- Standard Money
	self.getMoney = function()
		return self.money
	end

	self.addMoney = function(amount)
		self.setMoney(self.money + amount)
	end

	self.removeMoney = function(amount)
		self.setMoney(self.money - amount)
	end

	self.setMoney = function(amount)
		self.money = amount
		self.saveInventory()
		self.clientSync()
	end

	-- Dirty Money
	self.getDirty = function()
		return self.dirty
	end

	self.addDirty = function(amount)
		self.setDirty(self.dirty + amount)
	end

	self.removeDirty = function(amount)
		self.setDirty(self.dirty - amount)
	end

	self.setDirty = function(amount)
		self.dirty = amount
		self.saveInventory()
		self.clientSync()
	end

	-- Misc
	self.empty = function(forceAllItems)
		if forceAllItems then
			self.items = {}
		else
			local newItems = {}

			for i, item in ipairs(self.items) do
				if item.keep_on_death then
					table.insert(newItems, item)
				end
			end

			self.items = newItems
		end

		self.money = 0
		self.dirty = 0

		Wait(500)

		self.saveInventory()
		self.clientSync()
	end

	self.getWeight = function()
		local weight = 0

		for i, item in ipairs(self.items) do
			weight = weight + (item.weight * item.count)
		end

		return weight
	end

	self.getViewData = function(skipEmpty)
		local data = {}

		for i, item in ipairs(self.items) do
			if not skipEmpty or item.count > 0 then
				table.insert(data, { name = item.name, count = item.count, data = item.data })
			end
		end

		return data
	end

	self.toLogString = function()
		local string = ""

		for i, item in ipairs(self.items) do
			if item.count > 0 then
				string = string .. item.label .. "x" .. item.count .. "\n"
			end
		end

		return string
	end

	self.getSavingString = function()
		return json.encode(self.getViewData(true)) or "[]"
	end

	self.createParams = function(query)
		local params = {}

		local firstParam = true
		for col, val in pairs(self.saveLocation.criteria) do
			if firstParam then
				query = query .. " " .. col .. " = @" .. col
				firstParam = false
			else
				query = query .. " AND " .. col .. " = @" .. col
			end

			params["@" .. col] = val
		end

		return query, params
	end

	self.saveInventory = function()
		if self.saveLocation and self.saveLocation.table then
			local query, params
			local db = ""
			if self.saveLocation.settingDB then
				db = "fivem_settings."
			end
			if self.money and self.dirty then
				if self.saveLocation.column == "glovebox" then
					query, params = self.createParams("UPDATE " ..
						db ..
						self.saveLocation.table ..
						" SET " .. (self.saveLocation.column or "inventory") .. " = @inventory, glovebox_money = @money, glovebox_dirty = @dirty WHERE")
				else
					query, params = self.createParams("UPDATE " ..
						db ..
						self.saveLocation.table .. " SET " .. (self.saveLocation.column or "inventory") .. " = @inventory, money = @money, dirty = @dirty WHERE")
				end
			else
				query, params = self.createParams("UPDATE " ..
					db .. self.saveLocation.table .. " SET " .. (self.saveLocation.column or "inventory") .. " = @inventory WHERE")
			end

			params["@inventory"] = self.getSavingString()
			params["@money"] = self.money
			params["@dirty"] = self.dirty

			MySQL.update(query, params, function() end)

			-- GeneralLog("Inventory Saved", "Inventory Saved | InvName: "..self.name.." was saved and sync'd to "..json.encode(self.source), "blue", "debug-inventory")
		end
	end

	-- xPlayer not player id
	self.refreshFromPlayer = function(xPlayer)
		if (xPlayer) then
			xPlayer.getField("inventory", function(data)
				self.refresh(data)
			end)
		end
	end

	self.refresh = function(data)
		if type(data) == "string" then data = json.decode(data) end
		for i, item in pairs(data) do
			if type(i) == "number" then
				self.addItem(item.name, item.count, item.data, true, false, true)
			else
				self.addItem(i, item, {}, true, false, true)
			end
		end
	end

	if playerItems then
		self.refresh(playerItems)
	end

	self.toggleClientSync = function(state)
		self.clientSyncEnabled = state
	end

	self.clientSyncEnabled = true
	self.clientSync()

	inventories[self.name] = self

	return self
end

function initItem(template, count, data)
	local self = {}

	for k, v in pairs(template) do
		self[k] = v
	end

	if not self.name or not self.label or not self.type then return false end

	self.count = count
	self.data = data or {}

	return self
end
