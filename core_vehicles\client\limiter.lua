local limited = false
Citizen.CreateThread(function()
	while true do
		if ( IsControlJustPressed(0, 82) ) then
			local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
			DisableControlAction(0, 82, true)
			if ( vehicle ) then
				if ( GetPedInVehicleSeat(vehicle, -1) == PlayerPedId() ) then
					local cur = GetEntitySpeed(vehicle)
					local max = GetVehicleMaxSpeed(vehicle)
					if ( not limited ) then
						limited = true
						SetVehicleMaxSpeed(vehicle, cur)
						TriggerEvent("fdg_ui:SendNotification", "Max Speed Set <font color='green'>"..math.floor(cur*3.6+1).."</font> KM/H", {layout = "bottom-right"})
					else
						limited = false
						SetVehicleMaxSpeed(vehicle, max*3)
						TriggerEvent("fdg_ui:SendNotification", "<font color='red'>Max Speed Removed</font>", {layout = "bottom-right"})
					end

				end
			end
		end

		Wait(0)
	end
end)

function isValidLimit(veh, limit)
	local current = math.floor(GetEntitySpeed(veh) * 3.6)
	if (limit < current) then
		return false
	end
	return true
end

RegisterCommand("limit", function(source, args)
	local vehicle = GetVehiclePedIsIn(PlayerPedId(), false)
	if (vehicle) then
		if (GetPedInVehicleSeat(vehicle, -1) == PlayerPedId()) then
			if (tonumber(args[1]) ~= nil) then
				if (isValidLimit(vehicle, tonumber(args[1]))) then
					local calc = math.floor(tonumber(args[1])*0.277778) + 0.0
					limited = true
					SetVehicleMaxSpeed(vehicle, calc)
				end
			end
		end
	end
end, false)