function ToggleEngine(veh)
    local state = Entity(veh).state
    state.engineOn = not state.engineOn

    if state.engineOn then
        SetPedConfigFlag(PlayerPedId(), 429, false)
        SetVehicleEngineOn(veh, true, false, false)
    else
        SetPedConfigFlag(PlayerPedId(), 429, true)
        SetVehicleEngineOn(veh, false, false, false)
    end
end

function SetEngineState(veh, enabled)
    local state = Entity(veh).state
    state.engineOn = enabled
end

onBaseReady(function()
    Base.Controls.register("l", "Vehicle Locks", function()
        if (not isDead) then
            exports["core_vehicles"]:toggleVehicleLocks()
        end
    end)

    Base.Controls.register("k", "Vehicle Engine", function()
        if (not isDead) then
            if (IsPedInAnyVehicle(PlayerPedId(), false)) then
                local veh = GetVehiclePedIsIn(PlayerPedId(), false)
                if (getVehicleFuel(veh) > 1 and GetVehicleEngineHealth(veh) > 100.0 and GetPedInVehicleSeat(veh, -1) == PlayerPedId()) then
                    ToggleEngine(veh)
                end
            end
        end
    end)
end)

--- ===================================== QUI Alt Menu ========================================== ---

AddVehicleInteractionAction(
    function() -- Condition
        return GetVehiclePedIsIn(GetPlayerPed(-1))
    end,

    function() -- Action
        if (not isDead) then
            if (IsPedInAnyVehicle(PlayerPedId(), false)) then
                local veh = GetVehiclePedIsIn(PlayerPedId(), false)
                if (getVehicleFuel(veh) > 1 and GetVehicleEngineHealth(veh) > 100.0 and GetPedInVehicleSeat(veh, -1) == PlayerPedId()) then
                    ToggleEngine(veh)
                end
            end
        end
    end,

    { text = "Vehicle Engine", icon = "&#xF14ED;", action = "vehicle_engine" }, -- Menu Item

    { requires = { Vehicle = true } }                                           -- Requires
)

--- ============================================================================================= ---


--Add the following function into the driverTicks table.
--This function will be called every frame that the player is in the driver seats (up to 500ms delay)
AddEventHandler("vehicleDriverTick", function(veh)
    -- Vehicle Fuel Check
    if (getVehicleFuel(veh) <= 1 and GetIsVehicleEngineRunning(veh)) then
        SetEngineState(veh, false)

        -- Vehicle health / forced off (player control) check
    elseif (GetVehicleEngineHealth(veh) <= 100.0 or not IsVehicleDriveable(veh, 0)) then
        SetVehicleEngineOn(veh, false, true, true)
        SetEngineState(veh, false)
    end
end)

-- Vehicle engine disable on low fuel or high damage
-- AddEventHandler("vehicleEnter", function(veh)
--     -- Loop while player is in vehicle
--     while DoesEntityExist(veh) and GetPedInVehicleSeat(veh, -1) == PlayerPedId() do
--         Wait(0)

--         -- Vehicle Fuel Check
--         if (getVehicleFuel(veh) <= 1 and GetIsVehicleEngineRunning(veh)) then
--             SetEngineState(veh, false)

--             -- Vehicle health / forced off (player control) check
--         elseif (GetVehicleEngineHealth(veh) <= 100.0 or not IsVehicleDriveable(veh, 0)) then
--             SetVehicleEngineOn(veh, false, true, true)
--             SetEngineState(veh, false)
--         end
--     end
-- end)
