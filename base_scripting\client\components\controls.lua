Base.Controls = {}

Base.Controls.disabledKeys = {}
Base.Controls.controlList = {}
Base.Controls.callbacks = {}

Base.Controls.register = function(key, label, onPressed, onReleased, onHeld)
    local id = "lsla_"..string.lower(string.gsub(label, " ", ""))
    
    Base.Controls.disabledKeys[key] = false

    Base.Controls.controlList[id] = false
    Base.Controls.callbacks[id] = {
        onPressed = onPressed,
        onReleased = onReleased,
        onHeld = onHeld
    }

    RegisterCommand("+"..id, function()
        if (not Base.Controls.disabledKeys[key]) then
            Base.Controls.controlList[id] = true

            if Base.Controls.callbacks[id].onPressed then Base.Controls.callbacks[id].onPressed() end

            if Base.Controls.callbacks[id].onHeld then
                while Base.Controls.controlList[id] and onHeld do
                    Wait(0)
                    Base.Controls.callbacks[id].onHeld()
                end
            end
        end 
    end)

    RegisterCommand("-"..id, function()
        if (not Base.Controls.disabledKeys[key]) then
            Base.Controls.controlList[id] = false
            if Base.Controls.callbacks[id].onReleased then Base.Controls.callbacks[id].onReleased() end
        end
    end)

    RegisterKeyMapping("+"..id, label, "keyboard", key)
end

Base.Controls.disable = function(key, toggle)
    Base.Controls.disabledKeys[key] = toggle
end

Base.Controls.disableAll = function(toggle)
    for key in pairs(Base.Controls.disabledKeys) do
        Base.Controls.disabledKeys[key] = toggle
    end
end