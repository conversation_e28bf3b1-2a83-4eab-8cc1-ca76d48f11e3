--[[--------------------------------------------------------------------------
	*
	* <PERSON><PERSON> Trainer
	* (C) <PERSON> 2017
	* http://github.com/thestonedturtle/mellotrainer/releases
	*
	* This menu used the Scorpion Trainer as a framework to build off of.
	* https://github.com/pongo1231/ScorpionTrainer
	* (C) Emre Cürgül 2017
	*
	* A lot of useful functionality has been converted from the lambda menu.
	* https://lambda.menu
	* (C) Oui 2017
	*
	* Additional Contributors:
	* WolfKnight (https://forum.fivem.net/u/WolfKnight)
	*
---------------------------------------------------------------------------]]

local colorNames = {
	['0'] = "Metallic Black",
	['1'] = "Metallic Graphite Black",
	['2'] = "Metallic Black Steal",
	['3'] = "Metallic Dark Silver",
	['4'] = "Metallic Silver",
	['5'] = "Metallic Blue Silver",
	['6'] = "Metallic Steel Gray",
	['7'] = "Metallic Shadow Silver",
	['8'] = "Metallic Stone Silver",
	['9'] = "Metallic Midnight Silver",
	['10'] = "Metallic Gun Metal",
	['11'] = "Metallic Anthracite Grey",
	['12'] = "Matte Black",
	['13'] = "Matte Gray",
	['14'] = "Matte Light Grey",
	['15'] = "Util Black",
	['16'] = "Util Black Poly",
	['17'] = "Util Dark silver",
	['18'] = "Util Silver",
	['19'] = "Util Gun Metal",
	['20'] = "Util Shadow Silver",
	['21'] = "Worn Black",
	['22'] = "Worn Graphite",
	['23'] = "Worn Silver Grey",
	['24'] = "Worn Silver",
	['25'] = "Worn Blue Silver",
	['26'] = "Worn Shadow Silver",
	['27'] = "Metallic Red",
	['28'] = "Metallic Torino Red",
	['29'] = "Metallic Formula Red",
	['30'] = "Metallic Blaze Red",
	['31'] = "Metallic Graceful Red",
	['32'] = "Metallic Garnet Red",
	['33'] = "Metallic Desert Red",
	['34'] = "Metallic Cabernet Red",
	['35'] = "Metallic Candy Red",
	['36'] = "Metallic Sunrise Orange",
	['37'] = "Metallic Classic Gold",
	['38'] = "Metallic Orange",
	['39'] = "Matte Red",
	['40'] = "Matte Dark Red",
	['41'] = "Matte Orange",
	['42'] = "Matte Yellow",
	['43'] = "Util Red",
	['44'] = "Util Bright Red",
	['45'] = "Util Garnet Red",
	['46'] = "Worn Red",
	['47'] = "Worn Golden Red",
	['48'] = "Worn Dark Red",
	['49'] = "Metallic Dark Green",
	['50'] = "Metallic Racing Green",
	['51'] = "Metallic Sea Green",
	['52'] = "Metallic Olive Green",
	['53'] = "Metallic Green",
	['54'] = "Metallic Gasoline Blue Green",
	['55'] = "Matte Lime Green",
	['56'] = "Util Dark Green",
	['57'] = "Util Green",
	['58'] = "Worn Dark Green",
	['59'] = "Worn Green",
	['60'] = "Worn Sea Wash",
	['61'] = "Metallic Midnight Blue",
	['62'] = "Metallic Dark Blue",
	['63'] = "Metallic Saxony Blue",
	['64'] = "Metallic Blue",
	['65'] = "Metallic Mariner Blue",
	['66'] = "Metallic Harbor Blue",
	['67'] = "Metallic Diamond Blue",
	['68'] = "Metallic Surf Blue",
	['69'] = "Metallic Nautical Blue",
	['70'] = "Metallic Bright Blue",
	['71'] = "Metallic Purple Blue",
	['72'] = "Metallic Spinnaker Blue",
	['73'] = "Metallic Ultra Blue",
	['74'] = "Metallic Bright Blue",
	['75'] = "Util Dark Blue",
	['76'] = "Util Midnight Blue",
	['77'] = "Util Blue",
	['78'] = "Util Sea Foam Blue",
	['79'] = "Uil Lightning blue",
	['80'] = "Util Maui Blue Poly",
	['81'] = "Util Bright Blue",
	['82'] = "Matte Dark Blue",
	['83'] = "Matte Blue",
	['84'] = "Matte Midnight Blue",
	['85'] = "Worn Dark blue",
	['86'] = "Worn Blue",
	['87'] = "Worn Light blue",
	['88'] = "Metallic Taxi Yellow",
	['89'] = "Metallic Race Yellow",
	['90'] = "Metallic Bronze",
	['91'] = "Metallic Yellow Bird",
	['92'] = "Metallic Lime",
	['93'] = "Metallic Champagne",
	['94'] = "Metallic Pueblo Beige",
	['95'] = "Metallic Dark Ivory",
	['96'] = "Metallic Choco Brown",
	['97'] = "Metallic Golden Brown",
	['98'] = "Metallic Light Brown",
	['99'] = "Metallic Straw Beige",
	['100'] = "Metallic Moss Brown",
	['101'] = "Metallic Biston Brown",
	['102'] = "Metallic Beechwood",
	['103'] = "Metallic Dark Beechwood",
	['104'] = "Metallic Choco Orange",
	['105'] = "Metallic Beach Sand",
	['106'] = "Metallic Sun Bleeched Sand",
	['107'] = "Metallic Cream",
	['108'] = "Util Brown",
	['109'] = "Util Medium Brown",
	['110'] = "Util Light Brown",
	['111'] = "Metallic White",
	['112'] = "Metallic Frost White",
	['113'] = "Worn Honey Beige",
	['114'] = "Worn Brown",
	['115'] = "Worn Dark Brown",
	['116'] = "Worn straw beige",
	['117'] = "Brushed Steel",
	['118'] = "Brushed Black steel",
	['119'] = "Brushed Aluminium",
	['120'] = "Chrome",
	['121'] = "Worn Off White",
	['122'] = "Util Off White",
	['123'] = "Worn Orange",
	['124'] = "Worn Light Orange",
	['125'] = "Metallic Securicor Green",
	['126'] = "Worn Taxi Yellow",
	['127'] = "police car blue",
	['128'] = "Matte Green",
	['129'] = "Matte Brown",
	['130'] = "Worn Orange",
	['131'] = "Matte White",
	['132'] = "Worn White",
	['133'] = "Worn Olive Army Green",
	['134'] = "Pure White",
	['135'] = "Hot Pink",
	['136'] = "Salmon pink",
	['137'] = "Metallic Vermillion Pink",
	['138'] = "Orange",
	['139'] = "Green",
	['140'] = "Blue",
	['141'] = "Mettalic Black Blue",
	['142'] = "Metallic Black Purple",
	['143'] = "Metallic Black Red",
	['144'] = "hunter green",
	['145'] = "Metallic Purple",
	['146'] = "Metaillic V Dark Blue",
	['147'] = "MODSHOP BLACK1",
	['148'] = "Matte Purple",
	['149'] = "Matte Dark Purple",
	['150'] = "Metallic Lava Red",
	['151'] = "Matte Forest Green",
	['152'] = "Matte Olive Drab",
	['153'] = "Matte Desert Brown",
	['154'] = "Matte Desert Tan",
	['155'] = "Matte Foilage Green",
	['156'] = "DEFAULT ALLOY COLOR",
	['157'] = "Epsilon Blue",
}

local function _SetEntityAsNoLongerNeeded(entity)
	Citizen.InvokeNative(0xB736A491E64A32CF, Citizen.PointerValueIntInitialized(entity))
end

local lastVehicle

local function SpawnVehicle(model, x, y, z, heading, ped)
	-- Just in case they are in a vehicle which this trainer didn't spawn.
	if not lastVehicle and GetVehiclePedIsIn(ped, false) then
		lastVehicle = GetVehiclePedIsIn(ped, false)
	end

	if IsModelValid(model) then
		_LoadModel(model)

		--logging
		local name  = GetPlayerName(PlayerId())
		local label = GetDisplayNameFromVehicleModel(model)
		TriggerServerEvent('fdg_logs:sendToDiscord', name, name .. ' **spawned** ' .. label, 'red', 'admin-vehicle')
		--end

		local veh = CreateVehicle(model, x, y, z + 1, heading, true, true)

		if featureSpawnInsideCar then
			SetPedIntoVehicle(ped, veh, -1)
		end

		if featureDeleteLastVehicle then
			_SetEntityAsNoLongerNeeded(veh)
			-- Remove the last vehicle.
			if (lastVehicle) then
				if (GetVehicleNumberOfPassengers(lastVehicle) ~= 0 or IsVehicleSeatFree(lastVehicle, -1) == false) then
					drawNotification("~r~Last Vehicle could not be deleted.")
				else
					SetEntityAsMissionEntity(lastVehicle, true, true)
					DeleteVehicle(lastVehicle)
				end
			end
		end

		drawNotification("~g~Vehicle spawned!")
		lastVehicle = veh
		UpdateVehicleFeatureVariables(veh)
		toggleRadio(ped)

		SetModelAsNoLongerNeeded(veh)

		return veh
	else
		drawNotification("~r~Invalid Model!")
	end
end



RegisterNUICallback("vehspawnoptions", function(data, cb)
	local text = "~r~OFF"
	if (data.newstate) then
		text = "~g~ON"
	end
	if data.action == "despawn" then
		featureDeleteLastVehicle = data.newstate
		drawNotification("Delete Previous Vehicle: " .. tostring(text))
	elseif data.action == "insidecar" then
		featureSpawnInsideCar = data.newstate
		drawNotification("Spawn Into Vehicle: " .. tostring(text))
	elseif data.action == "infront" then
		featureSpawnCarInFront = data.newstate
		drawNotification("Spawn vehicle in front: " .. tostring(text))
	end

	if (cb) then cb("ok") end
end)



RegisterNUICallback("vehspawn", function(data, cb)
	local playerPed = GetPlayerPed(-1)
	local x, y, z
	local vehicle

	if (featureSpawnCarInFront) then
		x, y, z = table.unpack(GetOffsetFromEntityInWorldCoords(playerPed, 0.0, 7.5, 0.0))
	else
		x, y, z = table.unpack(GetEntityCoords(playerPed, true))
	end

	local heading = GetEntityHeading(playerPed)

	if data.action == "input" then
		local result = requestInput("", 60)

		if result then
			vehicle = SpawnVehicle(GetHashKey(string.upper(result)), x, y, z, heading, playerPed)
			UpdateVehicleFeatureVariables(vehicle)
		end
		return
	end

	local playerVeh = GetVehiclePedIsIn(playerPed, true)
	local vehhash = GetHashKey(data.action)

	vehicle = SpawnVehicle(vehhash, x, y, z, heading, playerPed)

	UpdateVehicleFeatureVariables(vehicle)

	if (cb) then cb("ok") end
end)

--[[------------------------------------------------------------------------
	Vehicle Saving and Loading
------------------------------------------------------------------------]]
--
local vehicles = {}
local vehicleCount = 0

RegisterNetEvent('wk:RecieveSavedVehicles')
AddEventHandler('wk:RecieveSavedVehicles', function(dataTable)
	vehicles = dataTable
	vehicleCount = getTableLength(dataTable)
end)

function CreateVehicleOptions(index)
	local spawnCar = {
		["menuName"] = "Spawn Car",
		["data"] = {
			["action"] = "spawnsavedveh " .. index
		}
	}

	local overwriteSave = {
		["menuName"] = "Overwrite With Current",
		["data"] = {
			["action"] = "vehiclesave " .. index
		}
	}

	local renameCar = {
		["menuName"] = "Rename Save",
		["data"] = {
			["action"] = "vehiclesave " .. index .. " r"
		}
	}

	local deleteCar = {
		["menuName"] = "Delete",
		["data"] = {
			["action"] = "deletesavedveh " .. index
		}
	}

	local options = { spawnCar, overwriteSave, renameCar, deleteCar }

	return options
end

RegisterNUICallback("loadsavedvehs", function(data, cb)
	local validOptions = {}

	for k, v in pairs(vehicles) do
		local vehicleOptions = CreateVehicleOptions(k)

		table.insert(validOptions, 1, {
			["menuName"] = v["saveName"],
			["data"] = {
				["sub"] = k -- [ "action" ] = "spawnsavedveh " .. k
			},
			["submenu"] = vehicleOptions
		})
	end

	table.insert(validOptions, {
		["menuName"] = "Create New Vehicle Save",
		["data"] = {
			["action"] = "vehiclesave"
		}
	})

	local customJSON = "{}"

	if (getTableLength(validOptions) > 0) then
		customJSON = json.encode(validOptions, { indent = true })
	end

	SendNUIMessage({
		createmenu = true,
		menuName = "loadsavedvehs",
		menudata = customJSON
	})

	if (cb) then cb("ok") end
end)

RegisterNUICallback("vehiclesave", function(data, cb)
	Citizen.CreateThread(function()
		local ped = GetPlayerPed(-1)
		local vehicleTableData = {}
		local saveName = nil
		local overwriting
		local renaming
		local index

		if (data.action == nil) then
			overwriting = false
			renaming = false
		else
			if (data.data[3] ~= nil) then
				renaming = true
			else
				overwriting = true
			end

			index = tonumber(data.action)
		end

		if (DoesEntityExist(ped) and not IsEntityDead(ped)) then
			local veh = GetVehiclePedIsIn(ped, false)

			if (GetPedInVehicleSeat(veh, -1) == ped) then
				while ((saveName == nil and not overwriting) or (saveName == nil and renaming)) do
					saveName = requestInput("Enter save name", 24)
					Citizen.Wait(1)
				end

				if (saveName or overwriting or renaming) then
					local model = GetEntityModel(veh)
					local primaryColour, secondaryColour = GetVehicleColours(veh)
					local pearlColour, wheelColour = GetVehicleExtraColours(veh)
					local mod1a, mod1b, mod1c = GetVehicleModColor_1(veh)
					local mod2a, mod2b = GetVehicleModColor_2(veh)
					local custR1, custG2, custB3, custR2, custG2, custB2

					if (GetIsVehiclePrimaryColourCustom(veh)) then
						custR1, custG1, custB1 = GetVehicleCustomPrimaryColour(veh)
					end

					if (GetIsVehicleSecondaryColourCustom(veh)) then
						custR2, custG2, custB2 = GetVehicleCustomSecondaryColour(veh)
					end

					if (renaming or not overwriting) then
						vehicleTableData["saveName"] = saveName
					else
						vehicleTableData["saveName"] = vehicles[index]["saveName"]
					end

					vehicleTableData["model"] = tostring(model)
					vehicleTableData["primaryColour"] = primaryColour
					vehicleTableData["secondaryColour"] = secondaryColour
					vehicleTableData["pearlColour"] = pearlColour
					vehicleTableData["wheelColour"] = wheelColour
					vehicleTableData["mod1Colour"] = { mod1a, mod1b, mod1c }
					vehicleTableData["mod2Colour"] = { mod2a, mod2b }
					vehicleTableData["custPrimaryColour"] = { custR1, custG1, custB1 }
					vehicleTableData["custSecondaryColour"] = { custR2, custG2, custB2 }

					local livery = GetVehicleLivery(veh)
					local plateText = GetVehicleNumberPlateText(veh)
					local plateType = GetVehicleNumberPlateTextIndex(veh)
					local wheelType = GetVehicleWheelType(veh)
					local windowTint = GetVehicleWindowTint(veh)
					local burstableTyres = GetVehicleTyresCanBurst(veh)
					local customTyres = GetVehicleModVariation(veh, 23)

					vehicleTableData["livery"] = livery
					vehicleTableData["plateText"] = plateText
					vehicleTableData["plateType"] = plateType
					vehicleTableData["wheelType"] = wheelType
					vehicleTableData["windowTint"] = windowTint
					vehicleTableData["burstableTyres"] = burstableTyres
					vehicleTableData["customTyres"] = customTyres

					local neonR, neonG, neonB = GetVehicleNeonLightsColour(veh)
					local smokeR, smokeG, smokeB = GetVehicleTyreSmokeColor(veh)

					local neonToggles = {}

					for i = 0, 3 do
						if (IsVehicleNeonLightEnabled(veh, i)) then
							table.insert(neonToggles, i)
						end
					end

					vehicleTableData["neonColour"] = { neonR, neonG, neonB }
					vehicleTableData["smokeColour"] = { smokeR, smokeG, smokeB }
					vehicleTableData["neonToggles"] = neonToggles

					local extras = {}

					if (DoesVehicleHaveExtras(veh)) then
						for i = 1, 30 do
							if (DoesExtraExist(veh, i)) then
								if (IsVehicleExtraTurnedOn(veh, i)) then
									table.insert(extras, i)
								end
							end
						end
					end

					vehicleTableData["extras"] = extras

					local mods = {}

					for i = 0, 49 do
						local isToggle = (i >= 17) and (i <= 22)

						if (isToggle) then
							mods[i] = IsToggleModOn(veh, i)
						else
							mods[i] = GetVehicleMod(veh, i)
						end
					end

					vehicleTableData["mods"] = mods

					if (not renaming and not overwriting) then
						vehicleCount = vehicleCount + 1
						vehicles[vehicleCount] = vehicleTableData
						TriggerServerEvent('wk:DataSave', "vehicles", vehicleTableData, vehicleCount)

						SendNUIMessage({
							reshowmenu = true
						})
					else
						vehicles[index] = vehicleTableData
						TriggerServerEvent('wk:DataSave', "vehicles", vehicleTableData, index)

						SendNUIMessage({
							trainerback = true
						})

						SendNUIMessage({
							reshowmenu = true
						})
					end

					resetTrainerMenus("loadsavedvehs")
				end
			end
		end
	end)
end)

RegisterNUICallback("spawnsavedveh", function(data, cb)
	local saveData = vehicles[tonumber(data.action)]
	local model = tonumber(saveData["model"])

	local ped = GetPlayerPed(-1)

	if (DoesEntityExist(ped) and not IsEntityDead(ped)) then
		local x, y, z

		if (featureSpawnInsideCar) then
			x, y, z = table.unpack(GetEntityCoords(ped, true))
		else
			x, y, z = table.unpack(GetOffsetFromEntityInWorldCoords(ped, 0.0, 7.5, 0.0))
		end

		local heading = GetEntityHeading(ped)

		local vehicle = SpawnVehicle(model, x, y, z, heading, ped)

		ApplySavedSettingsToVehicle(vehicle, saveData)
	end
end)

RegisterNUICallback("deletesavedveh", function(data, cb)
	local index = tonumber(data.action)

	-- Citizen.Trace( "Found " .. index .. " with type " .. type( index ) )

	if (vehicleCount > 0) then
		vehicles[index] = nil
		TriggerServerEvent('wk:DataSave', "vehicles", nil, index)

		SendNUIMessage({
			trainerback = true
		})

		SendNUIMessage({
			trainerback = true
		})

		resetTrainerMenus("loadsavedvehs")
	end
end)

function ApplySavedSettingsToVehicle(veh, data)
	local primaryColour = data["primaryColour"]
	local secondaryColour = data["secondaryColour"]
	local pearlColour = data["pearlColour"]
	local wheelColour = data["wheelColour"]
	local mod1Colour = data["mod1Colour"]
	local mod2Colour = data["mod2Colour"]
	local custPrimaryColour = data["custPrimaryColour"]
	local custSecondaryColour = data["custSecondaryColour"]
	local livery = data["livery"]
	local plateText = data["plateText"]
	local plateType = data["plateType"]
	local wheelType = data["wheelType"]
	local windowTint = data["windowTint"]
	local burstableTyres = data["burstableTyres"]
	local customTyres = data["customTyres"]
	local neonColour = data["neonColour"]
	local smokeColour = data["smokeColour"]
	local neonToggles = data["neonToggles"]
	local extras = data["extras"]
	local mods = data["mods"]

	-- Set the mod kit to 0, this is so we can do shit to the car
	SetVehicleModKit(veh, 0)

	SetVehicleTyresCanBurst(veh, burstableTyres)
	SetVehicleNumberPlateTextIndex(veh, plateType)
	SetVehicleNumberPlateText(veh, plateText)
	SetVehicleWindowTint(veh, windowTint)
	SetVehicleWheelType(veh, wheelType)

	for i = 1, 30 do
		if (DoesExtraExist(veh, i)) then
			SetVehicleExtra(veh, i, true)
		end
	end

	for k, v in pairs(extras) do
		local extra = tonumber(v)
		SetVehicleExtra(veh, extra, false)
	end

	for k, v in pairs(mods) do
		local k = tonumber(k)
		local isToggle = (k >= 17) and (k <= 22)

		if (isToggle) then
			ToggleVehicleMod(veh, k, v)
		else
			SetVehicleMod(veh, k, v, 0)
		end
	end

	local currentMod = GetVehicleMod(veh, 23)
	SetVehicleMod(veh, 23, currentMod, customTyres)
	SetVehicleMod(veh, 24, currentMod, customTyres)

	if (livery ~= -1) then
		SetVehicleLivery(veh, livery)
	end

	SetVehicleExtraColours(veh, pearlColour, wheelColour)
	SetVehicleModColor_1(veh, mod1Colour[1], mod1Colour[2], mod1Colour[3])
	SetVehicleModColor_2(veh, mod2Colour[1], mod2Colour[2])

	SetVehicleColours(veh, primaryColour, secondaryColour)

	if (custPrimaryColour[1] ~= nil and custPrimaryColour[2] ~= nil and custPrimaryColour[3] ~= nil) then
		SetVehicleCustomPrimaryColour(veh, custPrimaryColour[1], custPrimaryColour[2], custPrimaryColour[3])
	end

	if (custSecondaryColour[1] ~= nil and custSecondaryColour[2] ~= nil and custSecondaryColour[3] ~= nil) then
		SetVehicleCustomPrimaryColour(veh, custSecondaryColour[1], custSecondaryColour[2], custSecondaryColour[3])
	end

	SetVehicleNeonLightsColour(veh, neonColour[1], neonColour[2], neonColour[3])

	for i = 0, 3 do
		SetVehicleNeonLightEnabled(veh, i, false)
	end

	for k, v in pairs(neonToggles) do
		local index = tonumber(v)
		SetVehicleNeonLightEnabled(veh, index, true)
	end

	SetVehicleDirtLevel(veh, 0.0)

	UpdateVehicleFeatureVariables(veh)
end

function updateVehicleMechDamage(veh)
	SetVehicleEngineCanDegrade(veh, not featureVehMechDamage)
	SetVehicleCanBreak(veh, not featureVehMechDamage)
	SetVehicleWheelsCanBreak(veh, not featureVehMechDamage)
	SetDisableVehiclePetrolTankDamage(veh, featureVehMechDamage)
end

function updateVehicleCosmDamage(veh)
	SetVehicleCanBeVisiblyDamaged(veh, not featureVehCosDamage)
	SetVehicleStrong(veh, featureVehCosDamage)
	SetVehicleDoorBreakable(veh, 0, not featureVehCosDamage)
	SetVehicleDoorBreakable(veh, 1, not featureVehCosDamage)
	SetVehicleDoorBreakable(veh, 2, not featureVehCosDamage)
	SetVehicleDoorBreakable(veh, 3, not featureVehCosDamage)
	SetVehicleDoorBreakable(veh, 4, not featureVehCosDamage)
	SetVehicleDoorBreakable(veh, 5, not featureVehCosDamage)
	SetVehicleDoorBreakable(veh, 6, not featureVehCosDamage)
end

function UpdateVehicleFeatureVariables(veh)
	featureBulletproofWheels = not GetVehicleTyresCanBurst(veh)
	featureXeonLights = IsToggleModOn(veh, 22)
	featureCustomTires = GetVehicleModVariation(veh, 23)
	featureTurboMode = IsToggleModOn(veh, 18)

	featureNeonLeft = IsVehicleNeonLightEnabled(veh, 0)
	featureNeonRight = IsVehicleNeonLightEnabled(veh, 1)
	featureNeonFront = IsVehicleNeonLightEnabled(veh, 2)
	featureNeonRear = IsVehicleNeonLightEnabled(veh, 3)

	-- Cosmetic Damage
	updateVehicleCosmDamage(veh)

	-- Mechanical Damage
	updateVehicleMechDamage(veh)

	-- Invincible
	SetEntityInvincible(playerVeh, featureVehInvincible)

	windows = { false, false, false, false }

	featureTorqueMultiplier = 1
	featurePowerMultiplier = 1
	featureLowerForce = 0

	resetTrainerMenus("vehmods")
end

RegisterNUICallback("vehcolor", function(data, cb)
	local playerPed = GetPlayerPed(-1)
	local playerVeh = GetVehiclePedIsIn(playerPed, false)
	local color = stringsplit(data.action, ",")
	local r = tonumber(color[1])
	local g = tonumber(color[2])
	local b = tonumber(color[3])

	if not playerVeh then
		drawNotification("~r~Not in a vehicle!")
		return
	end

	SetVehicleCustomPrimaryColour(playerVeh, r, g, b)
	SetVehicleCustomSecondaryColour(playerVeh, r, g, b)
	drawNotification("~g~Repainted vehicle!")

	if (cb) then cb("ok") end
end)



-- Only show menu if they are in a vehicle.
RegisterNUICallback("requirevehicle", function(data, cb)
	local playerPed = GetPlayerPed(-1)
	local playerVeh = GetVehiclePedIsIn(playerPed, false)


	-- Not in a vehicle
	if playerVeh <= 0 then
		drawNotification("~r~Not in a vehicle")
	elseif (not (playerPed == (GetPedInVehicleSeat(playerVeh, -1)))) then
		drawNotification("~r~Not owner of vehicle")
	else
		local vehClass = GetVehicleClass(playerVeh)
		if (vehClass == 21) then
			-- Boat or Trains shouldn't pass the vehicleModification
			drawNotification("~r~This vehicle doesn't support modifications.")
		else
			local data = createVariableData()
			SetVehicleModKit(playerVeh, 0)
			SendNUIMessage({ vehicleaccess = true, updateVariables = true, data = data })
		end
	end
end)



local windows = { false, false, false, false }
RegisterNUICallback("veh", function(data, cb)
	local playerPed = GetPlayerPed(-1)
	local playerVeh = GetVehiclePedIsIn(playerPed, false)
	local action = data.action
	local text, text2

	if (data.newstate) then
		text = "~g~ON"
		text2 = "~r~OFF"
	else
		text = "~r~OFF"
		text2 = "~g~ON"
	end


	if action == "toggleinstantly" then
		featureCloseInstantly = data.newstate
		drawNotification("Open Doors Instantly: " .. text)
		return
	elseif action == "speedometer" then
		featureSpeedometer = data.newstate
		drawNotification("Speedometer: " .. text)
		return
	end



	-- Rest of these request them to be in a vehicle.
	if playerVeh <= 0 then
		drawNotification("~r~Not in a vehicle!")
		return
	end

	if action == "set" then
		SetVehicleOnGroundProperly(playerVeh)

		local name  = GetPlayerName(PlayerId())
		local model = GetEntityModel(playerVeh)
		local label = GetDisplayNameFromVehicleModel(model)
		TriggerServerEvent('fdg_logs:sendToDiscord', name, name .. ' **set** ' .. label, 'purple', 'admin-vehicle')
	elseif action == "fixset" then
		SetVehicleOnGroundProperly(playerVeh)
		SetVehicleFixed(playerVeh)
		SetVehicleDirtLevel(playerVeh, 0.0)
		drawNotification("~g~Vehicle repaired & set properly.")

		local name  = GetPlayerName(PlayerId())
		local model = GetEntityModel(playerVeh)
		local label = GetDisplayNameFromVehicleModel(model)
		local coords = GetEntityCoords(playerVeh)

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name .. ' **repaired/set** ' .. label, 'purple', 'admin-vehicle')

		TriggerServerEvent('clvehicles:fixset', coords, model, label)

	elseif action == "fix" then
		SetVehicleFixed(playerVeh)
		drawNotification("~g~Vehicle repaired.")

		local name  = GetPlayerName(PlayerId())
		local model = GetEntityModel(playerVeh)
		local label = GetDisplayNameFromVehicleModel(model)
		TriggerServerEvent('fdg_logs:sendToDiscord', name, name .. ' **repaired** ' .. label, 'purple', 'admin-vehicle')
	elseif action == "dirty" then
		SetVehicleDirtLevel(playerVeh, 15.0)
		drawNotification("~g~Vehicle dirtied.")
	elseif action == "clean" then
		SetVehicleDirtLevel(playerVeh, 0.0)
		drawNotification("~g~Vehicle cleaned.")
	elseif action == "toggledoor" then
		local doorInt = tonumber(data.data[3])

		if GetVehicleDoorAngleRatio(playerVeh, doorInt) == 0 then
			SetVehicleDoorOpen(playerVeh, doorInt, false, featureCloseInstantly)
		else
			SetVehicleDoorShut(playerVeh, doorInt, featureCloseInstantly)
		end
	elseif action == "closeall" then
		SetVehicleDoorsShut(playerVeh, featureCloseInstantly)
	elseif action == "openall" then
		for i = 0, 6, 1 do
			SetVehicleDoorOpen(playerVeh, i, false, featureCloseInstantly)
		end
	elseif action == "rollwindows" then
		local truecount = 0
		for i = 1, 4, 1 do
			if windows[i] then
				truecount = truecount + 1
			end
		end

		if truecount > 2 then
			for i = 1, 4, 1 do
				RollUpWindow(playerVeh, i - 1)
				windows[i] = false
			end
		else
			RollDownWindows(playerVeh)
			for i = 1, 4, 1 do
				windows[i] = true
			end
		end
	elseif action == "rollwindow" then
		local windowInt = tonumber(data.data[3])

		if (windows[windowInt + 1]) then
			RollUpWindow(playerVeh, windowInt)
		else
			RollDownWindow(playerVeh, windowInt)
		end

		windows[windowInt + 1] = not windows[windowInt + 1]
	elseif action == "fixwindow" then
		local windowInt = tonumber(data.data[3])
		FixVehicleWindow(playerVeh, windowInt)
	elseif action == "fixwindows" then
		for i = 0, 3, 1 do
			FixVehicleWindow(playerVeh, i)
		end
	end

	if (cb) then cb("ok") end
end)


RegisterNUICallback("vehmod", function(data, cb)
	local playerPed = GetPlayerPed(-1)
	local playerVeh = GetVehiclePedIsIn(playerPed, false)
	local action = data.action
	local text, text2

	if (data.newstate) then
		text = "~g~ON"
		text2 = "~r~OFF"
	else
		text = "~r~OFF"
		text2 = "~g~ON"
	end

	-- Rest of these request them to be in a vehicle.
	if playerVeh <= 0 then
		drawNotification("~r~Not in a vehicle!")
		SendNUIMessage({ toggleerror = true })
		return
	end

	SetVehicleModKit(playerVeh, 0)

	-- Toggle Options
	if action == "bulletwheels" then
		featureBulletproofWheels = data.newstate
		SetVehicleTyresCanBurst(playerVeh, not featureBulletproofWheels)

		drawNotification("Bulletproof Tires: " .. text)

		modLog(action, playerVeh, text)
	elseif action == "xenonlights" then
		featureXeonLights = data.newstate
		ToggleVehicleMod(playerVeh, 22, featureXeonLights)

		drawNotification("Xenon headlights: " .. text)

		modLog(action, playerVeh, text)
	elseif action == "customtires" then
		featureCustomTires = data.newstate
		SetVehicleMod(playerVeh, 23, GetVehicleMod(playerVeh, 23), featureCustomTires)
		if ((featureCustomTires and not GetVehicleModVariation(playerVeh, 23)) or (not featureCustomTires and GetVehicleModVariation(playerVeh, 23))) then
			SendNUIMessage({ toggleerror = true })
		end

		drawNotification("Custom Tires: " .. text)

		modLog(action, playerVeh, text)
	elseif action == "turbomode" then
		featureTurboMode = data.newstate
		ToggleVehicleMod(playerVeh, 18, data.newstate)

		drawNotification("Turbo Mode: " .. text)

		modLog(action, playerVeh, text)

		-- Window Tint
	elseif action == "windowtint" then
		local windowTintID = tonumber(data.data[3])
		SetVehicleWindowTint(playerVeh, windowTintID)

		modLog(action, playerVeh, windowTintID)

		-- Plate Modifications
	elseif action == "plate" then
		local plateID = tonumber(data.data[3])
		SetVehicleNumberPlateTextIndex(playerVeh, plateID)

		modLog(action, playerVeh, plateID)
	elseif action == "platetext" then
		local result = requestInput(GetVehicleNumberPlateText(playerVeh), 8)
		local oldplate = GetVehicleNumberPlateText(playerVeh)
		if result then
			SetVehicleNumberPlateText(playerVeh, result)
			paintLog(action, playerVeh, oldplate, result)
		end

		-- Tire Smoke=
	elseif action == "smokecolor" then
		local color1 = tonumber(data.data[3])
		local color2 = tonumber(data.data[4])
		local color3 = tonumber(data.data[5])


		ToggleVehicleMod(playerVeh, 20, true)
		SetVehicleTyreSmokeColor(playerVeh, color1, color2, color3)

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name .. ' changed tyre smoke colour ', 'blue', 'admin-vehicle')

		-- Neon underglow
	elseif action == "neonlights" then
		local location = data.data[3]
		local id
		if (location == "left") then
			featureNeonLeft = data.newstate
			id = 0
		elseif (location == "right") then
			featureNeonRight = data.newstate
			id = 1
		elseif (location == "front") then
			featureNeonFront = data.newstate
			id = 2
		elseif (location == "rear") then
			featureNeonRear = data.newstate
			id = 3
		end

		SetVehicleNeonLightEnabled(playerVeh, id, data.newstate)

		modLog(action, playerVeh, data.newstate)
	elseif action == "lightcolor" then
		local color1 = tonumber(data.data[3])
		local color2 = tonumber(data.data[4])
		local color3 = tonumber(data.data[5])

		SetVehicleNeonLightsColour(playerVeh, color1, color2, color3)

		local colorlog = { color1, color2, color3 }

		modLog(action, playerVeh, colorlog)

		-- Vehicle Paint Options.
	elseif action == "paint" then
		local cur = GetVehicleColours(playerVeh)
		local paintColor = tonumber(data.data[3])

		SetVehicleColours(playerVeh, paintColor, cur2)

		local newcolor, unused = GetVehicleColours(playerVeh)

		paintLog(action, playerVeh, colorNames[tostring(cur)], colorNames[tostring(newcolor)])
	elseif action == "paint2" then
		local cur1, cur2 = GetVehicleColours(playerVeh)
		local paintColor = tonumber(data.data[3])

		SetVehicleColours(playerVeh, cur1, paintColor)

		local unused, newcolor = GetVehicleColours(playerVeh)

		paintLog(action, playerVeh, colorNames[tostring(cur2)], colorNames[tostring(newcolor)])
	elseif action == "paint3" then
		local paintColor = tonumber(data.data[3])

		local oldcolor = GetVehicleColours(playerVeh)

		SetVehicleColours(playerVeh, paintColor, paintColor)

		local newcolor = GetVehicleColours(playerVeh)

		paintLog(action, playerVeh, colorNames[tostring(oldcolor)], colorNames[tostring(newcolor)])
	elseif action == "paintpearl" then
		local cur1, cur2 = GetVehicleExtraColours(playerVeh)
		local paintColor = tonumber(data.data[3])

		SetVehicleExtraColours(playerVeh, paintColor, cur2)

		local newcolor, cur2 = GetVehicleExtraColours(playerVeh)

		paintLog(action, playerVeh, colorNames[tostring(cur1)], colorNames[tostring(newcolor)])
	elseif action == "paintwheels" then
		local cur1, cur2 = GetVehicleExtraColours(playerVeh)
		local paintColor = tonumber(data.data[3])

		SetVehicleExtraColours(playerVeh, cur1, paintColor)

		local cur1, newcolor = GetVehicleExtraColours(playerVeh)

		paintLog(action, playerVeh, colorNames[tostring(cur1)], colorNames[tostring(newcolor)])
	elseif action == "changewheeltype" then
		local newtype = tonumber(data.data[3])
		SetVehicleWheelType(playerVeh, newtype)
		SetVehicleMod(playerVeh, 23, 0, customTires)

		modLog(action, playerVeh, newtype)

		drawNotification("~g~Tire Category Changed")
		local data = createVariableData()
		SendNUIMessage({ updateVariables = true, data = data })
	end
end)



local defaultVehAction = "vehmodify"
RegisterNUICallback("vehmodify", function(data, cb)
	local playerPed = GetPlayerPed(-1)
	local playerVeh = GetVehiclePedIsIn(playerPed, false)


	-- Not in a vehicle, menu shouldn't display.
	if playerVeh <= 0 then
		drawNotification("~r~Not in a vehicle!")
		SendNUIMessage({ toggleerror = true })
		return
	end

	SetVehicleModKit(playerVeh, 0)

	if (data.data[2] == "extra") then
		local extraID = tonumber(data.data[3])
		local newstate = data.newstate
		SetVehicleExtra(playerVeh, extraID, not newstate)
		drawNotification("Extra toggled.")
	end

	local modID = tonumber(data.data[2])
	if (data.data[3] == "clear") then
		RemoveVehicleMod(playerVeh, modID)
		if (modID == 48) then
			SetVehicleLivery(playerVeh, 0)
		end
		return
	end
	local modIndex = tonumber(data.data[3])

	if (modID == 48) then
		SetVehicleLivery(playerVeh, modIndex)
	end


	-- For some reason this is getting called when entering modification menu with nil values.
	-- I've added this check here to ensure we don't randomly change the vehicle when entering the menu
	if (modID == nil or modIndex == nil) then
		return
	end

	local name = GetPlayerName(PlayerId())
	local label = GetDisplayNameFromVehicleModel(GetEntityModel(playerVeh))
	local plate = GetVehicleNumberPlateText(playerVeh)
	local importantMods = {
		[11] = { mod = "Engine" },
		[12] = { mod = "Brakes" },
		[13] = { mod = "Transmission" },
		[15] = { mod = "Suspension" },
		[16] = { mod = "Armor" }
	}
	for k, v in pairs(importantMods) do
		if k == modID then
			TriggerServerEvent('fdg_logs:sendToDiscord', name, name .. " applied mod: **" .. v.mod .. "** to " .. modIndex .. " on " .. label .. " | " .. plate, 'red', 'admin-functions')
		end
	end
	SetVehicleMod(playerVeh, modID, modIndex, customTires)
	drawNotification("Mod Applied.")
end)


local vehicleMods = {
	["Spoilers"] = 0,
	["Front Bumper"] = 1,
	["Rear Bumper"] = 2,
	["Side Skirt"] = 3,
	["Exhaust"] = 4,
	["Frame"] = 5,
	["Grille"] = 6,
	["Hood"] = 7,
	["Fender"] = 8,
	["Right Fender"] = 9,
	["Roof"] = 10,
	["Vanity Plates"] = 25,
	["Trim"] = 27,
	["Ornaments"] = 28,
	["Dashboard"] = 29,
	["Dial"] = 30,
	["Door Speaker"] = 31,
	["Seats"] = 32,
	["Steering Wheel"] = 33,
	["Shifter Leavers"] = 34,
	["Plaques"] = 35,
	["Speakers"] = 36,
	["Trunk"] = 37,
	["Hydraulics"] = 38,
	["Engine Block"] = 39,
	["Air Filter"] = 40,
	["Struts"] = 41,
	["Arch Cover"] = 42,
	["Aerials"] = 43,
	["Trim 2"] = 44,
	["Tank"] = 45,
	["Windows"] = 46,
	["Livery"] = 48,
	["Extras"] = "extra"
}


RegisterNUICallback("vehmods", function(data, cb)
	local validOptions = {}
	local optCount = 0

	for keyName, value in pairs(vehicleMods) do
		local validCOmponents
		if (value == "extra") then
			validComponents = checkValidVehicleExtras()
		else
			validComponents = checkValidVehicleMods(value)
		end

		if #validComponents > 0 then
			table.insert(validOptions, {
				["menuName"] = keyName .. " (" .. #validComponents .. ")",
				["data"] = {
					["sub"] = value
				},
				["submenu"] = validComponents

			})

			optCount = optCount + 1
		end
	end
	local customJSON = "{}"
	if (optCount > 0) then
		customJSON = json.encode(validOptions, { indent = true })
	end

	--Citizen.Trace(customJSON);

	SendNUIMessage({
		createmenu = true,
		menuName = "vehmods",
		menudata = customJSON
	})
	if (cb) then cb("ok") end
end)


function createVariableData()
	local playerPed = GetPlayerPed(-1)
	local playerVeh = GetVehiclePedIsIn(playerPed, false)

	local wheelModID = 23
	if (playerVeh == 13) then
		wheelModID = 24
	end

	local data = json.encode({ wheeltype = tostring(GetVehicleWheelType(playerVeh)), wheelindex = tostring(GetVehicleMod(playerVeh, wheelModID)) }, { indent = true })
	--Citizen.Trace(data)

	return data
end

function checkValidVehicleExtras()
	local playerPed = GetPlayerPed(-1)
	local playerVeh = GetVehiclePedIsIn(playerPed, false)
	local valid = {}

	-- Get Extra Toggle Options
	for i = 0, 50, 1 do
		if (DoesExtraExist(playerVeh, i)) then
			local realModName = "Extra #" .. tostring(i)
			local text = "OFF"
			if (IsVehicleExtraTurnedOn(playerVeh, i)) then
				text = "ON"
				--Citizen.Trace(tostring(i).." is ON")
			end
			local realSpawnName = defaultVehAction .. " extra " .. tostring(i)
			table.insert(valid, {
				menuName = realModName,
				data = {
					["action"] = realSpawnName,
					["state"] = text
				}
			})
		end
	end

	return valid
end

function DoesVehicleHaveExtras(veh)
	for i = 1, 30 do
		if (DoesExtraExist(veh, i)) then
			return true
		end
	end

	return false
end

function checkValidVehicleMods(modID)
	local playerPed = GetPlayerPed(-1)
	local playerVeh = GetVehiclePedIsIn(playerPed, false)
	local valid = {}
	local modCount = GetNumVehicleMods(playerVeh, modID)



	-- Handle Liveries if they don't exist in modCount
	if (modID == 48 and modCount == 0) then
		--Citizen.Trace("Adding Non-mod Liveries")

		-- Local to prevent below code running.
		local modCount = GetVehicleLiveryCount(playerVeh)
		for i = 1, modCount, 1 do
			local realIndex = i - 1
			local modName = GetLiveryName(playerVeh, realIndex)
			local realModName = GetLabelText(modName)
			local realSpawnName = defaultVehAction .. " " .. tostring(modID) .. " " .. tostring(realIndex)

			--Citizen.Trace("modname:realModName "..tostring(modName)..":"..tostring(realModName))

			valid[i] = {
				menuName = realModName,
				data = {
					["action"] = realSpawnName
				}
			}
		end
	end


	-- Handles all other mods
	for i = 1, modCount, 1 do
		local realIndex = i - 1
		local modName = GetModTextLabel(playerVeh, modID, realIndex)
		local realModName = GetLabelText(modName)
		local realSpawnName = defaultVehAction .. " " .. tostring(modID) .. " " .. tostring(realIndex)


		valid[i] = {
			menuName = realModName,
			data = {
				["action"] = realSpawnName
			}
		}
	end


	-- Insert Stock Option for modifications
	if (modCount > 0) then
		local realSpawnName = defaultVehAction .. " " .. tostring(modID) .. " clear"
		table.insert(valid, 1, {
			menuName = "Stock",
			data = {
				["action"] = realSpawnName
			}
		})
	end

	return valid
end

local lowerForces = {
	[0] = 0.00,
	[1] = -0.018,
	[2] = -0.03,
	[3] = -0.05,
	[4] = -0.08,
	[5] = -0.11,
	[6] = -0.15
}


RegisterNUICallback("vehopts", function(data, cb)
	local playerPed = GetPlayerPed(-1)
	local playerVeh = GetVehiclePedIsIn(playerPed, false)
	local action = data.action
	local state = data.newstate

	local text, text2
	if (data.newstate) then
		text = "~g~ON"
		text2 = "~r~OFF"
	else
		text = "~r~OFF"
		text2 = "~g~ON"
	end


	-- No Drag Out
	if (action == "nodrag") then
		featureNoDragOut = state;
		featureNoDragOutUpdated = true;
		drawNotification("No Drag: " .. text)

		-- No Fall Off
	elseif (action == "nofall") then
		featureNoFallOff = state;
		featureNoFallOffUpdated = true;
		drawNotification("No Fall: " .. text)

		-- No Helmet
	elseif (action == "nohelmet") then
		featureNoHelmet = state;
		drawNotification("No Helmet: " .. text)
	end


	if (not IsPedInAnyVehicle(playerPed, false)) then
		drawNotification("Not in a vehicle.")
		return
	end

	local playerVeh = GetVehiclePedIsUsing(PlayerPedId())
	if (not (playerPed == (GetPedInVehicleSeat(playerVeh, -1)))) then
		drawNotification("Not owner of vehicle.")
		return
	end


	local name  = GetPlayerName(PlayerId())
	local label = GetDisplayNameFromVehicleModel(GetEntityModel(playerVeh))
	-- Drift Mode
	if (action == "driftmode") then
		if (not (IsThisModelACar(GetEntityModel(playerVeh)))) then
			drawNotification("Vehicle must be a car")
			return
		end

		featureDriftMode = state
		SetVehicleReduceGrip(playerVeh, featureDriftMode)
		drawNotification("Drift Mode: " .. text)

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name .. ' toggled **DRIFT MODE** ' .. text .. ' on ' .. label, 'red', 'admin-functions')

		-- Power Options
	elseif (action == "powerboost") then
		featurePowerMultiplier = tonumber(data.data[3])

		drawNotification("Power Boost Multiplier: " .. tostring(featurePowerMultiplier))

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name .. ' toggled **POWER BOOST** to ' .. tostring(featurePowerMultiplier) .. ' on ' .. label, 'red', 'admin-functions')

		-- Torque Options
	elseif (action == "torqueboost") then
		featureTorqueMultiplier = tonumber(data.data[3])

		drawNotification("Torque Multiplier: " .. tostring(featureTorqueMultiplier))

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name .. ' toggled **TORQUE BOOST** to ' .. tostring(featureTorqueMultiplier) .. ' on ' .. label, 'red', 'admin-functions')

		-- Lowering Level
	elseif (action == "lowering") then
		if (not (IsThisModelACar(GetEntityModel(playerVeh)))) then
			drawNotification("Vehicle must be a car")
			return
		end

		featureLowerForce = tonumber(data.data[3])

		drawNotification("Lowering: level " .. featureLowerForce)

		--
	elseif (action == "cosdamage") then
		featureVehCosDamage = state
		updateVehicleCosmDamage(playerVeh)
		drawNotification("No Cosmetic Damage: " .. text)

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name .. ' toggled **NO COSMETIC DAMAGE** to ' .. text .. ' on ' .. label, 'red', 'admin-functions')
		--
	elseif (action == "mechdamage") then
		featureVehMechDamage = state
		updateVehicleMechDamage(playerVeh)
		drawNotification("No Mechanical Damage: " .. text)

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name .. ' toggled **NO MECHANICAL DAMAGE** to ' .. text .. ' on ' .. label, 'red', 'admin-functions')
		--
	elseif (action == "invincible") then
		featureVehInvincible = state
		SetEntityInvincible(playerVeh, featureVehInvincible)
		drawNotification("Vehicle Indestructable: " .. text)

		TriggerServerEvent('fdg_logs:sendToDiscord', name, name .. ' toggled **VEHICLE INVINCIBLE** to ' .. text .. ' on ' .. label, 'red', 'admin-functions')
	end



	if (cb) then cb("ok") end
end)

-- Vehicle Options Thread
Citizen.CreateThread(function()
	while true do
		Wait(10)
		local playerPed = GetPlayerPed(-1)

		if IsPedInAnyVehicle(playerPed, true) then
			local playerVeh = GetVehiclePedIsUsing(playerPed)


			-- No Drag Out
			if (featureNoDragOutUpdated) then
				SetPedCanBeDraggedOut(playerPed, featureNoDragOut)
				featureNoDragOutUpdated = false
			end


			-- No FallL Off
			if (featureNoFallOffUpdated) then
				SetPedCanBeKnockedOffVehicle(playerPed, featureNoFallOff)
				featureNoFallOffUpdated = false
			end


			-- No Helmet
			if (featureNoHelmet) then
				SetPedHelmet(playerPed, featureNoHelmet)
				RemovePedHelmet(playerPed, true)
			end
		else
			Wait(500)
		end
	end
end)


function modLog(action, playerVeh, info)
	local name  = GetPlayerName(PlayerId())
	local label = GetDisplayNameFromVehicleModel(GetEntityModel(playerVeh))
	local plate = GetVehicleNumberPlateText(playerVeh)

	if action == 'bulletwheels' then
		idonteventknow = '**BULLETPROOF TYRES**'
	elseif action == 'xenonlights' then
		idonteventknow = '**XENON LIGHTS**'
	elseif action == 'turbomode' then
		idonteventknow = '**TURBO**'
	elseif action == 'customtires' then
		idonteventknow = '**CUSTOM TYRES**'
	elseif action == 'paintwheels' then
		idonteventknow = '**WHEEL**'
	elseif action == 'windowtint' then
		idonteventknow = '**TINT**'
	elseif action == 'plate' then
		idonteventknow = '**PLATE TYPE**'
	end

	if info == nil or info == '' then
		info = ' '
	end

	TriggerServerEvent('fdg_logs:sendToDiscord', name, name .. ' toggled ' .. idonteventknow .. ' ' .. info .. ' | ' .. label .. ' | ' .. plate, 'blue', 'admin-vehicle')
end

function paintLog(action, playerVeh, oldcolor, newcolor)
	local name  = GetPlayerName(PlayerId())
	local label = GetDisplayNameFromVehicleModel(GetEntityModel(playerVeh))
	local plate = GetVehicleNumberPlateText(playerVeh)

	if action == 'paint' then
		idonteventknow = '**PAINT PRIMARY**'
	elseif action == 'paint2' then
		idonteventknow = '**PAINT SECONDARY**'
	elseif action == 'paint3' then
		idonteventknow = '**PAINT PRIMARY/SECONDARY**'
	elseif action == 'paintpearl' then
		idonteventknow = '**PAINT PEARL**'
	elseif action == 'paintwheels' then
		idonteventknow = '**PAINT WHEEL**'
	elseif action == 'platetext' then
		idonteventknow = '**NUMBERPLATE**'
	end

	TriggerServerEvent('fdg_logs:sendToDiscord', name, name .. ' changed ' .. idonteventknow .. ' from **' .. oldcolor .. '** to **' .. newcolor .. '** | ' .. label .. ' | ' .. plate, 'blue', 'admin-vehicle')
end
