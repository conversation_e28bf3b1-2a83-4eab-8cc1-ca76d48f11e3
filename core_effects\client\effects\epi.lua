local epiEffect = TimedStatusEffect(StatusEffect("adrenaline", "Adrenaline Rush",
    {
        onStart = function(effect)
            DoScreenFadeOut(500)
            Wait(500)
            if not AnimpostfxIsRunning("DrugsMichaelAliensFight") then
                AnimpostfxPlay("DrugsMichaelAliensFight")
            end
            AddPlayerSpeedBuff("adrenaline", 0.5)
            Wait(500)
            DoScreenFadeIn(500)
        end,

        onTick = function(effect)

        end,

        onStop = function()
            DoScreenFadeOut(500)
            Wait(500)
            AnimpostfxStop("DrugsMichaelAliensFight")
            Wait(500)
            DoScreenFadeIn(500)
            RemoveSpeedBuff("adrenaline")
        end,
    },
    {
        alert = false,
        desc = "You are amped up on adrenaline.",
        tickRate = 0,
    }
), { maxTime = 2 * 60 })

RegisterInteraction("player", "inject_epi", { label = "Inject Epinephrine" },
    function(player)
        exports.main_roleplay:startDatabaseAnim("inject")
        TriggerServerEvent("effect:injectEpinephrine", GetPlayerServerId(player))
    end,
    function(player)
        return exports["core_inventory"]:getItem("syringe_epinephrine")
    end
)

-- RegisterInteraction("player", "inject_", { label = "Inject " },
--     function(player)
--         exports.main_roleplay:startDatabaseAnim("inject")
--         TriggerServerEvent("effect:injectDroperidol", GetPlayerServerId(player))
--     end,
--     function(player)
--         return exports["core_inventory"]:getItem("syringe_droperidol")
--     end
-- )

RegisterNetEvent("effects:startAdrenaline", function(startDelay, duration, shouldKill)
    Wait(startDelay)

    epiEffect.addTime(duration)
end)
