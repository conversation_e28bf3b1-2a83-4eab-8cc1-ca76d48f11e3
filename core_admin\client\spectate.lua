isSpectating = false
local changedSpectateTarget = false
local savedCoords, savedHeading = nil, 0.0
local player_being_spectated = -1
local _staff_spectating = {}

local function getPlayerFromSource(tgt)
    for _, plr in pairs(playerList) do
        if (plr.source == tgt.source) then
            return plr
        end
    end
    return false
end

local function setSpectateTarget(previous)
    local players = table.clone(playerList)
    table.sort(players, function(a,b) return a.accountid < b.accountid end)

    -- if nobody is being spectated then spectate the first player, just incase of any issues
    if (player_being_spectated == -1) then
        player_being_spectated = players[1]
    end

    for i = 1, #players, 1 do
        -- searches through players to find the one you are spectating, as the baseline on where to go.
        if (players[i].source == player_being_spectated.source) then
            if (previous) then
                player_being_spectated = ( i == 1 and players[#players] or players[i - 1] )
            else
                player_being_spectated = ( i == #players and players[1] or players[i + 1] )
            end
            changedSpectateTarget = true
            TriggerServerEvent("core_admin:changedSpectateTarget", player_being_spectated)
            break
        end
    end
end

local function isPlayerSpectatingTarget(tgt)
    if (player_being_spectated == -1) then
        return false
    end
    if (player_being_spectated.source == tgt.source) then
        return true
    end
    return false
end

local function startSpectatingPlayer(src)
    local player = getPlayerFromSource(src)
    if (player) then
        if (not isSpectating) then
            savedCoords = GetEntityCoords(PlayerPedId())
            savedHeading = GetEntityHeading(PlayerPedId())
			Base.Player.setEntityVisible(false)
            SetEntityInvincible(PlayerPedId(), true)
            SetEntityCollision(PlayerPedId(), false, false)
            FreezeEntityPosition(PlayerPedId(), true)
        end
        player_being_spectated = player
        isSpectating = true
        SendNUIMessage({type = 'startspectating'})
    end
end

local function stopSpectating()
    isSpectating = false
    player_being_spectated = -1
    NetworkSetInSpectatorMode(false)
    local ped = PlayerPedId()
	Base.Player.setEntityVisible(true)
    SetEntityInvincible(ped, false)
    SetEntityCollision(ped, true, false)
    FreezeEntityPosition(ped, false)
    SetEntityCoords(ped, savedCoords.x, savedCoords.y, savedCoords.z - 0.95, false, false, false, false)
    SetEntityHeading(ped, savedHeading)
    SendNUIMessage({type = 'stopspectating'})
    TriggerServerEvent("core_admin:stopSpectating")
    TriggerEvent("admin:overhead:macro", false)
end

RegisterNetEvent("fdg_admin:spectate")
AddEventHandler("fdg_admin:spectate", function(tgt)
    if (not isPlayerSpectatingTarget(tgt)) then
        startSpectatingPlayer(tgt)
    else
        stopSpectating()
    end
end)

RegisterNetEvent("fdg_admin:setInSpec")
AddEventHandler("fdg_admin:setInSpec", function(src, toggle)
    if (toggle) then
        _staff_spectating[tostring(src)] = src
    else
        _staff_spectating[tostring(src)] = nil
        local player = GetPlayerFromServerId(src)
        if (NetworkIsPlayerActive(player)) then
            NetworkConcealPlayer(player, false, 0)
        end
    end
end)

RegisterNUICallback('stopspectating', function(data, cb)
	stopSpectating()
end)

onBaseReady(function()
    while true do Wait(0)
        if (group == "user") then
            for _, src in pairs(_staff_spectating) do
                local player = GetPlayerFromServerId(src)
                if (NetworkIsPlayerActive(player) and player ~= PlayerId()) then
                    NetworkConcealPlayer(player, true, 0)
                end
            end
        end
        if (isSpectating) then
			if IsControlJustPressed(0, 108) then
				setSpectateTarget(true)
			end
			if IsControlJustPressed(0, 109) then
				setSpectateTarget(false)
			end
			if IsControlJustPressed(0, 110) then
				stopSpectating()
			end
			if IsControlJustPressed(0, 61) then
				TriggerServerEvent('fdg_admin:singleaction', player_being_spectated.source, "specinfo")
			end
        end
    end
end)

onBaseReady(function()
    while true do
        local sleep = 500
        if (isSpectating) then
            if (player_being_spectated ~= -1) then
                local player = GetPlayerFromServerId(player_being_spectated.source)
                if (NetworkIsPlayerActive(player)) then
                    local targetPed = GetPlayerPed(player)
                    if (DoesEntityExist(targetPed) and ( not NetworkIsInSpectatorMode() or changedSpectateTarget )) then
                        SetFocusEntity(targetPed)
                        if (changedSpectateTarget) then
                            NetworkSetInSpectatorMode(false)
                        end
                        NetworkSetInSpectatorMode(true, targetPed)
                        changedSpectateTarget = false
                        Base.Camera.destroy("spec_cam")
                    end
                    sleep = 0
                else
                    Base.Camera.create("spec_cam", 1, vector3(player_being_spectated.coords[1], player_being_spectated.coords[2], player_being_spectated.coords[3] + 1))
                    Base.Camera.render("spec_cam", true)
                    TriggerServerEvent("instance:match", player_being_spectated.source)
                end
            end

            for i = 30, 35, 1 do
                DisableControlAction(0, i, true)
            end
        end
        Wait(sleep)
    end
end)
