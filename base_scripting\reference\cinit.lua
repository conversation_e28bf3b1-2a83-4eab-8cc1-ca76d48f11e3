-- Base Framework Init
Base = {}
PlayerData = {}

baseLoaded = false
exportsLoaded = false
isDead = false
isCuffed = false
isInTrunk = false
isBlackout = false
subduty = true
duty = true
aop = 'map'

local callbacks = {}

function onBaseReady(cb)
    if baseLoaded then Citizen.CreateThread(cb) end
    table.insert(callbacks, cb)
end

local function loadBaseObj(obj)
    baseLoaded = false
    Wait(100)

    -- Load
    Base = obj
    PlayerData = Base.GetPlayerData()

    -- Trigger onBaseReady callbacks
    baseLoaded = true
    for i, cb in ipairs(callbacks) do Citizen.CreateThread(cb) end
end

Citizen.CreateThread(function()
    Wait(100)
    -- AddEventHandler("base:reset", loadBaseObj)
    TriggerEvent('base:init', loadBaseObj)
end)

-- Verbose Logging
local verboseLoggingEnabled = GetConvar("debug", "false") == "true"
function vlog(...)
    if verboseLoggingEnabled then
        print(...)
    end
end

AddEventHandler("verboseLogging", function(resourcename)
    if resourcename == GetCurrentResourceName() then
        verboseLoggingEnabled = not verboseLoggingEnabled
    end
end)

-- Synchronised States
RegisterNetEvent('updateaop')
AddEventHandler('updateaop', function(newaop) aop = newaop end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(xPlayer)
    PlayerData = xPlayer
    isDead = xPlayer.isDead or false
end)

RegisterNetEvent('xPlayer:updateGroups')
AddEventHandler('xPlayer:updateGroups', function(groups, permissions)
    PlayerData.groups = groups
    PlayerData.permissions = permissions
end)

RegisterNetEvent("main_dispatch:SyncCallsign")
AddEventHandler("main_dispatch:SyncCallsign", function(callsign) PlayerData.callsign = callsign end)

-- blackout
RegisterNetEvent("base_scripting:blackout", function(state) isBlackout = state end)

-- trunk

AddEventHandler("playerTrunk", function(state) isInTrunk = state end)

-- Death Sync
AddEventHandler("playerWounded", function() isDead = true end)
AddEventHandler("playerRevived", function() isDead = false end)

-- Cuff Sync
AddEventHandler("playerCuffed", function(cuffed) isCuffed = cuffed end)

-- Export Shortcuts
fdgMenus   = exports["core_menu"]
fdgJobs    = exports["core_jobs"]
fdgVehicle = exports["core_vehicles"]
fdgCitizen = exports["core_citizen"]

-- Developer Functions
debugmode  = false
function dbgprint(msg)
    if debugmode then print("[" .. GetCurrentResourceName() .. "] " .. msg) end
end

locale = {}
function _U(str, ...)
    if locale[str] then
        return string.format(locale[str], ...):gsub("^%l", string.upper)
    else
        return "Language option [" .. str .. "] not found"
    end
end

function table.clone(orig, copies)
    copies = copies or {}
    local orig_type = type(orig)
    local copy
    if orig_type == 'table' then
        if copies[orig] then
            copy = copies[orig]
        else
            copy = {}
            copies[orig] = copy
            for orig_key, orig_value in next, orig, nil do
                copy[table.clone(orig_key, copies)] = table.clone(orig_value, copies)
            end
            setmetatable(copy, table.clone(getmetatable(orig), copies))
        end
    else -- number, string, boolean, etc
        copy = orig
    end
    return copy
end
