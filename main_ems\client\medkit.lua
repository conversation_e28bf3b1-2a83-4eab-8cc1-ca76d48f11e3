local MEDKIT_ITEMS = {
    bandage = { label = "Bandage", emoji = "🩹" },
    morphinesyringe = { label = "Syringe", emoji = "💉" }
}

local function bagMoveAnim()
    RequestAnimDict("pickup_object")
    while not HasAnimDictLoaded("pickup_object") do Wait(0) end
    TaskPlayAnim(PlayerPedId(), "pickup_object", "pickup_low", 8.0, 8.0, -1, 0, 0, false, false, false)
end

CreateThread(function()
    while true do
        Wait(0)

        if HasPedGotWeapon(PlayerPedId(), GetHashKey("WEAPON_MEDPACK"), false) then
            SetTextComponentFormat("STRING")
            AddTextComponentString("Press ~INPUT_DETONATE~ to place down the medic bag")
            DisplayHelpTextFromStringLabel(0, 0, 1, -1)

            -- If G is pressed
            if IsControlJustReleased(0, 47) then
                bagMoveAnim()
                Wait(800)
                local coords = GetEntityCoords(PlayerPedId())
                local forward = GetEntityForwardVector(PlayerPedId())
                coords = coords + forward * 0.7
                RequestModel("xm_prop_x17_bag_med_01a")
                while not HasModelLoaded("xm_prop_x17_bag_med_01a") do Wait(0) end
                local obj = CreateObject("xm_prop_x17_bag_med_01a", coords.x, coords.y, coords.z, true, true, false)
                SetEntityHeading(obj, GetEntityHeading(PlayerPedId()))
                PlaceObjectOnGroundProperly(obj)
                SetEntityAsMissionEntity(obj, true, true)
                RemoveWeaponFromPed(PlayerPedId(), GetHashKey("WEAPON_MEDPACK"))
                TriggerServerEvent("medkit:place", NetworkGetNetworkIdFromEntity(obj), coords)
            end
        else
            Wait(1000)
        end
    end
end)

local function handleBagPickup(bagObj)
    if not DoesPlayerHaveOrgPermission("medkit") then
        return Base.Notification("You are not a medic")
    end

    NetworkRequestControlOfEntity(bagObj)
    while DoesEntityExist(bagObj) and not NetworkHasControlOfEntity(bagObj) do
        Wait(0)
    end

    if not DoesEntityExist(bagObj) then return end
    bagMoveAnim()
    Wait(800)
    SetEntityAsMissionEntity(bagObj, false, false)
    DeleteEntity(bagObj)
    Wait(200)
    StopAnimTask(PlayerPedId(), "pickup_object", "pickup_low", 1.0)
    GiveWeaponToPed(PlayerPedId(), GetHashKey("WEAPON_MEDPACK"), 1, false, true)
end

local function retriveMedkit(veh)
    if not DoesPlayerHaveOrgPermission("medkit") then
        return Base.Notification("You are not a medic")
    end

    local correctVeh = Entity(veh).state.job == "lses" or (Entity(veh).state.job == "afp" or Entity(veh).state.job == "police")
    local hasWeapon = HasPedGotWeapon(PlayerPedId(), GetHashKey("WEAPON_MEDPACK"), false)
    if not correctVeh then return end
    if hasWeapon then return end

    GiveWeaponToPed(PlayerPedId(), GetHashKey("WEAPON_MEDPACK"), 1, false, true)
    Base.Notification("You have retrieved a medpack from the vehicle")
end

local menuItems = {
    pickup = {
        displayData = {
            label = "Pickup Medkit",
            emoji = "⚕️",
            order = 1,
        },
        onInteract = handleBagPickup
    }
}

-- Generate menu items
for item, data in pairs(MEDKIT_ITEMS) do
    menuItems[item] = {
        displayData = { 
            label = data.label,
            emoji = data.emoji
        },
        onInteract = function()
            bagMoveAnim()
            Wait(1000)
            TriggerServerEvent("getMedicalItem", item)
        end
    }
end

RegisterInteraction("object", "medicbag", menuItems, function(entity)
    exports.base_interaction:OpenInteractMenu(menuItems, entity)
end, function(object)
    return GetEntityModel(object) == GetHashKey("xm_prop_x17_bag_med_01a")
end)

RegisterInteraction("vehicle", "medicbag.retrieve", { label = "Retrieve Medkit", emoji = "⚕️", order = 7 }, function(veh)
    retriveMedkit(veh)
    Entity(veh).state:set("interactionTime", GetNetworkTime(), true)
end, function(veh)
    local correctVeh = Entity(veh).state.job == "lses" or (Entity(veh).state.job == "afp" or Entity(veh).state.job == "police")
    local hasWeapon = HasPedGotWeapon(PlayerPedId(), GetHashKey("WEAPON_MEDPACK"), false)
    return correctVeh and not hasWeapon and DoesPlayerHaveOrgPermission("medkit")
end)

RegisterInteraction("vehicle", "medicbag.store", { label = "Store Medkit", emoji = "⚕️", order = 7 }, function(veh)
    local correctVeh = Entity(veh).state.job == "lses" or (Entity(veh).state.job == "afp" or Entity(veh).state.job == "police")
    local hasWeapon = HasPedGotWeapon(PlayerPedId(), GetHashKey("WEAPON_MEDPACK"), false)
    if not correctVeh then return end
    if not hasWeapon then return end

    RemoveWeaponFromPed(PlayerPedId(), GetHashKey("WEAPON_MEDPACK"))
    Base.Notification("You have stored a medpack in the vehicle")
    Entity(veh).state:set("interactionTime", GetNetworkTime(), true)
end, function(veh)
    local correctVeh = Entity(veh).state.job == "lses" or (Entity(veh).state.job == "afp" or Entity(veh).state.job == "police")
    local hasWeapon = HasPedGotWeapon(PlayerPedId(), GetHashKey("WEAPON_MEDPACK"), false)

    return correctVeh and hasWeapon
end)

RegisterNetEvent('medkit:printToConsole', function(message)
    print(message)
end)
