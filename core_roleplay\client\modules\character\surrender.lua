local surrendered = false

toggleSurrender = function()
    local ped = PlayerPedId()
    surrendered = false
    if isDead or isCuffed or isZiptied then return end
	if ( DoesEntityExist(ped) and not IsEntityDead(ped)) then
        if (surrendered) then
            stopSurrender()
        else
            startSurrender()
        end
    end
end

startSurrender = function()
    local ped = PlayerPedId()
    surrendered = true
    TriggerEvent("playerSurrendered", surrendered)

    -- Surrender entry animations
    Base.Animation.Start(ped, "random@arrests", "idle_2_hands_up", false, false, false, -1.0)
    Wait(4000)
    if not surrendered then return end
    Base.Animation.Start(ped, "random@arrests", "kneeling_arrest_idle", false, false, false, -1.0)
    Wait(500)
    if not surrendered then return end
    Base.Animation.Start(ped, "random@arrests@busted", "enter", false, false, false, -1.0)
    Wait(1000) -- custom flags

    if not surrendered then return end
    TaskPlayAnim(ped, "random@arrests@busted", "idle_a", 8.0, 1.0, -1, 9, 0, 0, 0, 0)

    while surrendered do
        Wait(1)

        if (isZiptied and not isDead) then

            Base.Animation.Start(ped, "random@arrests@busted", "enter", false, false, false, -1.0)
            Base.Player.blockActions(false)

            if not IsEntityPlayingAnim(PlayerPedId(), "random@arrests@busted", "idle_a", 3) then
                surrendered = false
            end
        end
    end

    stopSurrender()
end

stopSurrender = function()
    local ped = PlayerPedId()
    surrendered = false
    TriggerEvent("playerSurrendered", surrendered)

    Base.Animation.StartForcedAnim(ped, "random@arrests", "kneeling_arrest_get_up", false, false, false, false)
end

-- Exports / Toggles
exports("surrender", toggleSurrender)
RegisterCommand("k", toggleSurrender)
QUI.registerMenuAction("surrender", toggleSurrender)
AddEventHandler("handsup_stop", function() surrendered = false end)