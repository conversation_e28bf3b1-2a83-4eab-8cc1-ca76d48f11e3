<!DOCTYPE html>
<html>
<head>
	<script src="nui://game/ui/jquery.js" type="text/javascript"></script>
	<link href="https://fonts.googleapis.com/css?family=Baloo+Bhai&display=swap" rel="stylesheet">
	<style type="text/css">
		#mina {
			display: none;
			overflow: hidden;
		}
		#nosbar {
			display: none;
			overflow: hidden;
		}
	</style>
</head>
<body>

	<script>
		$(function(){
			var cancel = false;
			let cancelNos = false;
			window.onload = (e) => { 
				window.addEventListener('message', (event) => {	            
					var item = event.data;
					if (item !== undefined && item.type === "chalui") {		                
						if (item.display === true) {
		                    $("#mina").show();
							var start = new Date();
							var maxTime = item.time;
							var text = item.text;
                            var objective = item.box
							cancel = false;
							animateUpdate();
                            createObjective();
							updateProgress(0);

							$('#pbar_innertext').text(text);

							function updateProgress(percentage) {
							    $('#pbar_innerdiv').css("width", percentage + "%");
							}

							function animateUpdate() {
								var now = new Date();
								var timeDiff = now.getTime() - start.getTime();
								var perc = (timeDiff/maxTime)*100;
								if (perc <= 100 && !cancel) {
									updateProgress(perc);
									setTimeout(animateUpdate, 1);
								} else {
									$("#mina").hide();
								}
							}

                            function createObjective() {
                                $('#pbar_objective').css("margin-left", objective + "%");
                            }

						} else {
							$("#mina").hide();
							cancel = true;
		                }

                        if (item.click == true) {
                            var box = document.getElementById('pbar_innerdiv');
                            var width = box.style.width;
						

                            $.post("http://core_vehicles/clicked-chalbar", JSON.stringify({
                                option: "click_event",
                                value: width,
								clicked: true,
                            }));
                        }
					}

					if (item !== undefined && item.type === "nosbar") {       
						if (item.update === true) {
							if (cancelNos) { return }
		                    $("#nosbar").show();
							
							var max = 4000
							var currentValue = 0
							var currentValue = item.currentValue
							var perc = 0
							if (item.run === true) {
								animateUpdate();
							}

							$('#nosbar_innertext').text(text);

							function updateProgress(percentage) {
							    $('#nosbar_innerdiv').css("width", percentage + "%");
							}

							function animateUpdate() {
								perc = (currentValue/max)*100;
								if (perc < 100) {
									updateProgress(perc);
								} else if (perc >= 100) {
									cancelNos = true
									currentValue = 0
									$.post("http://core_vehicles/maxnos");
									$("#nosbar").hide();
								}
							}
						}
						if (item.reset == true) {
							cancelNos = false
							$("#nosbar").hide();
						}
						if (item.display == false) {
							$("#nosbar").hide();
						}
					}
				});
			};
		});
	</script>

<div id="mina" style="width: 100%;text-align: center;">
	<div id="pbar_outerdiv" style="display: inline-block; margin-top: 85vh; background-color: rgba(0,0,0,0.5); width: 35vh; height: 3.5vh; z-index: 1; position: relative; border: 1px solid white; border-radius: 2px;">
		<div id="pbar_innertext" style="color: white; z-index: 3; position: absolute; top: 0; left: 0; width: 100%; height: 100%; text-align: center; font-family: Arial, Helvetica, sans-serif; font-weight: bold; margin-top: 0.9vh; font-size: 1.6vh;">NOS Usage</div>
        <div id="pbar_innerdiv" style="background-color: rgba(0, 82, 177, 1); z-index: 2; height: 100%; width: 0%"></div>
		
        
	</div>
</div>

<div id="nosbar" style="width: 100%;text-align: center;">
	<div id="nosbar_outerdiv" style="display: inline-block; margin-top: 85vh; background-color: rgba(0,0,0,0.5); width: 35vh; height: 3.5vh; z-index: 1; position: relative; border: 1px solid white; border-radius: 2px;">
		<div id="nosbar_innertext" style="color: white; z-index: 3; position: absolute; top: 0; left: 0; width: 100%; height: 100%; text-align: center; font-family: Arial, Helvetica, sans-serif; font-weight: bold; margin-top: 0.9vh; font-size: 1.6vh;">NOS Usage</div>
        <div id="nosbar_innerdiv" style="background-color: rgba(0, 82, 177, 1); z-index: 2; height: 100%; width: 0%"></div>
		
        
	</div>
</div>

</body>
</html>