local res = GetActiveScreenResolution()
local HUDPlane = {

	PlaneSpeed = true,

	Panel      = true,

}

-- Move the entire UI
local UI = {

	x = 0.000, -- Base Screen Coords 	+ 	 x
	y = -0.001, -- Base Screen Coords 	+ 	-y

}

function toggleAirUI()
	if HUDPlane.Panel == true then
		HUDPlane.Panel = false
	elseif HUDPlane.Panel ~= true then
		HUDPlane.Panel = true
	end
end

Citizen.CreateThread(function()
	if res ~= 1920 then
		UI.x = UI.x + 0.130
	end

	while true do
		Citizen.Wait(1)
		local MyPed = GetPlayerPed(-1)
		local PedHeli = IsPedInAnyHeli(MyPed)
		local PedPlane = IsPedInAnyPlane(MyPed)

		if not PedHeli and not PedPlane then
			Wait(1000)
		else
			local MyPedVeh = GetVehiclePedIsIn(GetPlayerPed(-1), false)
			--  #### 		   EDITED IN			  ####  --
			local Height = GetEntityHeightAboveGround(MyPedVeh)
			local Roll = GetEntityRoll(MyPedVeh)
			local Pitch = GetEntityPitch(MyPedVeh)
			local MainRotor = GetHeliMainRotorHealth(MyPedVeh)
			local TailRotor = GetHeliTailRotorHealth(MyPedVeh)
			local EngineRunning = Citizen.InvokeNative(0xAE31E7DF9B5B132E, MyPedVeh)
			local LandingGear0 = Citizen.InvokeNative(0x9B0F3DCA3DB0F4CD, MyPedVeh, 0)
			--  ####         	 PLANE HUD	  	  	  ####  --
			if PedPlane then
				if HUDPlane.Panel and DoesPlayerHaveOrg("police") or DoesPlayerHaveOrg("lses") or DoesPlayerHaveOrg("ses") then
					drawTxt(UI.x + 0.514, UI.y + 1.174, 1.0, 1.0, 0.44, "Roll: " .. math.ceil(Roll), 255, 255, 255, 200)

					drawTxt(UI.x + 0.560, UI.y + 1.174, 1.0, 1.0, 0.44, "Altitude: " .. math.floor((math.ceil(Height) * 3.281) + 0.5), 255, 255, 255, 200)

					drawTxt(UI.x + 0.620, UI.y + 1.174, 1.0, 1.0, 0.44, "Pitch: " .. math.ceil(Pitch), 255, 255, 255, 200)


					if LandingGear0 then
						drawTxt(UI.x + 0.514, UI.y + 1.150, 1.0, 1.0, 0.45, "Landing Gear", 255, 0, 0, 200) -- Red
					else
						drawTxt(UI.x + 0.514, UI.y + 1.150, 1.0, 1.0, 0.45, "Landing Gear", 0, 255, 0, 200) -- Green
					end
					if EngineRunning then
						drawTxt(UI.x + 0.620, UI.y + 1.150, 1.0, 1.0, 0.45, "ENG", 0, 255, 0, 200) -- Green
					else
						drawTxt(UI.x + 0.620, UI.y + 1.150, 1.0, 1.0, 0.45, "ENG", 255, 0, 0, 200) -- Red
					end
				else
					if HUDPlane.Panel then
						drawTxt(UI.x + 0.514, UI.y + 1.264, 1.0, 1.0, 0.44, "Roll: " .. math.ceil(Roll), 255, 255, 255, 200)

						drawTxt(UI.x + 0.553, UI.y + 1.264, 1.0, 1.0, 0.44, "Altitude: " .. math.floor((math.ceil(Height) * 3.281) + 0.5), 255, 255, 255, 200)

						drawTxt(UI.x + 0.620, UI.y + 1.264, 1.0, 1.0, 0.44, "Pitch: " .. math.ceil(Pitch), 255, 255, 255, 200)

						if LandingGear0 then
							drawTxt(UI.x + 0.514, UI.y + 1.226, 1.0, 1.0, 0.45, "Landing Gear", 255, 0, 0, 200) -- Red
						else
							drawTxt(UI.x + 0.514, UI.y + 1.226, 1.0, 1.0, 0.45, "Landing Gear", 0, 255, 0, 200) -- Green
						end
						if EngineRunning then
							drawTxt(UI.x + 0.620, UI.y + 1.226, 1.0, 1.0, 0.45, "ENG", 0, 255, 0, 200) -- Green
						else
							drawTxt(UI.x + 0.620, UI.y + 1.226, 1.0, 1.0, 0.45, "ENG", 255, 0, 0, 200) -- Red
						end
					end
				end
			end

			if PedHeli then
				if HUDPlane.Panel and DoesPlayerHaveOrg("police") or DoesPlayerHaveOrg("lses") or DoesPlayerHaveOrg("ses") then
					drawTxt(UI.x + 0.514, UI.y + 1.174, 1.0, 1.0, 0.44, "Roll: " .. math.ceil(Roll), 255, 255, 255, 200)

					drawTxt(UI.x + 0.560, UI.y + 1.174, 1.0, 1.0, 0.44, "Altitude: " .. math.floor((math.ceil(Height) * 3.281) + 0.5), 255, 255, 255, 200)

					drawTxt(UI.x + 0.620, UI.y + 1.174, 1.0, 1.0, 0.44, "Pitch: " .. math.ceil(Pitch), 255, 255, 255, 200)

					if (MainRotor < 350) and (MainRotor > 151) then
						drawTxt(UI.x + 0.514, UI.y + 1.150, 1.0, 1.0, 0.45, "Main Rotor", 255, 255, 0, 200)
					elseif (MainRotor > 1) and (MainRotor < 150) then
						drawTxt(UI.x + 0.514, UI.y + 1.150, 1.0, 1.0, 0.45, "Main Rotor", 255, 0, 0, 200)
					elseif (MainRotor == 0) then
						drawTxt(UI.x + 0.514, UI.y + 1.150, 1.0, 1.0, 0.45, "Main Rotor", 255, 0, 0, 200)
					elseif not EngineRunning then
						drawTxt(UI.x + 0.514, UI.y + 1.150, 1.0, 1.0, 0.45, "Main Rotor", 255, 0, 0, 200)
					else
						drawTxt(UI.x + 0.514, UI.y + 1.150, 1.0, 1.0, 0.45, "Main Rotor", 0, 255, 0, 200)
					end

					if (TailRotor < 350) and (TailRotor > 151) then
						drawTxt(UI.x + 0.565, UI.y + 1.150, 1.0, 1.0, 0.45, "Tail Rotor", 255, 255, 0, 200)
					elseif (TailRotor > 1) and (TailRotor < 150) then
						drawTxt(UI.x + 0.565, UI.y + 1.150, 1.0, 1.0, 0.45, "Tail Rotor", 255, 0, 0, 200)
					elseif (TailRotor == 0) then
						drawTxt(UI.x + 0.565, UI.y + 1.150, 1.0, 1.0, 0.45, "Tail Rotor", 255, 0, 0, 200)
					elseif not EngineRunning then
						drawTxt(UI.x + 0.565, UI.y + 1.150, 1.0, 1.0, 0.45, "Tail Rotor", 255, 0, 0, 200)
					else
						drawTxt(UI.x + 0.565, UI.y + 1.150, 1.0, 1.0, 0.45, "Tail Rotor", 0, 255, 0, 200)
					end

					if EngineRunning then
						drawTxt(UI.x + 0.620, UI.y + 1.150, 1.0, 1.0, 0.45, "ENG", 0, 255, 0, 200)
					else
						drawTxt(UI.x + 0.620, UI.y + 1.150, 1.0, 1.0, 0.45, "ENG", 255, 0, 0, 200)
					end
				else
					if HUDPlane.Panel then
						drawTxt(UI.x + 0.514, UI.y + 1.264, 1.0, 1.0, 0.44, "Roll: " .. math.ceil(Roll), 255, 255, 255, 200)

						drawTxt(UI.x + 0.560, UI.y + 1.264, 1.0, 1.0, 0.44, "Altitude: " .. math.floor((math.ceil(Height) * 3.281) + 0.5), 255, 255, 255, 200)

						drawTxt(UI.x + 0.620, UI.y + 1.264, 1.0, 1.0, 0.44, "Pitch: " .. math.ceil(Pitch), 255, 255, 255, 200)

						if (MainRotor < 350) and (MainRotor > 151) then
							drawTxt(UI.x + 0.514, UI.y + 1.240, 1.0, 1.0, 0.45, "Main Rotor", 255, 255, 0, 200)
						elseif (MainRotor > 1) and (MainRotor < 150) then
							drawTxt(UI.x + 0.514, UI.y + 1.240, 1.0, 1.0, 0.45, "Main Rotor", 255, 0, 0, 200)
						elseif (MainRotor == 0) then
							drawTxt(UI.x + 0.514, UI.y + 1.240, 1.0, 1.0, 0.45, "Main Rotor", 255, 0, 0, 200)
						elseif not EngineRunning then
							drawTxt(UI.x + 0.514, UI.y + 1.240, 1.0, 1.0, 0.45, "Main Rotor", 255, 0, 0, 200)
						else
							drawTxt(UI.x + 0.514, UI.y + 1.240, 1.0, 1.0, 0.45, "Main Rotor", 0, 255, 0, 200)
						end

						if (TailRotor < 350) and (TailRotor > 151) then
							drawTxt(UI.x + 0.560, UI.y + 1.240, 1.0, 1.0, 0.45, "Tail Rotor", 255, 255, 0, 200)
						elseif (TailRotor > 1) and (TailRotor < 150) then
							drawTxt(UI.x + 0.560, UI.y + 1.240, 1.0, 1.0, 0.45, "Tail Rotor", 255, 0, 0, 200)
						elseif (TailRotor == 0) then
							drawTxt(UI.x + 0.560, UI.y + 1.240, 1.0, 1.0, 0.45, "Tail Rotor", 255, 0, 0, 200)
						elseif not EngineRunning then
							drawTxt(UI.x + 0.560, UI.y + 1.240, 1.0, 1.0, 0.45, "Tail Rotor", 255, 0, 0, 200)
						else
							drawTxt(UI.x + 0.560, UI.y + 1.240, 1.0, 1.0, 0.45, "Tail Rotor", 0, 255, 0, 200)
						end

						if EngineRunning then
							drawTxt(UI.x + 0.620, UI.y + 1.240, 1.0, 1.0, 0.45, "ENG", 0, 255, 0, 200)
						else
							drawTxt(UI.x + 0.620, UI.y + 1.240, 1.0, 1.0, 0.45, "ENG", 255, 0, 0, 200)
						end
					end
				end
			end
		end
	end
end)

function drawTxt(x, y, width, height, scale, text, r, g, b, a)
	SetTextFont(4)
	SetTextProportional(0)
	SetTextScale(scale, scale)
	SetTextColour(r, g, b, a)
	SetTextDropShadow(0, 0, 0, 0, 255)
	SetTextEdge(2, 0, 0, 0, 255)
	SetTextDropShadow()
	SetTextOutline()
	SetTextEntry("STRING")
	AddTextComponentString(text)
	DrawText(x - width / 2, y - height / 2 + 0.005)
end

function drawRct(x, y, width, height, r, g, b, a)
	DrawRect(x + width / 2, y + height / 2, width, height, r, g, b, a)
end
