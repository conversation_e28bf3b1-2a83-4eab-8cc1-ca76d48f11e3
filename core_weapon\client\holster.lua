GDA = {} -- Animation Task Framework
GDA.animationList = {}
GDA.weaponList = {
    { _name = "p_cs_joint_01", hashkey = 966099553, _switch = false, holsterable = false }
}
GDA.holsterList = {}
GDA.Player = {
    controlsDisabled = false,
    curWeapon = defaultWeapon,
    defaultWeapon = GetHashKey("WEAPON_UNARMED"),
    skin = {},
    holster = nil,
    emptyHolster = true
}

BLOCKED_ANIM_WEPS = {
    [GetHashKey("WEAPON_MEDPACK")] = true
}

function GDA.createAnimation(name, actions, blendInSpeed, blendOutSpeed, duration, flag, playbackRate, lockX, lockY,
                             lockZ)
    local animation = {
        _name = name,
        _actions = actions,
        _blendInSpeed = blendInSpeed,
        _blendOutSpeed = blendOutSpeed,
        _duration =
            duration,
        _flag = flag,
        _playbackRate = playbackRate,
        _lockX = lockX,
        _lockY = lockY,
        lockZ = lockZ
    }
    table.insert(GDA.animationList, animation)
end

function GDA.createWeapon(name, switch, canHolster)
    local weapon = {
        weapon = name,
        hashkey = GetHashKey(name),
        _switch = switch or false,
        holsterable = canHolster or
            false
    }
    table.insert(GDA.weaponList, weapon)
end

function GDA.createHolster(_withWeapon, _withoutWeapon, _clothingType, _sex)
    local holster = { withWeapon = _withWeapon, withoutWeapon = _withoutWeapon, type = _clothingType, plySex = _sex }
    table.insert(GDA.holsterList, holster)
end

-- Animations
GDA.createAnimation("reaction@intimidation@1h", { "intro", "outro" }, 8.0, 8.0, -1, 48, 0, 0, 0, 0)

-- Male Holsters
-- GDA.createHolster(152, 153, "chain", 0)
-- GDA.createHolster(155, 154, "chain", 0)
-- GDA.createHolster(157, 156, "chain", 0)
-- GDA.createHolster(158, 159, "chain", 0)
-- GDA.createHolster(160, 161, "chain", 0)
-- GDA.createHolster(162, 163, "chain", 0)
-- GDA.createHolster(164, 165, "chain", 0)
-- GDA.createHolster(166, 166, "chain", 0)
-- GDA.createHolster(169, 169, "chain", 0)
-- GDA.createHolster(354, 355, "chain", 0)
-- GDA.createHolster(357, 357, "chain", 0)
-- GDA.createHolster(359, 358, "chain", 0)

-- duplicate the above code but increase the first 2 numbers by 15
GDA.createHolster(178, 179, "chain", 0)
GDA.createHolster(181, 180, "chain", 0)
GDA.createHolster(183, 182, "chain", 0)
GDA.createHolster(184, 185, "chain", 0)
GDA.createHolster(186, 187, "chain", 0)
GDA.createHolster(188, 189, "chain", 0)
GDA.createHolster(190, 191, "chain", 0)
GDA.createHolster(192, 192, "chain", 0)
GDA.createHolster(195, 195, "chain", 0)
GDA.createHolster(196, 196, "chain", 0)
GDA.createHolster(211, 211, "chain", 0)
GDA.createHolster(212, 212, "chain", 0)
GDA.createHolster(380, 380, "chain", 0)
GDA.createHolster(383, 383, "chain", 0)
GDA.createHolster(384, 385, "chain", 0)


-- Female Holsters
-- GDA.createHolster(121, 122, "chain", 1)
-- GDA.createHolster(124, 123, "chain", 1)
-- GDA.createHolster(125, 125, "chain", 1)
-- GDA.createHolster(323, 323, "chain", 1)
-- GDA.createHolster(325, 324, "chain", 1)

-- duplicate the above code but increase the first 2 numbers by 15
GDA.createHolster(148, 149, "chain", 1)
GDA.createHolster(151, 150, "chain", 1)
GDA.createHolster(152, 152, "chain", 1)
GDA.createHolster(349, 349, "chain", 1)
GDA.createHolster(350, 350, "chain", 1)
GDA.createHolster(352, 351, "chain", 1)

-- Pistols, Holsterable
GDA.createWeapon("WEAPON_UNARMED", true, true)
GDA.createWeapon("WEAPON_PISTOL", true, true)
GDA.createWeapon("WEAPON_COMBATPISTOL", true, true)
GDA.createWeapon("WEAPON_COMBATPISTOLTEST", true, true)
GDA.createWeapon("WEAPON_REVOLVER", true, true)
GDA.createWeapon("WEAPON_APPISTOL", true, true)
GDA.createWeapon("WEAPON_APPISTOLTEST", true, true)
GDA.createWeapon("WEAPON_IMPACTGLOCK", true, true)
GDA.createWeapon("WEAPON_PISTOL50", true, true)
GDA.createWeapon("WEAPON_SNSPISTOL", true, true)
GDA.createWeapon("WEAPON_HEAVYPISTOL", true, true)
GDA.createWeapon("WEAPON_HEAVYPISTOLTEST", true, true)
GDA.createWeapon("WEAPON_VINTAGEPISTOL", true, true)
GDA.createWeapon("WEAPON_PISTOL_MK2", true, true)
GDA.createWeapon("WEAPON_FLAREGUN", true, true)
GDA.createWeapon("WEAPON_MARKSMANPISTOL", true, true)
GDA.createWeapon("WEAPON_STUNGUN", true, true)
GDA.createWeapon("WEAPON_MACHINEPISTOL", true, true)
GDA.createWeapon("WEAPON_SNSPISTOL_MK2", true, true)
GDA.createWeapon("WEAPON_REVOLVER_MK2", true, true)
GDA.createWeapon("WEAPON_DOUBLEACTION", true, true)
GDA.createWeapon("WEAPON_UMBRELLAPISTOL", true, true)
GDA.createWeapon("WEAPON_GADGETPISTOL", true, true)
GDA.createWeapon("WEAPON_NAVYREVOLVER", true, true)
GDA.createWeapon("WEAPON_CERAMICPISTOL", true, true)
GDA.createWeapon("WEAPON_TECPISTOL", true, true)

-- Other Weapons
GDA.createWeapon("WEAPON_KNIFE", true)
GDA.createWeapon("WEAPON_KNUCKLE", true)
GDA.createWeapon("WEAPON_NIGHTSTICK", true)
GDA.createWeapon("WEAPON_HAMMER", true)
GDA.createWeapon("WEAPON_BAT", true)
GDA.createWeapon("WEAPON_GOLFCLUB", true)
GDA.createWeapon("WEAPON_CROWBAR", true)
GDA.createWeapon("WEAPON_BOTTLE", true)
GDA.createWeapon("WEAPON_DAGGER", true)
GDA.createWeapon("WEAPON_HATCHET", true)
GDA.createWeapon("WEAPON_MACHETE", true)
GDA.createWeapon("WEAPON_FLASHLIGHT", true)
GDA.createWeapon("WEAPON_SWITCHBLADE")
GDA.createWeapon("WEAPON_POOLCUE", true)
GDA.createWeapon("WEAPON_WRENCH", true)
GDA.createWeapon("WEAPON_MICROSMG", true)
GDA.createWeapon("WEAPON_MINISMG", true)
GDA.createWeapon("WEAPON_SMG", true)
GDA.createWeapon("WEAPON_SMGTEST", true)
GDA.createWeapon("WEAPON_SMG_MK2", true)
GDA.createWeapon("WEAPON_ASSAULTSMG", true)
GDA.createWeapon("WEAPON_MG", true)
GDA.createWeapon("WEAPON_COMBATMG", true)
GDA.createWeapon("WEAPON_COMBATMG_MK2", true)
GDA.createWeapon("WEAPON_COMBATPDW", true)
GDA.createWeapon("WEAPON_GUSENBERG", true)
GDA.createWeapon("WEAPON_ASSAULTRIFLE", true)
GDA.createWeapon("WEAPON_ASSAULTRIFLE_MK2", true)
GDA.createWeapon("WEAPON_CARBINERIFLE", true)
GDA.createWeapon("WEAPON_CARBINERIFLETEST", true)
GDA.createWeapon("WEAPON_CARBINERIFLE_MK2", true)
GDA.createWeapon("WEAPON_ADVANCEDRIFLE", true)
GDA.createWeapon("WEAPON_SPECIALCARBINE", true)
GDA.createWeapon("WEAPON_BULLPUPRIFLE", true)
GDA.createWeapon("WEAPON_COMPACTRIFLE", true)
GDA.createWeapon("WEAPON_PUMPSHOTGUN", true)
GDA.createWeapon("WEAPON_SWEEPERSHOTGUN", true)
GDA.createWeapon("WEAPON_SAWNOFFSHOTGUN", true)
GDA.createWeapon("WEAPON_BULLPUPSHOTGUN", true)
GDA.createWeapon("WEAPON_ASSAULTSHOTGUN", true)
GDA.createWeapon("WEAPON_MUSKET", true)
GDA.createWeapon("WEAPON_HEAVYSHOTGUN", true)
GDA.createWeapon("WEAPON_DBSHOTGUN", true)
GDA.createWeapon("WEAPON_SNIPERRIFLE", true)
GDA.createWeapon("WEAPON_HEAVYSNIPER", true)
GDA.createWeapon("WEAPON_HEAVYSNIPER_MK2", true)
GDA.createWeapon("WEAPON_MARKSMANRIFLE", true)
GDA.createWeapon("WEAPON_GRENADELAUNCHER", true)
GDA.createWeapon("WEAPON_GRENADELAUNCHER_SMOKE", true)
GDA.createWeapon("WEAPON_RPG", true)
GDA.createWeapon("WEAPON_MINIGUN", true)
GDA.createWeapon("WEAPON_FIREWORK", true)
GDA.createWeapon("WEAPON_RAILGUN", true)
GDA.createWeapon("WEAPON_HOMINGLAUNCHER", true)
GDA.createWeapon("WEAPON_GRENADE")
GDA.createWeapon("WEAPON_STICKYBOMB")
GDA.createWeapon("WEAPON_COMPACTLAUNCHER", true)
GDA.createWeapon("WEAPON_SPECIALCARBINE_MK2", true)
GDA.createWeapon("WEAPON_SPECIALCARBINETEST_MK2", true)
GDA.createWeapon("WEAPON_BULLPUPRIFLE_MK2", true)
GDA.createWeapon("WEAPON_PUMPSHOTGUN_MK2", true)
GDA.createWeapon("WEAPON_MARKSMANRIFLE_MK2", true)
GDA.createWeapon("WEAPON_PROXMINE")
GDA.createWeapon("WEAPON_BZGAS")
GDA.createWeapon("WEAPON_SMOKEGRENADE")
GDA.createWeapon("WEAPON_MOLOTOV")
GDA.createWeapon("WEAPON_FIREEXTINGUISHER")
GDA.createWeapon("WEAPON_PETROLCAN")
GDA.createWeapon("WEAPON_SNOWBALL")
GDA.createWeapon("WEAPON_FLARE")
GDA.createWeapon("WEAPON_BALL")
GDA.createWeapon("WEAPON_BRIEFCASE", true)
GDA.createWeapon("WEAPON_BRIEFCASE_02", true)
GDA.createWeapon("WEAPON_COMBATSHOTGUN", true)
GDA.createWeapon("WEAPON_MILITARYRIFLE", true)
GDA.createWeapon("WEAPON_TACTICALRIFLE", true)
GDA.createWeapon("WEAPON_DD16", true)
GDA.createWeapon("WEAPON_PRECISIONRIFLE", true)

-- Addon Weapons
GDA.createWeapon("WEAPON_VICAXE", true)
GDA.createWeapon("WEAPON_BATTLEAXE", true)
GDA.createWeapon("WEAPON_BONESAW", true)
GDA.createWeapon("WEAPON_SCREWDRIVER", true)
GDA.createWeapon("WEAPON_BREADSTICK", true)
GDA.createWeapon("WEAPON_SHOVEL", true)
GDA.createWeapon("WEAPON_KATANA", true)
GDA.createWeapon("WEAPON_KATANA_THERMAL", true)
GDA.createWeapon("WEAPON_KATANA_THERMALC", true)
GDA.createWeapon("WEAPON_KATANA_THERMALG", true)
GDA.createWeapon("WEAPON_KATANA_THERMALPI", true)
GDA.createWeapon("WEAPON_KATANA_THERMALPU", true)
GDA.createWeapon("WEAPON_KATANA_THERMALR", true)
GDA.createWeapon("WEAPON_KATANA_THERMALW", true)
GDA.createWeapon("WEAPON_KATANA_THERMALY", true)
GDA.createWeapon("WEAPON_ENERGYSWORD", true)
GDA.createWeapon("WEAPON_SLEDGEHAMMER", true)
GDA.createWeapon("WEAPON_TRIBLADE", true)
GDA.createWeapon("WEAPON_MACHETE2", true)
GDA.createWeapon("WEAPON_CHRISREDFIELD", true)
GDA.createWeapon("WEAPON_FIREAXE", true)
GDA.createWeapon("WEAPON_DILDO", true)
GDA.createWeapon("WEAPON_DEVERDILDO", true)
GDA.createWeapon("WEAPON_BOOK", true)
GDA.createWeapon("WEAPON_PP91", true)
GDA.createWeapon("WEAPON_CROSSBOW", true)
GDA.createWeapon("WEAPON_M4A1", true)
GDA.createWeapon("WEAPON_RUBBERM4A1", true)
GDA.createWeapon("WEAPON_MK47", true)
GDA.createWeapon("WEAPON_CATRIFLE", true)
GDA.createWeapon("WEAPON_FAMAS", true)
GDA.createWeapon("WEAPON_FAMASTEST", true)
GDA.createWeapon("WEAPON_GROZA", true)
GDA.createWeapon("WEAPON_UMP45", true)
GDA.createWeapon("WEAPON_NSR9", true)
GDA.createWeapon("WEAPON_MCXSPEAR", true)
GDA.createWeapon("WEAPON_AK971", true)
GDA.createWeapon("WEAPON_M6IC", true)
GDA.createWeapon("WEAPON_M1", true)
GDA.createWeapon("WEAPON_SCAR", true)
GDA.createWeapon("WEAPON_BBSHOTGUN", true)
GDA.createWeapon("WEAPON_DP12", true)
GDA.createWeapon("WEAPON_MOSSBERG", true)
GDA.createWeapon("WEAPON_PAINTBALLGUN", true)
GDA.createWeapon("WEAPON_PAINTBALLGUN2", true)
GDA.createWeapon("WEAPON_PAINTBALLGUN3", true)
GDA.createWeapon("WEAPON_PAINTBALLGUN4", true)
GDA.createWeapon("WEAPON_PAINTBALLGUN5", true, true)
GDA.createWeapon("WEAPON_PDTRAINING1", true)
GDA.createWeapon("WEAPON_PDTRAINING2", true)
GDA.createWeapon("WEAPON_PDTRAINING3", true, true)
GDA.createWeapon("WEAPON_HUNTINGRIFLE", true)
GDA.createWeapon("WEAPON_AR15", true)
GDA.createWeapon("WEAPON_L85A2", true)
GDA.createWeapon("WEAPON_HEAVYRIFLE", true)
GDA.createWeapon("WEAPON_HK416", true)
GDA.createWeapon("WEAPON_P90", true)
GDA.createWeapon("WEAPON_P90TEST", true)
GDA.createWeapon("WEAPON_VECTOR", true)
GDA.createWeapon("WEAPON_SIG516", true)
GDA.createWeapon("weapon_mp7", true)
GDA.createWeapon("weapon_mp5", true)
GDA.createWeapon("weapon_mpx", true)
GDA.createWeapon("WEAPON_KNUCKLES2", true)
GDA.createWeapon("WEAPON_CAPSHIELD", true)
GDA.createWeapon("WEAPON_CHAIR", true)
GDA.createWeapon("WEAPON_GLOCK", true, true)
GDA.createWeapon("WEAPON_RAPTORGLOCK", true, true)
GDA.createWeapon("WEAPON_JERICHO", true, true)
GDA.createWeapon("WEAPON_PISTOL50_UWU", true, true)
GDA.createWeapon("WEAPON_AKURAAPPISTOL", true, true)
GDA.createWeapon("WEAPON_CELESTEAPPISTOL", true, true)
GDA.createWeapon("WEAPON_DRAGONSCALEAPPISTOL", true, true)
GDA.createWeapon("WEAPON_PADDLEPOPAPPISTOL", true, true)
GDA.createWeapon("WEAPON_REAPERAPPISTOL", true, true)
GDA.createWeapon("WEAPON_ROYALTYAPPISTOL", true, true)
GDA.createWeapon("weapon_asiimovpistol50", true, true)
GDA.createWeapon("weapon_bangpistol50", true, true)
GDA.createWeapon("weapon_blackanimepistol50", true, true)
GDA.createWeapon("weapon_blackicepistol50", true, true)
GDA.createWeapon("weapon_blazeitpistol50", true, true)
GDA.createWeapon("weapon_blazepistol50", true, true)
GDA.createWeapon("weapon_bloodlinepistol50", true, true)
GDA.createWeapon("weapon_dragonpistol50", true, true)
GDA.createWeapon("weapon_fatduckpistol50", true, true)
GDA.createWeapon("weapon_fdgpistol50", true, true)
GDA.createWeapon("weapon_hotcoldpistol50", true, true)
GDA.createWeapon("weapon_orangepistol50", true, true)
GDA.createWeapon("weapon_pixeldpistol50", true, true)
GDA.createWeapon("weapon_printstreampistol50", true, true)
GDA.createWeapon("weapon_reddragonpistol50", true, true)
GDA.createWeapon("weapon_riptidepistol50", true, true)
GDA.createWeapon("weapon_shenlongpistol50", true, true)
GDA.createWeapon("weapon_specialforcespistol50", true, true)
GDA.createWeapon("weapon_sultanpistol50", true, true)
GDA.createWeapon("weapon_waifupistol50", true, true)
GDA.createWeapon("WEAPON_SNUB", true, true)
GDA.createWeapon("WEAPON_M9A3", true, true)
GDA.createWeapon("WEAPON_NAILGUN", true)
GDA.createWeapon("WEAPON_MK18B", true)
GDA.createWeapon("WEAPON_MM4", true)
GDA.createWeapon("WEAPON_MP9A", true)
GDA.createWeapon("WEAPON_M14", true)
GDA.createWeapon("WEAPON_KNUCKLES3", true)
GDA.createWeapon("WEAPON_KNUCKLES4", true)
GDA.createWeapon("WEAPON_KNUCKLES5", true)
GDA.createWeapon("WEAPON_KNUCKLES6", true)
GDA.createWeapon("WEAPON_KNUCKLES7", true)
GDA.createWeapon("WEAPON_KNUCKLES8", true)
GDA.createWeapon("WEAPON_BAT2", true)
GDA.createWeapon("WEAPON_GUITAR", true)
GDA.createWeapon("WEAPON_CLAWS", true)
GDA.createWeapon("WEAPON_CLEAVER", true)
GDA.createWeapon("WEAPON_DILDOBAT", true)
GDA.createWeapon("WEAPON_HOTDOG", true)
GDA.createWeapon("WEAPON_KITCHENKNIFE", true)
GDA.createWeapon("WEAPON_NEEDLE", true)
GDA.createWeapon("WEAPON_PICKAXE", true)
GDA.createWeapon("WEAPON_SHOE", true)
GDA.createWeapon("WEAPON_SICKLE", true)
GDA.createWeapon("WEAPON_STOPPOLICE", true)
GDA.createWeapon("WEAPON_TWIG", true)
GDA.createWeapon("WEAPON_LEONKENNEDY", true)
GDA.createWeapon("WEAPON_BAT3", true)
GDA.createWeapon("WEAPON_M19", true)
GDA.createWeapon("WEAPON_M9KNIFE_1", true)
GDA.createWeapon("WEAPON_M9KNIFE_2", true)
GDA.createWeapon("WEAPON_M9KNIFE_3", true)
GDA.createWeapon("WEAPON_M9KNIFE_4", true)
GDA.createWeapon("WEAPON_M9KNIFE_5", true)
GDA.createWeapon("WEAPON_CTKNIFE_01", true)
GDA.createWeapon("WEAPON_CTKNIFE_02", true)
GDA.createWeapon("WEAPON_CTKNIFE_03", true)
GDA.createWeapon("WEAPON_TKNIFE_01", true)
GDA.createWeapon("WEAPON_TKNIFE_02", true)
GDA.createWeapon("WEAPON_TKNIFE_03", true)
GDA.createWeapon("WEAPON_TKNIFE_04", true)
GDA.createWeapon("WEAPON_KKNIFE_01", true)
GDA.createWeapon("WEAPON_KKNIFE_02", true)
GDA.createWeapon("WEAPON_KKNIFE_03", true)
GDA.createWeapon("WEAPON_KKNIFE_04", true)
GDA.createWeapon("WEAPON_KKNIFE_05", true)
GDA.createWeapon("WEAPON_STUNGUN2", true, true)
GDA.createWeapon("WEAPON_PDKNIFE", true)
GDA.createWeapon("WEAPON_FLASHBANG", true)
GDA.createWeapon("WEAPON_PEPPERSPRAY", true)
GDA.createWeapon("WEAPON_ANTIDOTE", true)
GDA.createWeapon("WEAPON_KL", true, true)
GDA.createWeapon("WEAPON_GRENADELAUNCHER_BZ", true)
GDA.createWeapon("WEAPON_POKEBALL", true)
GDA.createWeapon("WEAPON_PKNUCKLES", true)
GDA.createWeapon("WEAPON_SKYRIM", true)
GDA.createWeapon("WEAPON_RE5BLADE", true)
GDA.createWeapon("WEAPON_BREACHSHOTGUN", true)
GDA.createWeapon("WEAPON_DEVERILDO", true)
GDA.createWeapon("WEAPON_PDPISTOL50", true, true)
GDA.createWeapon("WEAPON_XMAS1PISTOL50", true, true)
GDA.createWeapon("WEAPON_XMAS2PISTOL50", true, true)
GDA.createWeapon("WEAPON_BLACKDRAGONAK", true)
GDA.createWeapon("WEAPON_ROYALGOLDAK", true)
GDA.createWeapon("WEAPON_ROYALJADEAK", true)
GDA.createWeapon("WEAPON_ROYALCRIMSONAK", true)


onBaseReady(function()
    while true do
        Citizen.Wait(100)
        IsPlayerSwitchingWeapons(GetPlayerPed(-1))
    end
end)

onBaseReady(function()
    while true do
        Citizen.Wait(0)
        DisableControls(GDA.Player.controlsDisabled)
    end
end)

onBaseReady(function()
    DisablePickups()
end)

function IsPlayerSwitchingWeapons(ped)
    local currentWeapon = GetSelectedPedWeapon(ped)
    local Player = GDA.Player
    local animationNumber = 1
    local delay = 1.5
    local tempWeapon = Player.curWeapon
    if currentWeapon ~= Player.curWeapon and IsWeaponSwitchable(currentWeapon) and not IsPedInAnyVehicle(ped, false) then -- If you switch to a weapon that is valid
        Player.curWeapon = currentWeapon
        if currentWeapon ~= Player.defaultWeapon and IsWeaponSwitchable(tempWeapon) then                                  -- If you switch from one weapon to another
            SwitchWeapon(ped, GDA.animationList[animationNumber], delay, 1, false, currentWeapon, tempWeapon)
        else                                                                                                              -- If you switch from fists to another weapon
            SwitchWeapon(ped, GDA.animationList[animationNumber], delay, 2, true, currentWeapon, tempWeapon)
        end
    elseif currentWeapon ~= Player.curWeapon and not IsPedInAnyVehicle(ped, false) then -- If your current weapon isn't your chosen weapon is not valid
        Player.curWeapon = currentWeapon
        if currentWeapon == Player.defaultWeapon then                                   -- If you switch to fists from another weapon
            SwitchWeapon(ped, GDA.animationList[animationNumber], delay, 2, true, currentWeapon, tempWeapon)
        else
            print("ERROR: Unexpected Handler 1")
        end
    end
end

local currentSwap = 1
function SwitchWeapon(ped, anim, delay, action, endEarly, newWeapon, oldWeapon)
    -- print("--CHANGING--WEAPON--\nChanging weapon from "..tostring(oldWeapon).." --> "..tostring(newWeapon))
    -- print("New weapon holsterable = ", IsWeaponHolsterable(newWeapon), newWeapon)
    -- print("Old weapon holsterable = ", IsWeaponHolsterable(oldWeapon), oldWeapon)
    -- print("Wearing Holster", IsWearingHolster())

    if BLOCKED_ANIM_WEPS[newWeapon] or BLOCKED_ANIM_WEPS[oldWeapon] then
        return
    end

    -- Ensure the player Exists
    if (DoesEntityExist(ped) and not IsEntityDead(ped)) then
        CreateThread(function()
            currentSwap = currentSwap + 1
            local thisSwap = currentSwap

            if ((not IsWearingHolster() or Base.Skin.isItemHidden("chain")) or (not IsWeaponHolsterable(newWeapon) or not IsWeaponHolsterable(oldWeapon)) and DoesWeaponUseAnim(oldWeapon)) then
                -- Hides Weapon
                SetPedCurrentWeaponVisible(ped, 0, 1, 1, 1)

                -- Request the animation dictionary
                RequestAnimDict(anim._name)

                -- Disable the controls
                GDA.Player.controlsDisabled = true

                -- Play the animation
                Base.Animation.StartForcedAnim(ped, anim._name, anim._actions[action], false, false, true, true)

                -- Wait and then set weapon
                Wait(delay * 1300)

                if (currentSwap == thisSwap) then
                    -- Shows Weapon
                    SetPedCurrentWeaponVisible(ped, 1, 1, 1, 1)

                    -- Clear Secondary task
                    ClearPedSecondaryTask(PlayerPedId())
                    GDA.Player.controlsDisabled = false
                end
            end
        end)
    end

    if IsWearingHolster() and not Base.Skin.isItemHidden("chain") then
        if IsWeaponHolsterable(oldWeapon) then
            if (not GDA.Player.emptyHolster and IsWeaponHolsterable(newWeapon)) then
                GDA.Player.controlsDisabled = true

                if (not HasAnimDictLoaded("rcmjosh4") or not HasAnimDictLoaded("reaction@intimidation@cop@unarmed")) then
                    RequestAnimDict("rcmjosh4")
                    RequestAnimDict("reaction@intimidation@cop@unarmed")
                    while not HasAnimDictLoaded("rcmjosh4") do
                        Wait(1)
                    end
                end

                TaskPlayAnim(ped, "rcmjosh4", "josh_leadout_cop2", 5.0, 2.0, -1, 48, 10, 0, 0, 0)
                Wait(500)
                TaskPlayAnim(ped, "reaction@intimidation@cop@unarmed", "outro", 8.0, 2.0, -1, 50, 2.0, 0, 0, 0)

                ClearPedSecondaryTask(PlayerPedId())
                GDA.Player.controlsDisabled = false
            end

            GDA.Player.emptyHolster = false
            ChangeHolster(GDA.Player.emptyHolster)
        end

        if IsWeaponHolsterable(newWeapon) and newWeapon ~= GetHashKey("WEAPON_UNARMED") then
            GDA.Player.emptyHolster = true
            ChangeHolster(GDA.Player.emptyHolster)
        end
    end
end

function ChangeHolster(emptyHolster)
    if IsWearingHolster() then
        local holsterItem = nil
        for k, v in pairs(GDA.holsterList) do
            if v.withWeapon == GDA.Player.holster or v.withoutWeapon == GDA.Player.holster then
                if emptyHolster then
                    holsterItem = v.withoutWeapon
                elseif not emptyHolster then
                    holsterItem = v.withWeapon
                end
                GDA.Player.holster = holsterItem
                --print("Changed type "..tostring(v.type).." component to "..tostring(v.withWeapon).." and emptyHolster = "..tostring(emptyHolster))
                SetClothing(holsterItem, v.type)
            end
        end
    end
end

function SetClothing(model, feature)
    Base.Skin.setClothingItem(feature, "model", model, false)
end

function IsWeaponSwitchable(weapon)
    for i = 1, #GDA.weaponList, 1 do
        if weapon == GDA.weaponList[i].hashkey and GDA.weaponList[i]._switch then return true end
    end
    return false
end

function IsWeaponHolsterable(weapon)
    for k, v in pairs(GDA.weaponList) do
        if weapon == v.hashkey then
            if v.holsterable then return true end
        end
    end
    if LocalPlayer.state.inArena then return true end
    if LocalPlayer.state.inZancEvent then return true end
    return false
end

local HOLSTER_MODEL_WHITELIST = {
    [`devfranklin`] = true
}

function IsWearingHolster()
    UpdateSkin()
    if GDA.Player.skin and GDA.Player.skin.chain then
        if not GDA.Player.holster or GDA.Player.holster ~= GDA.Player.skin.chain.model then
            GDA.Player.holster = GDA.Player.skin.chain.model
        end
        for k, v in pairs(GDA.holsterList) do
            if v.plySex == GDA.Player.skin["sex"] then
                if v.withWeapon == GDA.Player.holster or v.withoutWeapon == GDA.Player.holster then
                    if GDA.Player.skin.chain.texture > -1 and GDA.Player.skin.chain.texture <= GetNumberOfPedTextureVariations(GetPlayerPed(-1), 7, GDA.Player.holster) - 1 then
                        --print("Is Wearing Holster and sex is "..GDA.Player.skin["sex"])
                        return true
                    end
                end
            end
        end
    end

    -- Custom Models
    if HOLSTER_MODEL_WHITELIST[GetEntityModel(PlayerPedId())] then
        return true
    end

    if LocalPlayer.state.inArena then return true end
    if LocalPlayer.state.inZancEvent then return true end
    return false
end

function DoesWeaponUseAnim(weapon)
    for k, v in pairs(GDA.weaponList) do
        if (v.hashkey == weapon) then
            return true
        end
    end
    return false
end

function UpdateSkin()
    local currSkin = Base.Skin.getCurrentSkin()
    skin = currSkin.clothing
    if skin ~= nil then
        if skin ~= GDA.Player.skin then
            for k, v in pairs(skin) do
                GDA.Player.skin[k] = v
            end
        end


        GDA.Player.skin.sex = currSkin.sex
    end
end

function DisableControls(condition)
    if condition then
        DisableControlAction(1, 24, condition)           -- Disable Attack1
        DisableControlAction(1, 25, condition)           -- Disable Aim
        DisableControlAction(1, 37, condition)           -- Disable Weapon Wheel
        DisableControlAction(1, 45, condition)           -- Disable Disables Reload / Attack2
        DisablePlayerFiring(GetPlayerPed(-1), condition) -- Disables Player Firing
    end
end

local pickupList = { `PICKUP_AMMO_BULLET_MP`, `PICKUP_AMMO_FIREWORK`, `PICKUP_AMMO_FLAREGUN`,
    `PICKUP_AMMO_GRENADELAUNCHER`, `PICKUP_AMMO_GRENADELAUNCHER_MP`, `PICKUP_AMMO_HOMINGLAUNCHER`, `PICKUP_AMMO_MG`,
    `PICKUP_AMMO_MINIGUN`, `PICKUP_AMMO_MISSILE_MP`, `PICKUP_AMMO_PISTOL`, `PICKUP_AMMO_RIFLE`,
    `PICKUP_AMMO_RPG`, `PICKUP_AMMO_SHOTGUN`, `PICKUP_AMMO_SMG`, `PICKUP_AMMO_SNIPER`, `PICKUP_ARMOUR_STANDARD`,
    `PICKUP_CAMERA`, `PICKUP_CUSTOM_SCRIPT`, `PICKUP_GANG_ATTACK_MONEY`, `PICKUP_HEALTH_SNACK`, `PICKUP_HEALTH_STANDARD`,
    `PICKUP_MONEY_CASE`, `PICKUP_MONEY_DEP_BAG`, `PICKUP_MONEY_MED_BAG`,
    `PICKUP_MONEY_PAPER_BAG`, `PICKUP_MONEY_PURSE`, `PICKUP_MONEY_SECURITY_CASE`, `PICKUP_MONEY_VARIABLE`,
    `PICKUP_MONEY_WALLET`, `PICKUP_PARACHUTE`, `PICKUP_PORTABLE_CRATE_FIXED_INCAR`, `PICKUP_PORTABLE_CRATE_UNFIXED`,
    `PICKUP_PORTABLE_CRATE_UNFIXED_INCAR`,
    `PICKUP_PORTABLE_CRATE_UNFIXED_INCAR_SMALL`, `PICKUP_PORTABLE_CRATE_UNFIXED_LOW_GLOW`,
    `PICKUP_PORTABLE_DLC_VEHICLE_PACKAGE`, `PICKUP_PORTABLE_PACKAGE`, `PICKUP_SUBMARINE`,
    `PICKUP_VEHICLE_ARMOUR_STANDARD`, `PICKUP_VEHICLE_CUSTOM_SCRIPT`, `PICKUP_VEHICLE_CUSTOM_SCRIPT_LOW_GLOW`,
    `PICKUP_VEHICLE_HEALTH_STANDARD`, `PICKUP_VEHICLE_HEALTH_STANDARD_LOW_GLOW`, `PICKUP_VEHICLE_MONEY_VARIABLE`,
    `PICKUP_VEHICLE_WEAPON_APPISTOL`, `PICKUP_VEHICLE_WEAPON_ASSAULTSMG`, `PICKUP_VEHICLE_WEAPON_COMBATPISTOL`,
    `PICKUP_VEHICLE_WEAPON_GRENADE`, `PICKUP_VEHICLE_WEAPON_MICROSMG`,
    `PICKUP_VEHICLE_WEAPON_MOLOTOV`, `PICKUP_VEHICLE_WEAPON_PISTOL`, `PICKUP_VEHICLE_WEAPON_PISTOL50`,
    `PICKUP_VEHICLE_WEAPON_SAWNOFF`, `PICKUP_VEHICLE_WEAPON_SMG`, `PICKUP_VEHICLE_WEAPON_SMOKEGRENADE`,
    `PICKUP_VEHICLE_WEAPON_STICKYBOMB`, `PICKUP_WEAPON_ADVANCEDRIFLE`, `PICKUP_WEAPON_APPISTOL`,
    `PICKUP_WEAPON_ASSAULTRIFLE`, `PICKUP_WEAPON_ASSAULTSHOTGUN`, `PICKUP_WEAPON_ASSAULTSMG`, `PICKUP_WEAPON_AUTOSHOTGUN`,
    `PICKUP_WEAPON_BAT`, `PICKUP_WEAPON_BATTLEAXE`, `PICKUP_WEAPON_BOTTLE`, `PICKUP_WEAPON_BULLPUPRIFLE`,
    `PICKUP_WEAPON_BULLPUPSHOTGUN`, `PICKUP_WEAPON_CARBINERIFLE`,
    `PICKUP_WEAPON_COMBATMG`, `PICKUP_WEAPON_COMBATPDW`, `PICKUP_WEAPON_COMBATPISTOL`, `PICKUP_WEAPON_COMPACTLAUNCHER`,
    `PICKUP_WEAPON_COMPACTRIFLE`, `PICKUP_WEAPON_CROWBAR`, `PICKUP_WEAPON_DAGGER`, `PICKUP_WEAPON_DBSHOTGUN`,
    `PICKUP_WEAPON_FIREWORK`, `PICKUP_WEAPON_FLAREGUN`,
    `PICKUP_WEAPON_FLASHLIGHT`, `PICKUP_WEAPON_GRENADE`, `PICKUP_WEAPON_GRENADELAUNCHER`, `PICKUP_WEAPON_GUSENBERG`,
    `PICKUP_WEAPON_GOLFCLUB`, `PICKUP_WEAPON_HAMMER`, `PICKUP_WEAPON_HATCHET`, `PICKUP_WEAPON_HEAVYPISTOL`,
    `PICKUP_WEAPON_HEAVYSHOTGUN`, `PICKUP_WEAPON_HEAVYSNIPER`,
    `PICKUP_WEAPON_HOMINGLAUNCHER`, `PICKUP_WEAPON_KNIFE`, `PICKUP_WEAPON_KNUCKLE`, `PICKUP_WEAPON_MACHETE`,
    `PICKUP_WEAPON_MACHINEPISTOL`, `PICKUP_WEAPON_MARKSMANPISTOL`, `PICKUP_WEAPON_MARKSMANRIFLE`, `PICKUP_WEAPON_MG`,
    `PICKUP_WEAPON_MICROSMG`, `PICKUP_WEAPON_MINIGUN`, `PICKUP_WEAPON_MINISMG`,
    `PICKUP_WEAPON_MOLOTOV`, `PICKUP_WEAPON_MUSKET`, `PICKUP_WEAPON_NIGHTSTICK`, `PICKUP_WEAPON_PETROLCAN`,
    `PICKUP_WEAPON_PIPEBOMB`, `PICKUP_WEAPON_PISTOL`, `PICKUP_WEAPON_PISTOL50`, `PICKUP_WEAPON_POOLCUE`,
    `PICKUP_WEAPON_PROXMINE`, `PICKUP_WEAPON_PUMPSHOTGUN`, `PICKUP_WEAPON_RAILGUN`,
    `PICKUP_WEAPON_REVOLVER`, `PICKUP_WEAPON_RPG`, `PICKUP_WEAPON_SAWNOFFSHOTGUN`, `PICKUP_WEAPON_SMG`,
    `PICKUP_WEAPON_SMOKEGRENADE`, `PICKUP_WEAPON_SNIPERRIFLE`, `PICKUP_WEAPON_SNSPISTOL`, `PICKUP_WEAPON_SPECIALCARBINE`,
    `PICKUP_WEAPON_STICKYBOMB`, `PICKUP_WEAPON_STUNGUN`,
    `PICKUP_WEAPON_SWITCHBLADE`, `PICKUP_WEAPON_VINTAGEPISTOL`, `PICKUP_WEAPON_WRENCH`, `PICKUP_WEAPON_RAYCARBINE` }
function DisablePickups()
    for k, v in pairs(pickupList) do
        ToggleUsePickupsForPlayer(PlayerId(), v, false)
    end
end
