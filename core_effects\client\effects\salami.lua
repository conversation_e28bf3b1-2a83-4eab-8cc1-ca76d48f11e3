local salamiEffect = TimedStatusEffect(StatusEffect("salamistrength", "Salami Strength",
    {
        onStart = function(effect)
            SetWeaponDamageModifier("WEAPON_UNARMED", 3.22) -- Damage conversion to shadow daggers
        end,

        onTick = function(effect)
        end,

        onStop = function(effect)
            SetWeaponDamageModifier("WEAPON_UNARMED", 0.35) -- Default damage
            Base.Notification("Salami Strength has expired!")
        end,
    },
    {
        alert = false,
        desc = "You're feeling stronger after eating the salami!",
        subdesc = "Strength increased",
        tickRate = 1000
    }
), { maxTime = 30 * 60 })

RegisterNetEvent("eatSalami", function(itemName)
    ClearPedTasks(PlayerPedId())
    Wait(100)
    exports.main_progressbar:start(5000, "Eating Salami")
    Base.Animation.ActionAnimation("mp_player_inteat@burger", "mp_player_int_eat_burger", true, false, true, 5, function()
        exports.main_progressbar:stop()
        Base.Notification("You ate your salami")
        salamiEffect.addTime(3600) -- 30 minutes
        TriggerServerEvent("effects:consumeSalami", itemName)
    end, function()
        exports.main_progressbar:stop()
    end)
end)
