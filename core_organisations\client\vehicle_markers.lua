local function IsRenderingVehicle(networkID)
    return DoesEntityExist((NetworkDoesEntityExistWithNetworkId(networkID) and NetworkGetEntityFromNetworkId(networkID)))
end

local blips = {}
local function createBlip(data)
    local blip
    local isEntityBlip = false
    if DoesEntityExist(NetworkDoesEntityExistWithNetworkId(data.networkID) and NetworkGetEntityFromNetworkId(data.networkID)) then
        blip = AddBlipForEntity(NetworkDoesEntityExistWithNetworkId(data.networkID) and NetworkGetEntityFromNetworkId(data.networkID))
        ShowHeadingIndicatorOnBlip(blip, true)
        isEntityBlip = true
    else
        blip = AddBlipForCoord(data.coords.x, data.coords.y, data.coords.z)
        ShowHeadingIndicatorOnBlip(blip, false)
    end
    SetBlipScale(blip, 0.85) -- set scale
    SetBlipAsShortRange(blip, true)
    SetBlipColour(blip, data.blipColour)
    SetBlipCategory(blip, 7)

    -- Text Label
    BeginTextCommandSetBlipName("STRING")
    AddTextComponentString(data.label)
    EndTextCommandSetBlipName(blip)

    blips[data.networkID] = { blip = blip, shouldRemove = false, networkID = data.networkID, entityBlip = isEntityBlip, label = data.label }
end

RegisterNetEvent("orgs:syncTrackedVehicles")
AddEventHandler("orgs:syncTrackedVehicles", function(vehicles)
    for k, blip in pairs(blips) do
        blip.shouldRemove = true
    end

    -- exports["main_tablet_rework2"]:sendDispatchData(vehicles)
    for _, data in pairs(vehicles) do
        local hasOrg = false
        for orgName in pairs(data.jobs) do
            if DoesPlayerHaveOrg(orgName) then
                hasOrg = true
                break
            end
        end

        if hasOrg then
            if blips[data.networkID] and blips[data.networkID].label then
                if IsRenderingVehicle(data.networkID) and (blips[data.networkID].entityBlip) then
                    blips[data.networkID].shouldRemove = false
                elseif IsRenderingVehicle(data.networkID) and (not blips[data.networkID].entityBlip) then
                    RemoveBlip(blips[data.networkID].blip)
                    blips[data.networkID] = nil
                    createBlip(data)
                elseif not IsRenderingVehicle(data.networkID) and blips[data.networkID].entityBlip then
                    RemoveBlip(blips[data.networkID].blip)
                    blips[data.networkID] = nil
                    createBlip(data)
                else
                    SetBlipCoords(blips[data.networkID].blip, data.coords.x, data.coords.y, data.coords.z)
                    ShowHeadingIndicatorOnBlip(blips[data.networkID].blip, false)
                    blips[data.networkID].shouldRemove = false
                end
            else
                createBlip(data)
            end
        end
    end

    for k, blip in pairs(blips) do
        if blip.shouldRemove or (blip.entityBlip and not IsRenderingVehicle(blip.networkID)) then
            RemoveBlip(blip.blip)
            blips[k] = nil
        end
    end
end)

RegisterNetEvent("orgs:clearTrackedVehicles")
AddEventHandler("orgs:clearTrackedVehicles", function()
    for k, blip in pairs(blips) do
        RemoveBlip(blip.blip)
        blips[k] = nil
    end
end)

RegisterInteraction("vehicle", "jobtracker_remove", { label = "Remove Job Tracker", emoji = "🖲️" },
    function(vehicle)
        if DoesEntityExist(vehicle) then
            TriggerServerEvent("orgs:trackVehicleRemove", NetworkGetNetworkIdFromEntity(vehicle))
        end
    end,
    function(entity)
        if DoesPlayerHaveOrgPermission("orgveh.removetracker") and blips and blips[NetworkGetNetworkIdFromEntity(entity)] then
            return true
        end
        return false
    end
)
