local currentZone = false

CreateThread(function()
    Wait(500)
    for i, zone in ipairs(ESCORT_ZONES) do
        --zone.details.debugGrid = true
        zone.PolyZone = PolyZone:Create(zone.coords, zone.details)
    end
end)

local function escortFromZone(zone)
    print(zone)

    if type(zone) == "string" then
        for i, z in ipairs(ESCORT_ZONES) do
            if z.name == zone then
                zone = z
                break
            end
        end
    end

    print(zone)

    if not zone then return end

    local coords = zone.escortLocation.coords
    local heading = zone.escortLocation.heading
    local options = zone.escortLocation.options

    if coords then
        TeleportPlayerToCoords(coords, heading, options)
        TriggerEvent('fdg_ui:SendNotification', "You've been escorted outside by security")
    end
end

RegisterNetEvent("world:securityEscort")
AddEventHandler("world:securityEscort", function(zone)
    escortFromZone(zone)
end)

-- Interactions
function isPlayerAlive(entity)
    return (GetEntityHealth(entity) > 0.0) and (GetEntityHealth(entity) < 500.0)
end

function canSecurityEscort(entity)
    if IsPedArmed(entity, 4) then return false end

    -- if not isPlayerAlive(entity) then return false end

    currentZone = nil
    for i, z in ipairs(ESCORT_ZONES) do
        if z.PolyZone:isPointInside(GetEntityCoords(PlayerPedId())) then
            print(i, z)
            currentZone = z
        end
    end

    if not currentZone then return false end
    if not currentZone.Orgs then return false end

    local pData = Base.GetPlayerData()
    if not pData then return false end

    for i, org in ipairs(currentZone.Orgs) do
        if DoesPlayerHaveOrg(org) then
            return true
        end
    end

    return false
end

RegisterInteraction("player", "security", { label = "Call Security", emoji = "🚨" },
    function(entity)
        targetPlayer = GetPlayerServerId(entity)
        if currentZone then
            TriggerServerEvent("world:callSecurity", targetPlayer, currentZone.name)
        end
    end, function(entity)
        return canSecurityEscort(entity)
    end
)

function AreCoordsInEscortZone(coords)
    for i, zone in ipairs(ESCORT_ZONES) do
        local pz = PolyZone:Create(zone.coords, zone.details)
        if pz and pz:isPointInside(coords) then
            return true, zone
        end
    end

    return false
end

exports("AreCoordsInEscortZone", AreCoordsInEscortZone)
