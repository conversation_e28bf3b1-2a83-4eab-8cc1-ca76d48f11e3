RegisterNetEvent("core_hud:sendNotification")
AddEventHandler("core_hud:sendNotification", function(key, title, message, data)
  local d = config.setDefaultNotification()

  d.id = "NOTIFICATION_ADD"

  d.title = title
  d.data.message = message

  d.options.timeout = data.timeout or 4000
  d.data.messageIcon = data.messageIcon or ''
  d.options.position = data.position or "top-right"

  SendNUIMessage(d)
end)

RegisterNetEvent("core_hud:dispatchNotification")
AddEventHandler("core_hud:dispatchNotification", function(key, job, title, message, data, position)
  if IsPauseMenuActive() then return end

  local d = config.setDefaultNotification()
  
  d.id = "NOTIFICATION_ADD"

  d.title = title
  d.job = job
  d.priority = data.priority

  d.data.code = data.code
  d.data.message = message
  d.data.messageIcon = data.messageIcon or nil
  d.data.callsign = data.callsign
  d.data.location = data.location
  d.data.direction = data.direction
  d.options.timeout = data.timeout or 10000
  d.options.position = data.position or "top-right"
  d.overrides.headerBackground = data.headerBackground

  if position then
      local dist = GetDistanceBetweenCoords(GetEntityCoords(PlayerPedId()), position.x, position.y, position.z)
      local formattedDist = string.format("%.2f", dist / 1000) .. "km"
      d.data.location = d.data.location .. " | " .. formattedDist
  end

  SendNUIMessage(d)
end)

RegisterNetEvent("fdg_ui:SendNotification")
AddEventHandler("fdg_ui:SendNotification", function(message, data)
  local data = data or {}
  TriggerEvent("core_hud:sendNotification", nil, "", message, { timeout = data.timeout, position = data.layout })
end)
