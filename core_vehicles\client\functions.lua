TowTablet = {
	instantstopanim = false,
	objects = {},
	objSettings = {
		model = GetHashKey("hei_prop_dlc_tablet"),
		bone = 18905,
		offset = vector3(0.2, 0.05, 0.1),
		rotation = vector3(-25.0, -20.0, -140.0),
		dict = "anim@arena@amb@seat_drone_tablet@female@",
		anim = "idle_d",
	}
}

Citizen.CreateThread(function()
	if (not DecorIsRegisteredAsType("hasBeenPatched", 2)) then
		DecorRegister("hasBeenPatched", 2)
	end
end)

CreateThread(function()
	Wait(500)
	for i, zone in ipairs(MECH_ZONES_REPAIRS) do
		local polyzone = PolyZone:Create(zone.coords, zone.details)
	end
end)

function AreCoordsInRepairZone(coords)
	for i, zone in ipairs(MECH_ZONES_REPAIRS) do
		local pz = PolyZone:Create(zone.coords, zone.details)
		if pz and pz:isPointInside(coords) then
			return true, zone
		end
	end

	return false, "Unknown"
end

function clean(vehicle)
	if DoesEntityExist(vehicle) then
		Base.Animation.ActionAnimation("scenario", "WORLD_HUMAN_MAID_CLEAN", true, true, true, 10,
			function()
				TriggerEvent("fdg_ui:SendNotification", "Vehicle Cleaned")
				TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(vehicle), "clean")
			end
		)
	else
		TriggerEvent("fdg_ui:SendNotification", "No vehicle to clean")
	end
end

RegisterNetEvent("core_vehicles:clean")
AddEventHandler("core_vehicles:clean", function(vehNetId)
	local vehicle = NetworkGetEntityFromNetworkId(vehNetId)
	WashDecalsFromVehicle(vehicle, false)
	SetVehicleDirtLevel(vehicle, 0)
end)

function repairEngine(vehicle, value)
	if DoesEntityExist(vehicle) then
		local netid = NetworkGetNetworkIdFromEntity(vehicle)
		local repairType = (value == nil and "repairEngine" or "repairEnginehalf")
		local playerPed = PlayerPedId()
		local playerCoords = GetEntityCoords(playerPed)
		local inRangeOfMechVan = false
		local vehiclesInRadius = Base.Vehicles.GetVehiclesInRadius(playerPed, 25)

		for i = 1, #vehiclesInRadius do
			local currentVehicle = vehiclesInRadius[i]
			local vehicleModel = GetEntityModel(currentVehicle)
			local modelName = GetHashKey("MechVan")

			if vehicleModel == modelName then
				local vehicleCoords = GetEntityCoords(currentVehicle)
				local vehicleDistance = #(playerCoords - vehicleCoords)

				if vehicleDistance <= 25 then
					inRangeOfMechVan = true
					break
				end
			end
		end

		if (repairType == "repairEnginehalf") then
			if (GetVehicleEngineHealth(vehicle) >= 500.0) then
				TriggerEvent("fdg_ui:SendNotification", "This vehicle isn't damaged enough to be patched!")
				return
			end
		end

		local inRepairZone, zoneName = AreCoordsInRepairZone(GetEntityCoords(PlayerPedId()))

		-- print("Repairing Vehicle", netid, repairType, value, inRangeOfMechVan, inRepairZone, zoneName)

		TriggerServerEvent("core_vehicles:tryRepairVehicle", netid, repairType, value, inRangeOfMechVan, inRepairZone, zoneName)
	else
		TriggerEvent("fdg_ui:SendNotification", "No vehicle to repair")
	end
end

function flipVehicle(vehicle)
	if (DoesEntityExist(vehicle)) then
		exports["main_progressbar"]:start(2000, "Flipping Vehicle")
		Wait(2000)
		TriggerServerEvent("core_vehicles:flipVehicle", NetworkGetNetworkIdFromEntity(vehicle))
	else
		TriggerEvent("fdg_ui:SendNotification", "No vehicle to flip")
	end
end

RegisterNetEvent("core_vehicles:flipVehicle")
AddEventHandler("core_vehicles:flipVehicle", function(vehNetId, isTrailer)
	local veh = NetworkGetEntityFromNetworkId(vehNetId)

	local rot = GetEntityRotation(veh, 2)
	SetEntityRotation(veh, rot.x + 0.0, 0.0, rot.z + 0.0, 2, false)

	if isTrailer then
		local coords = GetEntityCoords(veh)
		SetEntityCoords(veh, coords.x, coords.y, coords.z + 0.5, false, false, false, true)
	end

	SetVehicleOnGroundProperly(veh)
end)

RegisterNetEvent("core_vehicles:repairEngine")
AddEventHandler("core_vehicles:repairEngine", function(vehNetId, value)
	local vehicle = NetworkGetEntityFromNetworkId(vehNetId)
	if not value then
		SetVehicleFixed(vehicle)
		SetVehicleDeformationFixed(vehicle)
		-- DecorSetBool(vehicle, "hasBeenPatched", false)
	elseif (GetVehicleEngineHealth(vehicle) < value) then
		SetVehicleEngineHealth(vehicle, 0.0 + value)
		-- DecorSetBool(vehicle, "hasBeenPatched", true)
	end
	SetVehicleUndriveable(vehicle, false)

	local damage = Base.Vehicles.Damage.Get(vehicle)
	local props  = Base.Vehicles.Props.Get(vehicle)
	TriggerServerEvent("core_vehicles:updateVehicle", NetworkGetNetworkIdFromEntity(vehicle), false, false, damage)
end)

function lockpick(vehicle)
	if not DoesEntityExist(vehicle) then
		TriggerEvent("fdg_ui:SendNotification", "No vehicle to lockpick")
		return
	end

	if DoesPlayerHaveOrgPermission("staffmechanic") then
		TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(vehicle), "setLockState", false)
		TriggerServerEvent("core_vehicles:addKey", GetPlayerServerId(PlayerId()), NetworkGetNetworkIdFromEntity(vehicle))
		GeneralLog("Lockpicked Vehicle", PlayerData.logName .. " has Lockpicked - " .. GetVehicleNumberPlateText(vehicle), "blue", "admin-functions")
		return
	end
	local canLockpick, reason = AwaitServerCallback("core_vehicles:attemptLockpick")

	if not canLockpick then
		TriggerEvent("fdg_ui:SendNotification", reason)
		return
	end

	TriggerEvent('fdg_ui:ShowNotification', { text = "You are unlocking this vehicle", theme = 'progress', timeout = 5000 })
	Base.Animation.ActionAnimation("scenario", "WORLD_HUMAN_WELDING", true, true, true, 5, function()
		TriggerEvent("fdg_ui:SendNotification", "Vehicle Unlocked")
		GeneralLog("Lockpicked Vehicle", PlayerData.logName .. " has Lockpicked - " .. GetVehicleNumberPlateText(vehicle), "blue", "player-action")
		TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(vehicle), "setLockState", false)

		if DoesPlayerHaveOrgPermission("vehicle.lockpick.assignKey") then
			TriggerServerEvent("core_vehicles:addKey", GetPlayerServerId(PlayerId()), NetworkGetNetworkIdFromEntity(vehicle))
		end
	end)
end

function setDoorState(vehicle, door, state)
	-- Not done or in resource export
end

RegisterNetEvent("core_vehicles:setDoorState")
AddEventHandler("core_vehicles:setDoorState", function(vehNetId, door, open)
	local forceChange = false
	if type(open) == "table" then
		forceChange = open[2]
		open = open[1]
	end
	local vehicle = NetworkGetEntityFromNetworkId(vehNetId)
	local loop = false
	if type(door) == "table" then
		for k, v in pairs(door) do
			if GetVehicleDoorLockStatus(vehicle) == 1 or forceChange then
				if open then
					SetVehicleDoorOpen(vehicle, v, false, false)
				else
					SetVehicleDoorShut(vehicle, v, false)
				end
			end
		end
	else
		if GetVehicleDoorLockStatus(vehicle) == 1 or forceChange then
			if open then
				SetVehicleDoorOpen(vehicle, door, false, false)
			else
				SetVehicleDoorShut(vehicle, door, false)
			end
		end
	end
end)

function toggleDoor(vehicle, door)
	-- Not done or in resource export
end

RegisterNetEvent("core_vehicles:toggleDoor")
AddEventHandler("core_vehicles:toggleDoor", function(vehNetId, args)
	local door = select(1, args)

	local vehicle = NetworkGetEntityFromNetworkId(vehNetId)
	if GetVehicleDoorLockStatus(vehicle) == 1 then
		if GetVehicleDoorAngleRatio(vehicle, door) > 0.0 then
			SetVehicleDoorShut(vehicle, door, false)
		else
			SetVehicleDoorOpen(vehicle, door, false, false)
		end
	elseif door == 4 then
		if GetVehicleDoorAngleRatio(vehicle, door) > 0.0 then
			SetVehicleDoorShut(vehicle, door, false)
		else
			SetVehicleDoorOpen(vehicle, door, false, false)
		end
	end
end)

local function startTowAnim()
	RequestModel(TowTablet.objSettings.model)
	while not HasModelLoaded(TowTablet.objSettings.model) do Citizen.Wait(150) end

	local playerPed = PlayerPedId()
	local coords = GetEntityCoords(playerPed)
	local bone = GetPedBoneIndex(playerPed, TowTablet.objSettings.bone)
	local object = CreateObject(TowTablet.objSettings.model, coords.x, coords.y, coords.z, true, true, false)

	table.insert(TowTablet.objects, object)
	AttachEntityToEntity(object, playerPed, bone, TowTablet.objSettings.offset.x, TowTablet.objSettings.offset.y, TowTablet.objSettings.offset.z,
		TowTablet.objSettings.rotation.x, TowTablet.objSettings.rotation.y, TowTablet.objSettings.rotation.z, true, false, false, false, 2, true)
	SetModelAsNoLongerNeeded(TowTablet.objSettings.handle)

	RequestAnimDict(TowTablet.objSettings.dict)
	while not HasAnimDictLoaded(TowTablet.objSettings.dict) do Wait(0) end
	TaskPlayAnim(playerPed, TowTablet.objSettings.dict, TowTablet.objSettings.anim, 3.0, -1, -1, 50, 0, false, false, false)
end

local function stopTowAnim()
	if not TowTablet.instantstopanim then
		Wait(3000)
	end
	for i, obj in ipairs(TowTablet.objects) do DeleteEntity(obj) end
	StopAnimTask(PlayerPedId(), TowTablet.objSettings.dict, TowTablet.objSettings.anim, 1.0)
	TowTablet.instantstopanim = false
end

function impound(vehicle, time)
	if DoesEntityExist(vehicle) then
		startTowAnim()

		OpenDialog(
			"Impound/Seizure Reason",
			function(reason)
				TriggerEvent("progress:start", 10000, "Registering tow request for vehicle")

				CreateThread(function()
					Wait(7000)
					TriggerServerEvent("core_vehicles:markVehicle", NetworkGetNetworkIdFromEntity(vehicle), reason or "", time or 0,
						exports["core_hud"]:getPlayerLocation())
					stopTowAnim()
				end)
			end,
			function()
				stopTowAnim()
			end,
			"string"
		)
	else
		TriggerEvent("fdg_ui:SendNotification", "No Vehicle Nearby")
	end
end

function checkEngineHealth(vehicle)
	local engineHealth = math.max(math.ceil((GetVehicleEngineHealth(vehicle) / 1000) * 100), 0)
	Base.Animation.ActionAnimation("scenario", "PROP_HUMAN_BUM_BIN", true, true, true, 10,
		function()
			TriggerEvent("fdg_ui:SendNotification", "Vehicle health: " .. engineHealth .. "%")
		end
	)
end

function regoCheck(vehicle)
	local playerPed = GetPlayerPed(-1)
	local coords = GetEntityCoords(playerPed)

	local attachedSettings = {
		target = PlayerId(),
		bone = 28422,
		x = -0.05,
		y = -0.01,
		z = 0.0,
		xr = -0.05,
		yr = 0.0,
		yz = 0.0,
		p9 = false,
		useSoftPinning = false,
		collision = false,
		isPed = -1,
		vertexIndex = 2,
		fixedRot = true
	}


	if DoesEntityExist(vehicle) then
		TriggerEvent("fdg_ui:SendNotification", "Checking Vehicle Registration", { timeout = 10000, theme = "progress", queue = "action" })

		Base.Objects.Create('prop_cs_tablet_02', coords.x, coords.y, coords.z, 0.0, "", false, attachedSettings)
		Base.Animation.ActionAnimation("cellphone@", "cellphone_text_read_base", true, false, true, 10,
			function()
				TriggerServerCallback("core_vehicles:getVehicle", function(entity)
					local networkOwner = entity.networkOwner
					local owner = entity.owner
					local rego = entity.rego
					local rego_expired = entity.rego_expired
					local keys = entity.keys
					local job = entity.job
					local job_label = entity.job_label
					displayRego(
						(job ~= nil and networkOwner.icname or (owner and owner.icname or "None")),
						keys,
						Base.Vehicles.GetLabel(GetEntityModel(vehicle)),
						(job ~= nil and job_label or (rego_expired and "Expired" or (rego ~= nil and rego or "Expired"))),
						"None",
						GetVehicleNumberPlateText(vehicle)
					)
				end, NetworkGetNetworkIdFromEntity(vehicle))
			end,
			function()
				Base.Objects.Delete(PlayerId() .. '_obj_prop_cs_tablet_02')
				TriggerEvent("fdg_ui:ClearNotifications", "player-action")
			end
		)

		Wait(10000)
		Base.Objects.Delete(PlayerId() .. '_obj_prop_cs_tablet_02')
	end
end

function displayRego(owner, coOwners, model, rego, flags, plate)
	local id = ""
	-- Owner
	id = id .. owner
	-- Model
	id = id .. "<br><i class='fas fa-car-side'></i> " .. model
	-- Rego
	id = id .. "<br><i class='far fa-registered'></i> " .. rego
	-- Flags
	id = id .. "<br><i class='fas fa-flag'></i> " .. flags
	TriggerEvent("core_hud:sendNotification", "id", "Rego For: " .. plate, id,
		{ messageIcon = "<i class='far fa-user'></i>", timeout = 10000, position = "top-right" })
end

-- Extras
RegisterNetEvent("core_vehicles:toggleExtra")
AddEventHandler("core_vehicles:toggleExtra", function(vehNetId, extraID)
	local vehicle = NetworkGetEntityFromNetworkId(vehNetId)
	SetVehicleExtra(vehicle, extraID, (IsVehicleExtraTurnedOn(vehicle, extraID) and 1) or 0)
end)

RegisterNetEvent("core_vehicles:setExtra")
AddEventHandler("core_vehicles:setExtra", function(vehNetId, extraID, state)
	local vehicle = NetworkGetEntityFromNetworkId(vehNetId)
	SetVehicleExtra(vehicle, extraID, state)
end)
