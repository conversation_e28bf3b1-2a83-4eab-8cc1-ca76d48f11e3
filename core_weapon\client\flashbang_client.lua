local AnimDict = "core";
local AnimName = "ent_anim_paparazzi_flash"
local FlashbangEquipped = false
local WeaponModel = "w_ex_flashbang"
local ped = GetPlayerPed(-1)

Citizen.CreateThread(function()
  if IsWeaponValid(4221696920) then
    AddTextEntry("WT_GNADE_FLSH", "Flashbang")
  end
end)


function FB_Thrown(prop)
  -- print("thrown")
  Wait(2500)
  local flashpos = GetEntityCoords(prop)
  SetEntityAsMissionEntity(prop, true, true)
  DeleteEntity(prop)
  AddExplosion(flashpos, 25, 0.0, 1.0, nil, true, true);
  TriggerServerEvent("Flashbang:DispatchExplosion", flashpos.x, flashpos.y, flashpos.z);
end

RegisterNetEvent("Flashbang:Explode", function(x, y, z, stunTime, afterTime, radius)
  -- print("boom")
  local stunRefTime = stunTime * 1000
  local afterRefTime = afterTime * 1000
  local finishTime = 0.0

  local pos = vector3(x, y, z)

  local ped = GetPlayerPed(-1);
  local pedPos = GetEntityCoords(ped)
  PlayParticles(pos)

  local distance = GetDistanceBetweenCoords(pedPos, pos, true)
  -- print(distance)


  if distance <= radius then
    --los check
    local lhandle = CreateObject(GetHashKey(WeaponModel), pos, false, true, false)
    local los = HasEntityClearLosToEntity(ped, lhandle, 17)
    DeleteEntity(lhandle)

    if los ~= false and not IsPedInAnyVehicle(ped, false) then
      --start the effects & animation
      AnimpostfxPlay("Dont_tazeme_bro", 0, true)
      ShakeGameplayCam("HAND_SHAKE", 15.0)
      --play animation
      -- Base.Animation.StartForcedAnim(ped, "anim@heists@ornate_bank@thermal_charge", "cover_eyes_intro", false, false, true)
      -- TaskPlayAnim(ped, "anim@heists@ornate_bank@thermal_charge", "cover_eyes_intro", 8.0, 2.0, -1, 50, 2.0, 0, 0, 0)
      -- local finishTime = GetGameTimer() + stunRefTime
      -- while GetGameTimer() < finishTime do
      --   DisablePlayerFiring(ped, true)
      --   Wait(0)
      -- end
      -- --stop animation
      -- Base.Animation.StopForcedAnim(ped)
      --lower the shake
      SetGameplayCamShakeAmplitude(10.0)
      finishTime = GetGameTimer() + afterRefTime
      while GetGameTimer() < finishTime do
        Wait(0)
      end
      StopGameplayCamShaking(true)
      AnimpostfxStop("Dont_tazeme_bro")
    end
  end
end)


function PlayParticles(pos)
  RequestNamedPtfxAsset(AnimDict)
  while (not HasNamedPtfxAssetLoaded(AnimDict)) do
    Wait(0)
  end
  UseParticleFxAsset(AnimDict)
  StartParticleFxLoopedAtCoord(AnimName, pos, 0.0, 0.0, 0.0, 25.0, false, false, false, false)
end

Citizen.CreateThread(function()
  ped = GetPlayerPed(-1)
  while true do
    ped = GetPlayerPed(-1)
    if FlashbangEquipped == false then
      if GetSelectedPedWeapon(ped) == GetHashKey("WEAPON_FLASHBANG") then
        FlashbangEquipped = true;
      end
    else
      if IsPedShooting(ped) then
        FlashbangEquipped = false;
        Wait(100)
        local pos = GetEntityCoords(ped)
        local handle = GetClosestObjectOfType(pos.x, pos.y, pos.z, 50.0, GetHashKey(WeaponModel), false, false, false)
        if handle ~= 0 then
          FB_Thrown(handle)
        end
      end
    end
    Wait(0)
  end
end)
