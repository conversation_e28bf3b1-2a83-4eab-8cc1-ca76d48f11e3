onBaseReady(
    function()
        RequestAnimDict("switch@michael@restaurant")
        RequestAnimSet("001510_02_gc_mics3_ig_1_base_michael")
        
        AddStateBagChangeHandler('nocollison', nil, function(bagName, key, value, _unused, replicated)

            if value ==false or value =="false" then return end
            

            local entNetworkID = tonumber(bagName:gsub('entity:', ''), 10)
            
            local attempts = 0 
           
            -- Needs to be like this for when ppl load in
            while not NetworkDoesEntityExistWithNetworkId(entNetworkID) and attempts < 5 do 
                attempts = attempts + 1
                Wait(100)
            end
            
            local stateBagObject =NetworkGetEntityFromNetworkId(entNetworkID)
            if GetEntityModel(stateBagObject) ~= GetHashKey("v_res_m_l_chair1") then return end 
            local playerIdx = GetPlayerFromServerId(value)
            local ped = GetPlayerPed(playerIdx)
            SetEntityNoCollisionEntity(stateBagObject,ped, false)
        
        end)
end)

local textDisplay = ""

                -- Seat Logic
local tablesLoaded = false
local closeToCasino = true
local closestTable = -1
local closestTableDistance = 1000
local currentTable = nil
local currentChair = nil
local currentChairNum

-- Betting Logic
local controlScaleform = nil
local bettingActive = false
local startRoundActive = false
local doubleDown = false
local BETTING_AMOUNT = 1000
local totalBet = 0
local currentBet = BETTING_AMOUNT
local currentBid = 0
local currentHand = "NA"
local tableCard= "Waiting For More Players"
local idleAnimation = false
local roundtime = 0
local round = "N/A"
local turnStatus = "Other"

local myseat

----------------------------------------------------------
-- Table Sync
----------------------------------------------------------
Citizen.CreateThread(function()
    TriggerEvent('chat:addSuggestion', '/addPokerTable', 'Add poker table.', { { name = "min", help = "Table buy-in amount" },{ name = "max", help = "Max bet for table" } })
  end)
  
local POKER_TABLES = {}

-- Sync tables from the server
RegisterNetEvent("poker:syncTables")
AddEventHandler("poker:syncTables", function(data)
    POKER_TABLES = data
    tablesLoaded = true
    -- loadDealers()
    -- Get the exact table coords
    reloadTablePositionsPoker()
end)

RegisterNetEvent("poker:addNewTable")
AddEventHandler("poker:addNewTable", function(data)
    POKER_TABLES[data.id]=data
    reloadTablePositionsPoker()
end)

RegisterNetEvent("poker:removeTable")
AddEventHandler("poker:removeTable", function(pos)
    POKER_TABLES[pos]=nil
    reloadTablePositionsPoker()
end)



Citizen.CreateThread(function()

    while true do
        Wait(100)
        if(roundtime > 0)then
            Wait(100)

            roundtime = roundtime - 1
        end
    end

end
)
----------------------------------------------------------
-- Main Loop
----------------------------------------------------------
local phone = exports["main_phone_2"]
function isPhoneOpen() -- in a seperate function that is only called on control presses to not interfere with UI elements
    return (phone and exports["main_phone_2"]:isPhoneOpen()) or phone == null
end

Citizen.CreateThread(function()
    while true do Wait(0)
        if currentTable then
            local height=0.105/2

            local globalheight=0.025    
            local height=0.0480+globalheight
            local height2 = height*1.5
            local height4 = height*2.15
            local height3 = height/2
            local width=0.030
            DrawScaleformMovieFullscreen(controlScaleform, 255, 255, 255, 255, 0)
            if startRoundActive or bettingActive then
                DrawRect(0.933, 0.928, 0.105, 0.032, 0, 0, 0, 150)
                DrawAdvancedNativeText(0.991, 0.935, 0.005, 0.0028, 0.29, "BET:", 255, 255, 255, 255, 0, 0)
                DrawAdvancedNativeText(1.041, 0.928, 0.005, 0.0028, 0.464, tostring(currentBet), 255, 255, 255, 255, 0, 0)
            end

            if bettingActive then
                -- Draw the instructional stuff
                -- Leave table with G or backspace
                if IsControlJustPressed(0, 194) and not isPhoneOpen() then
                    activeTurn=true
                    bettingActive=false
                    controlScaleform = setupBlackjackMidBetScaleform2("instructional_buttons")
                end

                -- Place bet with ENTER
                if IsControlJustPressed(0, 18) and not isPhoneOpen() then
                    TriggerServerEvent("poker:placeBet", currentTable, currentBet)
                    playCasinoAnimationPoker("large_bet")
                    PlaySoundFrontend(-1, "YES", "HUD_FRONTEND_DEFAULT_SOUNDSET",  true)
                    Wait(1000)
                    currentBet=0
                    roundtime=0
                end

                -- Lower bet with ARROW DOWN
                if IsControlJustPressed(0, 187) and not isPhoneOpen() then
                    local BJTable = POKER_TABLES[currentTable]
                    if not IsControlPressed(0, 21) then
                        currentBet = math.max(0, currentBet - (BJTable.increment or 1000))
                    else
                        currentBet = math.max(0, currentBet - (BJTable.increment or 1000) * 5)
                    end
                    PlaySoundFrontend(-1, "NAV_UP_DOWN", "HUD_FRONTEND_DEFAULT_SOUNDSET",  true)
                end

                -- Increase bet with ARROW UP
                if IsControlJustPressed(0, 188) and not isPhoneOpen() then
                    local BJTable = POKER_TABLES[currentTable]
                    if not IsControlPressed(0, 21) then
                        currentBet = math.min(BJTable.maxBet-BJTable.tempMinBet, currentBet + (BJTable.increment or 1000))
                    else
                        currentBet = math.min(BJTable.maxBet-BJTable.tempMinBet, currentBet + (BJTable.increment or 1000) * 5)
                    end
                    PlaySoundFrontend(-1, "NAV_UP_DOWN", "HUD_FRONTEND_DEFAULT_SOUNDSET",  true)
                end
            elseif startRoundActive then
                DrawScaleformMovieFullscreen(controlScaleform, 255, 255, 255, 255, 0)
                local BJTable = POKER_TABLES[currentTable]
                if IsControlJustPressed(0, 18) and not isPhoneOpen() and currentBet ~= 0 then
                    TriggerServerEvent("poker:placeBet", currentTable, BJTable.minBet)
                    playCasinoAnimationPoker("large_bet")
                    PlaySoundFrontend(-1, "YES", "HUD_FRONTEND_DEFAULT_SOUNDSET",  true)
                    Wait(1000)
                    currentBet=0
                    roundtime=0
                end

                if IsControlJustPressed(0, 194) and not isPhoneOpen() then
                    TriggerServerEvent("poker:requestEjection", currentTable)
                    PlaySoundFrontend(-1, "NO", "HUD_FRONTEND_DEFAULT_SOUNDSET",  true)
                end

            elseif activeTurn then
                -- Draw the instructional stuff
                DrawScaleformMovieFullscreen(controlScaleform, 255, 255, 255, 255, 0)
                --call With enter
             
                if(POKER_TABLES[currentTable].tempMinBet-totalBet ~=0)then
                    if IsControlJustPressed(0, 18) and not isPhoneOpen() then
                        TriggerServerEvent("poker:placeBet", currentTable, POKER_TABLES[currentTable].tempMinBet-totalBet)
                        playCasinoAnimationPoker("hit")
                        PlaySoundFrontend(-1, "NO", "HUD_FRONTEND_DEFAULT_SOUNDSET",  true)
                    end
                else
                    if IsControlJustPressed(0, 18) and not isPhoneOpen() then
                        TriggerServerEvent("poker:selectOption", currentTable, "check")
                        playCasinoAnimationPoker("hit")
                        PlaySoundFrontend(-1, "NO", "HUD_FRONTEND_DEFAULT_SOUNDSET",  true)
                    end
    
                end
            
                -- Raise with D
                if IsControlJustPressed(0, 35) and not isPhoneOpen()  then
                    controlScaleform = setupBlackjackInstructionalScaleform2("instructional_buttons")
                    currentBet=POKER_TABLES[currentTable].tempMinBet-totalBet
                    activeTurn=false
                    bettingActive=true
                end
  
                if IsControlJustPressed(0, 47) and not isPhoneOpen() then
                    TriggerServerEvent("poker:selectOption", currentTable, "fold")
                    playCasinoAnimationPoker("bust")
                end
                

            elseif leaveTable then
                DrawScaleformMovieFullscreen(controlScaleform, 255, 255, 255, 255, 0)

                if IsControlJustPressed(0, 47) and not isPhoneOpen() or IsControlJustPressed(0, 194) and not isPhoneOpen() then
                    TriggerServerEvent("poker:requestEjection", currentTable)
                end
            end
                
            if idleAnimation then
         
                if not IsEntityPlayingAnim(PlayerPedId(), "switch@michael@restaurant", "001510_02_gc_mics3_ig_1_base_michael", 3) then
                    local coords = GetEntityCoords(PlayerPedId())
                    if ( #( coords - currentChair.coords ) > 0.5 ) then
                        SetEntityCoords(PlayerPedId(), currentChair.coords.x, currentChair.coords.y, currentChair.coords.z, false, false, false)
                        SetEntityRotation(PlayerPedId(), currentChair.rotation.x, currentChair.rotation.y, currentChair.rotation.z - 180.0, 2, false)
                    end
                    TaskPlayAnim(PlayerPedId(), "switch@michael@restaurant", "001510_02_gc_mics3_ig_1_base_michael", 8.0, 8.0, -1, 1, 0.0, false, false, false)
                end
            end

        elseif closestTableDistance < 3 and not currentTable then
            local dealerCoords = closestTable.coords
            DrawText3Ds(dealerCoords.x, dealerCoords.y, dealerCoords.z + 1.2, (closestTable.dealer.name or "Dealer").."\n[~g~G~w~] Join | Min: ~r~$"..closestTable.minBet.."~w~ | Max ~g~$"..closestTable.maxBet)
            if IsControlJustPressed(0, 47) then
                TriggerServerEvent("poker:requestSeatAtTable", closestTable.id)
            end

        end

    end
end)

----------------------------------------------------------
-- Animation Logic
----------------------------------------------------------

Citizen.CreateThread(function()
    local dict = "anim_casino_b@amb@casino@games@shared@player@"
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do Wait(0) end

    local dict = "anim_casino_b@amb@casino@games@blackjack@player"
    RequestAnimDict(dict)
    while not HasAnimDictLoaded(dict) do Wait(0) end
end)

function getAnimDetailsPoker(animType)
    local animDict = "anim_casino_b@amb@casino@games@shared@player@"
    local animName = ""
    local animTime = 8000

    if animType == "stand" then
        animDict = "anim_casino_b@amb@casino@games@blackjack@player"
        animName = "decline_card_001"
        animTime = 2176

    elseif animType == "hit" then
        animDict = "anim_casino_b@amb@casino@games@blackjack@player"
        animName = "request_card"
        animTime = 1841

    elseif animType == "large_bet" then
        animDict = "anim_casino_b@amb@casino@games@blackjack@player"
        animName = "place_bet_large"
        animTime = 2681

    elseif animType == "bust" then
        animDict = "anim_casino_b@amb@casino@games@shared@player@"
        animName = "reaction_terrible_var_01"
        animTime = 7141

    elseif animType == "lost" then
        animDict = "anim_casino_b@amb@casino@games@shared@player@"
        animName = "reaction_bad_var_01"
        animTime = 5344

    elseif animType == "draw" then
        animDict = "anim_casino_b@amb@casino@games@shared@player@"
        animName = "reaction_impartial_var_01"
        animTime = 4808

    elseif animType == "win" then
        animDict = "anim_casino_b@amb@casino@games@shared@player@"
        animName = "reaction_good_var_01"
        animTime = 6180

    end

    return animDict, animName, animTime
end

function playCasinoAnimationPoker(animType)
    idleAnimation = false

    local animDict, animName, animTime = getAnimDetailsPoker(animType)
    TaskPlayAnim(PlayerPedId(), animDict, animName, 8.0, -8.0, -1, 48, 0.0, false, false, false)
    Citizen.CreateThread(function()
        local finish = GetGameTimer() + animTime
        while ( GetGameTimer() < finish ) do
            Wait(0)
        end
        idleAnimation = true
    end)
end

RegisterNetEvent("poker:playAnim")
AddEventHandler("poker:playAnim", playCasinoAnimationPoker)

----------------------------------------------------------
-- Card Drawing Logic
----------------------------------------------------------

local cardObjects = {}

function getCardFromNumberPoker(cardId)
    if cardId == "AceClubs" then
        return "vw_prop_vw_club_char_a_a"
    elseif cardId == "2Clubs" then
        return "vw_prop_vw_club_char_02a"
    elseif cardId == "3Clubs" then
        return "vw_prop_vw_club_char_03a"
    elseif cardId == "4Clubs" then
        return "vw_prop_vw_club_char_04a"
    elseif cardId == "5Clubs" then
        return "vw_prop_vw_club_char_05a"
    elseif cardId == "6Clubs" then
        return "vw_prop_vw_club_char_06a"
    elseif cardId == "7Clubs" then
        return "vw_prop_vw_club_char_07a"
    elseif cardId == "8Clubs" then
        return "vw_prop_vw_club_char_08a"
    elseif cardId == "9Clubs" then
        return "vw_prop_vw_club_char_09a"
    elseif cardId == "10Clubs" then
        return "vw_prop_vw_club_char_10a"
    elseif cardId == "JackClubs" then
        return "vw_prop_vw_club_char_j_a"
    elseif cardId == "QueenClubs" then
        return "vw_prop_vw_club_char_q_a"
    elseif cardId == "KingClubs" then
        return "vw_prop_vw_club_char_k_a"
    elseif cardId == "AceDiamonds" then
        return "vw_prop_vw_dia_char_a_a"
    elseif cardId == "2Diamonds" then
        return "vw_prop_vw_dia_char_02a"
    elseif cardId == "3Diamonds" then
        return "vw_prop_vw_dia_char_03a"
    elseif cardId == "4Diamonds" then
        return "vw_prop_vw_dia_char_04a"
    elseif cardId == "5Diamonds" then
        return "vw_prop_vw_dia_char_05a"
    elseif cardId == "6Diamonds" then
        return "vw_prop_vw_dia_char_06a"
    elseif cardId == "7Diamonds" then
        return "vw_prop_vw_dia_char_07a"
    elseif cardId == "8Diamonds" then
        return "vw_prop_vw_dia_char_08a"
    elseif cardId == "9Diamonds" then
        return "vw_prop_vw_dia_char_09a"
    elseif cardId == "10Diamonds" then
        return "vw_prop_vw_dia_char_10a"
    elseif cardId == "JackDiamonds" then
        return "vw_prop_vw_dia_char_j_a"
    elseif cardId == "QueenDiamonds" then
        return "vw_prop_vw_dia_char_q_a"
    elseif cardId == "KingDiamonds" then
        return "vw_prop_vw_dia_char_k_a"
    elseif cardId == "AceHearts" then
        return "vw_prop_vw_hrt_char_a_a"
    elseif cardId == "2Hearts" then
        return "vw_prop_vw_hrt_char_02a"
    elseif cardId == "3Hearts" then
        return "vw_prop_vw_hrt_char_03a"
    elseif cardId == "4Hearts" then
        return "vw_prop_vw_hrt_char_04a"
    elseif cardId == "5Hearts" then
        return "vw_prop_vw_hrt_char_05a"
    elseif cardId == "6Hearts" then
        return "vw_prop_vw_hrt_char_06a"
    elseif cardId == "7Hearts" then
        return "vw_prop_vw_hrt_char_07a"
    elseif cardId == "8Hearts" then
        return "vw_prop_vw_hrt_char_08a"
    elseif cardId == "9Hearts" then
        return "vw_prop_vw_hrt_char_09a"
    elseif cardId == "10Hearts" then
        return "vw_prop_vw_hrt_char_10a"
    elseif cardId == "JackHearts" then
        return "vw_prop_vw_hrt_char_j_a"
    elseif cardId == "QueenHearts" then
        return "vw_prop_vw_hrt_char_q_a"
    elseif cardId == "KingHearts" then
        return "vw_prop_vw_hrt_char_k_a"
    elseif cardId == "AceSpades" then
        return "vw_prop_vw_spd_char_a_a"
    elseif cardId == "2Spades" then
        return "vw_prop_vw_spd_char_02a"
    elseif cardId == "3Spades" then
        return "vw_prop_vw_spd_char_03a"
    elseif cardId == "4Spades" then
        return "vw_prop_vw_spd_char_04a"
    elseif cardId == "5Spades" then
        return "vw_prop_vw_spd_char_05a"
    elseif cardId == "6Spades" then
        return "vw_prop_vw_spd_char_06a"
    elseif cardId == "7Spades" then
        return "vw_prop_vw_spd_char_07a"
    elseif cardId == "8Spades" then
        return "vw_prop_vw_spd_char_08a"
    elseif cardId == "9Spades" then
        return "vw_prop_vw_spd_char_09a"
    elseif cardId == "10Spades" then
        return "vw_prop_vw_spd_char_10a"
    elseif cardId == "JackSpades" then
        return "vw_prop_vw_spd_char_j_a"
    elseif cardId == "QueenSpades" then
        return "vw_prop_vw_spd_char_q_a"
    elseif cardId == "KingSpades" then
        return "vw_prop_vw_spd_char_k_a"
    elseif cardId == "DummyCard" then
        return "vw_prop_vw_lux_card_01a"
    end
	
	return "vw_prop_casino_cards_single"
end

function setActiveSeat(tableID, chairID, timer,chairNetworkID)
    if (POKER_TABLES[tableID] == nil) then
        return
    end
    if(currentChairNum==charID)then
        PlaySoundFrontend(-1,"Menu_Accept","Phone_SoundSet_Default",1)
    end
    SendNUIMessage({ active = {seatNum=chairID}})
    SendNUIMessage({ roundTime = {amount=timer}})
    local tbl=POKER_TABLES[tableID]
    local pokerTableObj = GetClosestObjectOfType(tbl.coords.x, tbl.coords.y, tbl.coords.z, 1.0, tbl.prop, 0, 0, 0)
    local ent = Entity(pokerTableObj)
    for chairID = 1, 6 do
        local chair = NetworkGetEntityFromNetworkId(ent.state["chair"..chairID])
        SetEntityDrawOutline(chair,false)
    end
    local chair= NetworkGetEntityFromNetworkId(chairNetworkID)
    SetEntityDrawOutlineColor(0,100,50,183)
    SetEntityDrawOutline(chair,true)

end

function updateDealer(tableID,seat)

    if (POKER_TABLES[tableID] == nil) then
        return
    end

    SendNUIMessage({ dealer = {seatNum=seat}})

end

function showCard(tableID, chairID, handOne, handTwo, handRank)
    if (POKER_TABLES[tableID] == nil) then
        return
    end
    if currentTable==tableID then
        SendNUIMessage({ setHand = {seatNum=chairID,cardPos=1,card=handOne}})
        SendNUIMessage({ setHand = {seatNum=chairID,cardPos=2,card=handTwo}})
        SendNUIMessage({ rank = {seatNum=chairID,amount=handRank}})

    end
end

function dealCardToSeatPoker(tableID, chairID, handOne, handTwo, handRank)

    if (POKER_TABLES[tableID] == nil) then
        return
    end
    local BJTable = POKER_TABLES[tableID]
    local dealer = BJTable.dealer
    dealer.entity=GetPlayerPed(-1)
    local dealerAnimPrefix = ""

    if currentTable==tableID then
        SendNUIMessage({ setHand = {seatNum=chairID,cardPos=1,card=handOne}})
        SendNUIMessage({ setHand = {seatNum=chairID,cardPos=2,card=handTwo}})

    end

    
        local cardModelOne = getCardFromNumberPoker(handOne)
        local cardModelTwo = getCardFromNumberPoker(handTwo)
        local cardObjectOne = createNewCardPoker(BJTable, cardModelOne)
        local cardObjectTwo = createNewCardPoker(BJTable, cardModelTwo)
        local dummyCardObjectOne = createNewCardPoker(BJTable, getCardFromNumberPoker("DummyCard"),true)
        local dummyCardObjectTwo = createNewCardPoker(BJTable, getCardFromNumberPoker("DummyCard"),true)

        table.insert(cardObjects, cardObjectOne)
        table.insert(cardObjects, cardObjectTwo)
        table.insert(cardObjects, dummyCardObjectOne)
        table.insert(cardObjects, dummyCardObjectTwo)
        -- AttachEntityToEntity(cardObject, dealer.entity, GetPedBoneIndex(dealer.entity, 28422), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1, 2, 1)
        FreezeEntityPosition(cardObjectOne,true)
        FreezeEntityPosition(cardObjectTwo,true)
        FreezeEntityPosition(dummyCardObjectOne,true)
        FreezeEntityPosition(dummyCardObjectTwo,true)
        -- Add the card to the table
        if not BJTable.cards then BJTable.cards = {} end
        if not BJTable.cards[chairID] then BJTable.cards[chairID] = {} end
        local tempChairID=chairID
        table.insert(BJTable.cards[chairID], cardObjectOne)
        table.insert(BJTable.cards[chairID], cardObjectTwo)
        table.insert(BJTable.cards[chairID], dummyCardObjectOne)
        table.insert(BJTable.cards[chairID], dummyCardObjectTwo)
     

        local tableCoords = BJTable.objCoords
        local cardOffsetX, cardOffsetY, cardOffsetZ = 0.5737, 0.2376, 0.948025
        if chairID <= 6 then
            cardOffsetX, cardOffsetY, cardOffsetZ = getCardOffsetPoker(1, chairID, 0) --iVar9 is seat number 0-3
        end

        local cardOffsetPos = GetObjectOffsetFromCoords(tableCoords.x, tableCoords.y, tableCoords.z, BJTable.heading, cardOffsetX, cardOffsetY, cardOffsetZ)

        SetEntityCoordsNoOffset(cardObjectOne, cardOffsetPos.x, cardOffsetPos.y, cardOffsetPos.z, 0, 0, 1)
        FreezeEntityPosition(cardObjectOne,true)

        local seatHeading = BJTable.seats[chairID].rotation
        SetEntityRotation(cardObjectOne, 0.0, 0.0, seatHeading.z, 2, 1)
        SetEntityVisible(cardObjectOne, true)

        ---CARD TWO
        local tableCoords = BJTable.objCoords
        local cardOffsetX, cardOffsetY, cardOffsetZ = 0.5737, 0.2376, 0.948025
        if chairID <= 6 then
            cardOffsetX, cardOffsetY, cardOffsetZ = getCardOffsetPoker(2, chairID, 0) --iVar9 is seat number 0-3
        end

        local cardOffsetPos = GetObjectOffsetFromCoords(tableCoords.x, tableCoords.y, tableCoords.z, BJTable.heading, cardOffsetX, cardOffsetY, cardOffsetZ)

        SetEntityCoordsNoOffset(cardObjectTwo, cardOffsetPos.x, cardOffsetPos.y, cardOffsetPos.z, 0, 0, 1)

        FreezeEntityPosition(cardObjectTwo,true)

        local seatHeading = BJTable.seats[chairID].rotation
        SetEntityRotation(cardObjectTwo, 0.0, 0.0, seatHeading.z, 2, 1)
        SetEntityVisible(cardObjectTwo, true)

        SetEntityVisible(dummyCardObjectTwo, true)
        SetEntityVisible(dummyCardObjectOne, true)
        Wait(2000)

        FreezeEntityPosition(cardObjectOne,false)
        FreezeEntityPosition(cardObjectTwo,false) 
        
        FreezeEntityPosition(dummyCardObjectTwo,false)
        FreezeEntityPosition(dummyCardObjectOne,false) 

        Citizen.CreateThread(function()
        while DoesEntityExist(dummyCardObjectOne) do
            Wait(0)
            SetEntityLocallyInvisible(dummyCardObjectTwo)
            SetEntityLocallyInvisible(dummyCardObjectOne)
            end
        end)


        AttachEntityToEntity(cardObjectTwo, GetPlayerPed(-1), GetPedBoneIndex(GetPlayerPed(-1), 6286), 0.09, 0.06, -0.025, 39.94, -45.035, -85.005, 1, 1, 0, 0, 2, 1)
        AttachEntityToEntity(dummyCardObjectTwo, GetPlayerPed(-1), GetPedBoneIndex(GetPlayerPed(-1), 6286), 0.09, 0.06, -0.025, 39.94, -45.035, -85.005, 1, 1, 0, 0, 2, 1)

        AttachEntityToEntity(cardObjectOne, GetPlayerPed(-1), GetPedBoneIndex(GetPlayerPed(-1), 36029), 0.085, 0.065, 0.03, -135.01, 49.99, 115.0, 1, 1, 0, 0, 2, 1)
        AttachEntityToEntity(dummyCardObjectOne, GetPlayerPed(-1), GetPedBoneIndex(GetPlayerPed(-1), 36029), 0.085, 0.065, 0.03, -135.01, 49.99, 115.0, 1, 1, 0, 0, 2, 1)

        myseat=charID

        currentHand=handValue
        
end

function updateTableBet(tableID, seat,newBet,newTablePot,amount,term)
    if (POKER_TABLES[tableID] == nil) then
        return
    end
    if(currentTable == tableID) then

        local correctAmount=amount
        if term == "Raise" then
            correctAmount=newBet-POKER_TABLES[currentTable].tempMinBet
        end

        POKER_TABLES[currentTable].tempMinBet=newBet
        POKER_TABLES[currentTable].tablePot=newTablePot 
    
        
        SendNUIMessage({ pot = {amount=newTablePot}})
        SendNUIMessage({ setSeat = {seatNum=seat,action="bet",amount=correctAmount}})
        SendNUIMessage({ setSeat = {seatNum=seat,action="money",amount=newBet}})
        SendNUIMessage({ setSeat = {seatNum=seat,action="action",amount=term}})
        textDisplay= "SEAT "..seat.." "..term.." "..correctAmount
        SendNUIMessage({newLog={text=textDisplay}})
    end

end


--- DEAL CARD TO TABLE + UPDATE THE PLAYERS RANK 
function dealCardToTablePoker(tableID, cardName, cardCount, seat, handRanking)
    if (POKER_TABLES[tableID] == nil) then
        return
    end
    local BJTable = POKER_TABLES[tableID]
    local dealer = BJTable.dealer


    if(tableCard=="Waiting For More Players") then
        tableCard=cardName
    else
        tableCard=tableCard..", "..cardName
    end

    if handRanking then 
        SendNUIMessage({ rank = {seatNum=seat,amount=handRanking}})
    end

    N_0x469f2ecdec046337(1)
    StartAudioScene("DLC_VW_Casino_Cards_Focus_Hand")


    SendNUIMessage({ tableCard = {cardpos=cardCount,card=cardName}})
    local cardModel = getCardFromNumberPoker(cardName)
    local cardObject = createNewCardPoker(BJTable, cardModel)
    table.insert(cardObjects, cardObject)
    -- AttachEntityToEntity(cardObject, dealer.entity, GetPedBoneIndex(dealer.entity, 28422), 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0, 0, 0, 1, 2, 1)

    -- Add the card to the table
    if not BJTable.dealerCards then BJTable.dealerCards = {} end
    table.insert(BJTable.dealerCards, cardObject)


    Wait(500)

    -- Unhide the card object

    local animCount=0

    local tableCoords = BJTable.objCoords
    cardOffsetX, cardOffsetY, cardOffsetZ = getCardOffsetDealerPoker(cardCount)

    local cardOffsetPos = GetObjectOffsetFromCoords(tableCoords.x, tableCoords.y, tableCoords.z, BJTable.heading, cardOffsetX, cardOffsetY, cardOffsetZ)
    SetEntityCoordsNoOffset(cardObject, cardOffsetPos.x, cardOffsetPos.y, cardOffsetPos.z, 0, 0, 1)
    SetEntityRotation(cardObject, 0.0, 0.0, 90.0, 2, 1)

    Wait(200)
    SetEntityVisible(cardObject, true)

end

function getCardOffsetPoker(cardCount, chairID, bParam2)
    if chairID == 1 then
        if cardCount == 1 then
            return 0.4364123, -0.6773041, 0.821991
        elseif cardCount == 2 then
            return 0.4364123, -0.6773041+0.08, 0.821991
        end
    elseif chairID == 2 then
        if cardCount == 1 then
            return 0.4458337, 0.3892341, 0.8212814
        elseif cardCount == 2 then
            return 0.4458337, 0.3892341+0.08, 0.8212814
        end
    elseif chairID == 3 then
        if cardCount == 1 then
            return 0.03211045, 0.9218106, 0.8206253
        elseif cardCount == 2 then
            return 0.03211045+0.08, 0.9218106, 0.8206253
        end
    elseif chairID == 4 then
        if cardCount == 1 then
            return -0.4364123, 0.3892341, 0.8212814
        elseif cardCount == 2 then
            return -0.4364123, 0.3892341-0.08, 0.8212814
        end
    elseif chairID == 5 then
        if cardCount == 1 then
            return -0.4364123, -0.6773041, 0.821991
        elseif cardCount == 2 then
            return -0.4364123, -0.6773041+0.08, 0.821991
        end
    elseif chairID == 6 then
        if cardCount == 1 then
            return 0.03211045, -0.9218106, 0.8206253
        elseif cardCount == 2 then
            return 0.03211045+0.08, -0.9218106, 0.8206253
        end
    end  
	
	return 0.0, 0.0, 0.947875
end

function getCardOffsetDealerPoker(cardCount)
    if cardCount == 1 then
        return -0.01332,-0.250,0.397785
    elseif cardCount == 2 then
        return  -0.011332,-0.250+(cardCount-1)*0.156264,0.397785
    elseif cardCount == 3 then
        return -0.011332,-0.250+(cardCount-1)*0.156264,0.397785
    elseif cardCount == 4 then
        return  -0.011332,-0.250+(cardCount-1)*0.156264,0.397785
    elseif cardCount == 5 then
        return -0.011332,-0.250+(cardCount-1)*0.156264,0.397785
    elseif cardCount == 6 then
        return -0.011332,-0.250+(cardCount-1)*0.156264,0.397785
    elseif cardCount == 7 then
        return -0.011332,-0.250+(cardCount-1)*0.156264,0.397785
    end  
end

function createNewCardPoker(BJTable, cardModel,networkStatus)
    -- Hash and model loading
    local cardHash = GetHashKey(cardModel)
    if not HasModelLoaded(cardHash) then
        RequestModel(cardHash)
        while not HasModelLoaded(cardHash) do Wait(0) end
    end
    local networkStatusTemp=false

    if networkStatus then
        networkStatusTemp=true
    end
    -- Coords and object spawning
    local tableCoords = BJTable.objCoords
    local machineOffset = GetObjectOffsetFromCoords(tableCoords.x, tableCoords.y, tableCoords.z, BJTable.heading, 0.526, 0.571, 0.963)
    local cardObject = CreateObjectNoOffset(cardHash, machineOffset.x, machineOffset.y, machineOffset.z, networkStatusTemp, false, 1)
    SetModelAsNoLongerNeeded(cardHash)

    SetEntityVisible(cardObject, false)

    return cardObject
end

function updatePlayer(tableID,chairID,action)
    if (POKER_TABLES[tableID] == nil) then
        return
    end

    if( currentTable ==tableID) then
        SendNUIMessage({ setSeat = {seatNum=chairID,action="action",amount=action}})
    end

    if action=="Winner" then  
        local tbl=POKER_TABLES[tableID]
        local blackjackTableObj = GetClosestObjectOfType(tbl.coords.x, tbl.coords.y, tbl.coords.z, 1.0, tbl.prop, 0, 0, 0)
        local ent = Entity(blackjackTableObj)
        for chairloopID = 1, 6 do
            local chair = NetworkGetEntityFromNetworkId(ent.state["chair"..chairloopID])
            SetEntityDrawOutline(chair,false)
        end
        
        local chair = NetworkGetEntityFromNetworkId(ent.state["chair"..chairID])
        SetEntityDrawOutlineColor(255,215,0,180)
        SetEntityDrawOutline(chair,true)
    end
    textDisplay= "SEAT "..chairID.." "..action

    SendNUIMessage({newLog={text=textDisplay}})

end

function addSeat(tableID,chairID)

    if (POKER_TABLES[tableID] == nil) then
        return
    end

    if( currentTable ==tableID) then
        SendNUIMessage({ showSeat = {display=true,seatNum=chairID}})
        SendNUIMessage({ setSeat = {seatNum=seat,action="bet",amount=0}})
        SendNUIMessage({ setSeat = {seatNum=seat,action="money",amount=0}})
        SendNUIMessage({ setSeat = {seatNum=seat,action="action",amount="Joined"}})
    end
    textDisplay= "SEAT "..chairID.." ".."Joined"
    SendNUIMessage({newLog={text=textDisplay}})

end


function cleanupSeatPoker(tableID, chairID)
    if (POKER_TABLES[tableID] == nil) then
        return
    end

    local BJTable = POKER_TABLES[tableID]
    local dealer = BJTable.dealer
    local dealerEntity = BJTable.dealer.entity
    currentBet = BJTable.minBet or 1000
    local dealerAnimPrefix = ""
    if dealer.style > 6 then
        dealerAnimPrefix = "female_"
    end
    
    if (currentTable == tableID) then
        SendNUIMessage({ rank = {seatNum=chairID,amount=" "}})
    end

        Citizen.CreateThread(function()
            Wait(100)

                for i, object in ipairs(BJTable.cards[chairID] or {}) do
                    DeleteEntity(object)
                end

                BJTable.cards[chairID] = {}

        end)
        
        Citizen.CreateThread(function()
            Wait(5000)
            for i, object in ipairs(BJTable.cards[chairID] or {}) do
                DeleteEntity(object)
            end
            BJTable.cards[chairID] = {}
        end)
    -- end
end

function cleanupDealerPoker(tableID)
    if (POKER_TABLES[tableID] == nil) then
        return
    end
    local BJTable = POKER_TABLES[tableID]
    POKER_TABLES[tableID].tempMinBet=POKER_TABLES[tableID].minBet
    POKER_TABLES[tableID].tablePot=0
    SendNUIMessage({ pot = {amount=0}})

    local dealer = BJTable.dealer
    local dealerEntity = BJTable.dealer.entity

    if( currentTable ==tableID) then
        tableCard= "Waiting For More Players"
        SendNUIMessage({ cleanUpTable = true})
        
    end
    local dealerAnimPrefix = ""
    if dealer.style > 6 then
        dealerAnimPrefix = "female_"
    end
    local tbl=POKER_TABLES[tableID]
    local blackjackTableObj = GetClosestObjectOfType(tbl.coords.x, tbl.coords.y, tbl.coords.z, 1.0, tbl.prop, 0, 0, 0)
    local ent = Entity(blackjackTableObj)
    for chairID = 1, 6 do
        local chair = NetworkGetEntityFromNetworkId(ent.state["chair"..chairID])
        SetEntityDrawOutline(chair,false)
    end
    Citizen.CreateThread(function()

            for i, object in ipairs(BJTable.dealerCards or {}) do
                DeleteEntity(object)
            end

        BJTable.dealerCards = {}
    end)

    Citizen.CreateThread(function()
        Wait(5000)
        for i, object in ipairs(BJTable.dealerCards or {}) do
            DeleteEntity(object)
        end
        BJTable.dealerCards = {}
    end)    
end

function cleanupTablePoker(tableID)
    for chairID = 1, 6 do
      cleanupSeatPoker(tableID, chairID)

    end

    cleanupDealerPoker(tableID)
end

RegisterNetEvent("poker:setActiveSeat")
AddEventHandler("poker:setActiveSeat", setActiveSeat)

RegisterNetEvent("poker:dealCardToSeat")
AddEventHandler("poker:dealCardToSeat", dealCardToSeatPoker)

RegisterNetEvent("poker:showCard")
AddEventHandler("poker:showCard", showCard)

RegisterNetEvent("poker:dealCardToTable")
AddEventHandler("poker:dealCardToTable", dealCardToTablePoker)

RegisterNetEvent("poker:updateDealer")
AddEventHandler("poker:updateDealer", updateDealer)


RegisterNetEvent("poker:updateTableBet")
AddEventHandler("poker:updateTableBet", updateTableBet)

RegisterNetEvent("poker:updatePlayer")
AddEventHandler("poker:updatePlayer", updatePlayer)

RegisterNetEvent("poker:cleanupSeat")
AddEventHandler("poker:cleanupSeat", cleanupSeatPoker)

RegisterNetEvent("poker:addSeat")
AddEventHandler("poker:addSeat", addSeat)

RegisterNetEvent("poker:cleanupDealer")
AddEventHandler("poker:cleanupDealer", cleanupDealerPoker)

RegisterNetEvent("poker:clearCards")
AddEventHandler("poker:clearCards", cleanupTablePoker)

AddEventHandler("onClientResourceStop", function(name)
    if name ~= GetCurrentResourceName() then return end
    for i, entity in ipairs(cardObjects) do DeleteEntity(entity) end
end)



----------------------------------------------------------
-- Gameplay Logic
----------------------------------------------------------
RegisterNetEvent("poker:startRound")
AddEventHandler("poker:startRound", function(state)
    if(state) then
        PlaySoundFrontend(-1,"Menu_Accept","Phone_SoundSet_Default",1)
        controlScaleform = setupPokerFirstRoundScaleform("instructional_buttons")
    else
        controlScaleform=nil
        turnStatus = "Other"
        SendNUIMessage({ setSeat = {seatNum=currentChairNum,action="info",amount=""}})
    end
    if (waiting)then
        turnStatus = "~r~Active"
        PlaySoundFrontend(-1,"Menu_Accept","Phone_SoundSet_Default",1)

        SendNUIMessage({ active = {seatNum=currentChairNum}})
        -- SendNUIMessage({ setSeat = {seatNum=currentChairNum,action="info",amount="Active"}})
    end
   

    activeTurn = false
    leaveTable = false
    SendNUIMessage({ roundTime = {amount=15}})

    if(timer) then
     roundtime = timer-20
    end
    startRoundActive = state    
end)



RegisterNetEvent("poker:setBettingState")
AddEventHandler("poker:setBettingState", function(state,timer,waiting)

    if(state) then

        controlScaleform = setupBlackjackMidBetScaleform2("instructional_buttons")
    else
        controlScaleform=nil
        turnStatus = "Other"
        SendNUIMessage({ setSeat = {seatNum=currentChairNum,action="info",amount=""}})
    end
    if (waiting)then
        turnStatus = "~r~Active"
        PlaySoundFrontend(-1,"Menu_Accept","Phone_SoundSet_Default",1)
        SendNUIMessage({ active = {seatNum=currentChairNum}})
        -- SendNUIMessage({ setSeat = {seatNum=currentChairNum,action="info",amount="Active"}})
    end

    leaveTable = false
    SendNUIMessage({ roundTime = {amount=15}})
    if(timer) then
     roundtime = timer-5
    end
    bettingActive=false
    startRoundActive=false
    activeTurn = state    
end)



RegisterNetEvent("poker:setLeaveStatus")
AddEventHandler("poker:setLeaveStatus", function(state)
    controlScaleform = setupBlackjackLeaveScaleform2("instructional_buttons")
    bettingActive = false
    activeTurn = false
    leaveTable = state

end)

----------------------------------------------------------
-- Seats
----------------------------------------------------------

local lastInterior = 0

-- Closest Table Thread
Citizen.CreateThread(function()
    local casinoInterior = GetInteriorAtCoords(964.46, 40.76, 75.58)
    while true do Wait(1000)
        if tablesLoaded and closeToCasino then 
            local playerCoords = GetEntityCoords(PlayerPedId())
            closestTableDistance = 10
            closestTable = -1

            for key, value in pairs(POKER_TABLES) do                
                local distance = #(playerCoords - POKER_TABLES[key].coords)
                if distance < closestTableDistance then
                    closestTableDistance = distance
                    closestTable = POKER_TABLES[key]
                end
            end

            if closestTable ~= -1 and not closestTable.seats then
                getChairsForTablePoker(closestTable)
            end
        end

        local interiorId = GetInteriorFromEntity(PlayerPedId())
        if lastInterior ~= interiorId and interiorId == casinoInterior then
            lastInterior = interiorId
            reloadTablePositionsPoker()
        end
    end
end)

-- Server tells player which seat to sit in after request
RegisterNetEvent("poker:inviteToChair")
AddEventHandler("poker:inviteToChair", function(tableID, chairID, betting)
    if (POKER_TABLES[tableID] == nil) then
        return
    end 
    local BJTable = POKER_TABLES[tableID]
    currentTable = tableID
    bettingActive = betting
    currentChair = BJTable.seats[chairID]
    currentBet = BJTable.minBet or 1000
    currentChairNum=chairID
    SendNUIMessage({ toggleDisplay = true})
    playerSitInSeatPoker(tableID, chairID)
    idleAnimation = true
end)

-- Server tells player to leave the table
RegisterNetEvent("poker:ejected")
AddEventHandler("poker:ejected", function(seat,id)
    cleanupSeatPoker(id, seat)
    cleanupDealerPoker(id)
    currentTable = nil
    currentBet=nil
    SendNUIMessage({ toggleDisplay = false})
    SendNUIMessage({ cleanUpPokerUi = true})
    playerLeaveSeatPoker()

    idleAnimation = false
    Wait(3000)
    idleAnimation = false
end)

-- Reload the table data (coords, heading etc)
function reloadTablePositionsPoker()
    for k, v in pairs(POKER_TABLES) do
        if not v.loaded then
            v.tableObj = GetClosestObjectOfType(v.coords.x, v.coords.y, v.coords.z, 1.0, v.prop, 0, 0, 0)

            if DoesEntityExist(v.tableObj) then
                v.objCoords = GetEntityCoords(v.tableObj)
                v.heading = GetEntityHeading(v.tableObj)

                -- Calculate dealer coordinates
                local angle = v.heading + 90.0
                if v.heading >= 270.0 then angle = 0.0 + (v.heading - 270.0) end
                local radians = math.rad(angle)
                local x = v.objCoords.x + (0.774878 * math.cos(radians))
                local y = v.objCoords.y + (0.774878 * math.sin(radians))
                local z = v.coords.z + 1.01        

                -- Calculate dealer heading
                local heading = v.heading - 180
                if heading < 0.0 then 
                    heading = 360.0 + heading 
                end

                -- Update the table
                v.dealer.position = {}
                v.dealer.position.coords = vector3(x, y, z)
                v.dealer.position.heading = heading
                v.loaded = true
                -- v.dealer.entity = 
                -- v.dealer.entity = spawnDealer(v.dealer.style, v.dealer.position)
            end
        end
    end
end

-- Finds the vectors and rotation of chairs for a blackjack table
function getChairsForTablePoker(tbl)
    reloadTablePositionsPoker()

    local blackjackTableObj = GetClosestObjectOfType(tbl.coords.x, tbl.coords.y, tbl.coords.z, 1.0, tbl.prop, 0, 0, 0)
    local ent = Entity(blackjackTableObj)
    SetEntityInvincible(ent,true)
    if DoesEntityExist(blackjackTableObj) and DoesEntityHaveDrawable(blackjackTableObj) then
        tbl.seats = {}

        for chairID = 1, 6 do
            local chair = NetworkGetEntityFromNetworkId(ent.state["chair"..chairID])
            SetEntityInvincible(chair,true)
            SetEntityDrawOutline(chair,false)
            if chairID==1 then
                SetEntityRotation(chair,00.00,00.00,240.00,2,0)
            elseif chairID==2 then
                SetEntityRotation(chair,00.00,00.00,300.00,2,0)
            elseif chairID==5 then 
                SetEntityRotation(chair,00.00,00.00,120.00,2,0)
            elseif chairID==4 then
                SetEntityRotation(chair,00.00,00.00,60.00,2,0)
            elseif chairID==6 then
                SetEntityRotation(chair,00.00,00.00,180.00,2,0)
            end
            -- SetEntityNoCollisionEntity(chair,GetPlayerPed(-1), false)

            -- local boneId = GetEntityBoneIndexByName(blackjackTableObj, "Chair_Base_0"..chairBoneNumber)
         
            local chairCoords = GetEntityCoords(chair,false)
            local chairRotation = GetEntityRotation(chair,2)
 
            if (chairRotation.z<0) then
                chairRotation= chairRotation + vector3(0.0,0.0,360.0)
            end
            chairCoords=chairCoords -vector3(0.0,0.0,0.20)
           tbl.seats[chairID] = {coords = chairCoords, rotation = chairRotation,networkid=ent.state["chair"..chairID]}
        end
    end
end

-- Instructs ped to go to and sit on a chair
function playerSitInSeatPoker(tableID, chairID)
    reloadTablePositionsPoker()

    local seatDetails = POKER_TABLES[tableID].seats[chairID]
    local coords = seatDetails.coords
    local rotation = seatDetails.rotation

    -- Determines the coordinates where the animation starts
    local walkToVector = GetAnimInitialOffsetPosition("anim_casino_b@amb@casino@games@shared@player@", "sit_enter_left", coords.x, coords.y, coords.z, rotation.x, rotation.y, rotation.z, 0.01, 2)
    local targetRotationVector = GetAnimInitialOffsetRotation("anim_casino_b@amb@casino@games@shared@player@", "sit_enter_left", coords.x, coords.y, coords.z, rotation.x, rotation.y, rotation.z, 0.01, 2)
    local targetHeading = targetRotationVector.z
 

    -- Makes the ped walk to those coordinates
    TaskGoStraightToCoord(PlayerPedId(), walkToVector.x, walkToVector.y, walkToVector.z, 1.0, 2000, targetHeading, 1)

    local playerPos = GetEntityCoords(PlayerPedId())

    -- while #(playerPos - walkToVector) > 0.8 do
    --     Wait(50)
    --     playerPos = GetEntityCoords(PlayerPedId())
    -- end
    
    Wait(500)
    
    local data = {xOffset=0.0,yOffset=0.1,zOffset=-0.5}
    local ped = PlayerPedId()
    local object = NetworkGetEntityFromNetworkId(seatDetails.networkid)
    local objPos = GetOffsetFromEntityInWorldCoords(object, 0.0 + data.xOffset, 0.0 + -data.yOffset, 0.0 + -data.zOffset) 
    TriggerServerEvent("main_poker:setChairNoCollison",seatDetails.networkid)
    TaskStartScenarioAtPosition(ped, "PROP_HUMAN_SEAT_BENCH", objPos.x, objPos.y, objPos.z, GetEntityHeading(object) + 180.0, -1, true, true)
    Wait(2000)

end

-- Instructs ped to leave current seat
function playerLeaveSeatPoker()
    TaskPlayAnim(PlayerPedId(), "anim_casino_b@amb@casino@games@shared@player@", "sit_exit_left", 1.0, 1.0, 2500, 0)
end



----------------------------------------------------------
-- User Interface
----------------------------------------------------------


RegisterNetEvent("poker:clearText")
AddEventHandler("poker:clearText", function()
    textDisplay = ""
end)


RegisterNetEvent("poker:displayWinStatus")
AddEventHandler("poker:displayWinStatus", function(text)
    textDisplay = text
    SendNUIMessage({newLog={text=textDisplay}})
end)

RegisterNetEvent("poker:displayCustomText")
AddEventHandler("poker:displayCustomText", function(text)
    textDisplay = text
    -- SendNUIMessage({newLog={text=textDisplay}})
end)

--- THIS CAN ALSO HAPPEN IN dealCardToTable
RegisterNetEvent("poker:updateCurrentRank")
AddEventHandler("poker:updateCurrentRank", function(text,seat)
    SendNUIMessage({ rank = {seatNum=seat,amount=text}})
    currentHand = text
end)

RegisterNetEvent("poker:displayBetAmount")
AddEventHandler("poker:displayBetAmount", function(amount,total)
    -- textDisplay = "You have placed a bet for ~g~$"..amount

    totalBet=total
    if not startRoundActive then
        controlScaleform = setupBlackjackMidBetScaleform2("instructional_buttons")
    end
    -- controlScaleform = setupBlackjackMidBetScaleform("instructional_buttons")
    
end)




RegisterNetEvent("poker:displayInvalidAmount")
AddEventHandler("poker:displayInvalidAmount", function()
    textDisplay = "You don't have enough ~g~money ~w~to place this bet"
    SendNUIMessage({newLog={text=textDisplay}})
end)

RegisterNetEvent("poker:displayInvalidTableBet")
AddEventHandler("poker:displayInvalidTableBet", function()
    textDisplay = "You need to call the tables current hand"
    SendNUIMessage({newLog={text=textDisplay}})
end)


Citizen.CreateThread(function()
	local function draw2dText(text, coords)
		SetTextFont(4)
		SetTextProportional(1)
		SetTextScale(0.55, 0.55)
		SetTextColour(255, 255, 255, 255)
		SetTextDropShadow(0, 0, 0, 0, 255)
		SetTextEdge(1, 0, 0, 0, 255)
		SetTextDropShadow()
		SetTextOutline()
		SetTextWrap(0.0, 1.0)
		SetTextCentre(true)
		SetTextJustification(0)
		SetTextEntry('STRING')
		AddTextComponentString(text)
		DrawText(0.5, 0.89)
	end

	while true do Wait(0)
		if textDisplay and textDisplay ~= "" then
			draw2dText(textDisplay, {0.5, 0.9})
		end
	end
end)

----------------------------------------------------------
-- Utility
----------------------------------------------------------

-- Draws 3D text above table
function DrawText3Ds(x,y,z, text)
    local onScreen,_x,_y=World3dToScreen2d(x,y,z)
    local px,py,pz=table.unpack(GetGameplayCamCoords())
    
    SetTextScale(0.4, 0.4)
    SetTextFont(4)
    SetTextProportional(1)
    SetTextColour(255, 255, 255, 215)
    SetTextDropshadow(10, 0, 0, 0)
    SetTextEntry("STRING")
    SetTextCentre(1)
    AddTextComponentString(text)
    DrawText(_x,_y)
    -- local factor = (string.len(text)) / 370
    -- DrawRect(_x,_y+0.0125, 0.015+ factor, 0.03, 41, 11, 41, 68)
end

function DrawAdvancedNativeText(x,y,w,h,sc, text, r,g,b,a,font,jus)
    SetTextFont(font)
    SetTextScale(sc, sc)
	N_0x4e096588b13ffeca(jus)
    SetTextColour(254, 254, 254, 255)
    SetTextEntry("STRING")
    AddTextComponentString(text)
	DrawText(x - 0.1+w, y - 0.02+h)
end

----------------------------------------------------------
-- Scaleforms
----------------------------------------------------------

function ButtonMessage(text)
    BeginTextCommandScaleformString("STRING")
    AddTextComponentScaleform(text)
    EndTextCommandScaleformString()
end

function Button(ControlButton)
    N_0xe83a3e3557a56640(ControlButton)
end

function setupPokerFirstRoundScaleform(scaleform)
    local scaleform = RequestScaleformMovie(scaleform)
    while not HasScaleformMovieLoaded(scaleform) do
        Citizen.Wait(0)
    end
    PushScaleformMovieFunction(scaleform, "CLEAR_ALL")
    PopScaleformMovieFunctionVoid()
    
    PushScaleformMovieFunction(scaleform, "SET_CLEAR_SPACE")
    PushScaleformMovieFunctionParameterInt(200)
    PopScaleformMovieFunctionVoid()

    PushScaleformMovieFunction(scaleform, "SET_DATA_SLOT")
    PushScaleformMovieFunctionParameterInt(2)
    Button(GetControlInstructionalButton(2, 194, true)) -- The button to display
    ButtonMessage("Leave table") --BACKSPACE
    PopScaleformMovieFunctionVoid()

    PushScaleformMovieFunction(scaleform, "SET_DATA_SLOT")
    PushScaleformMovieFunctionParameterInt(1)
    Button(GetControlInstructionalButton(2, 191, true))
    ButtonMessage("Play Round") --ENTER
    PopScaleformMovieFunctionVoid()
    
    PushScaleformMovieFunction(scaleform, "DRAW_INSTRUCTIONAL_BUTTONS")
    PopScaleformMovieFunctionVoid()

    PushScaleformMovieFunction(scaleform, "SET_BACKGROUND_COLOUR")
    PushScaleformMovieFunctionParameterInt(0)
    PushScaleformMovieFunctionParameterInt(0)
    PushScaleformMovieFunctionParameterInt(0)
    PushScaleformMovieFunctionParameterInt(80)
    PopScaleformMovieFunctionVoid()

    return scaleform

end

function setupBlackjackInstructionalScaleform2(scaleform)
    local scaleform = RequestScaleformMovie(scaleform)
    while not HasScaleformMovieLoaded(scaleform) do
        Citizen.Wait(0)
    end
    PushScaleformMovieFunction(scaleform, "CLEAR_ALL")
    PopScaleformMovieFunctionVoid()
    
    PushScaleformMovieFunction(scaleform, "SET_CLEAR_SPACE")
    PushScaleformMovieFunctionParameterInt(200)
    PopScaleformMovieFunctionVoid()

    PushScaleformMovieFunction(scaleform, "SET_DATA_SLOT")
    PushScaleformMovieFunctionParameterInt(6)
    Button(GetControlInstructionalButton(2, 194, true)) -- The button to display
    ButtonMessage("Exit Raise") --BACKSPACE
    PopScaleformMovieFunctionVoid()

    PushScaleformMovieFunction(scaleform, "SET_DATA_SLOT")
    PushScaleformMovieFunctionParameterInt(1)
    Button(GetControlInstructionalButton(2, 191, true))
    ButtonMessage("Place bet") --ENTER
    PopScaleformMovieFunctionVoid()

    PushScaleformMovieFunction(scaleform, "SET_DATA_SLOT")
    PushScaleformMovieFunctionParameterInt(3)
    Button(GetControlInstructionalButton(2, 187, true))
    ButtonMessage("Down bet") --Page Down
    PopScaleformMovieFunctionVoid()

    PushScaleformMovieFunction(scaleform, "SET_DATA_SLOT")
    PushScaleformMovieFunctionParameterInt(4)
    Button(GetControlInstructionalButton(2, 188, true))
    ButtonMessage("Up bet") --Page Up
    PopScaleformMovieFunctionVoid()

    PushScaleformMovieFunction(scaleform, "SET_DATA_SLOT")
    PushScaleformMovieFunctionParameterInt(5)
    Button(GetControlInstructionalButton(2, 21, true))
    ButtonMessage("Bulk Change") --Page Up
    PopScaleformMovieFunctionVoid() 
    

    PushScaleformMovieFunction(scaleform, "DRAW_INSTRUCTIONAL_BUTTONS")
    PopScaleformMovieFunctionVoid()

    PushScaleformMovieFunction(scaleform, "SET_BACKGROUND_COLOUR")
    PushScaleformMovieFunctionParameterInt(0)
    PushScaleformMovieFunctionParameterInt(0)
    PushScaleformMovieFunctionParameterInt(0)
    PushScaleformMovieFunctionParameterInt(80)
    PopScaleformMovieFunctionVoid()

    return scaleform
end

function setupBlackjackMidBetScaleform2(scaleform)
    local scaleform = RequestScaleformMovie(scaleform)
    while not HasScaleformMovieLoaded(scaleform) do
        Citizen.Wait(0)
    end
    PushScaleformMovieFunction(scaleform, "CLEAR_ALL")
    PopScaleformMovieFunctionVoid()
    
    PushScaleformMovieFunction(scaleform, "SET_CLEAR_SPACE")
    PushScaleformMovieFunctionParameterInt(200)
    PopScaleformMovieFunctionVoid()

    --call
    if(POKER_TABLES[currentTable].tempMinBet-totalBet >1)then
        PushScaleformMovieFunction(scaleform, "SET_DATA_SLOT")
        PushScaleformMovieFunctionParameterInt(0)
        Button(GetControlInstructionalButton(2, 191, true))
        ButtonMessage("Call with ~r~"..POKER_TABLES[currentTable].tempMinBet-totalBet) --ENTER
        PopScaleformMovieFunctionVoid()
       
        PushScaleformMovieFunction(scaleform, "SET_DATA_SLOT")
        PushScaleformMovieFunctionParameterInt(2)
        Button(GetControlInstructionalButton(2, 35, true)) -- The button to display
        ButtonMessage("Raise") -- D
        PopScaleformMovieFunctionVoid()
    else
        PushScaleformMovieFunction(scaleform, "SET_DATA_SLOT")
        PushScaleformMovieFunctionParameterInt(3)
        Button(GetControlInstructionalButton(2, 191, true))
        ButtonMessage("Check") --ENTER
       
        PopScaleformMovieFunctionVoid()
        PushScaleformMovieFunction(scaleform, "SET_DATA_SLOT")
        PushScaleformMovieFunctionParameterInt(2)
        Button(GetControlInstructionalButton(2, 35, true)) -- The button to display
        ButtonMessage("Raise") -- D
        PopScaleformMovieFunctionVoid()
    end
    
    PushScaleformMovieFunction(scaleform, "SET_DATA_SLOT")
    PushScaleformMovieFunctionParameterInt(1)
    Button(GetControlInstructionalButton(2, 47, true)) -- The button to display
    ButtonMessage("Fold") -- G
    PopScaleformMovieFunctionVoid()


    PushScaleformMovieFunction(scaleform, "DRAW_INSTRUCTIONAL_BUTTONS")
    PopScaleformMovieFunctionVoid()

    PushScaleformMovieFunction(scaleform, "SET_BACKGROUND_COLOUR")
    PushScaleformMovieFunctionParameterInt(0)
    PushScaleformMovieFunctionParameterInt(0)
    PushScaleformMovieFunctionParameterInt(0)
    PushScaleformMovieFunctionParameterInt(80)
    PopScaleformMovieFunctionVoid()

    return scaleform
end

function setupBlackjackLeaveScaleform2(scaleform)
    local scaleform = RequestScaleformMovie(scaleform)
    while not HasScaleformMovieLoaded(scaleform) do
        Citizen.Wait(0)
    end
    PushScaleformMovieFunction(scaleform, "CLEAR_ALL")
    PopScaleformMovieFunctionVoid()
    
    PushScaleformMovieFunction(scaleform, "SET_CLEAR_SPACE")
    PushScaleformMovieFunctionParameterInt(200)
    PopScaleformMovieFunctionVoid()

    PushScaleformMovieFunction(scaleform, "SET_DATA_SLOT")
    PushScaleformMovieFunctionParameterInt(1)
    Button(GetControlInstructionalButton(2, 194, true)) -- The button to display
    ButtonMessage("Leave table") --BACKSPACE
    PopScaleformMovieFunctionVoid()    

    PushScaleformMovieFunction(scaleform, "DRAW_INSTRUCTIONAL_BUTTONS")
    PopScaleformMovieFunctionVoid()

    PushScaleformMovieFunction(scaleform, "SET_BACKGROUND_COLOUR")
    PushScaleformMovieFunctionParameterInt(0)
    PushScaleformMovieFunctionParameterInt(0)
    PushScaleformMovieFunctionParameterInt(0)
    PushScaleformMovieFunctionParameterInt(80)
    PopScaleformMovieFunctionVoid()

    return scaleform
end




  