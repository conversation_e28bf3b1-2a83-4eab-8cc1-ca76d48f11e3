Config = {}

--- ===================================== QUI Alt Menu ========================================== ---

AddVehicleInteractionAction(
    function() -- Condition
        local veh = GetVehiclePedIsIn(GetPlayerPed(-1))
        if GetVehicleDoorLockStatus(veh) == 1 then return true end
    end,

    function() -- Action
        local veh = GetVehiclePedIsIn(GetPlayerPed(-1))
        exports["core_vehicles"]:lockVehicle(veh)
    end,

    { text = "Lock Vehicle", icon = "&#xF0341;", action = "vehicle_lock"},  -- Menu Item

    {requires = {Vehicle = true}}  -- Requires
)

AddVehicleInteractionAction(
    function() -- Condition
        local veh = GetVehiclePedIsIn(GetPlayerPed(-1))
        if GetVehicleDoorLockStatus(veh) == 2 then return true end
    end,

    function() -- Action
        local veh = GetVehiclePedIsIn(GetPlayerPed(-1))
        exports["core_vehicles"]:unlockVehicle(veh)
    end,

    { text = "Unlock Vehicle", icon = "&#xF0FC7;", action = "vehicle_unlock"},  -- Menu Item

    {requires = {Vehicle = true}}  -- Requires
)

AddVehicleInteractionAction(
    function() -- Condition
        local veh = GetVehiclePedIsIn(GetPlayerPed(-1))
        if Entity(veh).state.bootlocked ~= true then return true end
    end,

    function() -- Action
        local veh = GetVehiclePedIsIn(GetPlayerPed(-1))
        exports["core_vehicles"]:lockVehicleBoot(veh)
    end,

    { text = "Lock Vehicle Boot", icon = "&#xF07AA;", action = "vehicle_lock_boot"},  -- Menu Item

    {requires = {Vehicle = true}}  -- Requires
)

AddVehicleInteractionAction(
    function() -- Condition
        local veh = GetVehiclePedIsIn(GetPlayerPed(-1))
        if Entity(veh).state.bootlocked == true then return true end
    end,

    function() -- Action
        local veh = GetVehiclePedIsIn(GetPlayerPed(-1))
        exports["core_vehicles"]:unlockVehicleBoot(veh)
    end,

    { text = "Unlock Vehicle Boot", icon = "&#xF07AA;", action = "vehicle_unlock_boot"},  -- Menu Item

    {requires = {Vehicle = true}}  -- Requires
)

--- ============================================================================================= ---

-- Toggle the locks on a vehicle (export)
function toggleVehicleLocks(veh)
	local closeVeh = veh or Base.Vehicles.GetClosestVehicle(GetPlayerPed(-1), 5.0, true)

	if isDead then return end

	if GetVehicleDoorLockStatus(closeVeh) == 1 then
		lockVehicle(closeVeh)
	else
		unlockVehicle(closeVeh)
	end
end

-- Set a vehicle to be locked (export)
function lockVehicle(veh)
	canUnlockVehicle(veh, function(allowed)
		if allowed then
			local invalidEntityOwner = GetPlayerServerId(NetworkGetEntityOwner(veh)) == 0

			if invalidEntityOwner then
				TriggerServerEvent("core_vehicles:resetEntityOwner", NetworkGetNetworkIdFromEntity(veh))
				return
			end

			lockAnimation()
			TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(veh), "setLockState", true)
			TriggerEvent("core_hud:sendNotification", "vehicle-locks", "", "<font color='red'>Locked</font>", { messageIcon = '<i class="fas fa-key"></i>' })
			Entity(veh).state:set("interactionTime", GetNetworkTime(), true)
		end
	end)
end

-- Set a vehicle to be unlocked (export)
function unlockVehicle(veh)
	canUnlockVehicle(veh, function(allowed)
		if allowed then
			local invalidEntityOwner = GetPlayerServerId(NetworkGetEntityOwner(veh)) == 0

			if invalidEntityOwner then
				TriggerServerEvent("core_vehicles:resetEntityOwner", NetworkGetNetworkIdFromEntity(veh))
				return
			end

			lockAnimation()
			TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(veh), "setLockState", false)
			TriggerEvent("core_hud:sendNotification", "vehicle-locks", "", "<font color='green'>Unlocked</font>", { messageIcon = '<i class="fas fa-key"></i>' })
			Entity(veh).state:set("interactionTime", GetNetworkTime(), true)
		end
	end)
end

function lockVehicleBoot(veh, hideNotification)
	canUnlockVehicle(veh, function(allowed)
		if allowed then
			lockAnimation()
			TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(veh), "setBootLockState", true)
			if not hideNotification then
				TriggerEvent("core_hud:sendNotification", "vehicle-locks", "", "<font color='red'>Boot Locked</font>", { messageIcon = '<i class="fas fa-key"></i>' })
			end
		end
	end)
end

function unlockVehicleBoot(veh, hideNotification)
	canUnlockVehicle(veh, function(allowed)
		if allowed then
			lockAnimation()
			TriggerServerEvent("core_vehicles:affectVehicle", NetworkGetNetworkIdFromEntity(veh), "setBootLockState", false)
			if not hideNotification then
				TriggerEvent("core_hud:sendNotification", "vehicle-locks", "", "<font color='green'>Boot Unlocked</font>", { messageIcon = '<i class="fas fa-key"></i>' })
			end
		end
	end)
end

-- Simple test to determine whether a player has access to lock / unlock a vehicle (export)
function canUnlockVehicle(veh, cb)

	if IsPedBeingStunned(PlayerPedId(), 0) then
		TriggerEvent('fdg_ui:SendNotification', '<font color="red">Your hands are seized up...</font>', {layout = 'bottom-right'})
		return
	end

	if LocalPlayer.state.wounded then
		cb(false)
		return
	end

	if (veh == 0 or veh == nil or veh == -1) then
		cb(false)
		return
	end

	local rawPlate = getRawPlate(veh)
	exports["base_vehicle"]:isOwner(veh, function(isOwner, hasKeys)
		if rawPlate == ("j"..PlayerData.identifier..PlayerData.charid) then
			cb(true)
		elseif rawPlate == ("m"..PlayerData.identifier..PlayerData.charid) then
			cb(true)
		elseif (isOwner or hasKeys or HasKey(veh)) then
			cb(true)
		else
			cb(false)
			TriggerEvent('fdg_ui:SendNotification', '<font color="red">You do not have the Keys</font>', {layout = 'bottom-right'})
		end
	end)
end

-- Converts a plate of a car into a comparable string (export)
function getRawPlate(veh)
	local plate = GetVehicleNumberPlateText(veh)
	if plate then
		return string.gsub(string.lower(plate), "%s+", "")
	end
end

RegisterNetEvent("core_vehicles:setLockState")
AddEventHandler('core_vehicles:setLockState', function(netId, lock, ignoreBeep)
	if NetworkDoesNetworkIdExist(netId) then
		local veh = NetworkGetEntityFromNetworkId(netId)

		if (lock) then
			Base.Sound.PlayOnEntity(veh, 5.0, "lock2", 0.25)
		else
			Base.Sound.PlayOnEntity(veh, 5.0, "lock1", 0.25)
		end

		if not GetIsVehicleEngineRunning(veh) and not ignoreBeep then
			for i = 1, ( lock and 2 or 1 ) do
				SetVehicleIndicatorLights(veh, 1, true)
				SetVehicleIndicatorLights(veh, 0, true)
				SetVehicleBrakeLights(veh, true)
				SetVehicleLights(veh, 2)
				Wait(230)
				SetVehicleIndicatorLights(veh, 1, false)
				SetVehicleIndicatorLights(veh, 0, false)
				SetVehicleBrakeLights(veh, false)
				SetVehicleLights(veh, 0)
				Wait(230)
			end
		end
	end
end)

RegisterNetEvent("core_vehicles:setBootLockState")
AddEventHandler('core_vehicles:setBootLockState', function(netId, lock, ignoreBeep)
	if NetworkDoesNetworkIdExist(netId) then
		local veh = NetworkGetEntityFromNetworkId(netId)

		if (lock) then
			Base.Sound.PlayOnEntity(veh, 5.0, "lock2", 0.25)
		else
			Base.Sound.PlayOnEntity(veh, 5.0, "lock1", 0.25)
		end

		if not GetIsVehicleEngineRunning(veh) and not ignoreBeep then
			for i = 1, ( lock and 2 or 1 ) do
				SetVehicleIndicatorLights(veh, 1, true)
				SetVehicleIndicatorLights(veh, 0, true)
				SetVehicleBrakeLights(veh, true)
				SetVehicleLights(veh, 2)
				Wait(230)
				SetVehicleIndicatorLights(veh, 1, false)
				SetVehicleIndicatorLights(veh, 0, false)
				SetVehicleBrakeLights(veh, false)
				SetVehicleLights(veh, 0)
				Wait(230)
			end
		end
	end
end)

function lockAnimation()
	if ( not IsPedInAnyVehicle(GetPlayerPed(-1), true) ) then
		local anim = { dict = "anim@mp_player_intmenu@key_fob@", name = "fob_click"}
		while ( not HasAnimDictLoaded(anim.dict) ) do
			RequestAnimDict(anim.dict)
			Wait(0)
		end
		TaskPlayAnim(GetPlayerPed(-1), anim.dict, anim.name, 8.0, -8.0, 1000, 50, 0, 0, 0, 0)
	end
end

CreateThread(function()
	while true do
		Wait(300)
		local vehicle = GetVehiclePedIsTryingToEnter(PlayerPedId())

		if vehicle ~= 0 and DoesEntityExist(vehicle) and NetworkDoesEntityExistWithNetworkId(NetworkGetNetworkIdFromEntity(vehicle)) then
			local invalidEntityOwner = GetPlayerServerId(NetworkGetEntityOwner(vehicle)) == 0

			if invalidEntityOwner then
				TriggerServerEvent("core_vehicles:resetEntityOwner", NetworkGetNetworkIdFromEntity(vehicle))
				Wait(15000)
			end
		end
	end
end)